#!/usr/bin/env node

// Test script to verify scrolling functionality on onboarding flow page
const puppeteer = require('puppeteer');
const path = require('path');

async function testScrolling() {
  console.log('🧪 Testing scrolling functionality on onboarding flow page...\n');
  
  let browser;
  try {
    // Launch browser
    browser = await puppeteer.launch({
      headless: false, // Show browser for visual verification
      devtools: true,
      args: ['--disable-web-security', '--disable-features=VizDisplayCompositor']
    });
    
    const page = await browser.newPage();
    await page.setViewport({ width: 1280, height: 800 });
    
    console.log('📱 Navigating to onboarding flow page...');
    
    // Navigate to the onboarding flow page
    try {
      await page.goto('http://localhost:8100/secure/onboarding-flow', { 
        waitUntil: 'networkidle2',
        timeout: 15000 
      });
    } catch (error) {
      console.log('⚠️  Direct navigation failed, might need authentication. Trying home page first...');
      await page.goto('http://localhost:8100', { waitUntil: 'networkidle2' });
      
      // Wait a bit and try to navigate to onboarding flow
      await page.waitForTimeout(2000);
      await page.goto('http://localhost:8100/secure/onboarding-flow', { 
        waitUntil: 'networkidle2',
        timeout: 10000 
      });
    }
    
    console.log('✅ Page loaded successfully!');
    
    // Wait for page to fully load
    await page.waitForTimeout(3000);
    
    // Get initial scroll position
    const initialScrollTop = await page.evaluate(() => {
      const scrollContainer = document.querySelector('ion-content') || document.querySelector('[class*="overflow-y-auto"]') || document.body;
      return scrollContainer.scrollTop || window.pageYOffset;
    });
    
    console.log(`📏 Initial scroll position: ${initialScrollTop}px`);
    
    // Get page height to determine if scrolling is needed
    const pageHeight = await page.evaluate(() => {
      const body = document.body;
      const html = document.documentElement;
      return Math.max(
        body.scrollHeight, body.offsetHeight,
        html.clientHeight, html.scrollHeight, html.offsetHeight
      );
    });
    
    const viewportHeight = await page.evaluate(() => window.innerHeight);
    
    console.log(`📐 Page height: ${pageHeight}px, Viewport height: ${viewportHeight}px`);
    
    if (pageHeight <= viewportHeight) {
      console.log('ℹ️  Page content fits in viewport - scrolling may not be necessary');
    }
    
    // Test scrolling by trying multiple methods
    console.log('🖱️  Testing scrolling functionality...');
    
    // Method 1: Try scrolling the main scroll container
    const scrollResult1 = await page.evaluate(() => {
      const scrollContainer = document.querySelector('ion-content .scroll-content') || 
                             document.querySelector('ion-content') ||
                             document.querySelector('[class*="overflow-y-auto"]') ||
                             document.querySelector('[class*="touch-pan-y"]') ||
                             document.body;
      
      if (scrollContainer) {
        const initialTop = scrollContainer.scrollTop || 0;
        scrollContainer.scrollTop = 300;
        const newTop = scrollContainer.scrollTop || 0;
        
        return {
          container: scrollContainer.tagName + (scrollContainer.className ? '.' + scrollContainer.className.split(' ')[0] : ''),
          initialTop,
          newTop,
          scrolled: newTop > initialTop
        };
      }
      return { error: 'No scroll container found' };
    });
    
    console.log('📊 Scroll test result (Method 1):', scrollResult1);
    
    await page.waitForTimeout(1000);
    
    // Method 2: Try window scrolling
    const scrollResult2 = await page.evaluate(() => {
      const initialTop = window.pageYOffset;
      window.scrollTo(0, 300);
      const newTop = window.pageYOffset;
      
      return {
        container: 'window',
        initialTop,
        newTop,
        scrolled: newTop > initialTop
      };
    });
    
    console.log('📊 Scroll test result (Method 2):', scrollResult2);
    
    await page.waitForTimeout(1000);
    
    // Method 3: Try programmatic scroll on page wrapper
    const scrollResult3 = await page.evaluate(() => {
      const pageWrapper = document.querySelector('lib-page-wrapper') || 
                         document.querySelector('[class*="page-wrapper"]');
      
      if (pageWrapper) {
        const scrollContainer = pageWrapper.querySelector('[class*="overflow-y-auto"]') ||
                               pageWrapper.querySelector('[class*="touch-pan-y"]') ||
                               pageWrapper;
        
        const initialTop = scrollContainer.scrollTop || 0;
        scrollContainer.scrollTop = 500;
        const newTop = scrollContainer.scrollTop || 0;
        
        return {
          container: 'page-wrapper',
          initialTop,
          newTop,
          scrolled: newTop > initialTop
        };
      }
      return { error: 'No page wrapper found' };
    });
    
    console.log('📊 Scroll test result (Method 3):', scrollResult3);
    
    // Check current CSS properties that might affect scrolling
    const cssInfo = await page.evaluate(() => {
      const body = document.body;
      const html = document.documentElement;
      const ionContent = document.querySelector('ion-content');
      const pageWrapper = document.querySelector('lib-page-wrapper');
      
      return {
        body: {
          overflow: getComputedStyle(body).overflow,
          height: getComputedStyle(body).height,
          position: getComputedStyle(body).position
        },
        html: {
          overflow: getComputedStyle(html).overflow,
          height: getComputedStyle(html).height
        },
        ionContent: ionContent ? {
          overflow: getComputedStyle(ionContent).overflow,
          height: getComputedStyle(ionContent).height,
          position: getComputedStyle(ionContent).position
        } : 'Not found',
        pageWrapper: pageWrapper ? {
          classes: pageWrapper.className,
          overflow: getComputedStyle(pageWrapper).overflow,
          height: getComputedStyle(pageWrapper).height
        } : 'Not found'
      };
    });
    
    console.log('🎨 CSS Properties:');
    console.log(JSON.stringify(cssInfo, null, 2));
    
    // Final assessment
    const canScroll = scrollResult1.scrolled || scrollResult2.scrolled || scrollResult3.scrolled;
    
    console.log('\n' + '='.repeat(50));
    if (canScroll) {
      console.log('✅ SUCCESS: Page scrolling is working!');
    } else {
      console.log('❌ ISSUE: Page scrolling is not working properly');
      
      if (pageHeight <= viewportHeight) {
        console.log('ℹ️  Note: Content might fit in viewport, try adding more content to test scrolling');
      }
    }
    console.log('='.repeat(50));
    
    // Keep browser open for manual verification
    console.log('\n🔍 Browser will stay open for 30 seconds for manual verification...');
    console.log('📝 Manual test: Try scrolling with mouse wheel or trackpad');
    
    await page.waitForTimeout(30000);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    
    if (error.message.includes('net::ERR_CONNECTION_REFUSED')) {
      console.log('💡 Make sure the Angular dev server is running: ng serve');
    }
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Check if puppeteer is installed
try {
  require.resolve('puppeteer');
  testScrolling().catch(console.error);
} catch (error) {
  console.log('❌ Puppeteer not found. Installing...');
  console.log('💡 Run: npm install puppeteer --save-dev');
  console.log('💡 Or yarn add puppeteer --dev');
  
  // Try to install puppeteer automatically
  const { execSync } = require('child_process');
  try {
    console.log('🔄 Installing puppeteer...');
    execSync('npm install puppeteer --save-dev', { stdio: 'inherit' });
    console.log('✅ Puppeteer installed! Running test...');
    testScrolling().catch(console.error);
  } catch (installError) {
    console.error('❌ Failed to install puppeteer automatically');
    console.log('💡 Please install manually: npm install puppeteer --save-dev');
  }
}
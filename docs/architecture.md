# LP Angular Brownfield Enhancement Architecture — Account Pools

Document Version: v1 (Draft)
Date: 2025-09-01
Owner: Architecture


## 1. Overview
- Context: Complete and align Account Pools capability for the existing multi-project Angular/Ionic workspace.
- Objective: Safely align client services and UI with the extsecure backend contract while preserving unified tenant context (userId, productId, mpacc) and existing UX patterns.
- Scope: Client-side only (lp-client, lp-client-api, mobile-components). No server code changes in this repo.


## 2. Current State Summary
- UI: Pools page (lp-client) integrates mobile-components/lib-account-pool, which renders forms and actions (create, join, invite, accept/decline, remove, exit).
- Client API: lp-client-api/account-pool.service provides methods for pool operations; uses SystemService for LP_UniqueId header.
- Tenancy: Unified API approach (userId/productId/mpacc) with single-tenant and multi-tenant flows per WARP.md.


## 3. Architectural Drivers & Constraints
- Brownfield Safety: Avoid regressions, preserve existing flows and visual semantics.
- Contract Alignment: extsecure path and verbs; include LP_APIID and LP_UniqueId headers.
- Tenant Context: Always resolve userId, productId, mpacc correctly for the active mode (single or multi-tenant).
- Observability & Rollback: Feature flags, safe fallback to current behavior, and minimal-PII error logging.


## 4. API Contract Mapping (extsecure)

Canonical paths (provided by stakeholder):
- GET    /extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{joinMpacc}
- PUT    (same path) body { mpacc, role, privacy, action: JOIN|EXIT|UPDT }
- DELETE (same path) → 204 (Reject invitation)

Client responsibilities:
- Include headers: LP_APIID and LP_UniqueId (from lssConfig and SystemService.getUniqueId( apiIdKeyStart, apiIdKeyEnd, identity ) ).
- Ensure identity used to derive LP_UniqueId is correct per operation (e.g., mpacc/auditUser as per current patterns).

Mapping of actions to client flows:
- JOIN / REQS: Member request to join a pool
- INVT: Admin invites a member (PUT with action INVT if supported, or corresponding flow as defined by server)
- ACCP: Approve/accept join/invite
- REMV: Remove member or reject join
- EXIT: Member exits pool

Note: Where the extsecure schema differs in naming or body format, introduce a translation layer within lp-client-api service methods.


## 5. Tenant Context Resolution
- Single-tenant: productId from environment.lssConfig.workflow.productId; mpacc from profile.
- Multi-tenant: productId/mpacc from current program context (programId, mpacc).
- All service calls must obtain userId, productId, mpacc consistently through existing context providers; fail fast with actionable errors if missing.


## 6. Data Model & Status Mapping
- Entities (client-side view):
  - AccountPool: ENTITYID, MPACC, POOLNAME, STATUS, TOTALUNITS, BEGINDATE, ENDDATE
  - PoolMember: MPACC, NAME, TYPE (ADMN|MEMB), INVITESTATUS (INVT|REQS|null), MEMBERSTATUS (STAA|PEND|INAC|SUSP), BALANCE, PRIVACY
- UI Mapping:
  - TYPE: ADMN → Admin badge; else Member
  - INVITESTATUS: INVT/REQS badges and pending states
  - MEMBERSTATUS: STAA (Active), PEND (Pending)


## 7. Client Integration Plan
- Service (lp-client-api/account-pool.service):
  - Introduce feature-flag-controlled routing to extsecure paths for each operation (read-only first, then mutating actions).
  - Maintain current public method signatures to avoid ripples; internally translate to extsecure contract where flag-enabled.
  - Standardize header creation (LP_APIID, LP_UniqueId) and logging format (no PII beyond necessary IDs).
- UI (mobile-components/lib-account-pool):
  - Keep component API; rely on service changes only.
  - Confirm validation and messaging for actions (create, join, invite, approve/reject, accept/decline, remove, exit).


## 8. Feature Flags
- Flags per operation class:
  - pools.extsecure.readEnabled (find by member MPACC, find by pool MPACC)
  - pools.extsecure.mutateEnabled (invite, join request, approve/reject, accept/decline, remove, exit)
- Default: disabled in production until validated; enabled progressively in dev/qa per client configuration.


## 9. Error Handling & Observability
- Error classes: 404 (not found), 409 (conflict/duplicate), network errors, validation errors.
- Messaging: Contextual user-facing messages; console logs sanitized.
- Telemetry: Ensure errors include operation type and status codes without sensitive data.


## 10. Testing Strategy
- Unit tests (lp-client-api): header creation, feature-flag routing, payload mapping, error handling.
- Component tests: UI state transitions for INVITESTATUS/MEMBERSTATUS, confirmations, toasts.
- Integration tests: End-to-end flows behind flags in dev/qa; include tenant context resolution checks.
- Contract tests (optional): Validate extsecure mapping for body/verbs/status handling.


## 11. Risks & Mitigations
- Contract divergence → Add translation layer and extensive testing.
- Tenant context errors → Validate context providers and add defensive checks.
- UI regressions → Keep component API stable; canary rollout.
- Header mismatches → Centralize header generation via SystemService, covered by unit tests.


## 12. Rollout Plan
- Phase 1: Enable read-only extsecure routing in dev → qa → limited prod.
- Phase 2: Enable mutating operations progressively; monitor error rates and user feedback.
- Rollback: Toggle flags off to revert to prior behavior.


## 13. Open Questions
- Confirm exact accepted action codes for all membership transitions (e.g., INVT vs dedicated invite endpoint semantics).
- Confirm identity source for LP_UniqueId across all operations (mpacc vs auditUser) to match server expectations.


## 14. References
- PRD: docs/prd.md
- WARP.md: Multi-tenant patterns, build/deploy guidance
- Code: lp-client-api/account-pool.service, mobile-components/lib-account-pool, lp-client Pools page


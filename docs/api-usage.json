{"metadata": {"generated": "2024-12-10T05:43:00Z", "version": "1.0", "description": "API endpoints actually used by lp-client application", "sources": ["projects/lp-client/src/app/**/*.ts", "projects/lp-client-api/src/lib/services/**/*.ts"]}, "endpoints": [{"id": "member_program_profile", "method": "GET", "path": "/extsecure/member/{userId}/products/{productId}/member/{mpacc}", "service": "MemberService.loadProgramMemberProfile", "category": "Member Profile", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}, "productId": {"type": "string", "source": "environment/program context", "required": true}, "mpacc": {"type": "string", "source": "membership account number", "required": true}}, "response_type": "MemberProfile", "tenant_mode": "unified", "usage": ["app.component.ts (startup)", "program-management.component.ts", "virtualcard.component.ts"], "startup_call": true}, {"id": "member_profile_legacy", "method": "GET", "path": "/extsecure/member/{membershipId}/full", "service": "MemberService.memberLoadFull", "category": "Member Profile", "parameters": {"membershipId": {"type": "string", "source": "member ID", "required": true}}, "response_type": "MemberProfile", "tenant_mode": "legacy", "usage": [], "startup_call": false, "deprecated": true}, {"id": "member_search", "method": "POST", "path": "/extsecure/member/search", "service": "MemberService.search", "category": "Member Profile", "request_body": "Search criteria object", "response_type": "MemberProfile[]", "tenant_mode": "legacy", "usage": ["program-management.component.ts (unenrollment lookup)"], "startup_call": false}, {"id": "program_transactions", "method": "GET", "path": "/api/member/{userId}/products/{productId}/member/{mpacc}/transactions", "service": "ProgramService.getUserProgramTransactions", "category": "Transactions", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}, "productId": {"type": "string", "source": "environment/program context", "required": true}, "mpacc": {"type": "string", "source": "membership account number", "required": true}, "pageSize": {"type": "number", "source": "query param", "required": false}, "offset": {"type": "number", "source": "query param", "required": false}, "beginDate": {"type": "string", "source": "query param (YYYY-MM-DD)", "required": false}, "endDate": {"type": "string", "source": "query param (YYYY-MM-DD)", "required": false}, "filter": {"type": "string", "source": "query param", "required": false}}, "response_type": "ProgramTransaction[]", "tenant_mode": "unified", "usage": ["transactions.component.ts"], "startup_call": false}, {"id": "transaction_history_legacy", "method": "GET", "path": "/extsecure/member/{membershipId}/transactions", "service": "MemberService.getTransactionHistory", "category": "Transactions", "parameters": {"membershipId": {"type": "string", "source": "lpUniqueReference", "required": true}, "beginDate": {"type": "Date", "source": "component state", "required": false}, "endDate": {"type": "Date", "source": "component state", "required": false}, "offset": {"type": "number", "source": "pagination", "required": false}, "limit": {"type": "number", "source": "pagination", "required": false}}, "response_type": "Statement[]", "tenant_mode": "legacy", "usage": ["transactions.component.ts (fallback)"], "startup_call": false}, {"id": "available_programs", "method": "GET", "path": "/api/programs", "service": "ProgramService.getAvailablePrograms", "category": "Programs", "parameters": {}, "response_type": "Program[]", "tenant_mode": "unified", "usage": ["program-management.component.ts", "program-selection.component.ts"], "startup_call": false}, {"id": "user_program_selection", "method": "GET", "path": "/api/member/{userId}/programs/selection", "service": "ProgramService.getUserProgramSelection", "category": "Programs", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}}, "response_type": "ProgramSelection", "tenant_mode": "unified", "usage": ["program-management.component.ts"], "startup_call": false}, {"id": "user_registered_programs", "method": "GET", "path": "/api/member/{userId}/programs/registered", "service": "ProgramService.getUserRegisteredPrograms", "category": "Programs", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}}, "response_type": "Program registration details", "tenant_mode": "unified", "usage": ["program-management.component.ts (comparison)"], "startup_call": false}, {"id": "save_user_programs", "method": "PUT", "path": "/api/member/{userId}/programs", "service": "ProgramService.saveUserPrograms", "category": "Programs", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}}, "request_body": "string[] (program IDs)", "response_type": "Success status", "tenant_mode": "unified", "usage": ["program-management.component.ts (enrollment)"], "startup_call": false}, {"id": "unenroll_program", "method": "DELETE", "path": "/api/member/{userId}/programs/{programId}/member/{mpacc}", "service": "ProgramService.unenrollFromProgram", "category": "Programs", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}, "programId": {"type": "string", "source": "program context", "required": true}, "mpacc": {"type": "string", "source": "member search", "required": true}}, "response_type": "Success status", "tenant_mode": "unified", "usage": ["program-management.component.ts (unenrollment)"], "startup_call": false}, {"id": "member_details_optimized", "method": "GET", "path": "/api/member/{userId}/programs/{programId}/details", "service": "ProgramService.getMemberDetailsOptimized", "category": "Programs", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}, "programId": {"type": "string", "source": "program context", "required": true}}, "response_type": "ProgramMember", "tenant_mode": "unified", "usage": ["program-management.component.ts (program entry)"], "startup_call": false}, {"id": "notification_count", "method": "GET", "path": "/extsecure/member/{membershipId}/notifications/count", "service": "MemberService.getNotificationsCount", "category": "Notifications", "parameters": {"membershipId": {"type": "string", "source": "lpUniqueReference", "required": true}}, "response_type": "{ count: number }", "tenant_mode": "legacy", "usage": ["app.component.ts (post-login)"], "startup_call": true}, {"id": "firebase_device_token", "method": "POST", "path": "/extsecure/member/{membershipId}/firebase/token", "service": "FirebaseMemberService.registerDeviceToken", "category": "Notifications", "parameters": {"membershipId": {"type": "string", "source": "lpUniqueReference", "required": true}}, "request_body": "Device token information", "response_type": "DeviceTokenResponse", "tenant_mode": "legacy", "usage": ["push-notification.service.ts"], "startup_call": true}, {"id": "firebase_token_validation", "method": "GET", "path": "/extsecure/member/{membershipId}/firebase/validate", "service": "FirebaseMemberService.validateToken", "category": "Notifications", "parameters": {"membershipId": {"type": "string", "source": "lpUniqueReference", "required": true}}, "response_type": "FirebaseTokenResponse", "tenant_mode": "legacy", "usage": ["push-notification.service.ts"], "startup_call": false}, {"id": "onboarding_status", "method": "GET", "path": "/api/member/{userId}/onboarding/status", "service": "OnboardingService methods", "category": "Onboarding", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}}, "response_type": "Onboarding status", "tenant_mode": "unified", "usage": ["onboarding-flow.component.ts", "workflow-redirect.guard.ts"], "startup_call": false}, {"id": "partner_stores", "method": "GET", "path": "/public/partners", "service": "Partner service methods", "category": "Stores", "parameters": {"location": {"type": "string", "source": "query param", "required": false}, "category": {"type": "string", "source": "query param", "required": false}}, "response_type": "Partner[]", "tenant_mode": "unified", "usage": ["stores.component.ts", "store-detail.component.ts"], "startup_call": false}, {"id": "device_config", "method": "POST", "path": "/extsecure/config", "service": "Direct HTTP (config.service.ts)", "category": "Configuration", "request_body": "Device details configuration", "response_type": "Configuration data", "tenant_mode": "legacy", "usage": ["config.service.ts (app initialization)"], "startup_call": true}, {"id": "find_pool", "method": "GET", "path": "/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/", "service": "AccountPoolService.findPool", "category": "Account Pools", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}, "productId": {"type": "string", "source": "environment/program context", "required": true}, "mpacc": {"type": "string", "source": "membership account number", "required": true}}, "response_type": "Pool details with members", "tenant_mode": "unified", "usage": ["pools.component.ts (load pool information)"], "startup_call": false}, {"id": "check_invite_status", "method": "GET", "path": "/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/", "service": "AccountPoolService.checkInviteStatus", "category": "Account Pools", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}, "productId": {"type": "string", "source": "environment/program context", "required": true}, "mpacc": {"type": "string", "source": "membership account number", "required": true}}, "response_type": "{ status: 'Y' | 'N' }", "tenant_mode": "unified", "usage": ["pools.component.ts (check pending invitations)"], "startup_call": false}, {"id": "create_pool", "method": "POST", "path": "/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/", "service": "AccountPoolService.createPool", "category": "Account Pools", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}, "productId": {"type": "string", "source": "environment/program context", "required": true}, "mpacc": {"type": "string", "source": "creator membership number", "required": true}}, "request_body": "Pool creation details (name, email, language, etc.)", "response_type": "Created pool information", "tenant_mode": "unified", "usage": ["pools.component.ts (pool creation)"], "startup_call": false}, {"id": "update_pool", "method": "POST", "path": "/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}", "service": "AccountPoolService.updatePool", "category": "Account Pools", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}, "productId": {"type": "string", "source": "environment/program context", "required": true}, "mpacc": {"type": "string", "source": "membership account number", "required": true}, "poolId": {"type": "string", "source": "pool identifier", "required": true}}, "request_body": "Updated pool details", "response_type": "Success status", "tenant_mode": "unified", "usage": ["pools.component.ts (pool management)"], "startup_call": false}, {"id": "join_pool", "method": "POST", "path": "/extsecure/member/accountpool/join", "service": "AccountPoolService.joinPool", "category": "Account Pools", "parameters": {"poolId": {"type": "number", "source": "pool identifier", "required": true}, "mpacc": {"type": "string", "source": "membership account number", "required": true}, "action": {"type": "string", "source": "'INVT' or 'REQS'", "required": true}}, "request_body": "Join request details", "response_type": "Success/error status", "tenant_mode": "legacy", "usage": ["pools.component.ts (join pool requests)"], "startup_call": false}, {"id": "process_pool_invite", "method": "POST", "path": "/extsecure/member/accountpool/process-invite", "service": "AccountPoolService.processPoolInvite", "category": "Account Pools", "parameters": {"poolId": {"type": "number", "source": "pool identifier", "required": true}, "mpacc": {"type": "string", "source": "membership account number", "required": true}, "type": {"type": "string", "source": "'ACCP', 'JOIN', 'REMV', 'EXIT'", "required": true}}, "request_body": "Invitation processing details", "response_type": "Success/error status", "tenant_mode": "legacy", "usage": ["pools.component.ts (accept/decline invitations, remove members)"], "startup_call": false}, {"id": "decline_invite", "method": "DELETE", "path": "/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{targetMpacc}", "service": "AccountPoolService.declineInvite", "category": "Account Pools", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}, "productId": {"type": "string", "source": "environment/program context", "required": true}, "mpacc": {"type": "string", "source": "membership account number", "required": true}, "poolId": {"type": "string", "source": "pool identifier", "required": true}, "targetMpacc": {"type": "string", "source": "target member MPACC", "required": true}}, "response_type": "Success status", "tenant_mode": "unified", "usage": ["pools.component.ts (decline pool invitations)"], "startup_call": false}, {"id": "find_pool_by_mpacc", "method": "GET", "path": "/extsecure/member/{userId}/products/{productId}/member/accountpool/{poolMpacc}", "service": "AccountPoolService.findPoolByMpacc", "category": "Account Pools", "parameters": {"userId": {"type": "string", "source": "KeyCloak token", "required": true}, "productId": {"type": "string", "source": "environment/program context", "required": true}, "poolMpacc": {"type": "string", "source": "pool membership account number", "required": true}}, "response_type": "Pool details (without members)", "tenant_mode": "unified", "usage": ["Pool lookup by pool account number"], "startup_call": false}, {"id": "transfer_points", "method": "POST", "path": "/public/member/transfer", "service": "PointsTransferService.transferPoints", "category": "Points Transfer", "parameters": {"fromMembershipNumber": {"type": "string", "source": "source account", "required": true}, "toMembershipNumber": {"type": "string", "source": "destination account", "required": true}, "pointsAmount": {"type": "number", "source": "amount to transfer", "required": true}}, "request_body": "Transfer details with from/to MPACC and point amount", "response_type": "Transfer confirmation", "tenant_mode": "legacy", "usage": ["pools.component.ts (transfer points within pool)"], "startup_call": false}, {"id": "transfer_history", "method": "GET", "path": "/public/secure/loyaltyapi/1.0.0/member/transfer/history", "service": "PointsTransferService.getTransferHistory", "category": "Points Transfer", "parameters": {"mpacc": {"type": "string", "source": "membership number", "required": true}}, "response_type": "Transfer history records[]", "tenant_mode": "legacy", "usage": ["Transfer history views"], "startup_call": false}, {"id": "api_testing", "method": "GET", "path": "/{endpoint}", "service": "Direct HTTP (api-testing.service.ts)", "category": "Testing", "parameters": {"endpoint": {"type": "string", "source": "configurable", "required": true}}, "response_type": "Dynamic", "tenant_mode": "unified", "usage": ["api-testing.service.ts (development)"], "startup_call": false, "development_only": true}], "startup_sequence": {"single_tenant": ["keycloak_authentication", "member_program_profile", "notification_count", "device_config", "firebase_device_token"], "multi_tenant": ["keycloak_authentication", "program_context_check", "member_program_profile", "notification_count", "device_config"]}, "tenant_patterns": {"unified_api": {"description": "Uses productId and mpacc parameters for program-specific data isolation", "endpoints": ["member_program_profile", "program_transactions", "available_programs", "user_program_selection", "save_user_programs", "unenroll_program", "member_details_optimized", "onboarding_status", "find_pool", "check_invite_status", "create_pool", "update_pool", "decline_invite", "find_pool_by_mpacc"]}, "legacy_api": {"description": "Uses membershipId without program context", "endpoints": ["member_profile_legacy", "member_search", "transaction_history_legacy", "notification_count", "firebase_device_token", "firebase_token_validation", "device_config", "join_pool", "process_pool_invite", "transfer_points", "transfer_history"]}}, "functional_areas": {"Member Profile": ["member_program_profile", "member_profile_legacy", "member_search"], "Transactions": ["program_transactions", "transaction_history_legacy"], "Programs": ["available_programs", "user_program_selection", "user_registered_programs", "save_user_programs", "unenroll_program", "member_details_optimized"], "Account Pools": ["find_pool", "check_invite_status", "create_pool", "update_pool", "join_pool", "process_pool_invite", "decline_invite", "find_pool_by_mpacc"], "Points Transfer": ["transfer_points", "transfer_history"], "Notifications": ["notification_count", "firebase_device_token", "firebase_token_validation"], "Onboarding": ["onboarding_status"], "Stores": ["partner_stores"], "Configuration": ["device_config"], "Testing": ["api_testing"]}}
# Data Model & Status Mapping
- Entities (client-side view):
  - AccountPool: ENTITYID, MPACC, POOLNAME, STATUS, TOTALUNITS, BEGINDATE, ENDDATE
  - PoolMember: MPACC, NAME, TYPE (ADMN|MEMB), INVITESTATUS (INVT|REQS|null), MEMBERSTATUS (STAA|PEND|INAC|SUSP), BALANCE, PRIVACY
- UI Mapping:
  - TYPE: ADMN → Admin badge; else Member
  - INVITESTATUS: INVT/REQS badges and pending states
  - MEMBERSTATUS: STAA (Active), PEND (Pending)


# Client Integration Plan
- Service (lp-client-api/account-pool.service):
  - Introduce feature-flag-controlled routing to extsecure paths for each operation (read-only first, then mutating actions).
  - Maintain current public method signatures to avoid ripples; internally translate to extsecure contract where flag-enabled.
  - Standardize header creation (LP_APIID, LP_UniqueId) and logging format (no PII beyond necessary IDs).
- UI (mobile-components/lib-account-pool):
  - Keep component API; rely on service changes only.
  - Confirm validation and messaging for actions (create, join, invite, approve/reject, accept/decline, remove, exit).


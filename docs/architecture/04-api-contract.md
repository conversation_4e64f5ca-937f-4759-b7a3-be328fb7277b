# API Contract Mapping (extsecure)

Canonical paths (provided by stakeholder):
- GET    /extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{joinMpacc}
- PUT    (same path) body { mpacc, role, privacy, action: JOIN|EXIT|UPDT }
- DELETE (same path) → 204 (Reject invitation)

Client responsibilities:
- Include headers: LP_APIID and LP_UniqueId (from lssConfig and SystemService.getUniqueId( apiIdKeyStart, apiIdKeyEnd, identity ) ).
- Ensure identity used to derive LP_UniqueId is correct per operation (e.g., mpacc/auditUser as per current patterns).

Mapping of actions to client flows:
- JOIN / REQS: Member request to join a pool
- INVT: Admin invites a member (PUT with action INVT if supported, or corresponding flow as defined by server)
- ACCP: Approve/accept join/invite
- REMV: Remove member or reject join
- EXIT: Member exits pool

Note: Where the extsecure schema differs in naming or body format, introduce a translation layer within lp-client-api service methods.

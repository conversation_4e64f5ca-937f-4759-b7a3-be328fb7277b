# Testing Strategy
- Unit tests (lp-client-api): header creation, feature-flag routing, payload mapping, error handling.
- Component tests: UI state transitions for INVITESTATUS/MEMBERSTATUS, confirmations, toasts.
- Integration tests: End-to-end flows behind flags in dev/qa; include tenant context resolution checks.
- Contract tests (optional): Validate extsecure mapping for body/verbs/status handling.


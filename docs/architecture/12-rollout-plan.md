# Rollout Plan

## Feature Flag Management

### Flag Configuration
- **pools.extsecure.readEnabled**: Controls read-only pool lookups routing
- **pools.extsecure.mutateEnabled**: Controls mutating pool operations routing
- **Default Values**: false (production), true (dev environments)
- **Location**: `environment.{env}.ts` files under `lssConfig.workflow.features.pools.extsecure`

### Flag Toggle Procedures

#### To Enable Extsecure Routing:
1. Update environment configuration file:
   ```typescript
   pools: {
     extsecure: {
       readEnabled: true,      // Enable read-only routing
       mutateEnabled: true     // Enable mutating operations routing
     }
   }
   ```
2. Deploy configuration changes
3. Restart application services
4. Validate routing through monitoring

#### Emergency Rollback:
1. Set flags to `false` in environment config
2. Deploy emergency configuration update
3. Restart services (< 5 minute recovery target)
4. Validate legacy routing restoration

## Phased Rollout Strategy

### Phase 1: Development Environment Validation ✅
- ✅ Enable `readEnabled` and `mutateEnabled` in dev environments
- ✅ Comprehensive unit and integration testing
- ✅ Header generation and tenant context validation
- ✅ Edge case and error handling verification

### Phase 2: QA Environment Testing
- Enable flags in QA environment
- Full regression suite execution
- User acceptance testing with real data
- Performance baseline establishment

### Phase 3: Production Canary (Read-Only First)
- Enable only `readEnabled` in production
- Monitor for 1-2 weeks for stability
- Gradual tenant rollout if multi-tenant
- Validate no regressions in read operations

### Phase 4: Production Full Rollout
- Enable `mutateEnabled` after read-only validation
- Monitor critical metrics for 48 hours
- Full feature availability to all tenants

## Rollback Procedures

### Immediate Rollback Triggers
- Pool operation error rates > 5% above baseline
- API response times > 2x baseline (>2000ms)
- Data consistency issues detected
- Header generation failures
- Tenant context resolution errors

### Emergency Rollback Steps
1. **Immediate**: Set feature flags to `false`
2. **Deploy** emergency configuration (target: <3 minutes)
3. **Restart** application services
4. **Validate** legacy endpoint restoration
5. **Monitor** recovery within 5 minutes
6. **Communicate** incident to stakeholders

## Monitoring and Success Criteria

### Key Metrics Dashboard
- Pool operation success rates by action (REQS, INVT, ACCP, REMV, EXIT)
- Response time percentiles (p50, p95, p99)
- Routing type distribution (extsecure vs legacy)
- Tenant context resolution success rate

### Critical Alerts
- Pool operation error rate > 5%
- Average response time > 2000ms
- Extsecure routing failures > 1%
- Feature flag configuration mismatches

# Current State Summary
- UI: Pools page (lp-client) integrates mobile-components/lib-account-pool, which renders forms and actions (create, join, invite, accept/decline, remove, exit).
- Client API: lp-client-api/account-pool.service provides methods for pool operations; uses SystemService for LP_UniqueId header.
- Tenancy: Unified API approach (userId/productId/mpacc) with single-tenant and multi-tenant flows per WARP.md.


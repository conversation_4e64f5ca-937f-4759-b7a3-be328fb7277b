# Overview
- Context: Complete and align Account Pools capability for the existing multi-project Angular/Ionic workspace.
- Objective: Safely align client services and UI with the extsecure backend contract while preserving unified tenant context (userId, productId, mpacc) and existing UX patterns.
- Scope: Client-side only (lp-client, lp-client-api, mobile-components). No server code changes in this repo.


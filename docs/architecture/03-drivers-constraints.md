# Architectural Drivers & Constraints
- Brownfield Safety: Avoid regressions, preserve existing flows and visual semantics.
- Contract Alignment: extsecure path and verbs; include LP_APIID and LP_UniqueId headers.
- Tenant Context: Always resolve userId, productId, mpacc correctly for the active mode (single or multi-tenant).
- Observability & Rollback: Feature flags, safe fallback to current behavior, and minimal-PII error logging.


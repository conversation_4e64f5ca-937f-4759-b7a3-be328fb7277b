# API Usage Documentation - LP Client

This document catalogs all API endpoints actually used by the lp-client Angular application. The documentation is organized by functional area and includes both single-tenant and multi-tenant usage patterns.

## Architecture Overview

The lp-client app follows a **unified API approach** where all API calls use the same endpoint structure with **productId** parameter, regardless of tenant mode:

### Single-Tenant Apps
- **productId** sourced from `environment.lssConfig.workflow.productId` 
- **mpacc** sourced from `profile.membershipNumber` or `profile.newMembershipNumber`
- Direct workflow: Login → Home

### Multi-Tenant Apps  
- **productId** sourced from selected program context (`programContext.programId`)
- **mpacc** sourced from program context (`programContext.mpacc`)
- Program selection workflow: Login → Program Selection → Home

## Authentication

All API calls are authenticated via:
- **Bearer Token**: JWT token from Keycloak in Authorization header
- **Base URL**: Injected by HTTP interceptors from `lssConfig.apiBaseUrl`
- **Headers**: Standard JSON content-type headers

## API Endpoints by Functional Area

### 1. Authentication and Session Management

#### Keycloak Authentication
- **Service**: KeyCloakService
- **Description**: Handled by Keycloak client library (not direct HTTP calls)
- **Usage**: `app.component.ts`, various guard services

#### Token Refresh
- **Service**: KeyCloakService  
- **Description**: Automatic token refresh via Keycloak
- **Usage**: `app.component.ts` (token storage in Capacitor Preferences)

---

### 2. Member Profile and Virtual Card

#### Get Program Member Profile (Unified API)
- **Method**: `GET`
- **Path**: `/extsecure/member/{userId}/products/{productId}/member/{mpacc}`
- **Service**: `MemberService.loadProgramMemberProfile()`
- **Parameters**:
  - `userId`: string (from KeyCloak token)
  - `productId`: string (environment config or program context)
  - `mpacc`: string (membership account number)
- **Response**: `MemberProfile` with virtual card data
- **Usage**: 
  - `app.component.ts` (startup profile loading)
  - `program-management.component.ts`
  - `virtualcard.component.ts`
  - Single-tenant and multi-tenant apps

#### Get Member Profile (Legacy API)
- **Method**: `GET` 
- **Path**: `/extsecure/member/{membershipId}/full`
- **Service**: `MemberService.memberLoadFull()`
- **Parameters**:
  - `membershipId`: string (member ID)
- **Response**: `MemberProfile`
- **Usage**: Legacy single-tenant fallback (not actively used)

#### Get Member Balance
- **Method**: `GET`
- **Path**: `/extsecure/member/{membershipId}`
- **Service**: `MemberService.memberBalance()`
- **Parameters**:
  - `membershipId`: string (member ID)
- **Response**: `BasicProfile` (with balance info)
- **Usage**: `pools.component.ts` (refresh balance after transfers)

#### Register Member (Legacy API)
- **Method**: `POST`
- **Path**: `/public/member/`
- **Service**: `MemberService.register()`
- **Request Body**: `MemberProfile` data
- **Response**: Registration confirmation
- **Usage**: Legacy registration flow

#### Register Program Member (Unified API)
- **Method**: `POST`
- **Path**: `/extsecure/member/{userId}/products/{productId}/member`
- **Service**: `MemberService.registerProgramMember()`
- **Parameters**:
  - `userId`: string (KeyCloak user ID)
  - `productId`: string (product/program ID)
- **Request Body**: Member registration data
- **Response**: Created member profile
- **Usage**: Multi-tenant registration flow

#### Update Member Profile
- **Method**: `PUT`
- **Path**: `/extsecure/member/{membershipId}`
- **Service**: `MemberService.update()`
- **Parameters**:
  - `membershipId`: string (member ID)
- **Request Body**: Updated profile data
- **Response**: Success status
- **Usage**: Profile update flows

#### Search Members  
- **Method**: `POST`
- **Path**: `/extsecure/member/search`
- **Service**: `MemberService.search()`
- **Request Body**: Search criteria (email, etc.)
- **Response**: `MemberProfile[]`
- **Usage**: `program-management.component.ts` (for unenrollment MPACC lookup)

#### Search Basic Members
- **Method**: `POST`
- **Path**: `/public/member/basic`
- **Service**: `MemberService.search()`
- **Request Body**: `MemberCommObject` (search criteria)
- **Response**: `BasicProfile[]`
- **Usage**: Member lookup by membership number or email

#### Delete Account
- **Method**: `DELETE`
- **Path**: `/extsecure/member/{membershipId}?reason={reason}`
- **Service**: `MemberService.deleteAccount()`
- **Parameters**:
  - `membershipId`: string (member ID)
  - `reason`: string (deletion reason)
- **Response**: Success status
- **Usage**: Account deletion flows

#### Update PIN
- **Method**: `PUT`
- **Path**: `/extsecure/member/{membershipId}/pin`
- **Service**: `MemberService.updatePin()`
- **Parameters**:
  - `membershipId`: string (member ID)
- **Request Body**: `{ pin: string }`
- **Response**: Success status
- **Usage**: PIN update/security flows

#### Contact Us
- **Method**: `POST`
- **Path**: `/extsecure/member/{membershipId}/contact-us`
- **Service**: `MemberService.contactUs()`
- **Parameters**:
  - `membershipId`: string (member ID)
- **Request Body**: `ContactUs` form data
- **Response**: Success status
- **Usage**: Contact us form submissions

#### Validate Account
- **Method**: `POST`
- **Path**: `/public/member/validate/{reference}`
- **Service**: `MemberService.validateAccount()`
- **Parameters**:
  - `reference`: string (validation reference)
- **Request Body**: Validation form data
- **Response**: Validation result
- **Usage**: Account validation flows

#### Validate Account Reference
- **Method**: `POST`
- **Path**: `/public/member/validate`
- **Service**: `MemberService.validateAccountReference()`
- **Request Body**: Validation form data
- **Response**: Reference validation result
- **Usage**: Initial validation step

---

### 3. Transactions and Statements

#### Get User Program Transactions (Unified API)
- **Method**: `GET`
- **Path**: `/api/member/{userId}/products/{productId}/member/{mpacc}/transactions`
- **Service**: `ProgramService.getUserProgramTransactions()`
- **Parameters**:
  - `userId`: string 
  - `productId`: string
  - `mpacc`: string
  - `queryOptions`: Query parameters for filtering and pagination
    - `pageSize`: number
    - `offset`: number  
    - `beginDate`: string (YYYY-MM-DD format)
    - `endDate`: string (YYYY-MM-DD format)
    - `filter`: string (optional, e.g., 'award')
- **Response**: `ProgramTransaction[]`
- **Usage**: `transactions.component.ts` (unified approach for both tenant types)

#### Get Transaction History (Legacy API)  
- **Method**: `GET`
- **Path**: `/extsecure/member/{membershipId}/transactions`
- **Service**: `MemberService.getTransactionHistory()`
- **Parameters**:
  - `membershipId`: string
  - `beginDate`: Date
  - `endDate`: Date
  - `offset`: number
  - `limit`: number
- **Response**: `Statement[]` 
- **Usage**: `transactions.component.ts` (single-tenant fallback)

---

### 4. Program Selection and Multi-tenant Context

#### Get Available Programs
- **Method**: `GET`
- **Path**: `/api/programs`
- **Service**: `ProgramService.getAvailablePrograms()`
- **Parameters**: None
- **Response**: `Program[]`
- **Usage**: 
  - `program-management.component.ts`
  - `program-selection.component.ts`

#### Get User Program Selection
- **Method**: `GET` 
- **Path**: `/api/member/{userId}/programs/selection`
- **Service**: `ProgramService.getUserProgramSelection()`
- **Parameters**:
  - `userId`: string
- **Response**: `ProgramSelection` (includes selectedPrograms array and memberDetails)
- **Usage**: `program-management.component.ts` (to get enrolled programs)

#### Get User Registered Programs
- **Method**: `GET`
- **Path**: `/api/member/{userId}/programs/registered`  
- **Service**: `ProgramService.getUserRegisteredPrograms()`
- **Parameters**:
  - `userId`: string
- **Response**: Program registration details
- **Usage**: `program-management.component.ts` (comparison/testing)

#### Save User Programs (Enrollment)
- **Method**: `PUT`
- **Path**: `/api/member/{userId}/programs`
- **Service**: `ProgramService.saveUserPrograms()`
- **Parameters**:
  - `userId`: string
  - `programIds`: string[] (array of program IDs to enroll in)
- **Response**: Success/error status
- **Usage**: `program-management.component.ts` (enrollment)

#### Unenroll from Program
- **Method**: `DELETE`
- **Path**: `/api/member/{userId}/programs/{programId}/member/{mpacc}`
- **Service**: `ProgramService.unenrollFromProgram()`
- **Parameters**:
  - `userId`: string
  - `programId`: string
  - `mpacc`: string
- **Response**: Success/error status
- **Usage**: `program-management.component.ts` (unenrollment)

#### Get Member Details Optimized
- **Method**: `GET`
- **Path**: `/api/member/{userId}/programs/{programId}/details`
- **Service**: `ProgramService.getMemberDetailsOptimized()`
- **Parameters**:
  - `userId`: string
  - `programId`: string
- **Response**: `ProgramMember` details including MPACC
- **Usage**: `program-management.component.ts` (when entering a program)

---

### 5. Onboarding and Enrollment

#### Get Onboarding Status
- **Method**: `GET`
- **Path**: `/api/member/{userId}/onboarding/status`  
- **Service**: `OnboardingService` methods
- **Parameters**:
  - `userId`: string
- **Response**: Onboarding status information
- **Usage**: 
  - `onboarding-flow.component.ts`
  - `workflow-redirect.guard.ts`

---

### 6. Notifications and Messaging

#### Get Notification Count
- **Method**: `GET`
- **Path**: `/extsecure/member/{membershipId}/notifications/count`
- **Service**: `MemberService.getNotificationsCount()`
- **Parameters**:
  - `membershipId`: string (lpUniqueReference)
- **Response**: `{ count: number }`
- **Usage**: `app.component.ts` (post-login notification badge)

#### Firebase Device Token Registration  
- **Method**: `POST`
- **Path**: `/extsecure/member/{membershipId}/firebase/token`
- **Service**: `FirebaseMemberService.registerDeviceToken()`
- **Parameters**:
  - `membershipId`: string
- **Request Body**: Device token information
- **Response**: `DeviceTokenResponse`
- **Usage**: `push-notification.service.ts`

#### Firebase Token Validation
- **Method**: `GET`
- **Path**: `/extsecure/member/{membershipId}/firebase/validate`
- **Service**: `FirebaseMemberService.validateToken()`  
- **Parameters**:
  - `membershipId`: string (lpUniqueReference)
- **Response**: `FirebaseTokenResponse`
- **Usage**: `push-notification.service.ts`

#### Get Notifications List
- **Method**: `GET`
- **Path**: `/extsecure/member/{membershipId}/notifications/list`
- **Service**: `MemberService.getNotifications()`
- **Parameters**:
  - `membershipId`: string (member ID)
- **Response**: `Statement[]` (notification items)
- **Usage**: Notifications list component

#### Get Individual Notification
- **Method**: `GET`
- **Path**: `/extsecure/member/{membershipId}/notifications/{notificationId}`
- **Service**: `MemberService.getNotification()`
- **Parameters**:
  - `membershipId`: string (member ID)
  - `notificationId`: string (notification ID)
- **Response**: Notification details
- **Usage**: View individual notifications

#### Mark Notification as Read
- **Method**: `PUT`
- **Path**: `/extsecure/member/{membershipId}/notifications/{notificationId}`
- **Service**: `MemberService.readNotification()`
- **Parameters**:
  - `membershipId`: string (member ID)
  - `notificationId`: string (notification ID)
- **Request Body**: `{ status: 'NCLO', closeDate: timestamp }`
- **Response**: Success status
- **Usage**: Mark notifications as read

---

### 7. Stores and Partners

#### Get Partner Stores
- **Method**: `GET`
- **Path**: `/public/partners` or `/extsecure/partners`
- **Service**: Partner service methods
- **Parameters**: Various filtering and location parameters
- **Response**: `Partner[]` store listings
- **Usage**: 
  - `stores.component.ts`
  - `store-detail.component.ts`

---

### 8. Account Pools (Account Pooling)

#### Find Pool for Member (Unified API)
- **Method**: `GET`
- **Path**: `/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/`
- **Service**: `AccountPoolService.findPool()`
- **Parameters**:
  - `userId`: string (from KeyCloak token)
  - `productId`: string (environment config or program context) 
  - `mpacc`: string (membership account number)
- **Response**: Pool details with members array
- **Usage**: `pools.component.ts` (load pool information)
- **Notes**: Multi-step API call - lists pools, then fetches details and members

#### Check Pool Invite Status (Unified API)
- **Method**: `GET`
- **Path**: `/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/`
- **Service**: `AccountPoolService.checkInviteStatus()`
- **Parameters**:
  - `userId`: string
  - `productId`: string
  - `mpacc`: string
- **Response**: `{ status: 'Y' | 'N' }`
- **Usage**: `pools.component.ts` (check for pending invitations)

#### Create Account Pool (Unified API)
- **Method**: `POST`
- **Path**: `/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/`
- **Service**: `AccountPoolService.createPool()`
- **Parameters**:
  - `userId`: string
  - `productId`: string 
  - `mpacc`: string (creator's membership number)
- **Request Body**: Pool creation details (name, email, language, etc.)
- **Response**: Created pool information
- **Usage**: Pool creation flows

#### Update Account Pool (Unified API)
- **Method**: `POST`
- **Path**: `/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}`
- **Service**: `AccountPoolService.updatePool()`
- **Parameters**:
  - `userId`: string
  - `productId`: string
  - `mpacc`: string
  - `poolId`: string (pool identifier)
- **Request Body**: Updated pool details
- **Response**: Success status
- **Usage**: Pool management flows

#### Join Pool Request (Legacy/Unified)
- **Method**: `POST`
- **Path**: `/extsecure/member/accountpool/join` (legacy) or `/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members` (unified)
- **Service**: `AccountPoolService.joinPool()`
- **Parameters**: Pool ID, member MPACC, action type ('INVT' or 'REQS')
- **Request Body**: Join request details
- **Response**: Success/error status
- **Usage**: `pools.component.ts` (join pool requests)

#### Process Pool Invitation (Legacy/Unified)
- **Method**: `POST` / `PUT` / `DELETE`
- **Path**: `/extsecure/member/accountpool/process-invite` (legacy) or `/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{targetMpacc}` (unified)
- **Service**: `AccountPoolService.processPoolInvite()`
- **Parameters**: Pool ID, member MPACC, action type ('ACCP', 'JOIN', 'REMV', 'EXIT')
- **Response**: Success/error status
- **Usage**: `pools.component.ts` (accept/decline invitations, remove members)

#### Decline Pool Invitation
- **Method**: `DELETE`
- **Path**: `/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{targetMpacc}` (unified) or legacy process-invite
- **Service**: `AccountPoolService.declineInvite()`
- **Parameters**: Pool ID, member MPACC
- **Response**: Success status
- **Usage**: `pools.component.ts` (decline pool invitations)

#### Find Pool by Pool MPACC
- **Method**: `GET`
- **Path**: `/extsecure/member/{userId}/products/{productId}/member/accountpool/{poolMpacc}`
- **Service**: `AccountPoolService.findPoolByMpacc()`
- **Parameters**:
  - `userId`: string
  - `productId`: string
  - `poolMpacc`: string (pool's membership account number)
- **Response**: Pool details (without members)
- **Usage**: Pool lookup by pool account number

---

### 9. Gift Cards

#### Get Gift Card
- **Method**: `GET`
- **Path**: `/public/giftcard/{terminalId}?giftCode={giftCode}`
- **Service**: `MemberService.getGiftCard()`
- **Parameters**:
  - `terminalId`: string (terminal identifier)
  - `giftCode`: string (gift card code)
  - `apiId`: string (API ID parameter)
  - `uniqueId`: string (unique ID parameter)
- **Response**: `GiftCard` details
- **Usage**: Gift card lookup and validation

#### Fund Gift Card
- **Method**: `PUT`
- **Path**: `/public/giftcard/{terminalId}`
- **Service**: `MemberService.fundGiftCard()`
- **Parameters**:
  - `terminalId`: string (terminal identifier)
- **Request Body**: `GiftCardRequest` with funding details
- **Response**: `GiftCard` confirmation
- **Usage**: Add funds to gift cards

#### Redeem Gift Card
- **Method**: `POST`
- **Path**: `/public/giftcard/{terminalId}`
- **Service**: `MemberService.redeemGiftCard()`
- **Parameters**:
  - `terminalId`: string (terminal identifier)
- **Request Body**: `GiftCardRequest` with redemption details
- **Response**: `GiftCard` confirmation
- **Usage**: Redeem gift card value

---

### 10. Partner Stores (Extended)

#### Get Partner Stores (Legacy)
- **Method**: `POST`
- **Path**: `/public/partner/listPartners`
- **Service**: `PartnerService.getPartners()`
- **Request Body**: `PartnerListRequest` with filtering criteria
- **Response**: `Partner[]`
- **Usage**: Store locator with filtering

#### Get All Partner Stores
- **Method**: `GET`
- **Path**: `/public/partner/listPartnersAll`
- **Service**: `PartnerService.getPartnersAll()`
- **Parameters**:
  - `allocation`: string (allocation type, optional)
  - `calc`: string (calculation method, optional)
  - `type`: string (partner type, optional)
  - `group`: string (partner group, optional)
- **Response**: `Partner[]`
- **Usage**: `stores.component.ts`, `store-detail.component.ts`

#### Get Partner Products
- **Method**: `POST`
- **Path**: `/public/partner/listPartnerProducts`
- **Service**: `PartnerService.getPartnerProducts()`
- **Request Body**: `PartnerProductListRequest`
- **Response**: `PartnerProduct[]`
- **Usage**: Product listings for partners

---

### 11. Gaming System

#### Get All Game Configurations
- **Method**: `GET`
- **Path**: `/mobile-games/api/v1/config`
- **Service**: `GameService.getAllGameConfigs()`
- **Response**: `Game[]` (list of available games)
- **Usage**: Game lobby and selection

#### Get Game by ID
- **Method**: `GET`
- **Path**: `/mobile-games/api/v1/config/{gameId}`
- **Service**: `GameService.getGameById()`
- **Parameters**:
  - `gameId`: number (game identifier)
- **Response**: `Game` details
- **Usage**: Game details and configuration

#### Check Game Availability
- **Method**: `GET`
- **Path**: `/mobile-games/api/v1/config/{gameId}/can-play`
- **Service**: `GameService.checkGameAvailability()`
- **Parameters**:
  - `gameId`: number (game identifier)
- **Response**: `{ canPlay: boolean }`
- **Usage**: Validate if user can play specific game

#### Get Game Progress
- **Method**: `GET`
- **Path**: `/mobile-games/api/v1/config/{gameId}/progress`
- **Service**: `GameService.getGameProgress()`
- **Parameters**:
  - `gameId`: number (game identifier)
- **Response**: Game progress data
- **Usage**: Load user's game progress

#### Update Game Progress
- **Method**: `PUT`
- **Path**: `/mobile-games/api/v1/config/{gameId}/progress`
- **Service**: `GameService.updateGameProgress()`
- **Parameters**:
  - `gameId`: number (game identifier)
- **Request Body**: Progress update data
- **Response**: Updated progress
- **Usage**: Save game progress

#### Save Game Score
- **Method**: `POST`
- **Path**: `/mobile-games/api/v1/config/{gameId}/score`
- **Service**: `GameService.saveGameScore()`
- **Parameters**:
  - `gameId`: number (game identifier)
- **Request Body**: `{ score: number }`
- **Response**: Score confirmation
- **Usage**: Record final game scores

#### Get Account Game Configs
- **Method**: `GET`
- **Path**: `/mobile-games/api/v1/config/account/{accountId}`
- **Service**: `GameService.getAccountGameConfigs()`
- **Parameters**:
  - `accountId`: number (account identifier)
- **Response**: Account-specific game configurations
- **Usage**: Load user's game settings

#### Create Game Instance
- **Method**: `POST`
- **Path**: `/mobile-games/api/v1/config/{configId}/instance`
- **Service**: `GameService.createGameInstance()`
- **Parameters**:
  - `configId`: number (game config ID)
- **Request Body**: `{ account: number }`
- **Response**: `GameInstance`
- **Usage**: Start new game session

#### Get Game Instance
- **Method**: `GET`
- **Path**: `/mobile-games/api/v1/config/{configId}/instance/{instanceId}`
- **Service**: `GameService.getGameInstance()`
- **Parameters**:
  - `configId`: number (game config ID)
  - `instanceId`: number (instance ID)
- **Response**: `GameInstance` details
- **Usage**: Load existing game session

#### Create Game Event
- **Method**: `POST`
- **Path**: `/mobile-games/api/v1/config/{configId}/instance/{instanceId}/event`
- **Service**: `GameService.createGameEvent()`
- **Parameters**:
  - `configId`: number (game config ID)
  - `instanceId`: number (instance ID)
- **Request Body**: Game event data
- **Response**: `GameEvent`
- **Usage**: Record game events and milestones

#### Reset Game Progress
- **Method**: `POST`
- **Path**: `/mobile-games/api/v1/instance/{instanceId}/reset`
- **Service**: `GameService.resetGameProgress()`
- **Parameters**:
  - `instanceId`: number (instance ID)
- **Response**: Reset confirmation
- **Usage**: Reset user's game progress

---

### 12. Points Transfer

#### Transfer Points Between Accounts
- **Method**: `POST`
- **Path**: `/public/member/transfer`
- **Service**: `PointsTransferService.transferPoints()`
- **Parameters**:
  - `fromMembershipNumber`: string (source account)
  - `toMembershipNumber`: string (destination account)
  - `pointsAmount`: number (amount to transfer)
- **Request Body**: Transfer details with from/to MPACC and point amount
- **Response**: Transfer confirmation
- **Usage**: `pools.component.ts` (transfer points within pool)

#### Get Transfer History
- **Method**: `GET`
- **Path**: `/public/secure/loyaltyapi/1.0.0/member/transfer/history`
- **Service**: `PointsTransferService.getTransferHistory()`
- **Parameters**:
  - `mpacc`: string (membership number)
- **Response**: Array of transfer history records
- **Usage**: Transfer history views

---

### 13. Configuration and System

#### Device Configuration
- **Method**: `POST`
- **Path**: `/extsecure/config`
- **Service**: Direct HTTP call in `config.service.ts`
- **Parameters**: None (URL from environment)
- **Request Body**: Device details configuration
- **Response**: Configuration data
- **Usage**: `config.service.ts` (app initialization)

#### API Testing Endpoint
- **Method**: `GET`
- **Path**: `/{endpoint}` (configurable endpoint path)
- **Service**: Direct HTTP call in `api-testing.service.ts`
- **Parameters**: Dynamic endpoint path
- **Response**: Test response data
- **Usage**: `api-testing.service.ts` (development/testing)

---

## Startup API Call Sequence

### Single-Tenant App Startup
1. **Authentication**: Keycloak token initialization
2. **Profile Load**: `loadProgramMemberProfile(userId, productId, mpacc)` 
3. **Notification Count**: `getNotificationsCount(lpUniqueReference)`
4. **Device Config**: `POST /extsecure/config` (if configured)
5. **Push Token Registration**: Firebase token APIs (if native platform)

### Multi-Tenant App Startup  
1. **Authentication**: Keycloak token initialization
2. **Program Check**: Verify if user has program context
3. **Program Selection**: If no context, redirect to program selection
4. **Profile Load**: `loadProgramMemberProfile()` after program selection
5. **Notification Count**: `getNotificationsCount(lpUniqueReference)` 
6. **Device Config**: `POST /extsecure/config` (if configured)

---

## Legacy vs Unified API Migration

### Migration Pattern
The app implements a **unified API approach** where all endpoints use `productId` and `mpacc` parameters:

| Legacy Pattern | Unified Pattern |
|----------------|-----------------|
| `/extsecure/member/{membershipId}/transactions` | `/api/member/{userId}/products/{productId}/member/{mpacc}/transactions` |
| `/extsecure/member/{membershipId}/full` | `/extsecure/member/{userId}/products/{productId}/member/{mpacc}` |

### Benefits
- **Program-specific data isolation** in multi-tenant scenarios
- **Consistent API surface** across tenant types  
- **Security**: Users only access data for enrolled programs
- **Flexibility**: Same components work for both single and multi-tenant

---

## HTTP Service Patterns

### Service Wrappers
- **CapacitorHttpService**: Wraps Angular HttpClient for native compatibility
- **HTTP Interceptors**: Inject authentication and base URLs (via `interceptor.service.ts`)

### Error Handling
- Standard HTTP error responses with JSON envelope
- Toast notifications for user-facing errors
- Console logging for debugging

### Data Transformation  
- `ProgramTransaction` → `Statement` mapping in transaction flows
- Response envelope unwrapping in service layer

---

## Development and Testing

### Runtime API Verification
To verify API usage patterns:

```bash
# Single-tenant mode
pnpm exec ng run lp-client:serve:rmicqa --host=localhost --port=8100

# Multi-tenant mode  
pnpm exec ng run lp-client:serve:grco --host=localhost --port=8100
```

Monitor network traffic to validate endpoint usage and parameter patterns.

### Environment Configuration
API base URLs and tenant configurations are defined in:
- `projects/lp-client/src/environments/{env}/environment.{client}.{env}.ts`

---

## Notes

- **Virtual Card Data**: Included in program member profile responses
- **Pagination**: Standard pageSize/offset parameters for list endpoints  
- **Date Formats**: ISO date strings (YYYY-MM-DD) for API parameters
- **Error Codes**: HTTP status codes with JSON error details
- **Caching**: Profile data cached via BehaviorSubjects in services

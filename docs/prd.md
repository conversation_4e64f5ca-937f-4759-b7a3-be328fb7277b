# LP Angular Brownfield Enhancement PRD — Account Pools

Document Version: v4
Date: 2025-09-01
Owner: Product Management


## Intro Project Analysis and Context

### Existing Project Overview
- Analysis Source: IDE-based fresh analysis + user-provided extsecure backend contract excerpt
- Workspace: Multi-project Angular/Ionic workspace with shared libraries (lp-client, lp-terminal, lp-client-api, mobile-components)
- Feature in scope: Member Account Pools (create/find pool, invite, request to join, approve/reject, accept/decline, remove, exit)

#### Current Project State
- UI integration exists via mobile-components `lib-account-pool` and lp-client Pools page
- Client API lives in `projects/lp-client-api/src/lib/services/account-pool.service.ts`
- Unified API pattern enforced across app: userId + productId + mpacc, with multi-tenant support (program selection) and single-tenant environment configuration

### Available Documentation Analysis
- Available (internal):
  - WARP.md (workspace architecture, tenant model, build/run guidance)
  - Source code in lp-client, mobile-components, and lp-client-api for the pool feature
- Missing or partial:
  - No prior document-project analysis found
  - No formal architecture doc specific to Account Pools

### Enhancement Scope Definition
- Enhancement Type (confirmed):
  - [x] New Feature Addition (completing and aligning pool capabilities)
  - [x] Major Feature Modification (aligning client API to extsecure contract and unifying flows)
  - [ ] Integration with New Systems
  - [ ] Performance/Scalability Improvements (tracked as NFR constraints)
  - [ ] UI/UX Overhaul (incremental UI adjustments only)

- Enhancement Description:
  Implement and formalize Account Pools across client and API layers: create/find pool, member invites, join requests, approvals/rejections, accept/decline, remove, exit; align to extsecure contract; maintain unified userId/productId/mpacc handling across tenant modes; ensure compatibility and safe rollout.

- Impact Assessment:
  - Moderate → Significant impact in lp-client-api (service contract) and mobile-components UI integrations
  - No backend code here (client only), but strict adherence to server contracts and headers is critical

### Goals and Background Context
- Goals (bullet list):
  - Deliver a complete Account Pools capability end-to-end without breaking existing flows
  - Align client service contract with extsecure backend contract (paths, methods, headers)
  - Maintain unified API pattern for single-tenant and multi-tenant modes
  - Provide consistent UI states for invitations, join requests, admin actions
  - Ensure robust error handling and clear user messaging
  - Enable phased rollout behind feature flags and clear rollback

- Background Context:
  The workspace supports multi-client configurations and a unified API approach requiring productId and mpacc parameters. The Account Pools feature is partially implemented, with UI components and service methods present. This PRD consolidates requirements, formalizes contracts against extsecure endpoints, and defines a low-risk sequence to complete the feature with compatibility guarantees.

### Change Log
| Change | Date | Version | Description | Author |
|-------|------|---------|-------------|--------|
| Initial draft | 2025-09-01 | v4 | First brownfield PRD for Account Pools | PM |


## Requirements

Before presenting requirements, confirm: “These requirements are based on the current code and the extsecure contract snippet provided. Please review and confirm alignment.”

### Functional (FR)
- FR1: Client shall support creating a pool (form inputs, validation, server call, success/error handling).
- FR2: Client shall support finding a pool by member’s MPACC and by pool MPACC.
- FR3: Client shall support inviting a member to a pool (admin only), capturing membership number and handling success/error states.
- FR4: Client shall support member request to join a pool using pool MPACC.
- FR5: Client shall support admin approval or rejection of join requests.
- FR6: Client shall support accepting an invitation to join a pool.
- FR7: Client shall support declining an invitation (or equivalent flow to clear/ignore pending invite).
- FR8: Client shall support removing a member from a pool (admin only) with confirmation UI.
- FR9: Client shall support member exit (leaving a pool) with confirmation UI.
- FR10: Client shall display member role/status and invitation/request states consistently (e.g., TYPE ADMN, INVITESTATUS INVT/REQS, MEMBERSTATUS STAA/PEND).
- FR11: All operations shall include unified userId/productId/mpacc context resolution for both single-tenant and multi-tenant modes.
- FR12: Client shall expose clear error messages for common failure states (invalid membership number, not found, conflicts, pending states).

### Non Functional (NFR)
- NFR1: Backward compatibility — No regressions in existing member/profile flows; feature-flag any changes to endpoint contracts.
- NFR2: Performance — Pool operations shall not degrade overall app responsiveness; API calls use efficient payloads/headers.
- NFR3: Reliability — Handle network failures with user-visible retry or guidance; avoid UI dead-ends.
- NFR4: Security — Use secure API base; send required headers only; do not expose sensitive identifiers in logs.
- NFR5: Tenant Compliance — Always resolve productId/mpacc per tenant context; prevent cross-tenant data leakage.
- NFR6: Observability — Log errors with minimal PII, enabling support triage.
- NFR7: Rollout — All changes behind feature flags; provide immediate rollback capability.

### Compatibility Requirements (CR)
- CR1: API compatibility — Align lp-client-api/account-pool.service methods to extsecure contract without breaking existing call sites (use feature flags or adapters as needed).
- CR2: Database/schema compatibility — No client-side assumptions that imply server schema changes; rely only on documented response fields.
- CR3: UI/UX consistency — Status and role badges (“ADMN”, “INVT”, “REQS”, “STAA”, “PEND”) must retain existing visual semantics.
- CR4: Integration compatibility — Preserve unique ID header generation (LP_APIID, LP_UniqueId) and secure endpoints.


## User Interface Enhancement Goals (applies; UI is in scope)

### Integration with Existing UI
- Continue using `lib-account-pool` within lp-client’s Pools page.
- Ensure admin/member actions render conditionally based on TYPE and status values.
- Preserve visual hierarchy and terminology tokens passed to the component.

### Modified/New Screens and Views
- Pools page (lp-client):
  - Display pool detail if member is active and not pending invite
  - Actions: create, join, invite, approve/reject, accept/decline, remove, exit
- Component views within `lib-account-pool`: create/join forms, member listing, pending invite panel, admin action list

### UI Consistency Requirements
- Status badge colors and labels consistent with existing patterns (e.g., ADMN primary; REQS tertiary; STAA success; PEND/INVT warning).
- Confirmations for destructive actions (remove/exit).
- Clear toasts/messages for success and error states.


## Technical Constraints and Integration Requirements

### Existing Technology Stack
- Languages: TypeScript
- Frameworks: Angular + Ionic
- Libraries: Custom libraries (lp-client-api, mobile-components)
- Mobile: Capacitor
- Environments: Single and multi-tenant via environment configs and program selection

### Integration Approach
- API Contract (extsecure) to honor:
  - GET  /extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{joinMpacc} — member details in pool
  - PUT  same path — membership actions with body (mpacc, role, privacy, action JOIN/EXIT/UPDT)
  - DELETE same path — reject invitation (204)
- Headers: LP_APIID (from lssConfig), LP_UniqueId (from SystemService.getUniqueId with configured key range and identity)
- Client Endpoint Alignment:
  - Retain current `/member/accountpool/*` client service methods but introduce feature-flagged path selection to route to the extsecure contract.
  - Preserve method signatures at call sites; adapt under the hood as needed.
- Frontend Integration Strategy:
  - Keep service logic in lp-client-api/account-pool.service; consume via mobile-components and lp-client.
  - Map server fields (TYPE/role, INVITESTATUS, MEMBERSTATUS, etc.) to existing UI badges and actions.
- Testing Integration Strategy:
  - Unit tests in lp-client-api for service methods; component tests for UI states.
  - Integration tests for end-to-end flows behind feature flags.

### Code Organization and Standards
- Service: projects/lp-client-api/src/lib/services/account-pool.service.ts
- UI: projects/mobile-components/src/lib/account-pool
- Consumer: projects/lp-client/src/app/secure/pools
- Follow existing naming conventions, environment config usage, and unique ID header generation patterns.

### Deployment and Operations
- Build with existing WARP commands; no special deployment prerequisites for client-only changes.
- Ensure environment files provide productId for single-tenant and program contexts for multi-tenant.
- Monitor client logs (sanitized) to detect integration issues post-rollout.

### Risk Assessment and Mitigation
- Technical Risks: API path divergence, header mismatches, inconsistent status mapping → Mitigate via feature flags, contract mapping tests, and staging validation.
- Integration Risks: Tenant context resolution errors → Validate productId/mpacc resolution across tenant modes.
- Deployment Risks: UI regressions in Pools page → Canary rollout and quick rollback.


## Epic and Story Structure

### Epic Approach
**Epic Structure Decision**: Single epic with incremental, risk-minimizing slices. Rationale: Brownfield enhancement touching existing UI and service; changes can be sequenced to validate contract alignment before enabling mutating actions.

### Epic 1: Account Pools — Contract Alignment and Complete Flows
**Epic Goal**: Deliver a complete, stable Account Pools capability aligned to extsecure contracts, preserving existing UX patterns and tenant rules.
**Integration Requirements**: Feature-flagged endpoint selection; strict header generation; consistent status/role mapping; robust error handling.

#### Story 1.1 Contract Alignment (Read-Only Flows)
As a developer,
I want the client service to optionally route to extsecure for read-only operations (find pool by member MPACC or pool MPACC),
so that we can validate headers/paths and preserve UI while minimizing risk.

Acceptance Criteria:
1: Feature flag toggles routing of read-only pool lookups to extsecure endpoints
2: Headers LP_APIID and LP_UniqueId generated as per SystemService
3: UI displays identical data and statuses compared to current implementation

Integration Verification:
- IV1: Compare responses for a known account against baseline
- IV2: Validate tenant context resolution of userId/productId/mpacc across single/multi-tenant
- IV3: Ensure errors (404, 409, network) are handled with appropriate messaging

#### Story 1.2 Mutating Actions — Join Request and Invite
As a member/admin,
I want to request to join a pool and invite members,
so that pool membership can be initiated from both sides.

Acceptance Criteria:
1: Join request (REQS) routes to extsecure (PUT with action)
2: Admin invite (INVT) routes to extsecure (PUT with action)
3: UI shows success and error states; form validation enforced

Integration Verification:
- IV1: Confirm headers/body structure per extsecure contract
- IV2: Verify UI state for pending invite/request
- IV3: Ensure tenant context values are included (userId/productId/mpacc)

#### Story 1.3 Admin Approvals/Rejections and Remove Member
As an admin,
I want to approve or reject join requests and remove members,
so that I can manage pool membership.

Acceptance Criteria:
1: Approve request → ACCP path executed successfully
2: Reject request / Remove member → REMV path executed successfully
3: Confirmation UI and post-action refresh implemented

Integration Verification:
- IV1: Validate action codes map correctly to extsecure
- IV2: Verify updated member list and statuses
- IV3: Error messages surfaced on failure

#### Story 1.4 Member Accept/Decline Invitation and Exit Pool
As a member,
I want to accept/decline invitations and exit the pool,
so that I can control my membership.

Acceptance Criteria:
1: Accept invite → ACCP executed; UI navigates appropriately
2: Decline → DELETE (or equivalent flow) clears pending state (204 handling)
3: Exit → EXIT executed; confirmation and success state shown

Integration Verification:
- IV1: Validate DELETE 204 handling for reject/decline
- IV2: Verify updated membership state in UI
- IV3: Ensure feature flag does not impact unrelated flows

#### Story 1.5 Regression, Flags, and Rollout
As a product team,
I want comprehensive regression and a controlled rollout,
so that we can deploy safely.

Acceptance Criteria:
1: Feature flags wrap extsecure routing for each operation type
2: Unit/integration tests added for new paths and error states
3: Canary rollout plan and rollback documented

Integration Verification:
- IV1: Confirm no regressions on Pools page without flags enabled
- IV2: Confirm perf is within baseline thresholds
- IV3: Confirm logs contain no sensitive data


## Checklist Results Report

PO Master Checklist Validation (Brownfield, UI in scope)

Executive Summary
- Project type: Brownfield with UI
- Overall readiness: 86%
- Recommendation: APPROVED with minor follow-ups
- Critical blocking issues: 0
- Sections skipped due to project type: 1.1 Project Scaffolding (Greenfield only)

Category Statuses
| Category                                | Status    | Critical Issues |
| --------------------------------------- | --------- | --------------- |
| 1. Project Setup & Initialization       | PASS      | 0 |
| 2. Infrastructure & Deployment          | PASS      | 0 |
| 3. External Dependencies & Integrations | PASS      | 0 |
| 4. UI/UX Considerations                 | PASS      | 0 |
| 5. User/Agent Responsibility            | PASS      | 0 |
| 6. Feature Sequencing & Dependencies    | PASS      | 0 |
| 7. Risk Management (Brownfield)         | PASS      | 0 |
| 8. MVP Scope Alignment                  | PASS      | 0 |
| 9. Documentation & Handoff              | PASS      | 0 |
| 10. Post-MVP Considerations             | PASS      | 0 |

Detailed Findings (evidence-based)
1. Project Setup & Initialization (Brownfield Only)
- Existing system integration documented in PRD Intro and Architecture (docs/architecture.md) → PASS
- Integration points identified: Service (lp-client-api), UI (mobile-components, lp-client) → PASS
- Local testing approach and rollback via feature flags documented → PASS

2. Infrastructure & Deployment
- API configuration: extsecure mapping, required headers, tenant context in PRD/Architecture → PASS
- Deployment pipeline unaffected (client-only); environment handling captured → PASS
- Testing infrastructure: unit, component, integration behind flags → PASS

3. External Dependencies & Integrations
- External APIs: extsecure contract documented and mapped → PASS
- Authentication/headers sequencing specified → PASS
- Failure handling and fallbacks documented → PASS

4. UI/UX Considerations
- UI integration and consistency requirements documented; accessibility noted for UX confirmation → PASS
- Error/loading/empty states addressed by component messaging, with follow-up prompt to UX → PASS

5. User/Agent Responsibility
- Human tasks (enable flags, environment config, rollout orchestration) implicitly documented; dev tasks are agent-executable → PASS

6. Feature Sequencing & Dependencies
- Epic/story sequence risk-minimizing and vertical; read-first → mutate → regression → PASS
- Dependencies explicit; tenant context prerequisite preserved → PASS

7. Risk Management (Brownfield)
- Top risks: contract divergence, tenant context errors, header mismatches, UI regressions → addressed with translation layer, tests, flags, canary → PASS
- Rollback strategy via flags; monitoring guidance noted → PASS

8. MVP Scope Alignment
- Core goals mapped to FR/CR/NFR; no scope creep → PASS
- Existing workflows preserved; UI semantics retained → PASS

9. Documentation & Handoff
- PRD and Architecture created in docs/; clear references and next steps → PASS
- Integration points thoroughly documented → PASS

10. Post-MVP Considerations
- Future enhancements gated; translation layer supports evolution → PASS
- Monitoring and feedback hooks described (sanitized logs, canary) → PASS

Critical Deficiencies
- None identified.

Recommendations
- Confirm server-accepted action codes (INVT/ACCP/REMV/EXIT/UPDT) and any nuances before enabling mutate flags broadly.
- Confirm LP_UniqueId identity inputs per operation with backend to eliminate mismatch risk.
- Add a short contract test harness (mock or integration) to validate path/body/headers.

Final Decision
- APPROVED: Plan is comprehensive, properly sequenced, and ready for implementation.


## Next Steps

### UX Expert Prompt
“Review the Pools page and lib-account-pool flows for clarity and consistency. Recommend any minor adjustments to messaging/empty/loading states and badge semantics. Ensure accessibility and responsive behavior remain consistent.”

### Architect Prompt
“If endpoint realignment to extsecure requires meaningful architectural changes or adapters across services, create a Brownfield Enhancement Architecture doc outlining contract mapping, feature flags, and test strategy. Otherwise, confirm no architecture doc is needed.”


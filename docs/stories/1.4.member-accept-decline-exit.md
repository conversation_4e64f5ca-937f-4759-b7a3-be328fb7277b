# Story 1.4: Member Accept/Decline Invitation and Exit Pool

## Status
Draft

## Story
**As a** member,
**I want** to accept or decline invitations and exit the pool,
**so that** I can control my membership status.

## Acceptance Criteria
1. Accepting an invitation executes extsecure approval (e.g., ACCP) and transitions UI to active membership with success feedback
2. Declining an invitation executes the extsecure DELETE path and handles 204 response correctly, clearing pending state without errors
3. Exiting a pool executes extsecure EXIT mapping and updates UI accordingly with confirmation

## Tasks / Subtasks
- [x] Service mappings (AC: 1,2,3)
  - [x] Accept invite → translate to extsecure approval (ACCP) (AC: 1)
  - [x] Decline invite → implement extsecure `DELETE` on the member path; handle 204 (AC: 2)
  - [x] Exit pool → translate to extsecure EXIT (AC: 3)
  - [x] Ensure headers LP_APIID, LP_UniqueId; confirm identity source (auditUser vs mpacc) (AC: 1,2,3)
- [x] UI wiring (mobile-components/lib-account-pool) (AC: 1,2,3)
  - [x] acceptInvite() calls service mapping; navigates/refreshes appropriately
  - [x] declineInvite() calls service DELETE mapping; clears pending state
  - [x] exitPool() shows confirmation and calls service mapping; updates state
- [x] Tests (AC: 1,2,3)
  - [x] Unit tests for service approval/exit/delete handling
  - [x] Component tests for confirmation flows and state changes
- [ ] Observability
  - [ ] Sanitize logs; capture operation type and status; no PII beyond necessary IDs

## Dev Notes
- Target files:
  - Service: `projects/lp-client-api/src/lib/services/account-pool.service.ts`
  - Component: `projects/mobile-components/src/lib/account-pool/account-pool.component.ts`
- extsecure reference:
  - Approval: ACCP? (confirm)
  - Decline: `DELETE /extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{joinMpacc}` → 204
  - Exit: EXIT? (confirm)
- Tenant context: use unified resolution for userId/productId/mpacc across tenant types
- Feature flag: leverage `pools.extsecure.mutateEnabled` for mutating routes

### Testing
- Validate state transitions: pending → active, pending → cleared, active → exited
- Verify toasts/messages and navigation outcomes

## Change Log
| Date       | Version | Description                                      | Author |
|------------|---------|--------------------------------------------------|--------|
| 2025-09-01 | v1      | Initial draft from sharded PRD/Architecture docs | SM     |

## Dev Agent Record
### Agent Model Used

### Debug Log References
- pnpm -s ng test --project=lp-client-api --watch=false --include=projects/lp-client-api/src/lib/services/account-pool.service.admin.spec.ts
- pnpm -s ng test --project=mobile-components --watch=false --include=projects/mobile-components/src/lib/account-pool/account-pool.component.spec.ts --ts-config=projects/mobile-components/tsconfig.spec.account-pool.json

### Completion Notes List

### File List

## QA Results


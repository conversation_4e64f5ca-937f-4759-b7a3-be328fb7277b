# Story 1.2: Mutating Actions — Join Request and Invite

## Status
Draft

## Story
**As a** member/admin,
**I want** to request to join a pool and invite members,
**so that** pool membership can be initiated from both sides according to the extsecure contract.

## Acceptance Criteria
1. Join request uses extsecure mapping (PUT) with correct path, headers (LP_APIID, LP_UniqueId), and body (mpacc, role, privacy, action=REQS or equivalent) and returns expected statuses
2. Admin invite uses extsecure mapping (PUT) with correct path, headers, and body (mpacc, role, privacy, action=INVT or equivalent) and returns expected statuses
3. UI shows pending/request/invite states (INVITESTATUS=REQS/INVT) and clear success/error feedback without regressions

## Tasks / Subtasks
- [x] Add/confirm feature flag for mutating routing (AC: 1,2)
  - [x] Key: `pools.extsecure.mutateEnabled` with safe environment defaults (AC: 1,2)
- [x] Service: Map join/invite to extsecure (AC: 1,2)
  - [x] Implement translation to extsecure path `PUT /extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{joinMpacc}` (AC: 1,2)
  - [x] Provide request body: `{ mpacc, role, privacy, action: 'REQS'|'INVT' }` (confirm exact server expectations) (AC: 1,2)
  - [x] Ensure headers LP_APIID, LP_UniqueId from SystemService; confirm identity input (mpacc vs auditUser) (AC: 1,2)
- [ ] UI wiring (mobile-components/lib-account-pool) (AC: 3)
  - [ ] requestToJoinPool() uses extsecure mapping when flag enabled (AC: 1)
  - [ ] inviteMember() uses extsecure mapping when flag enabled (AC: 2)
  - [ ] Reflect pending invite/request states; show toasts/messages (AC: 3)
- [ ] Tests (AC: 1,2,3)
  - [x] Unit tests for service mapping and headers
  - [x] Component tests for form validation and state feedback (note: workspace test runner has unrelated compile issues; spec added but full run blocked)
  - [ ] Integration tests behind flags for end-to-end flows
- [ ] Observability
  - [ ] Sanitize logs; ensure parity with existing logging patterns; capture operation type and status

## Dev Notes
- Target files:
  - Service: `projects/lp-client-api/src/lib/services/account-pool.service.ts`
  - Component: `projects/mobile-components/src/lib/account-pool/account-pool.component.ts`
- extsecure reference:
  - Base: `/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{joinMpacc}`
  - Body: `{ mpacc, role, privacy, action }`
- Mapping guidance:
  - Join Request: `joinMpacc` is the requesting member’s MPACC; `mpacc` path segment denotes current user; confirm conventions
  - Invite: `joinMpacc` is the invitee’s MPACC; admin is current user; confirm role defaults
- Tenant context (must be resolved for every call):
  - Single-tenant: productId from environment.lssConfig.workflow.productId; mpacc from profile
  - Multi-tenant: productId/mpacc from program context
- Feature flag: `pools.extsecure.mutateEnabled`

### Testing
- Co-locate unit tests with service; extend component tests for invites & join requests
- Verify INVITESTATUS transitions and user feedback messages

## Change Log
| Date       | Version | Description                                      | Author |
|------------|---------|--------------------------------------------------|--------|
| 2025-09-01 | v1      | Initial draft from sharded PRD/Architecture docs | SM     |

## Dev Agent Record
### Agent Model Used

### Debug Log References
- pnpm -s ng test --project=lp-client-api --watch=false --include=projects/lp-client-api/src/lib/services/account-pool.service.mutate.spec.ts
- Long-form mapping tests (REQS/INVT) passing; fallback-to-form test passing when productId missing

### Completion Notes List
- Added mutate flag hook (defaults to false when absent)
- Confirmed join/invite service routes and headers; added unit tests (form-encoded body)
- UI already wires to service methods; no code changes required in component

### File List
- projects/lp-client-api/src/lib/services/account-pool.service.ts

## QA Results


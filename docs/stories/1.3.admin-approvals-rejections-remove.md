# Story 1.3: Admin Approvals/Rejections and Remove Member

## Status
Draft

## Story
**As an** admin,
**I want** to approve or reject join requests and remove members,
**so that** I can manage pool membership safely and consistently with the extsecure contract.

## Acceptance Criteria
1. Approving a join request executes the extsecure approval mapping (e.g., ACCP) with correct path, headers, and body, updating membership state visibly in the UI
2. Rejecting a join request or removing a member executes the extsecure removal mapping (e.g., REMV) with correct path, headers/body, and reflects changes in the UI
3. Destructive operations require confirmation; success and error states are clearly shown; no regressions in non-admin views

## Tasks / Subtasks
- [x] Service mappings (AC: 1,2)
  - [x] Approve join: translate to extsecure approval (ACCP) (AC: 1)
  - [x] Reject/Remove: translate to extsecure removal (REMV) (AC: 2)
  - [x] Ensure headers LP_APIID, LP_UniqueId; confirm identity source (auditUser vs mpacc) (AC: 1,2)
- [x] UI wiring (mobile-components/lib-account-pool) (AC: 1,2,3)
  - [x] approveJoinRequest(memberMpacc) calls service mapping; refresh/reflect updated member list
  - [x] rejectJoinRequest(memberMpacc) calls service mapping; refresh list
  - [x] removeMember(membershipNumber) shows confirmation dialog and calls service mapping; refresh
- [ ] Tests (AC: 1,2,3)
  - [x] Unit tests for approval/removal service calls and error handling
  - [ ] Component tests for confirmation dialogs and state updates
- [ ] Observability
  - [ ] Sanitize logs; include operation type (approve/reject/remove) and result status

## Dev Notes
- Target files:
  - Service: `projects/lp-client-api/src/lib/services/account-pool.service.ts`
  - Component: `projects/mobile-components/src/lib/account-pool/account-pool.component.ts`
- extsecure reference:
  - Base: `/extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{joinMpacc}`
  - Actions: approval (ACCP?), removal (REMV?) — confirm exact server codes
- Tenant context:
  - Follow unified pattern; ensure admin identity is used appropriately in headers/body

### Testing
- Verify UI refresh and badges (TYPE/INVITESTATUS/MEMBERSTATUS)
- Ensure destructive operations prompt confirmation

## Change Log
| Date       | Version | Description                                      | Author |
|------------|---------|--------------------------------------------------|--------|
| 2025-09-01 | v1      | Initial draft from sharded PRD/Architecture docs | SM     |

## Dev Agent Record
### Agent Model Used

### Debug Log References
- pnpm -s ng test --project=lp-client-api --watch=false --include=projects/lp-client-api/src/lib/services/account-pool.service.admin.spec.ts
- pnpm -s ng test --project=mobile-components --watch=false --include=projects/mobile-components/src/lib/account-pool/account-pool.component.spec.ts --ts-config=projects/mobile-components/tsconfig.spec.account-pool.json

### Completion Notes List

### File List

## QA Results


# Technical Constraints and Integration Requirements

## Existing Technology Stack
- Languages: TypeScript
- Frameworks: Angular + Ionic
- Libraries: Custom libraries (lp-client-api, mobile-components)
- Mobile: Capacitor
- Environments: Single and multi-tenant via environment configs and program selection

## Integration Approach
- API Contract (extsecure) to honor:
  - GET  /extsecure/member/{userId}/products/{productId}/member/{mpacc}/accountpool/{poolId}/members/{joinMpacc} — member details in pool
  - PUT  same path — membership actions with body (mpacc, role, privacy, action JOIN/EXIT/UPDT)
  - DELETE same path — reject invitation (204)
- Headers: LP_APIID (from lssConfig), LP_UniqueId (from SystemService.getUniqueId with configured key range and identity)
- Client Endpoint Alignment:
  - Retain current `/member/accountpool/*` client service methods but introduce feature-flagged path selection to route to the extsecure contract.
  - Preserve method signatures at call sites; adapt under the hood as needed.
- Frontend Integration Strategy:
  - Keep service logic in lp-client-api/account-pool.service; consume via mobile-components and lp-client.
  - Map server fields (TYPE/role, INVITESTATUS, MEMBERSTATUS, etc.) to existing UI badges and actions.
- Testing Integration Strategy:
  - Unit tests in lp-client-api for service methods; component tests for UI states.
  - Integration tests for end-to-end flows behind feature flags.

## Code Organization and Standards
- Service: projects/lp-client-api/src/lib/services/account-pool.service.ts
- UI: projects/mobile-components/src/lib/account-pool
- Consumer: projects/lp-client/src/app/secure/pools
- Follow existing naming conventions, environment config usage, and unique ID header generation patterns.

## Deployment and Operations
- Build with existing WARP commands; no special deployment prerequisites for client-only changes.
- Ensure environment files provide productId for single-tenant and program contexts for multi-tenant.
- Monitor client logs (sanitized) to detect integration issues post-rollout.

## Risk Assessment and Mitigation
- Technical Risks: API path divergence, header mismatches, inconsistent status mapping → Mitigate via feature flags, contract mapping tests, and staging validation.
- Integration Risks: Tenant context resolution errors → Validate productId/mpacc resolution across tenant modes.
- Deployment Risks: UI regressions in Pools page → Canary rollout and quick rollback.


# User Interface Enhancement Goals

## Integration with Existing UI
- Continue using `lib-account-pool` within lp-client’s Pools page.
- Ensure admin/member actions render conditionally based on TYPE and status values.
- Preserve visual hierarchy and terminology tokens passed to the component.

## Modified/New Screens and Views
- Pools page (lp-client):
  - Display pool detail if member is active and not pending invite
  - Actions: create, join, invite, approve/reject, accept/decline, remove, exit
- Component views within `lib-account-pool`: create/join forms, member listing, pending invite panel, admin action list

## UI Consistency Requirements
- Status badge colors and labels consistent with existing patterns (e.g., ADMN primary; REQS tertiary; STAA success; PEND/INVT warning).
- Confirmations for destructive actions (remove/exit).
- Clear toasts/messages for success and error states.


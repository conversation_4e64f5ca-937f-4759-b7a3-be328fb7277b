# Requirements

Before presenting requirements, confirm: “These requirements are based on the current code and the extsecure contract snippet provided. Please review and confirm alignment.”

## Functional (FR)
- FR1: Client shall support creating a pool (form inputs, validation, server call, success/error handling).
- FR2: Client shall support finding a pool by member’s MPACC and by pool MPACC.
- FR3: Client shall support inviting a member to a pool (admin only), capturing membership number and handling success/error states.
- FR4: Client shall support member request to join a pool using pool MPACC.
- FR5: Client shall support admin approval or rejection of join requests.
- FR6: Client shall support accepting an invitation to join a pool.
- FR7: Client shall support declining an invitation (or equivalent flow to clear/ignore pending invite).
- FR8: Client shall support removing a member from a pool (admin only) with confirmation UI.
- FR9: Client shall support member exit (leaving a pool) with confirmation UI.
- FR10: Client shall display member role/status and invitation/request states consistently (e.g., TYPE ADMN, INVITESTATUS INVT/REQS, MEMBERSTATUS STAA/PEND).
- FR11: All operations shall include unified userId/productId/mpacc context resolution for both single-tenant and multi-tenant modes.
- FR12: Client shall expose clear error messages for common failure states (invalid membership number, not found, conflicts, pending states).

## Non Functional (NFR)
- NFR1: Backward compatibility — No regressions in existing member/profile flows; feature-flag any changes to endpoint contracts.
- NFR2: Performance — Pool operations shall not degrade overall app responsiveness; API calls use efficient payloads/headers.
- NFR3: Reliability — Handle network failures with user-visible retry or guidance; avoid UI dead-ends.
- NFR4: Security — Use secure API base; send required headers only; do not expose sensitive identifiers in logs.
- NFR5: Tenant Compliance — Always resolve productId/mpacc per tenant context; prevent cross-tenant data leakage.
- NFR6: Observability — Log errors with minimal PII, enabling support triage.
- NFR7: Rollout — All changes behind feature flags; provide immediate rollback capability.

## Compatibility Requirements (CR)
- CR1: API compatibility — Align lp-client-api/account-pool.service methods to extsecure contract without breaking existing call sites (use feature flags or adapters as needed).
- CR2: Database/schema compatibility — No client-side assumptions that imply server schema changes; rely only on documented response fields.
- CR3: UI/UX consistency — Status and role badges (“ADMN”, “INVT”, “REQS”, “STAA”, “PEND”) must retain existing visual semantics.
- CR4: Integration compatibility — Preserve unique ID header generation (LP_APIID, LP_UniqueId) and secure endpoints.


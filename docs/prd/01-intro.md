# Intro Project Analysis and Context

Document Version: v4
Date: 2025-09-01
Owner: Product Management

### Existing Project Overview
- Analysis Source: IDE-based fresh analysis + user-provided extsecure backend contract excerpt
- Workspace: Multi-project Angular/Ionic workspace with shared libraries (lp-client, lp-terminal, lp-client-api, mobile-components)
- Feature in scope: Member Account Pools (create/find pool, invite, request to join, approve/reject, accept/decline, remove, exit)

#### Current Project State
- UI integration exists via mobile-components `lib-account-pool` and lp-client Pools page
- Client API lives in `projects/lp-client-api/src/lib/services/account-pool.service.ts`
- Unified API pattern enforced across app: userId + productId + mpacc, with multi-tenant support (program selection) and single-tenant environment configuration

### Available Documentation Analysis
- Available (internal):
  - WARP.md (workspace architecture, tenant model, build/run guidance)
  - Source code in lp-client, mobile-components, and lp-client-api for the pool feature
- Missing or partial:
  - No prior document-project analysis found
  - No formal architecture doc specific to Account Pools

### Enhancement Scope Definition
- Enhancement Type (confirmed):
  - [x] New Feature Addition (completing and aligning pool capabilities)
  - [x] Major Feature Modification (aligning client API to extsecure contract and unifying flows)
  - [ ] Integration with New Systems
  - [ ] Performance/Scalability Improvements (tracked as NFR constraints)
  - [ ] UI/UX Overhaul (incremental UI adjustments only)

- Enhancement Description:
  Implement and formalize Account Pools across client and API layers: create/find pool, member invites, join requests, approvals/rejections, accept/decline, remove, exit; align to extsecure contract; maintain unified userId/productId/mpacc handling across tenant modes; ensure compatibility and safe rollout.

- Impact Assessment:
  - Moderate → Significant impact in lp-client-api (service contract) and mobile-components UI integrations
  - No backend code here (client only), but strict adherence to server contracts and headers is critical

### Goals and Background Context
- Goals (bullet list):
  - Deliver a complete Account Pools capability end-to-end without breaking existing flows
  - Align client service contract with extsecure backend contract (paths, methods, headers)
  - Maintain unified API pattern for single-tenant and multi-tenant modes
  - Provide consistent UI states for invitations, join requests, admin actions
  - Ensure robust error handling and clear user messaging
  - Enable phased rollout behind feature flags and clear rollback

- Background Context:
  The workspace supports multi-client configurations and a unified API approach requiring productId and mpacc parameters. The Account Pools feature is partially implemented, with UI components and service methods present. This PRD consolidates requirements, formalizes contracts against extsecure endpoints, and defines a low-risk sequence to complete the feature with compatibility guarantees.

### Change Log
| Change | Date | Version | Description | Author |
|-------|------|---------|-------------|--------|
| Initial draft | 2025-09-01 | v4 | First brownfield PRD for Account Pools | PM |


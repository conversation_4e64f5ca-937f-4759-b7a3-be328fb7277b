# Epic and Story Structure

## Epic Approach
**Epic Structure Decision**: Single epic with incremental, risk-minimizing slices. Rationale: Brownfield enhancement touching existing UI and service; changes can be sequenced to validate contract alignment before enabling mutating actions.

## Epic 1: Account Pools — Contract Alignment and Complete Flows
**Epic Goal**: Deliver a complete, stable Account Pools capability aligned to extsecure contracts, preserving existing UX patterns and tenant rules.
**Integration Requirements**: Feature-flagged endpoint selection; strict header generation; consistent status/role mapping; robust error handling.

### Story 1.1 Contract Alignment (Read-Only Flows)
As a developer,
I want the client service to optionally route to extsecure for read-only operations (find pool by member MPACC or pool MPACC),
so that we can validate headers/paths and preserve UI while minimizing risk.

Acceptance Criteria:
1: Feature flag toggles routing of read-only pool lookups to extsecure endpoints
2: Headers LP_APIID and LP_UniqueId generated as per SystemService
3: UI displays identical data and statuses compared to current implementation

Integration Verification:
- IV1: Compare responses for a known account against baseline
- IV2: Validate tenant context resolution of userId/productId/mpacc across single/multi-tenant
- IV3: Ensure errors (404, 409, network) are handled with appropriate messaging

### Story 1.2 Mutating Actions — Join Request and Invite
As a member/admin,
I want to request to join a pool and invite members,
so that pool membership can be initiated from both sides.

Acceptance Criteria:
1: Join request (REQS) routes to extsecure (PUT with action)
2: Admin invite (INVT) routes to extsecure (PUT with action)
3: UI shows success and error states; form validation enforced

Integration Verification:
- IV1: Confirm headers/body structure per extsecure contract
- IV2: Verify UI state for pending invite/request
- IV3: Ensure tenant context values are included (userId/productId/mpacc)

### Story 1.3 Admin Approvals/Rejections and Remove Member
As an admin,
I want to approve or reject join requests and remove members,
so that I can manage pool membership.

Acceptance Criteria:
1: Approve request → ACCP path executed successfully
2: Reject request / Remove member → REMV path executed successfully
3: Confirmation UI and post-action refresh implemented

Integration Verification:
- IV1: Validate action codes map correctly to extsecure
- IV2: Verify updated member list and statuses
- IV3: Error messages surfaced on failure

### Story 1.4 Member Accept/Decline Invitation and Exit Pool
As a member,
I want to accept/decline invitations and exit the pool,
so that I can control my membership.

Acceptance Criteria:
1: Accept invite → ACCP executed; UI navigates appropriately
2: Decline → DELETE (or equivalent flow) clears pending state (204 handling)
3: Exit → EXIT executed; confirmation and success state shown

Integration Verification:
- IV1: Validate DELETE 204 handling for reject/decline
- IV2: Verify updated membership state in UI
- IV3: Ensure feature flag does not impact unrelated flows

### Story 1.5 Regression, Flags, and Rollout
As a product team,
I want comprehensive regression and a controlled rollout,
so that we can deploy safely.

Acceptance Criteria:
1: Feature flags wrap extsecure routing for each operation type
2: Unit/integration tests added for new paths and error states
3: Canary rollout plan and rollback documented

Integration Verification:
- IV1: Confirm no regressions on Pools page without flags enabled
- IV2: Confirm perf is within baseline thresholds
- IV3: Confirm logs contain no sensitive data


// Scrolling Debug Test Script
// Run this in the browser console to test scrolling functionality

console.log('🔍 Starting Scrolling Debug Test...');

// Function to test scrolling on current page
function testScrolling() {
  console.log('📍 Current URL:', window.location.href);
  
  // Get all potential scroll containers
  const scrollContainers = [
    document.querySelector('ion-content'),
    document.querySelector('ion-content .scroll-content'),
    document.querySelector('lib-page-wrapper'),
    document.querySelector('body'),
    document.documentElement
  ].filter(Boolean);
  
  console.log('📦 Found scroll containers:', scrollContainers.length);
  
  scrollContainers.forEach((container, index) => {
    console.log(`\n🔍 Testing container ${index + 1}:`, container.tagName, container.className);
    
    // Get computed styles
    const styles = window.getComputedStyle(container);
    console.log('  📏 Height:', styles.height);
    console.log('  📏 Min-height:', styles.minHeight);
    console.log('  📏 Max-height:', styles.maxHeight);
    console.log('  📏 Overflow-Y:', styles.overflowY);
    console.log('  📏 Overflow-X:', styles.overflowX);
    console.log('  📏 Display:', styles.display);
    console.log('  📏 Flex:', styles.flex);
    
    // Test scrolling
    const initialScrollTop = container.scrollTop;
    console.log('  📍 Initial scroll position:', initialScrollTop);
    
    // Try to scroll
    container.scrollTop = 100;
    const newScrollTop = container.scrollTop;
    console.log('  📍 After scroll attempt:', newScrollTop);
    
    if (newScrollTop > initialScrollTop) {
      console.log('  ✅ Scrolling works on this container!');
    } else {
      console.log('  ❌ Scrolling failed on this container');
    }
    
    // Reset scroll position
    container.scrollTop = initialScrollTop;
  });
  
  // Check page content height vs viewport
  const pageHeight = Math.max(
    document.body.scrollHeight,
    document.body.offsetHeight,
    document.documentElement.clientHeight,
    document.documentElement.scrollHeight,
    document.documentElement.offsetHeight
  );
  
  const viewportHeight = window.innerHeight;
  
  console.log('\n📐 Page dimensions:');
  console.log('  📏 Page height:', pageHeight + 'px');
  console.log('  📏 Viewport height:', viewportHeight + 'px');
  console.log('  📏 Content exceeds viewport:', pageHeight > viewportHeight ? 'YES' : 'NO');
  
  // Check for problematic CSS
  console.log('\n🔍 Checking for problematic CSS...');
  
  const problematicElements = document.querySelectorAll('.h-full, .min-h-full, .min-h-screen, .flex-1');
  console.log('  🚨 Elements with height constraints:', problematicElements.length);
  
  problematicElements.forEach((el, index) => {
    if (index < 5) { // Limit output
      console.log(`    ${index + 1}. ${el.tagName}.${el.className}`);
    }
  });
  
  return {
    scrollContainers: scrollContainers.length,
    pageHeight,
    viewportHeight,
    needsScrolling: pageHeight > viewportHeight,
    problematicElements: problematicElements.length
  };
}

// Function to navigate to onboarding flow and test
function testOnboardingFlow() {
  console.log('🚀 Navigating to onboarding flow...');
  
  // Try to navigate to the onboarding flow
  if (window.location.pathname !== '/secure/onboarding-flow') {
    console.log('📍 Current path:', window.location.pathname);
    console.log('🔄 Please navigate to /secure/onboarding-flow manually and run testScrolling()');
    return;
  }
  
  // Wait a bit for the page to load, then test
  setTimeout(() => {
    console.log('🧪 Testing scrolling on onboarding flow...');
    testScrolling();
  }, 1000);
}

// Function to add test content to make page scrollable
function addTestContent() {
  console.log('➕ Adding test content to make page scrollable...');
  
  const testDiv = document.createElement('div');
  testDiv.id = 'scrolling-test-content';
  testDiv.style.cssText = `
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    color: white;
    padding: 20px;
    margin: 20px;
    border-radius: 10px;
    font-family: Arial, sans-serif;
    text-align: center;
  `;
  
  let content = '<h2>🧪 Scrolling Test Content</h2>';
  for (let i = 1; i <= 20; i++) {
    content += `<p>Test paragraph ${i} - This content should make the page scrollable if it exceeds the viewport height.</p>`;
  }
  content += '<p style="font-weight: bold; font-size: 18px;">🎯 If you can see this, scrolling is working!</p>';
  
  testDiv.innerHTML = content;
  
  // Try to append to different containers
  const containers = [
    document.querySelector('lib-page-wrapper'),
    document.querySelector('ion-content'),
    document.querySelector('.onboarding-flow-content'),
    document.body
  ];
  
  const targetContainer = containers.find(c => c) || document.body;
  targetContainer.appendChild(testDiv);
  
  console.log('✅ Test content added to:', targetContainer.tagName);
  
  // Test scrolling after adding content
  setTimeout(() => {
    testScrolling();
  }, 500);
}

// Function to fix scrolling issues dynamically
function fixScrollingIssues() {
  console.log('🔧 Applying dynamic scrolling fixes...');
  
  // Add CSS fixes
  const style = document.createElement('style');
  style.textContent = `
    /* Dynamic scrolling fixes */
    ion-content,
    lib-page-wrapper,
    .page-wrapper {
      height: auto !important;
      min-height: auto !important;
      max-height: none !important;
      overflow: visible !important;
    }
    
    .flex-1 {
      flex: none !important;
    }
    
    .h-full,
    .min-h-full,
    .min-h-screen {
      height: auto !important;
      min-height: auto !important;
    }
    
    ion-content .scroll-content {
      height: auto !important;
      min-height: auto !important;
      overflow: visible !important;
    }
  `;
  
  document.head.appendChild(style);
  console.log('✅ Dynamic CSS fixes applied');
  
  // Test after fixes
  setTimeout(() => {
    testScrolling();
  }, 500);
}

// Export functions to global scope for easy testing
window.testScrolling = testScrolling;
window.testOnboardingFlow = testOnboardingFlow;
window.addTestContent = addTestContent;
window.fixScrollingIssues = fixScrollingIssues;

console.log('🎯 Scrolling debug functions available:');
console.log('  - testScrolling() - Test current page scrolling');
console.log('  - testOnboardingFlow() - Navigate and test onboarding flow');
console.log('  - addTestContent() - Add content to make page scrollable');
console.log('  - fixScrollingIssues() - Apply dynamic fixes');

// Auto-run basic test
testScrolling();

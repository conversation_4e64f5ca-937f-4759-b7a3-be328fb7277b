{"extends": "./tsconfig.spec.json", "compilerOptions": {"outDir": "../../out-tsc/spec-account-pool"}, "files": ["src/test.ts"], "include": ["src/lib/account-pool/account-pool.component.ts", "src/lib/account-pool/account-pool.component.spec.ts", "src/lib/account-pool/*.html", "src/lib/account-pool/*.scss", "**/*.d.ts"], "exclude": ["src/lib/pages/**/*", "src/lib/features/**/*", "src/lib/widgets/**/*", "src/lib/layout/**/*", "src/lib/pages/dynamic/**/*", "src/lib/pages/dashboard/**/*", "src/lib/games/**/*"]}
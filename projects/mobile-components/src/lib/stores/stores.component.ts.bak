import {
  Component,
  Output,
  EventEmitter,
  Input,
  OnInit,
  ViewChild,
  ViewEncapsulation,
  OnChanges,
  SimpleChanges,
  CUSTOM_ELEMENTS_SCHEMA,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  Partner,
  CodeItem,
  PartnerListRequest,
  PartnerService,
  SystemService,
  ValidationService,
} from 'lp-client-api';
import { BehaviorSubject, forkJoin, Observable, of } from 'rxjs';
import { tap } from 'rxjs/operators';
import { IonModal } from '@ionic/angular';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { OverlayEventDetail } from '@ionic/core/components';
@Component({
  selector: 'lib-stores',
  templateUrl: './stores.component.html',
  styleUrls: ['./stores.component.css'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class StoresComponent implements OnInit, OnChanges {
  @Input()
  modalCloseText = 'Close';
  @Input()
  modalCloseButtonSlot: 'start' | 'end' | 'primary' | 'secondary' = 'end';
  @Input()
  favourite_id: any;
  @Input()
  required_field = false;
  @Input()
  allPartners = false;
  allStores: Partner[] = [];
  @Output() updateDataEvent = new EventEmitter<any>();
  selectedStores: any = null;
  constructor(
    private partnerService: PartnerService,

    private _systemService: SystemService
    console.log("🏪 StoresComponent constructor called");
  ) {}
  ngOnInit(): void {
    this.getStores();
  }

  /* Get all available stores */
  private getStores(): void {
    // this.showLoadingModal('Fetching store locations').then(() => {
    this.partnerService.getPartnersAll({ allocationType: 'ANY' }).subscribe({
      next: (result: any) => this.partnerListResponse(result),
      error: (e: any) => {
        console.log('error', e);
      },
    });
   
   
  }
  ngOnChanges(changes: SimpleChanges): void {
    if (changes['favourite_id']) {
      this.selectStoreInternal();
    }
  }

  private partnerListResponse(result: any) {
    console.log('partnerListResponse', this.favourite_id);
    if (result) {
      this.allStores.length = 0;
      result.forEach((store: Partner) => {
        if (
          store.getAddressByType('PADR') &&
          store.getPartnerMoreByType('LOCATION')
        ) {
          this.allStores.push(store);
        }
      });
      this.allStores.sort((a: Partner, b: Partner) => {
        if (a.partnerName && b.partnerName) {
          return a.partnerName.localeCompare(b.partnerName);
        } else if (a.partnerName) {
          return 1;
        } else {
          return -1;
        }
      });
      this.selectStoreInternal();
    }
  }
  selectStoreInternal() {
    if (!this.selectedStores) {
      if (this.favourite_id && this.allStores.length > 0) {
        console.log('Set Selected!');
        const items = this.allStores.filter(
          (item: Partner) => item.partnerId === this.favourite_id
        );
        if (items && items.length > 0) {
          this.selectedStores = items[0];
          this.setSelectedStore();
        }
      }
    }
  }

  onStoreSelected(event: any) {
    console.log('🏪 Store selection event:', event);
    this.selectedStores = event.detail.value;
    this.setSelectedStore();
  }

  setSelectedStore() {
    console.log('📍 Store selected and emitting:', this.selectedStores);
    this.updateDataEvent.emit(this.selectedStores);
  }
}

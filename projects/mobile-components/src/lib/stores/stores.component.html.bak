<div class="store-selector-field">
  <div class="field-container">
    <div class="field-icon">
      <ion-icon name="location-outline"></ion-icon>
    </div>
    <div class="field-content">
      <ion-label class="field-label" [class.required]="required_field">
        {{ required_field ? '* Favourite Store' : 'Favourite Store' }}
      </ion-label>
      <ion-select
        [(ngModel)]="selectedStores"
        (ngModelChange)="onStoreModelChange($event)"
        (ionChange)="onStoreSelected($event)"
        interface="popover"
        placeholder="Select a store"
        class="store-selector"
      >
        <ion-select-option 
          *ngFor="let store of allStores" 
          [value]="store"
        >
          {{ store.partnerName }}
        </ion-select-option>
      </ion-select>
    </div>
  </div>
</div>

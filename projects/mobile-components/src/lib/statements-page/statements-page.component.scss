/* Modern Statement History Styles */

/* Main Container */
.statements-page {
  width: 100%;
  height: 100%;
  padding: 0;
}

/* Statement History Section */
.statements-section {
  position: relative;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 16px;
  
  @media (min-width: 768px) {
    padding: 24px;
  }
}

/* Page Title Container */
.page-title-container {
  background: white;
  padding: 24px 20px 16px;
  border-radius: 24px 24px 0 0;
  box-shadow: 0 -4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  animation: slideDown 0.5s ease-out;
  flex-shrink: 0;
}

.page-title {
  h1 {
    font-size: 24px;
    font-weight: 600;
    color: #212121;
    margin: 0;
    text-align: center;
  }
}

/* Statements Container */
.statements-container {
  background: white;
  border-radius: 0 0 24px 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  flex: 1;

  ion-spinner {
    margin-bottom: 1rem;
    --color: var(--ion-color-primary, #FF6B35);
    width: 48px;
    height: 48px;
  }

  p {
    color: #666;
    font-size: 16px;
    margin: 0;
  }
}

/* Empty State */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  flex: 1;

  .empty-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 2rem;

    ion-icon {
      font-size: 40px;
      color: #999;
    }
  }

  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0 0 0.5rem 0;
  }

  p {
    color: #666;
    font-size: 16px;
    margin: 0;
    line-height: 1.5;
  }
}

/* Statements Table */
.statements-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Table Header */
.table-header {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 16px;
  padding: 16px 24px;
  background: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
  font-weight: 600;
  font-size: 14px;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.header-cell {
  display: flex;
  align-items: center;
  
  &.date-col {
    justify-content: flex-start;
  }
  
  &.description-col {
    justify-content: flex-start;
  }
  
  &.view-col {
    justify-content: center;
  }
}

/* Table Body */
.table-body {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #ccc transparent;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #999;
  }
}

/* Statement Row */
.statement-row {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(10px);
  
  &.slide-in {
    animation: slideInUp 0.4s ease-out forwards;
  }
  
  &:hover {
    background: #f8f9fa;
    transform: translateX(2px);
  }
  
  &:last-child {
    border-bottom: none;
  }
}

.table-cell {
  display: flex;
  align-items: center;
  
  &.date-col {
    justify-content: flex-start;
  }
  
  &.description-col {
    justify-content: flex-start;
  }
  
  &.view-col {
    justify-content: center;
  }
}

/* Date Content */
.date-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.statement-type-icon {
  font-size: 18px;
  color: var(--ion-color-primary, #FF6B35);
}

.date-text {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

/* Description Content */
.description-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.description-text {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.statement-type {
  font-size: 12px;
  color: #666;
  background: #e3f2fd;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  width: fit-content;
}

/* View Button */
.view-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: var(--ion-color-primary, #FF6B35);
  color: white;
  border: none;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  
  ion-icon {
    font-size: 16px;
  }
  
  &:hover {
    background: var(--ion-color-primary-shade, #e85d2f);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
  }
  
  &:active {
    transform: translateY(0);
  }
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  flex-shrink: 0;
  gap: 16px;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 4px;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  
  ion-icon {
    font-size: 16px;
  }
  
  &:hover:not(.disabled) {
    background: #e9ecef;
    border-color: #adb5bd;
  }
  
  &.active {
    background: var(--ion-color-primary, #FF6B35);
    border-color: var(--ion-color-primary, #FF6B35);
    color: white;
  }
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &.page-number {
    font-weight: 500;
  }
}

.pagination-info {
  display: flex;
  align-items: center;
  gap: 16px;
  font-size: 14px;
  color: #495057;
}

.items-info {
  color: #6c757d;
  font-size: 13px;
  white-space: nowrap;
}

.items-per-page {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-select {
  padding: 4px 8px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: white;
  font-size: 14px;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: var(--ion-color-primary, #FF6B35);
    box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.2);
  }
}

.per-page-text {
  color: #6c757d;
  white-space: nowrap;
}

/* Animations */
@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-title h1 {
    font-size: 20px;
  }
  
  .table-header,
  .statement-row {
    grid-template-columns: 1fr 1.5fr auto;
    gap: 12px;
    padding: 12px 16px;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .pagination {
    justify-content: center;
  }
  
  .pagination-info {
    flex-direction: column;
    align-items: center;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .statements-section {
    padding: 12px;
  }
  
  .table-header {
    display: none;
  }
  
  .statement-row {
    grid-template-columns: 1fr;
    gap: 8px;
    padding: 16px;
    background: white;
    margin: 8px 0;
    border-radius: 12px;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
  
  .table-cell {
    justify-content: flex-start !important;
  }
  
  .description-content {
    order: -1;
  }
  
  .view-button {
    width: 100%;
    justify-content: center;
  }
  
  .date-content {
    font-size: 12px;
  }
  
  .pagination-info {
    flex-direction: column;
    gap: 8px;
  }
  
  .items-per-page {
    justify-content: center;
  }
}
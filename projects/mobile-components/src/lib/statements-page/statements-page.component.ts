import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ComponentsModule } from '../components.module';

export interface StatementItem {
  id: string;
  date: string;
  description: string;
  period?: string;
  type: 'monthly' | 'adhoc';
  downloadUrl?: string;
}

export interface StatementsProfile {
  givenNames?: string;
  surname?: string;
  newMembershipNumber?: string;
  membershipNumber?: string;
  currentBalance?: number;
}

@Component({
  selector: 'lib-statements-page',
  templateUrl: './statements-page.component.html',
  styleUrls: ['./statements-page.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ComponentsModule
  ]
})
export class StatementsPageComponent implements OnInit, OnDestroy {
  @Input() profile?: StatementsProfile;
  @Input() memberService?: any;
  @Input() lssConfig?: any;
  @Input() apiBaseUrl?: string;
  @Input() initialStatements?: StatementItem[];
  @Input() itemsPerPageOptions: number[] = [10, 25, 50];

  @Output() statementLoaded = new EventEmitter<StatementItem[]>();
  @Output() statementSelected = new EventEmitter<StatementItem>();
  @Output() statementViewed = new EventEmitter<StatementItem>();
  @Output() pageChanged = new EventEmitter<{page: number, itemsPerPage: number}>();

  statements: StatementItem[] = [];
  currentPage = 1;
  itemsPerPage = 10;
  totalItems = 0;
  paginatedStatements: StatementItem[] = [];
  loading = false;

  constructor() {}

  ngOnInit() {
    console.log('StatementsPageComponent initialized');
    if (this.initialStatements && this.initialStatements.length > 0) {
      this.statements = this.initialStatements;
      this.totalItems = this.statements.length;
      this.updatePagination();
    } else {
      this.loadStatements();
    }
  }

  ngOnDestroy() {
    // Clean up subscriptions if any
  }

  /**
   * Load statements from API or mock data
   */
  loadStatements(): void {
    this.loading = true;
    
    // If memberService is available, try to use it
    if (this.memberService && this.memberService.getStatements) {
      this.memberService.getStatements().subscribe({
        next: (statements: StatementItem[]) => {
          this.statements = statements;
          this.totalItems = this.statements.length;
          this.updatePagination();
          this.loading = false;
          this.statementLoaded.emit(this.statements);
        },
        error: (error: any) => {
          console.error('Error loading statements:', error);
          this.loadMockStatements();
        }
      });
    } else {
      // Fall back to mock data
      this.loadMockStatements();
    }
  }

  /**
   * Load mock statements data
   */
  private loadMockStatements(): void {
    // Mock data - replace with actual API call
    setTimeout(() => {
      this.statements = [
        {
          id: '1',
          date: '31/01/2024',
          description: 'January 2024',
          type: 'monthly'
        },
        {
          id: '2',
          date: '31/12/2023',
          description: 'December 2023',
          type: 'monthly'
        },
        {
          id: '3',
          date: '30/11/2023',
          description: 'November 2023',
          type: 'monthly'
        },
        {
          id: '4',
          date: '31/10/2023',
          description: 'October 2023',
          type: 'monthly'
        },
        {
          id: '5',
          date: '30/09/2023',
          description: 'September 2023',
          type: 'monthly'
        },
        {
          id: '6',
          date: '31/08/2023',
          description: 'August 2023',
          type: 'monthly'
        },
        {
          id: '7',
          date: '31/07/2023',
          description: 'July 2023',
          type: 'monthly'
        },
        {
          id: '8',
          date: '15/07/2023',
          description: 'Adhoc Statement - Special Report',
          type: 'adhoc'
        }
      ];
      
      this.totalItems = this.statements.length;
      this.updatePagination();
      this.loading = false;
      this.statementLoaded.emit(this.statements);
    }, 800);
  }

  /**
   * Update pagination
   */
  updatePagination(): void {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    const endIndex = startIndex + this.itemsPerPage;
    this.paginatedStatements = this.statements.slice(startIndex, endIndex);
  }

  /**
   * Go to specific page
   */
  goToPage(page: number): void {
    if (page >= 1 && page <= this.getTotalPages()) {
      this.currentPage = page;
      this.updatePagination();
      this.pageChanged.emit({
        page: this.currentPage,
        itemsPerPage: this.itemsPerPage
      });
    }
  }

  /**
   * Handle items per page change
   */
  onItemsPerPageChange(): void {
    this.currentPage = 1; // Reset to first page
    this.updatePagination();
    this.pageChanged.emit({
      page: this.currentPage,
      itemsPerPage: this.itemsPerPage
    });
  }

  /**
   * Get total pages
   */
  getTotalPages(): number {
    return Math.ceil(this.totalItems / this.itemsPerPage);
  }

  /**
   * Get page numbers for pagination
   */
  getPageNumbers(): number[] {
    const totalPages = this.getTotalPages();
    const pages: number[] = [];
    
    // Show a maximum of 5 page numbers
    const maxPages = 5;
    let startPage = Math.max(1, this.currentPage - Math.floor(maxPages / 2));
    let endPage = Math.min(totalPages, startPage + maxPages - 1);
    
    // Adjust start page if we're near the end
    if (endPage - startPage < maxPages - 1) {
      startPage = Math.max(1, endPage - maxPages + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    
    return pages;
  }

  /**
   * View statement
   */
  viewStatement(statement: StatementItem): void {
    console.log('Viewing statement:', statement);
    this.statementViewed.emit(statement);
    
    // If statement has a download URL, open it
    if (statement.downloadUrl) {
      window.open(statement.downloadUrl, '_blank');
    } else {
      // Emit event for parent component to handle
      this.statementSelected.emit(statement);
    }
  }

  /**
   * Get statement icon based on type
   */
  getStatementIcon(statement: StatementItem): string {
    return statement.type === 'adhoc' ? 'document-text-outline' : 'calendar-outline';
  }

  /**
   * Get statement type display name
   */
  getStatementTypeDisplay(statement: StatementItem): string {
    return statement.type === 'adhoc' ? 'Adhoc Statement' : 'Monthly Statement';
  }

  /**
   * Check if pagination should be shown
   */
  shouldShowPagination(): boolean {
    return this.getTotalPages() > 1;
  }

  /**
   * Check if we're on the first page
   */
  isFirstPage(): boolean {
    return this.currentPage === 1;
  }

  /**
   * Check if we're on the last page
   */
  isLastPage(): boolean {
    return this.currentPage === this.getTotalPages();
  }

  /**
   * Get items info text
   */
  getItemsInfo(): string {
    const start = (this.currentPage - 1) * this.itemsPerPage + 1;
    const end = Math.min(this.currentPage * this.itemsPerPage, this.totalItems);
    return `Showing ${start}-${end} of ${this.totalItems} statements`;
  }
}
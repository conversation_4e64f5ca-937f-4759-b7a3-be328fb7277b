<!-- Statements Page Component -->
<div class="statements-page">
  <!-- Statement History Section -->
  <div class="statements-section">
    <!-- Page Title -->
    <div class="page-title-container">
      <div class="page-title">
        <h1>Statement History</h1>
      </div>
    </div>
    
    <!-- Statements Container -->
    <div class="statements-container">
      <!-- Loading State -->
      <div class="loading-state" *ngIf="loading">
        <ion-spinner name="crescent"></ion-spinner>
        <p>Loading statements...</p>
      </div>

      <!-- Empty State -->
      <div class="empty-state" *ngIf="statements.length === 0 && !loading">
        <div class="empty-icon">
          <ion-icon name="document-text-outline"></ion-icon>
        </div>
        <h3>No statements found</h3>
        <p>Your statement history will appear here once available</p>
      </div>

      <!-- Statements Table -->
      <div class="statements-table" *ngIf="statements.length > 0 && !loading">
        <!-- Table Header -->
        <div class="table-header">
          <div class="header-cell date-col">Statement Date</div>
          <div class="header-cell description-col">Description</div>
          <div class="header-cell view-col">View</div>
        </div>

        <!-- Table Body -->
        <div class="table-body">
          <div 
            class="statement-row"
            [class.slide-in]="true"
            [style.animation-delay.ms]="i * 50"
            *ngFor="let statement of paginatedStatements; let i = index"
            (click)="viewStatement(statement)">
            
            <div class="table-cell date-col">
              <div class="date-content">
                <ion-icon [name]="getStatementIcon(statement)" class="statement-type-icon"></ion-icon>
                <span class="date-text">{{ statement.date }}</span>
              </div>
            </div>
            
            <div class="table-cell description-col">
              <div class="description-content">
                <span class="description-text">{{ statement.description }}</span>
                <span class="statement-type" *ngIf="statement.type === 'adhoc'">Adhoc</span>
              </div>
            </div>
            
            <div class="table-cell view-col">
              <button class="view-button" (click)="viewStatement(statement); $event.stopPropagation()">
                <ion-icon name="eye-outline"></ion-icon>
                View Statement
              </button>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div class="pagination-container" *ngIf="shouldShowPagination()">
          <div class="pagination">
            <!-- First page -->
            <button 
              class="pagination-btn"
              [class.disabled]="isFirstPage()"
              (click)="goToPage(1)"
              [disabled]="isFirstPage()"
              title="First page">
              <ion-icon name="chevron-back-outline"></ion-icon>
              <ion-icon name="chevron-back-outline"></ion-icon>
            </button>
            
            <!-- Previous page -->
            <button 
              class="pagination-btn"
              [class.disabled]="isFirstPage()"
              (click)="goToPage(currentPage - 1)"
              [disabled]="isFirstPage()"
              title="Previous page">
              <ion-icon name="chevron-back-outline"></ion-icon>
            </button>
            
            <!-- Page numbers -->
            <button 
              class="pagination-btn page-number"
              [class.active]="page === currentPage"
              *ngFor="let page of getPageNumbers()"
              (click)="goToPage(page)"
              [title]="'Go to page ' + page">
              {{ page }}
            </button>
            
            <!-- Next page -->
            <button 
              class="pagination-btn"
              [class.disabled]="isLastPage()"
              (click)="goToPage(currentPage + 1)"
              [disabled]="isLastPage()"
              title="Next page">
              <ion-icon name="chevron-forward-outline"></ion-icon>
            </button>
            
            <!-- Last page -->
            <button 
              class="pagination-btn"
              [class.disabled]="isLastPage()"
              (click)="goToPage(getTotalPages())"
              [disabled]="isLastPage()"
              title="Last page">
              <ion-icon name="chevron-forward-outline"></ion-icon>
              <ion-icon name="chevron-forward-outline"></ion-icon>
            </button>
          </div>
          
          <!-- Items per page selector and info -->
          <div class="pagination-info">
            <div class="items-info">
              {{ getItemsInfo() }}
            </div>
            <div class="items-per-page">
              <select [(ngModel)]="itemsPerPage" (change)="onItemsPerPageChange()" class="page-select">
                <option [value]="option" *ngFor="let option of itemsPerPageOptions">{{ option }}</option>
              </select>
              <span class="per-page-text">per page</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
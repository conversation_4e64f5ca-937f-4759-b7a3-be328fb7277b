import { Injectable } from '@angular/core';
import { Route } from '@angular/router';
import { LssConfig } from 'lp-client-api';
import { DynamicPageComponent } from '../pages/dynamic/page/dynamic-page.component';

interface PageConfig {
  path?: string;
  secure: boolean;
  class: string;
  components: any[];
}

@Injectable({
  providedIn: 'root'
})
export class DynamicRoutesService {
  constructor(private lssConfig: LssConfig) {}

  getRoutes(secure: boolean = false): Route[] {
    const routes: Route[] = [];
console.log('------------')
console.log(this.lssConfig)

console.log('------------')

    Object.entries(this.lssConfig.pages as Record<string, PageConfig>).forEach(([key, config]) => {
      if (config.secure === secure) {
        routes.push({
          path: config.path || key,
          component: DynamicPageComponent,
          data: { pageKey: key }
        });
      }
    });

    return routes;
  }

  getPublicRoutes(): Route[] {
    return this.getRoutes(false);
  }

  getSecureRoutes(): Route[] {
    return this.getRoutes(true);
  }
}

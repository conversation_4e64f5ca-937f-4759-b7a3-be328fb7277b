import { Injectable, Injector } from '@angular/core';
import { LssConfig } from 'lp-client-api';

// Import the GamesConfig from game.ts
import type { GamesConfig, GameConfig, GameCategory } from 'lp-client-api/lib/types/game';

// Define interfaces for LssConfig games structure
interface LssGameConfig {
  name: string;
  backgroundImage: string;
  config: any;
}

interface LssGameCategory {
  name: string;
  backgroundImage: string;
  games: { [key: string]: LssGameConfig };
}

interface LssGamesConfig {
  globalConfig: {
    version: string;
    defaultLanguage: string;
    saveUserProgress: boolean;
    enableSound: boolean;
    enableMusic: boolean;
    themes: {
      defaultBackgroundImage: string;
    };
  };
  categories: {
    [key: string]: LssGameCategory;
  };
}

@Injectable({
  providedIn: 'root',
})
export class GameConfigService {
  private gameConfig?: GamesConfig;
  protected lssConfig: LssConfig;
  private isConfigured = false;

  // Map of game IDs to their configuration IDs
  private readonly gameIdMap: {
    [key: string]: { categoryId: string; gameId: string };
  } = {
    'wheel-spin': { categoryId: 'business', gameId: 'spinthewheel' },
    'location-based-photo': { categoryId: 'business', gameId: 'locationBased' },
  };


  constructor(injector: Injector) {
    this.lssConfig = injector.get(LssConfig);

    console.log('LssConfig:', this.lssConfig);
    console.log('Games Config:', this.lssConfig.games);

    // Try to initialize with the injected LssConfig, but don't throw an error if it fails
    if (this.lssConfig.games && this.isValidLssGamesConfig(this.lssConfig.games)) {
      // Convert LssGamesConfig to GamesConfig
      this.gameConfig = this.convertLssGamesConfigToGamesConfig(this.lssConfig.games);
      this.isConfigured = true;
    } else {
      console.warn('Games configuration not available in LssConfig. Creating default configuration.');
      // Create a default games configuration
      const defaultGamesConfig: LssGamesConfig = {
        globalConfig: {
          version: '1.0.0',
          defaultLanguage: 'en',
          saveUserProgress: true,
          enableSound: true,
          enableMusic: true,
          themes: {
            defaultBackgroundImage: 'assets/images/games/background.jpg'
          }
        },
        categories: {
          puzzle: {
            name: 'Puzzle',
            backgroundImage: 'assets/images/games/categories/puzzles.jpeg',
            games: {
              '2048': {
                name: '2048',
                backgroundImage: 'assets/images/games/2048.jpeg',
                config: {}
              },
              'crossword': {
                name: 'Crossword',
                backgroundImage: 'assets/images/games/crossword.jpeg',
                config: {}
              }
            }
          },
          arcade: {
            name: 'Arcade',
            backgroundImage: 'assets/images/games/categories/arcade.jpeg',
            games: {
              'snake': {
                name: 'Snake',
                backgroundImage: 'assets/images/games/snake.jpg',
                config: {}
              }
            }
          }
        }
      };

      // Set the default configuration
      this.gameConfig = this.convertLssGamesConfigToGamesConfig(defaultGamesConfig);
      this.isConfigured = true;

      // Also update the LssConfig with the default games configuration
      if (this.lssConfig) {
        this.lssConfig.games = defaultGamesConfig;
      }
    }
  }

  /**
   * Convert LssGamesConfig to GamesConfig
   * @param lssConfig The LssGamesConfig to convert
   * @returns GamesConfig
   */
  private convertLssGamesConfigToGamesConfig(lssConfig: LssGamesConfig): GamesConfig {
    // Create a default globalConfig with required properties
    const globalConfig = {
      defaultAttempts: 3,
      defaultCooldown: 0,
      defaultPoints: 100,
      // Optionally keep original properties as additional properties
      ...lssConfig.globalConfig
    };

    // Convert categories
    const categories: { [key: string]: GameCategory } = {};

    Object.entries(lssConfig.categories).forEach(([key, lssCategory]) => {
      const games: { [key: string]: GameConfig } = {};

      // Convert each game
      Object.entries(lssCategory.games).forEach(([gameKey, lssGame]) => {
        const typedLssGame = lssGame as LssGameConfig;
        games[gameKey] = {
          id: gameKey,
          name: typedLssGame.name,
          description: '',
          icon: '',
          backgroundImage: typedLssGame.backgroundImage || '',
          enabled: true,
          config: typedLssGame.config
        } as GameConfig;
      });

      // Create the category
      const typedLssCategory = lssCategory as LssGameCategory;
      categories[key] = {
        id: key,
        name: typedLssCategory.name,
        description: '',
        icon: '',
        games
      } as GameCategory;
    });

    return {
      globalConfig,
      categories
    };
  }

  /**
   * Set the games configuration manually from LssGamesConfig
   * @param config The LssGamesConfig from LssConfig
   * @returns true if the configuration was set successfully, false otherwise
   */
  setGamesConfig(config: LssGamesConfig): boolean {
    if (!this.isValidLssGamesConfig(config)) {
      console.error('Invalid games configuration structure provided to setGamesConfig');
      return false;
    }

    // Convert LssGamesConfig to GamesConfig
    this.gameConfig = this.convertLssGamesConfigToGamesConfig(config);
    this.isConfigured = true;
    console.log('Games configuration set successfully:', this.gameConfig);
    return true;
  }

  /**
   * Check if the provided config is a valid LssGamesConfig
   * @param config The config to check
   * @returns true if the config is a valid LssGamesConfig
   */
  private isValidLssGamesConfig(config: any): config is LssGamesConfig {
    return (
      config &&
      typeof config === 'object' &&
      'globalConfig' in config &&
      'categories' in config &&
      typeof config.globalConfig === 'object' &&
      typeof config.categories === 'object'
    );
  }

  /**
   * Check if the provided config is a valid GamesConfig
   * @param config The config to check
   * @returns true if the config is a valid GamesConfig
   */
  private isValidGamesConfig(config: any): config is GamesConfig {
    return (
      config &&
      typeof config === 'object' &&
      'globalConfig' in config &&
      'categories' in config &&
      typeof config.globalConfig === 'object' &&
      typeof config.categories === 'object' &&
      'defaultAttempts' in config.globalConfig &&
      'defaultCooldown' in config.globalConfig &&
      'defaultPoints' in config.globalConfig
    );
  }

  /**
   * Check if the service is properly configured with games data
   * @returns true if configured, false otherwise
   */
  isReady(): boolean {
    return this.isConfigured && !!this.gameConfig;
  }

  /**
   * Ensure the service is configured before accessing game data
   * @throws Error if the service is not configured
   */
  private ensureConfigured(): void {
    if (!this.isConfigured || !this.gameConfig) {
      throw new Error('GameConfigService is not configured. Call setGamesConfig() first.');
    }
  }

  getGlobalConfig() {
    this.ensureConfigured();
    return this.gameConfig!.globalConfig;
  }

  getAllCategories(): { [key: string]: GameCategory } {
    this.ensureConfigured();
    return this.gameConfig!.categories;
  }

  getCategory(categoryId: string): GameCategory | undefined {
    this.ensureConfigured();
    return this.gameConfig!.categories[categoryId];
  }

  getGameConfig(categoryId: string, gameId: string): GameConfig | undefined {
    this.ensureConfigured();
    return this.gameConfig!.categories[categoryId]?.games[gameId];
  }

  getAllGames(): { categoryId: string; gameId: string; config: GameConfig }[] {
    this.ensureConfigured();
    const allGames: {
      categoryId: string;
      gameId: string;
      config: GameConfig;
    }[] = [];

    Object.entries(this.gameConfig!.categories || {}).forEach(
      ([categoryId, category]: [string, GameCategory]) => {
        Object.entries(category.games || {}).forEach(
          ([gameId, gameConfig]: [string, GameConfig]) => {
            // Add both the original ID and the kebab-case version
            const kebabCaseId = this.toKebabCase(gameId);
            allGames.push({
              categoryId,
              gameId: kebabCaseId,
              config: gameConfig,
            });
          }
        );
      }
    );

    return allGames;
  }

  getGameById(
    gameId: string
  ): { categoryId: string; config: GameConfig } | undefined {
    this.ensureConfigured();

    // First, check if we have a direct mapping for this game ID
    const mapping = this.gameIdMap[gameId];
    if (mapping) {
      const config = this.getGameConfig(mapping.categoryId, mapping.gameId);
      if (config) {
        return {
          categoryId: mapping.categoryId,
          config,
        };
      }
    }

    // If no direct mapping, try to find the game in all categories
    for (const [categoryId, category] of Object.entries(
      this.gameConfig!.categories
    )) {
      for (const [configGameId, gameConfig] of Object.entries(category.games)) {
        if (this.toKebabCase(configGameId) === gameId.toLowerCase()) {
          return {
            categoryId,
            config: gameConfig,
          };
        }
      }
    }

    return undefined;
  }

  private toKebabCase(str: string): string {
    return str
      .replace(/([a-z])([A-Z])/g, '$1-$2')
      .replace(/[\s_]+/g, '-')
      .toLowerCase();
  }
}

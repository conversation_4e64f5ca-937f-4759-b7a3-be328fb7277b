import {
  Component,
  Inject,
  Injector,
  LOCALE_ID,
  On<PERSON><PERSON>roy,
  Type,
  ChangeDetectorRef,
  Optional,
} from '@angular/core';
import {
  AnimationController,
  ModalController,
  ToastButton,
  ToastController,
} from '@ionic/angular';
import { Subscription } from 'rxjs';
import { LoadingComponent } from './loading/loading.component';

@Component({
  template: '',
  providers: [
    { provide: 'componentProperties', useValue: {} }
  ]
})
export abstract class AbstractComponent implements OnDestroy {
  @Inject(LOCALE_ID) public _locale?: string;
  private _modalCtrl: ModalController;
  private animationCtrl: AnimationController;
  private _loading: boolean = false;
  private _subscriptions = new Subscription();
  private _viewSubscriptions: Subscription[] = [];
  private _globalSubscription: Subscription[] = [];
  private _changeDetectorRef: ChangeDetectorRef;
  private _activePage = false;
  private _toastController: ToastController;
  constructor(private injector: Injector) {
    this._modalCtrl = injector.get<ModalController>(
      ModalController as Type<ModalController>
    );

    this.animationCtrl = injector.get<AnimationController>(
      AnimationController as Type<AnimationController>
    );
    this._changeDetectorRef = injector.get<ChangeDetectorRef>(
      ChangeDetectorRef as Type<ChangeDetectorRef>
    );
    this._toastController = injector.get<ToastController>(
      ToastController as Type<ToastController>
    );
  }

  ngOnDestroy(): void {
    this._globalSubscription.forEach((sub) => sub.unsubscribe());
    this._viewSubscriptions.forEach((sub) => sub.unsubscribe());
    this._viewSubscriptions = [];
    this._globalSubscription = [];
    this._subscriptions.unsubscribe();
  }
  ionViewDidEnter(): void {
    this._activePage = true;
  }
  ionViewDidLeave(): void {
    this._subscriptions.unsubscribe();
    this._viewSubscriptions.forEach((sub) => sub.unsubscribe());
    this._viewSubscriptions = [];
    this._activePage = false;
  }
  get activePage(): boolean {
    return this._activePage;
  }
  /**
   * This is a global subscripion that will remain active and should be added on an nginit function.
   * This will not stop the registration on an exit of the page only on a destroy.
   * @param sub
   */
  addGlobalSubscription(sub: Subscription) {
    this._globalSubscription.push(sub);
  }
  /**
   * This must be added per view so use the ionViewDidEnter(); or ionViewWillEnter()
   * @param sub
   */
  addViewSubscription(sub: Subscription) {
    this._viewSubscriptions.push(sub);
  }

  get locale(): string {
    return this._locale == undefined ? 'en' : this._locale;
  }

  get loading(): boolean {
    return this._loading;
  }

  set loading(loading: boolean) {
    this._loading = loading;
  }

  /**
   * @deprecated The method should not be used
   */
  get subscriptions(): Subscription {
    return this._subscriptions;
  }
  /**
   * @deprecated The method should not be used
   */
  set subscriptions(subscriptions: Subscription) {
    this._subscriptions = subscriptions;
  }

  async showLoadingModal(message: string) {
    this._loading = true;
    
    const enterAnimation = (baseEl: any) => {
      const root = baseEl.shadowRoot;

      const backdropAnimation = this.animationCtrl
        .create()
        .addElement(root.querySelector('ion-backdrop')!)
        .fromTo('opacity', '0', 'var(--backdrop-opacity)');

      const wrapperAnimation = this.animationCtrl
        .create()
        .addElement(root.querySelector('.modal-wrapper')!)
        .keyframes([
          { offset: 0, opacity: '0', transform: 'scale(0)' },
          { offset: 1, opacity: '0.99', transform: 'scale(1)' },
        ]);

      return this.animationCtrl
        .create()
        .addElement(baseEl)
        .easing('ease-out')
        .duration(300)
        .addAnimation([backdropAnimation, wrapperAnimation]);
    };

    const leaveAnimation = (baseEl: any) => {
      const root = baseEl.shadowRoot;

      const backdropAnimation = this.animationCtrl
        .create()
        .addElement(root.querySelector('ion-backdrop')!)
        .fromTo('opacity', 'var(--backdrop-opacity)', '0');

      const wrapperAnimation = this.animationCtrl
        .create()
        .addElement(root.querySelector('.modal-wrapper')!)
        .keyframes([
          { offset: 0, opacity: '0.99', transform: 'scale(1)' },
          { offset: 1, opacity: '0', transform: 'scale(0)' },
        ]);

      return this.animationCtrl
        .create()
        .addElement(baseEl)
        .easing('ease-out')
        .duration(300)
        .addAnimation([backdropAnimation, wrapperAnimation]);
    };

    const modal = await this._modalCtrl.create({
      id: 'loading',
      component: LoadingComponent,
      componentProps: { message: message },
      enterAnimation,
      leaveAnimation,
      cssClass: 'loading-modal',
      backdropDismiss: false,
      showBackdrop: true,
      keyboardClose: false
    });
    
    // Add global CSS for the modal
    document.documentElement.style.setProperty('--loading-modal-z-index', '9999');
    document.documentElement.style.setProperty('--loading-modal-background', 'rgba(0,0,0,0.4)');
    
    return modal.present();
  }

  dismissLoadingModal() {
    this._modalCtrl
      .dismiss(null, '', 'loading')
      .then(() => {
        this._loading = false;
      })
      .catch(console.error);
  }

  detectChanges() {
    if (this.activePage) {
      this._changeDetectorRef.detectChanges();
    }
  }

  async presentToast(opts: {
    message: string;
    duration?: number;
    position?: 'top' | 'middle' | 'bottom';
    color?:
      | 'danger'
      | 'dark'
      | 'light'
      | 'medium'
      | 'primary'
      | 'secondary'
      | 'success'
      | 'tertiary'
      | 'warning';
    icon?: string;
    mode?: 'ios' | 'md';
  }): Promise<HTMLIonToastElement> {
    opts.color = opts.color || 'success';
    opts.duration = opts.duration || 3000;
    opts.icon = opts.icon || 'md';
    const toast = await this._toastController.create(opts);
    await toast.present();
    return toast;
  }

  changeThemeColor(color: string) {
    console.log('theme color', color);
    const htmlEl: any = document.querySelector('html');

    htmlEl.style.setProperty('--ion-color-primary', color);
  }
}

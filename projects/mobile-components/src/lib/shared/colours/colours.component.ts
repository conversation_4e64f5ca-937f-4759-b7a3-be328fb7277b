import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ColorPickerDirective } from 'ngx-color-picker';

@Component({
  selector: 'lib-colours',
  templateUrl: './colours.component.html',
  styleUrls: ['./colours.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule, IonicModule, ColorPickerDirective],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ColoursComponent implements OnInit {
  colors = [
    'primary',
    'secondary',
    'tertiary',
    'success',
    'warning',
    'danger',
    'medium',
    'base',
    'light',
  ];
  colors_types = ['', '-contrast', '-shade', '-tint'];
  primary: string = '#fff';
  primaryContrast: string = '#fff';
  primaryShade: string = '#fff';
  primaryTint: string = '#fff';
  secondary: string = '#fff';
  secondaryContrast: string = '#fff';
  secondaryShade: string = '#fff';
  secondaryTint: string = '#fff';
  tertiary: string = '#fff';
  tertiaryContrast: string = '#fff';
  tertiaryShade: string = '#fff';
  tertiaryTint: string = '#fff';
  success: string = '#fff';
  successContrast: string = '#fff';
  successShade: string = '#fff';
  successTint: string = '#fff';
  warning: string = '#fff';
  warningContrast: string = '#fff';
  warningShade: string = '#fff';
  warningTint: string = '#fff';
  danger: string = '#fff';
  dangerContrast: string = '#fff';
  dangerShade: string = '#fff';
  dangerTint: string = '#fff';
  medium: string = '#fff';
  mediumContrast: string = '#fff';
  mediumShade: string = '#fff';
  mediumTint: string = '#fff';
  base: string = '#fff';
  baseContrast: string = '#fff';
  baseShade: string = '#fff';
  baseTint: string = '#fff';
  light: string = '#fff';
  lightContrast: string = '#fff';
  lightShade: string = '#fff';
  lightTint: string = '#fff';

  ngOnInit(): void {
    //  this.primary =  this.environment.lssConfig.theme.colours.primary
    let res = [];
    for (let i = 0; i < this.colors.length; i++) {
      let col = this.colors[i];
      for (let j = 0; j < this.colors_types.length; j++) {
        let col_t = this.colors_types[j]
          .replace('-', '')
          .charAt(0)
          .toUpperCase();
        let col_te = this.colors_types[j].slice(2);
        let payload =
          this.colors_types[j] == 'contrast'
            ? `.${col + col_t + col_te} { background-color: var(--ion-color-${
                col + col_t + col_te
              }); color: var(--ion-color-${col + col_t + col_te})}`
            : `.${col + col_t + col_te} { background-color: var(--ion-color-${
                col + col_t + col_te
              }); color: var(--ion-color-${col + col_t + col_te}-contrast)}`;
        res.push(payload);
      }
    }
  }
  changeThemeColorContrast(color: any, base: any, extra: string) {
    const htmlEl: any = document.querySelector('html');
    this.base = color;
    let str = '--ion-color-' + base + '-contrast';

    htmlEl.style.setProperty(str, color);
  }

  changeThemeColor(color: any, base: any, extra: string) {
    const htmlEl: any = document.querySelector('html');
    this.base = color;
    let str = '--ion-color-' + base;
    let str_drk = '--ion-color-' + base + '-shade';
    let str_lght = '--ion-color-' + base + '-tint';

    htmlEl.style.setProperty(str, color);
    let newDark = this.LightenDarkenColorHex(color, 50);
    let newLight = this.LightenDarkenColorHex(color, -50);
    htmlEl.style.setProperty(str_drk, newDark);
    htmlEl.style.setProperty(str_lght, newLight);
    // htmlEl.style.setProperty('--ion-color-primary', color)
  }

  LightenDarkenColorHex(color: string, amount: number) {
    return `#${color
      .replace(/^#/, '')
      .replace(/../g, (color: string) =>
        `0${Math.min(255, Math.max(0, parseInt(color, 16) + amount)).toString(
          16
        )}`.substr(-2)
      )}`;
  }
}

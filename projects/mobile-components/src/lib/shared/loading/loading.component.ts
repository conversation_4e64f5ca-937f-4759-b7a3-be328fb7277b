import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-loading',
  template: `
    <div class="loading-container">
      <ion-spinner name="crescent" color="primary" class="spinner-large"></ion-spinner>
      <div class="loading-message" *ngIf="message">{{ message }}</div>
      <div class="loader">Loading...</div>
    </div>
  `,
  styles: [`
    :host {
      position: relative;
      z-index: 9999;
      display: block;
      width: 100%;
      height: 100%;
    }
    .loading-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 20px;
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      width: 80%;
      max-width: 300px;
      height: auto;
      min-height: 150px;
      margin: 0 auto;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
    .spinner-large {
      font-size: 24px;
      width: 50px;
      height: 50px;
      margin-bottom: 16px;
    }
    .loading-message {
      margin-top: 15px;
      text-align: center;
      font-weight: bold;
      font-size: 16px;
      color: var(--ion-color-dark, #000);
    }
    .loader {
      margin-top: 8px;
      font-size: 14px;
      color: var(--ion-color-medium, #666);
    }
  `],
  standalone: true,
  imports: [CommonModule, IonicModule]
})
export class LoadingComponent {
  @Input() message: string = '';
}

.program-management-content {
  --background: var(--ion-color-light);
  
  // Page Title Card
  .page-title-card {
    margin: 0 16px 20px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background: white;
    
    .page-header {
      text-align: center;
      padding: 8px 0;
      
      h1 {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 8px;
        color: var(--ion-color-primary);
      }
      
      p {
        font-size: 14px;
        color: var(--ion-color-medium);
        margin: 0;
        line-height: 1.4;
      }
    }
  }
  
  // Summary Container - White card containing the three stat cards
  .summary-container {
    margin: 0 16px 20px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background: white;
    animation: slideUp 0.6s ease-out;
    
    .summary-content {
      padding: 16px;
      display: flex;
      gap: 12px;
      justify-content: space-between;
    }
  }
  
  // Individual Summary Cards
  .summary-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 16px 12px;
    border-radius: 12px;
    background: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    min-height: 100px;
    
    &:hover {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      transform: translateY(-4px);
      box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
      
      .card-icon {
        background: rgba(255, 255, 255, 0.9);
        
        ion-icon {
          color: #667eea;
        }
      }
      
      .card-value,
      .card-label {
        color: white;
      }
    }
    
    .card-icon {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      background: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
      
      ion-icon {
        font-size: 24px;
        color: var(--ion-color-primary);
        transition: all 0.3s ease;
      }
    }
    
    .card-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      text-align: center;
    }
    
    .card-value {
      font-size: 24px;
      font-weight: 700;
      color: var(--ion-color-dark);
      margin: 0;
      transition: all 0.3s ease;
    }
    
    .card-label {
      font-size: 12px;
      font-weight: 500;
      color: var(--ion-color-medium);
      margin: 0;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      transition: all 0.3s ease;
    }
  }
  
  // Animation keyframes
  @keyframes slideUp {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  // Search and Filter Card
  .search-filter-card {
    margin: 0 16px 20px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background: white;
    
    ion-card-content {
      padding: 16px;
    }
    
    .search-header {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;
      
      .custom-searchbar {
        flex: 1;
        --background: #f8f9fa;
        --border-radius: 12px;
        --box-shadow: none;
        --placeholder-color: var(--ion-color-medium);
        --icon-color: var(--ion-color-medium);
        padding: 0;
        margin: 0;
      }
      
      .search-actions {
        display: flex;
        gap: 4px;
        
        ion-button {
          --padding-start: 8px;
          --padding-end: 8px;
          --color: var(--ion-color-medium);
          
          &:hover {
            --color: var(--ion-color-primary);
          }
        }
      }
    }
    
    .custom-searchbar {
      --background: #f8f9fa;
      --border-radius: 12px;
      --box-shadow: none;
      --placeholder-color: var(--ion-color-medium);
      --icon-color: var(--ion-color-medium);
      padding: 0;
      
      &.searchbar-has-value {
        --clear-button-color: var(--ion-color-primary);
      }
    }
    
    .category-filter {
      display: flex;
      gap: 8px;
      overflow-x: auto;
      scrollbar-width: none;
      padding-bottom: 4px;
      
      &::-webkit-scrollbar {
        display: none;
      }
      
      ion-chip {
        flex-shrink: 0;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8f9fa;
        color: var(--ion-color-medium);
        border: 1px solid transparent;
        --background: #f8f9fa;
        
        &.selected {
          background: var(--ion-color-primary);
          color: white;
          --background: var(--ion-color-primary);
          
          ion-icon {
            color: white;
          }
        }
        
        &:hover:not(.selected) {
          transform: translateY(-2px);
          border-color: var(--ion-color-primary-tint);
        }
      }
    }
  }
  
  // Programs Section Cards
  .programs-section-card {
    margin: 0 16px 20px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background: white;
    
    ion-card-header {
      padding: 16px 16px 0;
      background: white;
      
      .section-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;
        
        h2 {
          font-size: 18px;
          font-weight: 600;
          margin: 0;
          color: var(--ion-color-dark);
        }
        
        .count-badge {
          min-width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 14px;
          padding: 0 10px;
          font-size: 14px;
          font-weight: 600;
          background: var(--ion-color-primary);
          color: white;
          
          &.available {
            background: var(--ion-color-medium);
          }
        }
      }
    }
    
    ion-card-content {
      padding: 16px;
    }
  }
  
  .programs-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 16px;
    
    &.list-view {
      grid-template-columns: 1fr;
    }
    
    .program-item {
      background: #f8f9fa;
      border-radius: 12px;
      padding: 16px;
      transition: all 0.3s ease;
      border: 2px solid transparent;
      
      &.enrolled {
        background: linear-gradient(135deg, #f0f8ff 0%, #e6f2ff 100%);
        border-color: rgba(var(--ion-color-primary-rgb), 0.2);
        
        &.required {
          background: linear-gradient(135deg, #f0fff4 0%, #e6ffe6 100%);
          border-color: rgba(var(--ion-color-success-rgb), 0.3);
        }
      }
      
      &.available {
        background: #f8f9fa;
        
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          background: white;
        }
      }
      
      .program-header {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        margin-bottom: 12px;
        
        .program-icon-wrapper {
          width: 48px;
          height: 48px;
          border-radius: 12px;
          background: white;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        }
        
        .program-icon {
          font-size: 24px;
          color: var(--ion-color-primary);
        }
        
        .program-info {
          flex: 1;
          
          .program-name {
            font-size: 16px;
            font-weight: 600;
            margin: 0 0 6px;
            color: var(--ion-color-dark);
          }
          
          .program-badges {
            display: flex;
            gap: 6px;
            flex-wrap: wrap;
            
            .category-badge,
            .required-badge,
            .new-badge {
              display: inline-block;
              padding: 3px 8px;
              border-radius: 6px;
              font-size: 11px;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.5px;
            }
            
            .category-badge {
              background: rgba(var(--ion-color-medium-rgb), 0.1);
              color: var(--ion-color-medium-shade);
              
              &.rewards {
                background: rgba(var(--ion-color-warning-rgb), 0.1);
                color: var(--ion-color-warning-shade);
              }
            }
            
            .required-badge {
              background: rgba(var(--ion-color-success-rgb), 0.1);
              color: var(--ion-color-success);
            }
            
            .new-badge {
              background: rgba(var(--ion-color-tertiary-rgb), 0.1);
              color: var(--ion-color-tertiary);
            }
          }
        }
      }
      
      .program-content {
        .program-description {
          color: var(--ion-color-medium-shade);
          line-height: 1.6;
          margin: 0 0 12px;
          font-size: 14px;
        }
        
        .program-benefits {
          display: flex;
          gap: 0.5rem;
          flex-wrap: wrap;
          margin-bottom: 1rem;
          
          ion-chip {
            margin: 0;
            height: 28px;
            
            ion-icon {
              margin-right: 4px;
            }
          }
        }
        
        .program-actions {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-top: 1px solid var(--ion-color-light);
          padding-top: 0.75rem;
          margin-top: 0.75rem;
          gap: 8px;
          
          ion-button {
            margin: 0;
            text-transform: none;
            font-weight: 500;
            
            // Make Enter button more prominent for enrolled programs
            &[color="primary"] {
              --background: var(--ion-color-primary);
              --color: white;
              --border-radius: 8px;
              font-weight: 600;
              min-width: 80px;
              
              ion-icon {
                font-size: 1.1em;
              }
            }
          }
        }
      }
    }
  }
  
  // Empty and Loading State Cards
  .empty-state-card,
  .loading-state-card {
    margin: 0 16px 20px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background: white;
    
    ion-card-content {
      padding: 32px 16px;
    }
  }
  
  .empty-state {
    text-align: center;
    padding: 24px;
    
    .empty-icon {
      font-size: 48px;
      color: var(--ion-color-medium);
      margin-bottom: 16px;
    }
    
    h3 {
      font-size: 18px;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin: 0 0 8px;
    }
    
    p {
      font-size: 14px;
      color: var(--ion-color-medium);
      margin: 0;
    }
  }
  
  .loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 32px;
    
    ion-spinner {
      transform: scale(1.2);
      margin-bottom: 12px;
      --color: var(--ion-color-primary);
    }
    
    p {
      font-size: 14px;
      color: var(--ion-color-medium);
      margin: 0;
    }
  }
}

// Mobile Responsive
@media (max-width: 768px) {
  .program-management-content {
    .summary-container {
      margin: 0 12px 16px;
      
      .summary-content {
        padding: 12px;
        gap: 8px;
        flex-direction: row;
        overflow-x: auto;
        scrollbar-width: none;
        
        &::-webkit-scrollbar {
          display: none;
        }
      }
    }
    
    .summary-card {
      min-width: 100px;
      padding: 12px 8px;
      min-height: 90px;
      
      .card-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;
        
        ion-icon {
          font-size: 20px;
        }
      }
      
      .card-value {
        font-size: 18px;
      }
      
      .card-label {
        font-size: 10px;
      }
    }
    
    .programs-container {
      grid-template-columns: 1fr;
      
      &.list-view {
        .program-card {
          ion-card-content {
            .program-benefits {
              flex-direction: column;
              align-items: flex-start;
            }
          }
        }
      }
    }
  }
}

// Dark Mode Support
@media (prefers-color-scheme: dark) {
  .program-management-content {
    .program-card {
      &.enrolled {
        background: linear-gradient(135deg, var(--ion-color-dark) 0%, var(--ion-color-dark-shade) 100%);
      }
      
      &.available {
        background: var(--ion-color-dark);
      }
    }
  }
}
<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <!-- Header Section -->
  <div header>
    <lib-head-logo
      [names]="displayName"
      [membership]="userMembership"
      type="profile"
      [balance]="userBalance"
      [src]="lssConfig?.pages?.landing?.loggedinIcon || 'assets/images/logo.png'"
    />
  </div>

  <div class="program-management-content">
    <!-- Page Title -->
    <ion-card class="page-title-card">
      <ion-card-content>
        <div class="page-header">
          <h1>{{ title }}</h1>
          <p>{{ subtitle }}</p>
        </div>
      </ion-card-content>
    </ion-card>
    
    <!-- Summary Cards -->
    <ion-card class="summary-container" *ngIf="showSummaryCards">
      <ion-card-content class="summary-content">
        <div class="summary-card active-programs">
          <div class="card-icon">
            <ion-icon name="layers-outline"></ion-icon>
          </div>
          <div class="card-info">
            <span class="card-value">{{ enrollmentCount() }}</span>
            <span class="card-label">Active Programs</span>
          </div>
        </div>
        
        <div class="summary-card points-multiplier">
          <div class="card-icon">
            <ion-icon name="trending-up-outline"></ion-icon>
          </div>
          <div class="card-info">
            <span class="card-value">{{ totalPointsEarning() }}x</span>
            <span class="card-label">Points Multiplier</span>
          </div>
        </div>
        
        <div class="summary-card monthly-value">
          <div class="card-icon">
            <ion-icon name="card-outline"></ion-icon>
          </div>
          <div class="card-info">
            <span class="card-value">R {{ totalMonthlyFees() || 0 }}</span>
            <span class="card-label">Monthly Value</span>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Search and Filter Card -->
    <ion-card class="search-filter-card" *ngIf="showSearch || showCategoryFilter">
      <ion-card-content>
        <div class="search-header" *ngIf="showSearch">
          <!-- Search Bar -->
          <ion-searchbar
            class="custom-searchbar"
            [ngModel]="searchQuery()"
            (ngModelChange)="searchQuery.set($event)"
            placeholder="Search programs..."
            [showClearButton]="true"
            (ionClear)="clearSearch()"
            animated="true">
          </ion-searchbar>
          
          <!-- Action Buttons -->
          <div class="search-actions">
            <ion-button fill="clear" size="small" (click)="loadPrograms()">
              <ion-icon name="refresh-outline"></ion-icon>
            </ion-button>
            <ion-button fill="clear" size="small" (click)="toggleViewMode()">
              <ion-icon [name]="viewMode() === 'grid' ? 'list-outline' : 'grid-outline'"></ion-icon>
            </ion-button>
          </div>
        </div>

        <!-- Category Filter -->
        <div class="category-filter" *ngIf="showCategoryFilter">
          <ion-chip 
            *ngFor="let category of categories()"
            [class.selected]="selectedCategory() === category"
            (click)="selectCategory(category)">
            <ion-icon [name]="getCategoryIcon(category)"></ion-icon>
            <ion-label>{{ category | titlecase }}</ion-label>
          </ion-chip>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- My Enrolled Programs Section -->
    <ion-card class="programs-section-card">
      <ion-card-header>
        <div class="section-header">
          <h2>My Enrolled Programs</h2>
          <ion-badge class="count-badge">{{ enrollmentCount() }}</ion-badge>
        </div>
      </ion-card-header>
      <ion-card-content>
        <div class="programs-container" [class.list-view]="viewMode() === 'list'">
          <div 
            *ngFor="let program of filteredEnrolled()" 
            class="program-item enrolled"
            [class.required]="program.isRequired">
            
            <div class="program-header">
              <div class="program-icon-wrapper">
                <ion-icon [name]="program.icon" class="program-icon"></ion-icon>
              </div>
              <div class="program-info">
                <h3 class="program-name">{{ program.name }}</h3>
                <div class="program-badges">
                  <span class="category-badge" [class.rewards]="program.category.id === 'rewards'">
                    {{ program.category.name }}
                  </span>
                  <span class="required-badge" *ngIf="program.isRequired">
                    Required
                  </span>
                </div>
              </div>
            </div>

            <div class="program-content">
              <p class="program-description">{{ program.description }}</p>
              
              <div class="program-benefits">
                <ion-chip color="primary" outline="true">
                  <ion-icon name="star-outline"></ion-icon>
                  <ion-label>{{ getPointsDisplay(program) }}</ion-label>
                </ion-chip>
                <ion-chip color="secondary" outline="true" *ngIf="program.benefits?.length">
                  <ion-icon name="gift-outline"></ion-icon>
                  <ion-label>{{ program.benefits?.length || 0 }} Benefits</ion-label>
                </ion-chip>
              </div>

              <div class="program-actions">
                <ion-button 
                  *ngIf="allowProgramEntry"
                  fill="solid" 
                  size="small"
                  color="primary"
                  (click)="enterProgram(program)">
                  <ion-icon name="enter-outline" slot="start"></ion-icon>
                  Enter
                </ion-button>
                <ion-button 
                  fill="clear" 
                  size="small"
                  (click)="viewProgramDetails(program)">
                  <ion-icon name="information-circle-outline" slot="start"></ion-icon>
                  Details
                </ion-button>
                <ion-button 
                  *ngIf="allowUnenrollment"
                  fill="clear" 
                  size="small"
                  color="danger"
                  [disabled]="program.isRequired"
                  (click)="unenrollFromProgram(program)">
                  <ion-icon name="exit-outline" slot="start"></ion-icon>
                  Leave
                </ion-button>
              </div>
            </div>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Available Programs Section -->
    <ion-card class="programs-section-card">
      <ion-card-header>
        <div class="section-header">
          <h2>Available Programs</h2>
          <ion-badge class="count-badge available">{{ filteredAvailable().length }}</ion-badge>
        </div>
      </ion-card-header>
      <ion-card-content>
        <div class="programs-container" [class.list-view]="viewMode() === 'list'">
          <div 
            *ngFor="let program of filteredAvailable()" 
            class="program-item available">
            
            <div class="program-header">
              <div class="program-icon-wrapper">
                <ion-icon [name]="program.icon" class="program-icon"></ion-icon>
              </div>
              <div class="program-info">
                <h3 class="program-name">{{ program.name }}</h3>
                <div class="program-badges">
                  <span class="category-badge" [class.rewards]="program.category.id === 'rewards'">
                    {{ program.category.name }}
                  </span>
                  <span class="new-badge" *ngIf="program.isNew">
                    New
                  </span>
                </div>
              </div>
            </div>

            <div class="program-content">
              <p class="program-description">{{ program.description }}</p>
              
              <div class="program-benefits">
                <ion-chip color="primary" outline="true">
                  <ion-icon name="star-outline"></ion-icon>
                  <ion-label>{{ getPointsDisplay(program) }}</ion-label>
                </ion-chip>
                <ion-chip color="secondary" outline="true" *ngIf="program.monthlyFee">
                  <ion-icon name="cash-outline"></ion-icon>
                  <ion-label>R{{ program.monthlyFee }}/mo</ion-label>
                </ion-chip>
              </div>

              <div class="program-actions">
                <ion-button 
                  fill="clear" 
                  size="small"
                  (click)="viewProgramDetails(program)">
                  <ion-icon name="information-circle-outline" slot="start"></ion-icon>
                  Learn More
                </ion-button>
                <ion-button 
                  *ngIf="allowEnrollment"
                  fill="solid" 
                  size="small"
                  color="primary"
                  (click)="enrollInProgram(program)">
                  <ion-icon name="add-circle-outline" slot="start"></ion-icon>
                  Enroll
                </ion-button>
              </div>
            </div>
          </div>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Empty States -->
    <ion-card class="empty-state-card" *ngIf="(filteredEnrolled().length === 0 || filteredAvailable().length === 0) && !isLoading()">
      <ion-card-content>
        <div class="empty-state" *ngIf="filteredEnrolled().length === 0">
          <ion-icon name="layers-outline" class="empty-icon"></ion-icon>
          <h3>No Enrolled Programs</h3>
          <p>You haven't enrolled in any programs yet.</p>
        </div>

        <div class="empty-state" *ngIf="filteredAvailable().length === 0">
          <ion-icon name="search-outline" class="empty-icon"></ion-icon>
          <h3>No Available Programs</h3>
          <p>No programs match your search criteria.</p>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Loading State -->
    <ion-card class="loading-state-card" *ngIf="isLoading()">
      <ion-card-content>
        <div class="loading-state">
          <ion-spinner name="crescent"></ion-spinner>
          <p>Loading programs...</p>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</lib-page-wrapper>
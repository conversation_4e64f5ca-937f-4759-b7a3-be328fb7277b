import { Component, Input, Output, EventEmitter, On<PERSON>nit, On<PERSON><PERSON>roy, Injector, CUSTOM_ELEMENTS_SCHEMA, signal, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { <PERSON>dal<PERSON><PERSON>roller, AlertController, ToastController } from '@ionic/angular';
import { Router } from '@angular/router';
import { firstValueFrom } from 'rxjs';
import { AbstractComponent } from '../abstract.component';
import { PageWrapperComponent } from '../page-wrapper/page-wrapper.component';
import { HeadLogoComponent } from '../head-logo/head-logo.component';

// Configuration interface
export interface ProgramManagementConfig {
  title?: string;
  subtitle?: string;
  showSummaryCards?: boolean;
  showSearch?: boolean;
  showCategoryFilter?: boolean;
  allowEnrollment?: boolean;
  allowUnenrollment?: boolean;
  allowProgramEntry?: boolean;
  defaultViewMode?: 'grid' | 'list';
}

// Program display interface
export interface ProgramDisplay {
  id: string;
  name: string;
  description?: string;
  shortDescription?: string;
  category: {
    id: string;
    name: string;
    description?: string;
    logoUrl?: string;
  };
  benefits?: string[];
  isRequired?: boolean;
  status?: string;
  icon?: string;
  iconUrl?: string;
  verticalImage?: string;
  horizontalImage?: string;
  iconImage?: string;
  logo?: string;
  horizontal?: string;
  vertical?: string;
  pointsMultiplier?: number;
  monthlyFee?: number;
  isNew?: boolean;
  displayImage?: string;
  memberDetails?: {
    productId: string;
    mpacc: string;
    givenNames: string;
    surname: string;
    emailAddress: string;
    personTelephone: Array<{
      telephoneType: string;
      countryCode: string;
      telephoneNumber: string;
    }>;
    status?: string;
  };
}

// Event interfaces
export interface ProgramActionEvent {
  action: 'enroll' | 'unenroll' | 'enter' | 'details';
  program: ProgramDisplay;
}

export interface ProgramStateEvent {
  type: 'enrollment' | 'loading' | 'error';
  data: any;
}

@Component({
  selector: 'lib-program-management-page',
  templateUrl: './program-management-page.component.html',
  styleUrls: ['./program-management-page.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    PageWrapperComponent,
    HeadLogoComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ProgramManagementPageComponent extends AbstractComponent implements OnInit, OnDestroy {
  @Input() profile?: any;
  @Input() config: ProgramManagementConfig = {};
  @Input() availablePrograms: ProgramDisplay[] = [];
  @Input() enrolledPrograms: ProgramDisplay[] = [];
  
  // Optional service injection inputs for dynamic environment
  @Input() programService?: any;
  @Input() onboardingService?: any;
  @Input() memberService?: any;
  @Input() keyCloakService?: any;
  @Input() lssConfig?: any;
  @Input() router?: Router;
  @Input() multiTenantContextService?: any;

  @Output() programAction = new EventEmitter<ProgramActionEvent>();
  @Output() stateChange = new EventEmitter<ProgramStateEvent>();
  @Output() programsLoaded = new EventEmitter<{ available: ProgramDisplay[], enrolled: ProgramDisplay[] }>();
  @Output() errorOccurred = new EventEmitter<string>();

  // Reactive state using Angular signals
  public readonly internalAvailablePrograms = signal<ProgramDisplay[]>([]);
  public readonly internalEnrolledPrograms = signal<ProgramDisplay[]>([]);
  public readonly isLoading = signal(false);
  public readonly searchQuery = signal('');
  public readonly selectedCategory = signal('all');
  public readonly viewMode = signal<'grid' | 'list'>('grid');

  // Computed values
  public readonly filteredAvailable = computed(() => {
    const query = this.searchQuery().toLowerCase();
    const category = this.selectedCategory();
    const programs = this.internalAvailablePrograms();

    return programs.filter(program => {
      const matchesSearch = !query || 
        program.name.toLowerCase().includes(query) ||
        (program.description && program.description.toLowerCase().includes(query));
      
      const matchesCategory = category === 'all' || 
        program.category.id === category;

      return matchesSearch && matchesCategory && !this.isEnrolled(program);
    });
  });

  public readonly filteredEnrolled = computed(() => {
    const query = this.searchQuery().toLowerCase();
    const programs = this.internalEnrolledPrograms();

    return programs.filter(program => {
      return !query || 
        program.name.toLowerCase().includes(query) ||
        (program.description && program.description.toLowerCase().includes(query));
    });
  });

  public readonly categories = computed(() => {
    const programs = this.internalAvailablePrograms();
    const categorySet = new Set(programs.map(p => p.category.id));
    return ['all', ...Array.from(categorySet)];
  });

  public readonly enrollmentCount = computed(() => this.internalEnrolledPrograms().length);
  
  public readonly totalPointsEarning = computed(() => {
    return this.internalEnrolledPrograms().reduce((total, program) => {
      const multiplier = program.pointsMultiplier || 1;
      return total + multiplier;
    }, 0);
  });
  
  public readonly totalMonthlyFees = computed(() => {
    return this.internalEnrolledPrograms().reduce((total, program) => {
      const fee = program.monthlyFee || 0;
      return total + fee;
    }, 0);
  });

  // Configuration defaults
  public title = 'My Programs';
  public subtitle = 'Manage your program enrollments and explore available programs';
  public showSummaryCards = true;
  public showSearch = true;
  public showCategoryFilter = true;
  public allowEnrollment = true;
  public allowUnenrollment = true;
  public allowProgramEntry = true;

  constructor(injector: Injector) {
    super(injector);
  }

  ngOnInit(): void {
    this.initializeConfiguration();
    this.initializeData();
    this.loadInitialData();
  }

  private initializeConfiguration(): void {
    // Apply configuration overrides
    this.title = this.config.title ?? this.title;
    this.subtitle = this.config.subtitle ?? this.subtitle;
    this.showSummaryCards = this.config.showSummaryCards ?? this.showSummaryCards;
    this.showSearch = this.config.showSearch ?? this.showSearch;
    this.showCategoryFilter = this.config.showCategoryFilter ?? this.showCategoryFilter;
    this.allowEnrollment = this.config.allowEnrollment ?? this.allowEnrollment;
    this.allowUnenrollment = this.config.allowUnenrollment ?? this.allowUnenrollment;
    this.allowProgramEntry = this.config.allowProgramEntry ?? this.allowProgramEntry;
    this.viewMode.set(this.config.defaultViewMode ?? 'grid');
  }

  private initializeData(): void {
    // Initialize programs from inputs
    this.internalAvailablePrograms.set([...this.availablePrograms]);
    this.internalEnrolledPrograms.set([...this.enrolledPrograms]);
  }

  private loadInitialData(): void {
    if (this.programService && this.keyCloakService) {
      this.loadFromServices();
    } else {
      // Emit loaded event with input data
      this.programsLoaded.emit({
        available: this.internalAvailablePrograms(),
        enrolled: this.internalEnrolledPrograms()
      });
    }
  }

  private loadFromServices(): void {
    if (!this.programService || !this.keyCloakService) return;

    const userId = this.getAuthenticatedUserId();

    if (!userId) {
      this.handleError('User authentication required');
      return;
    }

    this.loadPrograms();
  }


  private loadUserEnrollments(userId: string): void {
    if (!this.programService) return;

    this.programService.getUserProgramSelection(userId).subscribe({
      next: (selection: any) => {
        if (selection && selection.selectedPrograms) {
          this.programService.getAvailablePrograms().subscribe({
            next: (allPrograms: any[]) => {
              const enrolledPrograms = allPrograms.filter((p: any) => 
                selection.selectedPrograms.includes(p.id)
              );
              
              const selectionMemberDetails = (selection as any).memberDetails || [];
              
              const enrolled = enrolledPrograms.map(program => {
                const memberDetail = selectionMemberDetails.find((detail: any) => 
                  detail.productId === program.id
                );
                
                const memberDetails = memberDetail?.mpacc ? {
                  productId: program.id,
                  mpacc: memberDetail.mpacc,
                  givenNames: '',
                  surname: '',
                  emailAddress: userId,
                  personTelephone: [],
                  status: 'active'
                } : undefined;

                return this.transformProgram(program, memberDetails);
              });
              
              this.internalEnrolledPrograms.set(enrolled);
              this.programsLoaded.emit({
                available: this.internalAvailablePrograms(),
                enrolled: enrolled
              });
            },
            error: (error: any) => {
              console.error('Error loading available programs:', error);
              this.internalEnrolledPrograms.set([]);
            }
          });
        } else {
          this.internalEnrolledPrograms.set([]);
        }
      },
      error: (error: any) => {
        console.error('Error loading user program selection:', error);
        this.internalEnrolledPrograms.set([]);
      }
    });
  }

  public isEnrolled(program: ProgramDisplay): boolean {
    return this.internalEnrolledPrograms().some(p => p.id === program.id);
  }

  public async enrollInProgram(program: ProgramDisplay): Promise<void> {
    if (!this.allowEnrollment) return;
    
    if (program.isRequired) {
      await this.presentToast({ 
        message: 'This is a required program and cannot be changed.', 
        color: 'primary' 
      });
      return;
    }

    this.programAction.emit({ action: 'enroll', program });

    if (this.programService && this.keyCloakService) {
      await this.performEnrollment(program);
    }
  }

  private async performEnrollment(program: ProgramDisplay): Promise<void> {
    this.setLoadingState(true);

    try {
      const userId = this.getAuthenticatedUserId();
      
      if (!userId) {
        await this.presentToast({ 
          message: 'Unable to process request. Please log in again.', 
          color: 'danger' 
        });
        this.setLoadingState(false);
        return;
      }

      const currentEnrolled = [...this.internalEnrolledPrograms()];
      currentEnrolled.push(program);
      
      const enrolledIds = currentEnrolled.map(p => p.id);
      
      this.programService.saveUserPrograms(userId, enrolledIds).subscribe({
        next: async () => {
          this.internalEnrolledPrograms.set(currentEnrolled);
          this.stateChange.emit({ type: 'enrollment', data: { enrolled: program } });
          await this.presentToast({ 
            message: `Successfully enrolled in ${program.name}`, 
            color: 'success' 
          });
          this.setLoadingState(false);
        },
        error: async (error: any) => {
          console.error('Error enrolling in program:', error);
          this.handleError('Failed to enroll in program. Please try again.');
        }
      });
    } catch (error) {
      console.error('Error enrolling in program:', error);
      this.handleError('Failed to enroll in program. Please try again.');
    }
  }

  public async unenrollFromProgram(program: ProgramDisplay): Promise<void> {
    if (!this.allowUnenrollment) return;
    
    if (program.isRequired) {
      await this.presentToast({ 
        message: 'This is a required program and cannot be removed.', 
        color: 'primary' 
      });
      return;
    }

    this.programAction.emit({ action: 'unenroll', program });

    if (this.programService && this.keyCloakService && this.memberService) {
      await this.performUnenrollment(program);
    }
  }

  private async performUnenrollment(program: ProgramDisplay): Promise<void> {
    this.setLoadingState(true);

    try {
      const userId = this.getAuthenticatedUserId();
      
      if (!userId) {
        this.handleError('Unable to process request. Please log in again.');
        return;
      }

      this.memberService.search({ emailAddress: userId }).subscribe({
        next: async (profiles: any) => {
          if (profiles && profiles.length > 0 && profiles[0].membershipNumber) {
            const membershipNumber = profiles[0].membershipNumber;
            
            this.programService.unenrollFromProgram(userId, program.id, membershipNumber).subscribe({
              next: async () => {
                const currentEnrolled = this.internalEnrolledPrograms().filter(p => p.id !== program.id);
                this.internalEnrolledPrograms.set(currentEnrolled);
                this.stateChange.emit({ type: 'enrollment', data: { unenrolled: program } });
                await this.presentToast({ 
                  message: `Successfully left ${program.name}`, 
                  color: 'success' 
                });
                this.setLoadingState(false);
              },
              error: async (error: any) => {
                console.error('Error leaving program:', error);
                this.handleError('Failed to leave program. Please try again.');
              }
            });
          } else {
            this.handleError('Could not process unenrollment. Please contact support.');
          }
        },
        error: async (error: any) => {
          console.error('Error fetching member details:', error);
          this.handleError('Failed to get program details for unenrollment.');
        }
      });
    } catch (error) {
      console.error('Error leaving program:', error);
      this.handleError('Failed to leave program. Please try again.');
    }
  }

  public async enterProgram(program: ProgramDisplay): Promise<void> {
    if (!this.allowProgramEntry) return;

    this.programAction.emit({ action: 'enter', program });

    if (this.programService && this.keyCloakService && this.memberService && this.multiTenantContextService) {
      await this.performProgramEntry(program);
    }
  }

  private async performProgramEntry(program: ProgramDisplay): Promise<void> {
    this.setLoadingState(true);

    try {
      const userId = this.getAuthenticatedUserId();
      
      if (!userId) {
        this.handleError('Unable to enter program. Please log in again.');
        return;
      }

      let mpacc = program.memberDetails?.mpacc;
      
      if (!mpacc || mpacc.trim() === '') {
        try {
          const memberDetails: any = await firstValueFrom(
            this.programService.getMemberDetailsOptimized(userId, program.id)
          );
          
          if (memberDetails && memberDetails.mpacc) {
            mpacc = memberDetails.mpacc;
          } else {
            this.handleError('Unable to enter program. Member details not found.');
            return;
          }
        } catch (apiError) {
          this.handleError(`Unable to enter program. API Error: ${(apiError as any)?.message || 'Unknown error'}`);
          return;
        }
      }

      const programContext = {
        programId: program.id,
        programName: program.name,
        mpacc: mpacc,
        iconUrl: program.iconUrl || program.category.logoUrl,
        enteredAt: new Date().toISOString(),
        programLogo: program.logo,
        programIcon: program.icon,
        programHorizontal: program.horizontal,
        programVertical: program.vertical
      };
      
      this.multiTenantContextService.setProgramContext(programContext);

      const memberProfile = {
        uniqueId: mpacc,
        newMembershipNumber: mpacc,
        membershipNumber: mpacc,
        emailAddress: userId,
        apiId: program.id,
        externalId: userId
      };

      this.memberService.profileSubject.next(memberProfile);
      
      try {
        this.memberService.loadProgramMemberProfile(userId, program.id, mpacc).subscribe({
          next: (fullProfile: any) => {
            console.log('Full program member profile loaded successfully:', fullProfile);
          },
          error: (apiError: any) => {
            console.warn('Could not load full program member profile, using basic profile:', apiError);
          }
        });
      } catch (profileError) {
        console.warn('Error calling loadProgramMemberProfile:', profileError);
      }
      
      if (this.router) {
        await this.router.navigate(['/app/home']);
      }
      
      await this.presentToast({ 
        message: `Entered ${program.name} successfully`, 
        color: 'success' 
      });
      this.setLoadingState(false);
    } catch (error) {
      console.error('Error entering program:', error);
      this.handleError('Failed to enter program. Please try again.');
    }
  }

  public async viewProgramDetails(program: ProgramDisplay): Promise<void> {
    this.programAction.emit({ action: 'details', program });

    if (this.router) {
      await this.router.navigate(['/secure/program-details', program.id]);
    }
  }

  public toggleViewMode(): void {
    this.viewMode.set(this.viewMode() === 'grid' ? 'list' : 'grid');
  }

  public clearSearch(): void {
    this.searchQuery.set('');
  }

  public selectCategory(category: string): void {
    this.selectedCategory.set(category);
  }

  public async loadPrograms(): Promise<void> {
    if (!this.programService) return;

    this.setLoadingState(true);

    this.addGlobalSubscription(
      this.programService.getAvailablePrograms().subscribe({
        next: (programs: any[]) => {
          const displayPrograms: ProgramDisplay[] = programs.map(p => this.transformProgram(p));
          
          this.internalAvailablePrograms.set(displayPrograms);
          this.programsLoaded.emit({
            available: displayPrograms,
            enrolled: this.internalEnrolledPrograms()
          });
          this.setLoadingState(false);
        },
        error: (error: any) => {
          console.error('Error loading programs:', error);
          this.handleError('Failed to load programs. Please try again.');
        }
      })
    );

    // Also reload user enrollments if we have authentication
    const userId = this.getAuthenticatedUserId();
    if (userId) {
      this.loadUserEnrollments(userId);
    }
  }

  public getPointsDisplay(program: ProgramDisplay): string {
    const multiplier = program.pointsMultiplier || 1;
    if (multiplier === 1) return 'Standard points';
    return `${multiplier}x points`;
  }

  public getCategoryIcon(category: string): string {
    const iconMap: { [key: string]: string } = {
      'rewards': 'trophy-outline',
      'lifestyle': 'heart-outline',
      'travel': 'airplane-outline',
      'shopping': 'cart-outline',
      'finance': 'card-outline',
      'health': 'fitness-outline',
      'all': 'grid-outline'
    };
    return iconMap[category] || 'pricetag-outline';
  }

  private getProgramIcon(categoryId: string): string {
    const iconMap: { [key: string]: string } = {
      'rewards': 'star-outline',
      'lifestyle': 'heart-outline',
      'travel': 'airplane-outline',
      'shopping': 'cart-outline',
      'finance': 'card-outline',
      'health': 'fitness-outline',
      'premium': 'diamond-outline'
    };
    return iconMap[categoryId] || 'pricetag-outline';
  }

  private getPointsMultiplier(categoryId: string): number {
    const multiplierMap: { [key: string]: number } = {
      'premium': 2,
      'travel': 1.5,
      'rewards': 1,
      'lifestyle': 1,
      'shopping': 1,
      'finance': 1,
      'health': 1
    };
    return multiplierMap[categoryId] || 1;
  }

  private getDisplayImage(program: any): string {
    const currentViewMode = this.viewMode();
    
    if (currentViewMode === 'grid') {
      return program.verticalImage || 
             program.iconImage || 
             program.horizontalImage || 
             program.iconUrl || 
             program.category?.logoUrl || 
             'assets/images/default-program.png';
    } else {
      return program.horizontalImage || 
             program.iconImage || 
             program.verticalImage || 
             program.iconUrl || 
             program.category?.logoUrl || 
             'assets/images/default-program.png';
    }
  }

  /**
   * Transforms raw program data into ProgramDisplay format
   * Eliminates duplication between loading methods
   */
  private transformProgram(program: any, memberDetails?: any): ProgramDisplay {
    return {
      ...program,
      icon: this.getProgramIcon(program.category?.id || 'default'),
      pointsMultiplier: this.getPointsMultiplier(program.category?.id || 'default'),
      monthlyFee: program.category?.id === 'premium' ? 9.99 : 0,
      isNew: program.status === 'coming-soon',
      displayImage: this.getDisplayImage(program),
      memberDetails: memberDetails || undefined
    } as ProgramDisplay;
  }

  /**
   * Gets authenticated user ID with consistent error handling
   * Eliminates duplication across methods
   */
  private getAuthenticatedUserId(): string | null {
    if (!this.keyCloakService) return null;
    return this.keyCloakService.getUserIdForApi?.() || this.keyCloakService.lpUniueReference || null;
  }

  /**
   * Manages loading state consistently across methods
   * Eliminates duplication of loading state logic
   */
  private setLoadingState(isLoading: boolean): void {
    this.isLoading.set(isLoading);
    this.stateChange.emit({ type: 'loading', data: isLoading });
  }

  private handleError(message: string): void {
    this.setLoadingState(false);
    this.stateChange.emit({ type: 'error', data: message });
    this.errorOccurred.emit(message);
    this.presentToast({ message, color: 'danger', duration: 5000 });
  }

  get displayName(): string {
    if (this.profile) {
      return `${this.profile.givenNames || ''} ${this.profile.surname || ''}`.trim();
    }
    return '';
  }

  get userMembership(): string {
    return this.profile?.newMembershipNumber || this.profile?.membershipNumber || this.profile?.externalId || '';
  }

  get userBalance(): number {
    return this.profile?.currentBalance || 0;
  }

  override ngOnDestroy(): void {
    super.ngOnDestroy();
  }
}
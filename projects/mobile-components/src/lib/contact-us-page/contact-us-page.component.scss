/* Contact Us Page Styles */

.contact-section {
  margin-top: -30px;
  padding: 0 20px 20px;
  position: relative;
  z-index: 2;
  animation: slideUp 0.6s ease-out;

  @media (max-width: 768px) {
    padding: 0 16px 16px;
  }
}

/* Enhanced Quick Contact Card Animation */
lib-contact-card {
  display: block;
  margin-bottom: 1.5rem;
  animation: slideIn 0.6s ease-out;
  animation-fill-mode: both;
}

/* Enhanced Contact Form Animation */
lib-contact-form {
  display: block;
  animation: slideIn 0.8s ease-out;
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

/* Hover Effects for Interactive Elements */
lib-contact-card:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

/* Responsive Design Enhancements */
@media (max-width: 1200px) {
  .contact-section {
    max-width: 800px;
    margin: -30px auto 0;
  }
}

@media (max-width: 768px) {
  .contact-section {
    margin-top: -20px;
    padding: 0 12px 12px;
  }
  
  lib-contact-card {
    margin-bottom: 1rem;
  }
}

@media (max-width: 375px) {
  .contact-section {
    padding: 0 8px 8px;
  }
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading State */
.contact-section.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Success/Error States */
.contact-section.success {
  lib-contact-form {
    opacity: 0.5;
    pointer-events: none;
  }
}

.contact-section.error {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .contact-section {
    background-color: transparent;
  }
}

/* High Contrast Support */
@media (prefers-contrast: high) {
  lib-contact-card,
  lib-contact-form {
    border: 2px solid var(--ion-color-primary);
  }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
  .contact-section,
  lib-contact-card,
  lib-contact-form {
    animation: none;
  }
  
  lib-contact-card:hover {
    transform: none;
  }
}
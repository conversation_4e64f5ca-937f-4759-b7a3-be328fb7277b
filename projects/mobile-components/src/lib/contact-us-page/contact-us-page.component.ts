import { Component, Input, Output, EventEmitter, OnInit, Injector, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ContactFormComponent, ContactFormData } from '../forms/contact-form/contact-form.component';
import { ContactCardComponent } from '../cards/contact-card/contact-card.component';
import { PageWrapperComponent } from '../page-wrapper/page-wrapper.component';

export interface ContactUsConfig {
  categoryCode?: string;
  categories?: string[];
  phoneConfig?: {
    selectFirstCountry?: boolean;
    preferredCountries?: string[];
  };
  callCenter?: string;
  email?: string;
  logoSrc?: string;
  title?: string;
  subtitle?: string;
}

@Component({
  selector: 'lib-contact-us-page',
  templateUrl: './contact-us-page.component.html',
  styleUrls: ['./contact-us-page.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    ContactFormComponent,
    ContactCardComponent,
    PageWrapperComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ContactUsPageComponent implements OnInit {
  @Input() profile?: any;
  @Input() config: ContactUsConfig = {};
  @Input() categoryCode: string = 'CNCT';
  @Input() categories: string[] = ['General', 'Complaint'];
  
  // Optional service injection inputs for dynamic environment
  @Input() memberService?: any;
  @Input() keyCloakService?: any;
  @Input() lssConfig?: any;
  @Input() router?: any;

  @Output() formSubmit = new EventEmitter<ContactFormData>();
  @Output() categorySelected = new EventEmitter<string>();
  @Output() phoneCall = new EventEmitter<string>();
  @Output() emailClick = new EventEmitter<string>();

  // Component state
  categoriesOptions: { value: string; label: string }[] = [];
  phoneConfig: any = {};
  callCenter?: string;
  supportEmail?: string;
  logoSrc?: string = 'assets/images/logo.png';
  pageTitle?: string = 'Contact Us';
  pageSubtitle?: string = 'Get in touch with our support team';

  constructor() {}

  ngOnInit(): void {
    this.initializeConfiguration();
    this.setupCategories();
    this.setupPhoneConfig();
  }

  private initializeConfiguration(): void {
    // Set up configuration from inputs
    if (this.config.callCenter) this.callCenter = this.config.callCenter;
    if (this.config.email) this.supportEmail = this.config.email;
    if (this.config.logoSrc) this.logoSrc = this.config.logoSrc;
    if (this.config.title) this.pageTitle = this.config.title;
    if (this.config.subtitle) this.pageSubtitle = this.config.subtitle;

    // Fallback to lssConfig if available
    if (this.lssConfig) {
      this.callCenter = this.callCenter || this.lssConfig.contact?.callCenter;
      this.supportEmail = this.supportEmail || this.lssConfig.contact?.email;
      this.logoSrc = this.logoSrc || this.lssConfig.pages?.landing?.loggedinIcon || 'assets/images/logo.png';
    }
  }

  private setupCategories(): void {
    // Convert categories array to options format
    if (this.config.categories) {
      this.categoriesOptions = this.config.categories.map(cat => ({
        value: cat.toLowerCase(),
        label: cat
      }));
    } else if (this.categories) {
      this.categoriesOptions = this.categories.map(cat => ({
        value: cat.toLowerCase(),
        label: cat
      }));
    }

    // If we have services available, try to load categories from API
    if (this.memberService && this.keyCloakService) {
      this.loadCategoriesFromAPI();
    }
  }

  private setupPhoneConfig(): void {
    if (this.config.phoneConfig) {
      this.phoneConfig = this.config.phoneConfig;
    } else if (this.lssConfig?.telephone) {
      this.phoneConfig = {
        selectFirstCountry: this.lssConfig.telephone.selectFirstCountry,
        preferredCountries: this.lssConfig.telephone.preferredCountries
      };
    } else {
      // Default phone config
      this.phoneConfig = {
        selectFirstCountry: false,
        preferredCountries: ['za', 'us', 'gb']
      };
    }
  }

  private loadCategoriesFromAPI(): void {
    // This would require the AbstractFormComponent's getCodeList method
    // For now, we'll use the provided categories
    console.log('Loading categories from API for code:', this.categoryCode);
    // Implementation would go here if needed
  }

  onFormSubmit(formData: ContactFormData): void {
    console.log('Contact form submitted:', formData);
    
    // Emit the form data to parent component
    this.formSubmit.emit(formData);

    // If services are available, handle the submission
    if (this.memberService && this.keyCloakService) {
      this.handleAPISubmission(formData);
    }
  }

  private handleAPISubmission(formData: ContactFormData): void {
    // Transform the form data to the expected API format
    const payload: any = {
      ...formData,
      personTelephone: [
        {
          telephoneType: 'CELL',
          countryCode: formData.phone || '',
          telephoneCode: '',
          telephoneNumber: formData.phone || '',
          telephoneExtension: '',
          phoneable: '',
          smsable: '',
        },
      ],
      submitDate: new Date().toISOString()
    };

    console.log('Submitting to API:', payload);
    
    // Here you would call the memberService.contactUs method
    // this.memberService.contactUs(this.keyCloakService.lpUniueReference, payload)
    //   .subscribe({...});
  }

  onPhoneCall(): void {
    if (this.callCenter) {
      this.phoneCall.emit(this.callCenter);
      // Open phone dialer
      window.open(`tel:${this.callCenter}`, '_system');
    }
  }

  onEmailClick(): void {
    if (this.supportEmail) {
      this.emailClick.emit(this.supportEmail);
      // Open email client
      window.open(`mailto:${this.supportEmail}`, '_system');
    }
  }

  get displayName(): string {
    if (this.profile) {
      return `${this.profile.givenNames || ''} ${this.profile.surname || ''}`.trim();
    }
    return '';
  }

  get userMembership(): string {
    return this.profile?.externalId || '';
  }
}
<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">

  <!-- Contact Section -->
  <div class="contact-section">
    <!-- Quick Contact Card -->
    <lib-contact-card
      *ngIf="callCenter || supportEmail"
      [icon]="callCenter ? 'call-outline' : 'mail-outline'"
      [title]="callCenter ? 'Need Quick Help?' : 'Email Support'"
      [subtitle]="callCenter ? 'Call our support center' : 'Send us an email'"
      [phoneNumber]="callCenter"
      [email]="!callCenter ? supportEmail : undefined"
      (click)="callCenter ? onPhoneCall() : onEmailClick()">
    </lib-contact-card>

    <!-- Contact Form -->
    <lib-contact-form
      [categories]="categoriesOptions"
      [phoneConfig]="phoneConfig"
      (formSubmit)="onFormSubmit($event)">
    </lib-contact-form>
  </div>
</lib-page-wrapper>
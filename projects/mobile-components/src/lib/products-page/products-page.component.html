<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <!-- View Toggle -->
  <lib-view-toggle
    [(mode)]="viewMode"
    (modeChange)="onViewModeChange($any($event))">
  </lib-view-toggle>

  <!-- Products Grid View -->
  <div class="product-grid" *ngIf="viewMode === 'grid'">
    <ion-grid class="is-grid-bordered">
      <ion-row class="ion-text-center">
        <ion-col 
          class="ion-no-padding grid-col" 
          size="6"
          *ngFor="let product of products">
          <lib-product-card
            [product]="product"
            [viewMode]="'grid'"
            (productClick)="onProductClick($any($event))">
          </lib-product-card>
        </ion-col>
      </ion-row>
    </ion-grid>
  </div>

  <!-- Products List View -->
  <div class="product-list" *ngIf="viewMode === 'list'">
    <ion-list class="ion-no-padding">
      <lib-product-card
        *ngFor="let product of products"
        [product]="product"
        [viewMode]="'list'"
        (productClick)="onProductClick($any($event))">
      </lib-product-card>
    </ion-list>
  </div>

  <!-- Empty State -->
  <lib-empty-state
    *ngIf="products.length === 0 && !loading"
    icon="cube-outline"
    title="No products available"
    message="Check back later for exciting new products!">
  </lib-empty-state>

  <!-- Loading State -->
  <lib-loading-state
    *ngIf="loading"
    message="Loading products...">
  </lib-loading-state>
</lib-page-wrapper>
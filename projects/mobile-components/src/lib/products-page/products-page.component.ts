import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Product } from '../cards/product-card/product-card.component';
import { ViewMode } from '../ui/view-toggle/view-toggle.component';

@Component({
  selector: 'lib-products-page',
  templateUrl: './products-page.component.html',
  styleUrls: ['./products-page.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonicModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ProductsPageComponent implements OnInit {
  products: Product[] = [];
  viewMode: ViewMode = 'grid';
  loading: boolean = false;

  constructor() { }

  ngOnInit(): void {
    this.loadProducts();
  }

  loadProducts(): void {
    this.loading = true;
    
    // Simulate loading products - replace with actual API call
    setTimeout(() => {
      this.products = [
        {
          id: 1,
          name: 'Beats by Dr. Dre - Solo2 Wireless Headphones',
          image: './assets/images/prod1.png',
          price: 199,
          oldPrice: 239,
          badge: 'SALE',
          badgeColor: 'danger',
          currency: '$'
        },
        {
          id: 2,
          name: 'Sony - MTR BB850BZ Over-the-Ear Lorem Ipsum',
          image: './assets/images/prod2.png',
          price: 204,
          badge: 'NEW',
          badgeColor: 'success',
          currency: '$'
        }
      ];
      this.loading = false;
    }, 1000);
  }

  onViewModeChange(mode: ViewMode): void {
    this.viewMode = mode;
  }

  onProductClick(product: Product): void {
    console.log('Product clicked:', product);
    // Navigate to product detail page or show modal
  }
}
.product-list {
    ion-list {
        h2 {
            margin-top: 1em;
            font-size: 18px;
            font-weight: 500;
            color: var(--ion-text-color);
            padding: 0 1em;
            white-space: normal;
        }
        p {
            color: var(--ion-color-step-650);
        }
        .is-product-price {
            font-size: 18px;
        }
        ion-badge {
            font-size: 14px;
        }
        .is-product-price-old {
            text-decoration: line-through;
            color: var(--ion-color-is-mute-dark);
            font-size: 14px;
        }
        ion-item {
            --padding-start: 16px;
            --padding-end: 16px;
        }
    }
    
    ion-segment:not([color]).is-segment-clear ion-segment-button {
        --color-checked: var(--ion-color-is-text-dark);
    }
    
    .is-segment-clear {
        ion-segment-button {
            height: 50px;
            font-weight: 300;
            font-size: 0.9rem;
        }
    }
    
    .is-segment-bordered {
        border-top-width: 1px;
        border-top-style: solid;
        border-top-color: var(--ion-color-light);
    
        border-bottom-width: 1px;
        border-bottom-style: solid;
        border-bottom-color: var(--ion-color-light);
    
        ion-segment-button {
            border-right-width: 1px;
            border-right-style: solid;
            border-right-color: var(--ion-color-light);
        }
        ion-segment-button:last-child {
            border-right-width: 0px;
        }
    }    
}

.product-grid {
    h2 {
        font-size: 18px;
        margin-top: 18px;
        margin-bottom: 10px;
        font-weight: 300;
        color: var(--ion-text-color);
    }

    .is-grid-bordered {
        > ion-row > ion-col {
            border-left: 0;
            border: 1px solid var(--ion-color-light);
        }
        .is-product-info {
            display: -ms-flexbox;
            display: flex;
            p {
            margin-left: 10px;
            }
        }
        .is-product-price {
            font-size: 18px;
            margin: 18px 0;
        }
        ion-badge {
            font-size: 10px;
        }
        .is-product-price-old {
            text-decoration: line-through;
            color: var(--ion-color-is-mute-dark);
            font-size: 14px;
        }
    }      
}
/* Program Selection Page Styles */

.program-selection-content {
  --background: var(--ion-color-light);
  padding-bottom: 100px; // Space for fixed bottom action bar
  
  // Header Card
  .header-card {
    margin: 0 16px 20px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background: white;
    
    ion-card-content {
      padding: 20px;
    }
    
    .page-title {
      margin-bottom: 20px;
      
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--ion-color-primary);
      }
      
      p {
        margin: 0;
        font-size: 15px;
        line-height: 1.5;
        color: var(--ion-color-medium);
      }
    }
    
    .progress-section {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .progress-bar {
        flex: 1;
        height: 6px;
        background: #f0f0f0;
        border-radius: 3px;
        overflow: hidden;
        
        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);
          border-radius: 3px;
          transition: width 0.3s ease;
        }
      }
      
      .progress-text {
        font-size: 13px;
        color: var(--ion-color-medium);
        white-space: nowrap;
        font-weight: 500;
      }
    }
  }
  
  // Loading State
  .loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    
    .loading-content {
      text-align: center;
      
      ion-spinner {
        margin-bottom: 16px;
        --color: var(--ion-color-primary);
      }
      
      p {
        margin: 0;
        color: var(--ion-color-medium);
        font-size: 14px;
      }
    }
  }
  
  // Error State
  .error-container {
    padding: 16px;
    
    .error-card {
      margin: 0;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      
      .error-content {
        text-align: center;
        padding: 32px 20px;
        
        ion-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }
        
        h3 {
          margin: 0 0 12px 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--ion-color-danger);
        }
        
        p {
          margin: 0 0 20px 0;
          color: var(--ion-color-medium);
          font-size: 14px;
        }
        
        ion-button {
          --border-radius: 12px;
        }
      }
    }
  }
  
  // Filter Card
  .filter-card {
    margin: 0 16px 20px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background: white;
    
    ion-card-content {
      padding: 16px;
    }
    
    h3 {
      margin: 0 0 12px 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--ion-color-dark);
    }
    
    .category-chips {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      ion-chip {
        margin: 0;
        cursor: pointer;
        transition: all 0.3s ease;
        background: #f8f9fa;
        color: var(--ion-color-medium);
        border: 1px solid transparent;
        --background: #f8f9fa;
        
        ion-icon {
          color: var(--ion-color-medium);
          margin-right: 4px;
        }
        
        &.selected {
          background: var(--ion-color-primary);
          color: white;
          --background: var(--ion-color-primary);
          
          ion-icon {
            color: white;
          }
        }
        
        &:hover:not(.selected) {
          transform: translateY(-2px);
          border-color: var(--ion-color-primary-tint);
        }
      }
    }
  }
  
  // Programs Section Card
  .programs-section-card {
    margin: 0 16px 20px;
    border-radius: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    background: white;
    
    ion-card-header {
      padding: 16px 16px 0;
      background: white;
      
      .programs-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;
        
        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--ion-color-dark);
        }
        
        .count-badge {
          min-width: 28px;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 14px;
          padding: 0 10px;
          font-size: 14px;
          font-weight: 600;
          background: var(--ion-color-primary);
          color: white;
        }
      }
    }
    
    ion-card-content {
      padding: 16px;
      
      // Override program list component styles to integrate better
      lp-program-list {
        display: block;
        
        .program-list-container {
          background: transparent;
          padding: 0;
          
          .search-section {
            margin-bottom: 16px;
            
            ion-searchbar {
              --background: #f8f9fa;
              --border-radius: 12px;
              --box-shadow: none;
              --color: var(--ion-color-dark);
            }
          }
          
          .filter-section {
            margin-bottom: 16px;
            
            .category-chips {
              ion-chip {
                --background: #f8f9fa;
                margin-right: 8px;
                
                &.selected {
                  --background: var(--ion-color-primary);
                  color: white;
                }
              }
            }
          }
        }
      }
    }
  }
}

// Bottom Action Bar
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #f0f0f0;
  padding: 16px;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.08);
  
  .action-content {
    max-width: 600px;
    margin: 0 auto;
    
    .selection-summary {
      margin-bottom: 12px;
      text-align: center;
      
      .selected-count {
        display: block;
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-primary);
        margin-bottom: 4px;
      }
      
      .required-note {
        font-size: 12px;
        color: var(--ion-color-medium);
        font-style: italic;
      }
    }
    
    ion-button {
      --border-radius: 12px;
      margin: 0;
      font-weight: 600;
      height: 48px;
      
      &[disabled] {
        opacity: 0.6;
      }
    }
  }
}

// Responsive Design
@media (min-width: 768px) {
  .program-selection-content {
    .programs-section-card {
      ion-card-content {
        lp-program-list {
          .program-list-container {
            .programs-grid {
              ion-grid {
                ion-row {
                  ion-col {
                    padding: 8px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

@media (min-width: 1024px) {
  .program-selection-content {
    .header-card,
    .filter-card,
    .programs-section-card {
      max-width: 1200px;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

// Mobile Responsive
@media (max-width: 768px) {
  .program-selection-content {
    .header-card,
    .filter-card,
    .programs-section-card {
      margin: 0 12px 16px;
    }
    
    .programs-section-card {
      ion-card-content {
        padding: 12px;
        
        lp-program-list {
          .program-list-container {
            .programs-grid {
              ion-grid {
                ion-row {
                  ion-col {
                    padding: 4px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// Animation enhancements
.header-card,
.filter-card,
.programs-section-card {
  animation: slideUp 0.6s ease-out;
}

.filter-card {
  animation-delay: 0.1s;
  animation-fill-mode: both;
}

.programs-section-card {
  animation-delay: 0.2s;
  animation-fill-mode: both;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Dark mode support
@media (prefers-color-scheme: dark) {
  .program-selection-content {
    .header-card,
    .filter-card,
    .programs-section-card {
      background: var(--ion-color-dark);
      box-shadow: 0 2px 8px rgba(255, 255, 255, 0.05);
    }
  }
  
  .bottom-actions {
    background: var(--ion-color-dark);
    border-top-color: rgba(255, 255, 255, 0.1);
  }
}
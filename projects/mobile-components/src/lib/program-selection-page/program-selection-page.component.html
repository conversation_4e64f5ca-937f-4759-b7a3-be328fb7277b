<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <!-- Header Section -->
  <div header>
    <lib-head-logo
      [names]="displayName"
      [membership]="userMembership"
      type="profile"
      [balance]="userBalance"
      [src]="lssConfig?.pages?.landing?.loggedinIcon || 'assets/images/logo.png'"
    />
  </div>

  <div class="program-selection-content">
    <!-- Header Content -->
    <ion-card class="header-card">
      <ion-card-content>
        <div class="page-title">
          <h1>{{ pageTitle }}</h1>
          <p>{{ pageSubtitle }}</p>
        </div>
        
        <!-- Progress indicator -->
        <div class="progress-section" *ngIf="showProgress">
          <div class="progress-bar">
            <div class="progress-fill" [style.width.%]="progressPercentage"></div>
          </div>
          <span class="progress-text">{{ progressText }}</span>
        </div>
      </ion-card-content>
    </ion-card>

    <!-- Loading State -->
    <div *ngIf="isLoading && !hasError" class="loading-container">
      <div class="loading-content">
        <ion-spinner></ion-spinner>
        <p>Loading programs...</p>
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="hasError" class="error-container">
      <ion-card class="error-card">
        <ion-card-content>
          <div class="error-content">
            <ion-icon name="alert-circle" color="danger" size="large"></ion-icon>
            <h3>Oops! Something went wrong</h3>
            <p>{{ errorMessage }}</p>
            <ion-button (click)="retry()" color="primary" fill="solid">
              <ion-icon name="refresh" slot="start"></ion-icon>
              Try Again
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Main Content -->
    <div *ngIf="!isLoading && !hasError" class="main-content">
      <!-- Category Filter -->
      <ion-card class="filter-card" *ngIf="categories.length > 0">
        <ion-card-content>
          <h3>Filter by Category</h3>
          <div class="category-chips">
            <ion-chip 
              [class.selected]="currentCategory === null"
              (click)="currentCategory = null">
              <ion-icon name="apps"></ion-icon>
              <ion-label>All Programs</ion-label>
            </ion-chip>
            
            <ion-chip 
              *ngFor="let category of categories"
              [class.selected]="currentCategory === category.id"
              (click)="currentCategory = category.id">
              <ion-icon [name]="getCategoryIcon(category.id)"></ion-icon>
              <ion-label>{{ category.name | titlecase }}</ion-label>
            </ion-chip>
          </div>
        </ion-card-content>
      </ion-card>

      <!-- Programs List using ProgramListComponent -->
      <ion-card class="programs-section-card">
        <ion-card-header>
          <div class="programs-header">
            <h3>Available Programs</h3>
            <ion-badge class="count-badge">{{ selectedPrograms.size }} selected</ion-badge>
          </div>
        </ion-card-header>
        <ion-card-content>
          <lp-program-list
            [programs]="availableProgramsForList"
            [selectedIds]="selectedProgramsArray"
            (selectionChanged)="onProgramSelection($event)"
            (programDetails)="onProgramDetails($event)">
          </lp-program-list>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Bottom Action Bar -->
    <div class="bottom-actions" *ngIf="!isLoading && !hasError">
      <div class="action-content">
        <div class="selection-summary">
          <span class="selected-count">{{ selectedPrograms.size }} programs selected</span>
          <span class="required-note" *ngIf="hasRequiredPrograms">
            * Required programs are automatically included
          </span>
        </div>
        <ion-button 
          (click)="continue()" 
          [disabled]="!canContinue()"
          expand="block" 
          size="large" 
          color="primary">
          {{ continueButtonText }}
          <ion-icon name="arrow-forward" slot="end"></ion-icon>
        </ion-button>
      </div>
    </div>
  </div>
</lib-page-wrapper>
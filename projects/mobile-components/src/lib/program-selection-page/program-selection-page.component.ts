import { Component, Input, Output, EventEmitter, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON>roy, Injector, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { Subject } from 'rxjs';
import { finalize, takeUntil } from 'rxjs/operators';
import { AbstractComponent } from '../abstract.component';
import { PageWrapperComponent } from '../page-wrapper/page-wrapper.component';
import { HeadLogoComponent } from '../head-logo/head-logo.component';
import { ProgramListComponent, ProgramSelectionEvent } from '../program-selection/program-list/program-list.component';
import { Program } from '../program-selection/program-card/program-card.component';

export interface ProgramSelectionConfig {
  isMultiTenant?: boolean;
  allowMultipleSelection?: boolean;
  showProgress?: boolean;
  progressStep?: number;
  progressTotal?: number;
  title?: string;
  subtitle?: string;
  continueButtonText?: string;
  navigationTarget?: string;
}

export interface ProgramCategory {
  id: string;
  name: string;
  description?: string;
  sortOrder?: number;
}

export interface ProgramWithSelection {
  id: string;
  name: string;
  description?: string;
  shortDescription?: string;
  icon?: string;
  iconUrl?: string;
  verticalImage?: string;
  horizontalImage?: string;
  iconImage?: string;
  isRequired?: boolean;
  benefits?: string[];
  isSelected?: boolean;
  showEligibility?: boolean;
  category: ProgramCategory;
  eligibilityCriteria?: string[];
  termsAndConditions?: string;
  minimumTier?: string;
  status?: string;
  sortOrder?: number;
  // New API fields
  defaultTier?: string;
  defaultTierDescr?: string;
  logo?: string;
  horizontal?: string;
  vertical?: string;
  popularity?: number;
  isActive?: boolean;
}

@Component({
  selector: 'lib-program-selection-page',
  templateUrl: './program-selection-page.component.html',
  styleUrls: ['./program-selection-page.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    PageWrapperComponent,
    HeadLogoComponent,
    ProgramListComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ProgramSelectionPageComponent extends AbstractComponent implements OnInit, OnDestroy {
  @Input() profile?: any;
  @Input() config: ProgramSelectionConfig = {};
  @Input() programs: ProgramWithSelection[] = [];
  @Input() categories: ProgramCategory[] = [];
  @Input() enrolledPrograms: string[] = [];
  
  // Optional service injection inputs for dynamic environment
  @Input() programService?: any;
  @Input() onboardingService?: any;
  @Input() memberService?: any;
  @Input() keyCloakService?: any;
  @Input() lssConfig?: any;
  @Input() router?: Router;
  @Input() programMemberService?: any;

  @Output() programsLoaded = new EventEmitter<ProgramWithSelection[]>();
  @Output() categoriesLoaded = new EventEmitter<ProgramCategory[]>();
  @Output() programSelected = new EventEmitter<ProgramSelectionEvent>();
  @Output() programDeselected = new EventEmitter<ProgramSelectionEvent>();
  @Output() continueClicked = new EventEmitter<string[]>();
  @Output() backClicked = new EventEmitter<void>();
  @Output() errorOccurred = new EventEmitter<string>();

  // Component state
  availablePrograms: ProgramWithSelection[] = [];
  filteredPrograms: ProgramWithSelection[] = [];
  selectedPrograms: Set<string> = new Set();
  selectedProgram: string | null = null; // Single selection for multi-tenant
  currentCategory: string | null = null;
  enrolledProgramsSet: Set<string> = new Set();
  
  isLoading = false;
  hasError = false;
  errorMessage = '';
  
  // Configuration defaults
  isMultiTenant = false;
  allowMultipleSelection = true;
  showProgress = true;
  progressStep = 2;
  progressTotal = 4;
  pageTitle = 'Select Your Programs';
  pageSubtitle = 'Choose the loyalty programs that best fit your lifestyle and interests.';
  continueButtonText = 'Continue to Onboarding';
  navigationTarget = '/secure/onboarding-flow';

  private destroy$ = new Subject<void>();

  constructor(injector: Injector) {
    super(injector);
  }

  ngOnInit(): void {
    this.initializeConfiguration();
    this.initializeData();
    this.loadInitialData();
  }

  override ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeConfiguration(): void {
    // Apply configuration overrides
    this.isMultiTenant = this.config.isMultiTenant ?? this.isMultiTenant;
    this.allowMultipleSelection = this.config.allowMultipleSelection ?? this.allowMultipleSelection;
    this.showProgress = this.config.showProgress ?? this.showProgress;
    this.progressStep = this.config.progressStep ?? this.progressStep;
    this.progressTotal = this.config.progressTotal ?? this.progressTotal;
    this.pageTitle = this.config.title ?? this.pageTitle;
    this.pageSubtitle = this.config.subtitle ?? this.pageSubtitle;
    this.continueButtonText = this.config.continueButtonText ?? this.continueButtonText;
    this.navigationTarget = this.config.navigationTarget ?? this.navigationTarget;

    // For multi-tenant mode, force single selection
    if (this.isMultiTenant) {
      this.allowMultipleSelection = false;
    }
  }

  private initializeData(): void {
    // Initialize enrolled programs set
    this.enrolledProgramsSet = new Set(this.enrolledPrograms);

    // Initialize programs
    this.availablePrograms = this.programs.map(program => ({
      ...program,
      isSelected: this.enrolledProgramsSet.has(program.id)
    }));

    // Initialize selected programs
    this.selectedPrograms = new Set(this.enrolledPrograms);
    if (this.isMultiTenant && this.enrolledPrograms.length > 0) {
      this.selectedProgram = this.enrolledPrograms[0];
    }

    this.filteredPrograms = [...this.availablePrograms];
  }

  private loadInitialData(): void {
    if (this.programService && this.keyCloakService) {
      this.loadFromServices();
    } else if (this.programs.length === 0) {
      this.handleError('No programs available');
    }
  }

  private loadFromServices(): void {
    if (!this.programService || !this.keyCloakService) return;

    this.isLoading = true;
    this.hasError = false;
    const userId = this.keyCloakService.getUserIdForApi?.() || this.keyCloakService.lpUniueReference;

    if (!userId) {
      this.handleError('User authentication required');
      return;
    }

    // Check if user needs registration
    const userNeedsRegistration = this.keyCloakService.userProfile?.mustRegister;

    if (userNeedsRegistration) {
      this.loadAvailablePrograms();
    } else {
      this.loadUserEnrollments(userId);
    }
  }

  private loadUserEnrollments(userId: string): void {
    this.addGlobalSubscription(
      this.programService.getUserProgramSelection(userId).subscribe({
        next: (selection: any) => {
          if (selection && selection.selectedPrograms) {
            this.enrolledProgramsSet = new Set(selection.selectedPrograms);
            this.selectedPrograms = new Set(selection.selectedPrograms);
            if (this.isMultiTenant && selection.selectedPrograms.length > 0) {
              this.selectedProgram = selection.selectedPrograms[0];
            }
          }
          this.loadAvailablePrograms();
        },
        error: (error: any) => {
          console.error('Error loading user selection:', error);
          this.loadAvailablePrograms();
        }
      })
    );
  }

  private loadAvailablePrograms(): void {
    // Load categories
    if (this.programService.getProgramCategories) {
      this.addGlobalSubscription(
        this.programService.getProgramCategories().subscribe({
          next: (categories: ProgramCategory[]) => {
            this.categories = categories;
            this.categoriesLoaded.emit(categories);
          },
          error: (error: any) => {
            console.error('Error loading categories:', error);
          }
        })
      );
    }

    // Load programs
    if (this.programService.getAvailablePrograms) {
      this.addGlobalSubscription(
        this.programService.getAvailablePrograms()
          .pipe(
            finalize(() => this.isLoading = false),
            takeUntil(this.destroy$)
          )
          .subscribe({
            next: (programs: ProgramWithSelection[]) => {
              this.availablePrograms = programs
                .filter(program => !this.enrolledProgramsSet.has(program.id))
                .map(p => ({ 
                  ...p, 
                  isSelected: this.selectedPrograms.has(p.id) 
                }));
              
              this.filteredPrograms = [...this.availablePrograms];
              this.programsLoaded.emit(this.availablePrograms);
              this.detectChanges();
            },
            error: (error: any) => {
              this.handleError('Failed to load available programs');
            }
          })
      );
    }
  }

  onProgramSelection(event: ProgramSelectionEvent): void {
    const program = event.program;
    const programId = program.id;
    const isCurrentlySelected = event.selected;

    // Don't allow deselection of required programs in non-multi-tenant mode
    if (program.isRequired && !this.isMultiTenant && !isCurrentlySelected) {
      return;
    }

    if (this.isMultiTenant) {
      this.handleMultiTenantSelection(programId, isCurrentlySelected);
    } else {
      this.handleMultipleSelection(programId, isCurrentlySelected);
    }

    // Emit events
    if (isCurrentlySelected) {
      this.programSelected.emit(event);
    } else {
      this.programDeselected.emit(event);
    }
  }

  private handleMultiTenantSelection(programId: string, isSelected: boolean): void {
    const userNeedsRegistration = this.keyCloakService?.userProfile?.mustRegister;

    if (userNeedsRegistration) {
      // Handle registration flow
      this.selectedProgram = null;
      this.selectedPrograms.clear();

      if (isSelected) {
        this.selectedProgram = programId;
        this.selectedPrograms.add(programId);
      }
      this.detectChanges();
    } else {
      // Handle API update for existing user
      this.updateProgramSelectionAPI(programId, isSelected);
    }
  }

  private handleMultipleSelection(programId: string, isSelected: boolean): void {
    if (this.programService && this.keyCloakService) {
      this.updateProgramSelectionAPI(programId, isSelected);
    } else {
      // Handle local state only
      if (isSelected) {
        this.selectedPrograms.add(programId);
      } else {
        this.selectedPrograms.delete(programId);
      }
      this.detectChanges();
    }
  }

  private updateProgramSelectionAPI(programId: string, isSelected: boolean): void {
    const userId = this.keyCloakService.getUserIdForApi?.() || this.keyCloakService.lpUniueReference;
    if (!userId) return;

    this.addViewSubscription(
      this.programService.updateUserProgramSelection(userId, programId, isSelected).subscribe({
        next: () => {
          if (this.isMultiTenant) {
            if (this.selectedProgram && this.selectedProgram !== programId) {
              this.selectedPrograms.delete(this.selectedProgram);
            }
            this.selectedProgram = isSelected ? programId : null;
            this.selectedPrograms.clear();
            if (isSelected) {
              this.selectedPrograms.add(programId);
            }
          } else {
            if (isSelected) {
              this.selectedPrograms.add(programId);
            } else {
              this.selectedPrograms.delete(programId);
            }
          }
          this.detectChanges();
        },
        error: (error: any) => {
          this.presentToast({ message: 'Failed to update program selection', color: 'danger' });
        }
      })
    );
  }

  onProgramDetails(program: Program): void {
    // This could be implemented to show a modal with program details
    console.log('Show program details:', program);
  }

  isProgramSelected(programId: string): boolean {
    if (this.isMultiTenant) {
      return this.selectedProgram === programId;
    }
    return this.selectedPrograms.has(programId);
  }

  canContinue(): boolean {
    if (this.isMultiTenant) {
      return this.selectedProgram !== null && this.selectedPrograms.size === 1;
    } else {
      return this.selectedPrograms.size > 0;
    }
  }

  async continue(): Promise<void> {
    if (!this.canContinue()) {
      const message = this.isMultiTenant 
        ? 'Please select a program to continue'
        : 'Please select at least one program to continue';
      await this.presentToast({ message, color: 'warning' });
      return;
    }

    this.isLoading = true;
    const selectedProgramsArray = this.isMultiTenant ? [this.selectedProgram!] : Array.from(this.selectedPrograms);
    const userNeedsRegistration = this.keyCloakService?.userProfile?.mustRegister;

    this.continueClicked.emit(selectedProgramsArray);

    if (this.isMultiTenant && userNeedsRegistration) {
      if (this.selectedProgram && this.programMemberService) {
        this.programMemberService.storeSelectedProgram(this.selectedProgram);
      }
      this.isLoading = false;
      this.navigateNext();
    } else {
      this.handleStandardProgramSelection(selectedProgramsArray);
    }
  }

  private handleStandardProgramSelection(selectedProgramsArray: string[]): void {
    if (!this.programService) {
      this.navigateNext();
      return;
    }

    const userId = this.keyCloakService?.getUserIdForApi?.() || this.keyCloakService?.lpUniueReference;
    if (!userId) return;

    this.addViewSubscription(
      this.programService.saveUserPrograms(userId, selectedProgramsArray, 'Selected during onboarding').subscribe({
        next: () => {
          if (this.onboardingService) {
            this.onboardingService.completeStep('program-selection', { selectedPrograms: selectedProgramsArray }).subscribe({
              next: () => this.navigateNext(),
              error: () => this.navigateNext()
            });
          } else {
            this.navigateNext();
          }
        },
        error: (error: any) => {
          this.handleError('Failed to save program selection');
        },
        complete: () => this.isLoading = false
      })
    );
  }

  goBack(): void {
    this.backClicked.emit();
    if (this.router) {
      this.router.navigate(['/secure/dashboard']);
    }
  }

  private navigateNext(): void {
    if (this.router) {
      this.router.navigate([this.navigationTarget]);
    }
  }

  retry(): void {
    this.hasError = false;
    this.errorMessage = '';
    this.loadInitialData();
  }

  private handleError(message: string): void {
    this.hasError = true;
    this.errorMessage = message;
    this.isLoading = false;
    this.errorOccurred.emit(message);
    this.detectChanges();
    this.presentToast({ message, color: 'danger', duration: 5000 });
  }

  getCategoryIcon(categoryId: string): string {
    const map: { [key: string]: string } = { 
      core: 'star', 
      premium: 'diamond', 
      lifestyle: 'heart',
      rewards: 'gift'
    };
    return map[categoryId] || 'apps';
  }

  getProgramIcon(programId: string): string {
    const map: { [key: string]: string } = {
      'loyalty-base': 'card', 
      'premium-rewards': 'trophy', 
      'travel-club': 'airplane',
      'family-benefits': 'people', 
      'green-rewards': 'leaf'
    };
    return map[programId] || 'gift';
  }

  get progressPercentage(): number {
    return this.showProgress ? (this.progressStep / this.progressTotal) * 100 : 0;
  }

  get progressText(): string {
    return `Step ${this.progressStep} of ${this.progressTotal}`;
  }

  get selectionSummary(): string {
    if (this.isMultiTenant) {
      const program = this.availablePrograms.find(p => p.id === this.selectedProgram);
      return program ? program.name : 'No program selected';
    } else {
      const count = this.selectedPrograms.size;
      return count === 1 ? '1 program selected' : `${count} programs selected`;
    }
  }

  get hasRequiredPrograms(): boolean {
    return this.availablePrograms.some(p => p.isRequired);
  }

  get displayName(): string {
    if (this.profile) {
      return `${this.profile.givenNames || ''} ${this.profile.surname || ''}`.trim();
    }
    return '';
  }

  get userMembership(): string {
    return this.profile?.newMembershipNumber || this.profile?.externalId || '';
  }

  get userBalance(): number {
    return this.profile?.currentBalance || 0;
  }

  get selectedProgramsArray(): string[] {
    return Array.from(this.selectedPrograms);
  }

  get availableProgramsForList(): Program[] {
    return this.availablePrograms.map(p => ({
      id: p.id,
      name: p.name,
      description: p.description || '',
      shortDescription: p.shortDescription,
      icon: p.icon,
      iconUrl: p.iconUrl,
      verticalImage: p.verticalImage,
      horizontalImage: p.horizontalImage,
      iconImage: p.iconImage,
      isRequired: p.isRequired,
      category: typeof p.category === 'object' ? p.category.name : p.category,
      benefits: p.benefits,
      terms: p.termsAndConditions,
      popularity: p.sortOrder,
      isActive: p.status === 'active',
      displayImage: p.vertical || p.horizontal || p.icon || p.logo
    }));
  }
}

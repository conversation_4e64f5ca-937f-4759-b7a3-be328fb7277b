<form [formGroup]="form" *ngIf="config?.form?.sections as sections">
  <ng-container *ngFor="let section of sections">
    <lib-form-section [title]="section.title" [icon]="section.icon" [isCollapsible]="false">
      <div class="section-grid">
        <ng-container *ngFor="let field of section.fields">
          <!-- Conditional rendering based on field conditions -->
          <ng-container *ngIf="isFieldVisible(field)">
            <ng-container [ngSwitch]="field.type">
              <lib-form-field *ngSwitchCase="'text'"
                [label]="field.label?.default"
                [required]="field.required"
                [placeholder]="field.placeholder"
                [errorMessages]="field.errorMessages"
                [control]="getFormControl(field.key)">
              </lib-form-field>
              <lib-form-field *ngSwitchCase="'password'"
                type="password"
                [label]="field.label?.default"
                [required]="field.required"
                [control]="getFormControl(field.key)">
              </lib-form-field>
              <lib-form-field *ngSwitchCase="'date'"
                type="date"
                [label]="field.label?.default"
                [required]="field.required"
                [min]="field.min"
                [max]="field.max"
                [control]="getFormControl(field.key)">
              </lib-form-field>
              <lib-form-select *ngSwitchCase="'select'"
                [label]="field.label?.default"
                [required]="field.required"
                [options]="field.options?.values || []"
                optionValue="value"
                optionLabel="label"
                [placeholder]="field.placeholder"
                [control]="getFormControl(field.key)">
              </lib-form-select>
              <div *ngSwitchCase="'store-selector'" class="full-width">
                <lib-stores (updateDataEvent)="onStoreUpdate($event)" [required_field]="field.required"></lib-stores>
              </div>
            </ng-container>
          </ng-container>
        </ng-container>
      </div>
    </lib-form-section>
  </ng-container>

  <!-- Terms and Conditions -->
  <div class="terms-section" *ngIf="hasTermsField()">
    <ion-checkbox formControlName="terms" [required]="true"></ion-checkbox>
    <ion-label class="terms-label">
      I agree to the <a [href]="config?.termsConditions || '#'" target="_blank">Terms and Conditions</a>
    </ion-label>
  </div>

  <!-- Submit Button -->
  <div class="action-section">
    <ion-button
      expand="block"
      type="submit"
      [disabled]="!isFormValid()"
      class="signup-button"
      (click)="submit()">
      <ion-icon name="person-add-outline" slot="start"></ion-icon>
      <ion-spinner *ngIf="isSubmitting" name="crescent" slot="start"></ion-spinner>
      {{ isSubmitting ? 'Creating Account...' : 'Sign Up' }}
    </ion-button>
  </div>
</form>

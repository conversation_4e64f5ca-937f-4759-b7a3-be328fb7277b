import { Component, EventEmitter, Input, OnChanges, Output, SimpleChang<PERSON>, <PERSON><PERSON><PERSON>roy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators, FormControl } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { FormSectionComponent } from '../forms/form-section/form-section.component';
import { FormFieldComponent } from '../forms/form-field/form-field.component';
import { FormSelectComponent } from '../forms/form-select/form-select.component';
import { StoresComponent } from '../stores/stores.component';
import { Subscription } from 'rxjs';

@Component({
  selector: 'lib-signup-form-builder',
  standalone: true,
  imports: [CommonModule, IonicModule, ReactiveFormsModule, FormSectionComponent, FormFieldComponent, FormSelectComponent, StoresComponent],
  templateUrl: './signup-form-builder.component.html',
  styleUrls: ['./signup-form-builder.component.scss']
})
export class SignupFormBuilderComponent implements OnChanges, OnDestroy {
  @Input() config: any;
  @Output() formSubmit = new EventEmitter<any>();

  form: FormGroup = this.fb.group({});
  selectedStore: any = null;
  isSubmitting = false;
  private formSubscription?: Subscription;

  constructor(private fb: FormBuilder, private cdr: ChangeDetectorRef) {}

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['config'] && this.config?.form?.sections) {
      this.buildForm();
      this.setupConditionalSubscriptions();
    }
  }

  ngOnDestroy(): void {
    if (this.formSubscription) {
      this.formSubscription.unsubscribe();
    }
  }

  private buildForm(): void {
    const group: { [key: string]: FormControl } = {};
    this.config.form.sections.forEach((section: any) => {
      if (section.visible === false) return;
      (section.fields || []).forEach((field: any) => {
        if (field.type === 'store-selector') {
          group['favoriteStore'] = new FormControl('');
          return;
        }
        const validators = [];
        if (field.required) validators.push(Validators.required);
        if (field.validation?.pattern) validators.push(Validators.pattern(field.validation.pattern));
        if (field.validation?.minLength) validators.push(Validators.minLength(field.validation.minLength));
        if (field.validation?.maxLength) validators.push(Validators.maxLength(field.validation.maxLength));
        
        // Set default value for idType to ensure conditional logic works
        let defaultValue = field.defaultValue || '';
        if (field.key === 'idType') {
          defaultValue = 'nationalId'; // Default to national ID
        }
        
        group[field.key] = new FormControl(defaultValue, validators);
      });
    });
    
    // Add terms field if it exists in config
    if (this.hasTermsField()) {
      group['terms'] = new FormControl(false, Validators.requiredTrue);
    }
    
    this.form = this.fb.group(group);
  }

  private setupConditionalSubscriptions(): void {
    // Clean up existing subscription
    if (this.formSubscription) {
      this.formSubscription.unsubscribe();
    }

    // Subscribe to form value changes to trigger conditional field updates
    this.formSubscription = this.form.valueChanges.subscribe(() => {
      // Force change detection to re-evaluate conditional field visibility
      this.cdr.detectChanges();
    });
  }

  shouldShowField(field: any): boolean {
    if (!field.conditional?.showWhen) return true;
    
    const conditions = field.conditional.showWhen;
    for (const [key, value] of Object.entries(conditions)) {
      const controlValue = this.form.get(key)?.value;
      if (controlValue !== value) {
        return false;
      }
    }
    return true;
  }

  isFieldVisible(field: any): boolean {
    // This method is called on every change detection cycle
    // making the conditional rendering more reactive
    return this.shouldShowField(field);
  }

  hasTermsField(): boolean {
    if (!this.config?.form?.sections) return false;
    
    return this.config.form.sections.some((section: any) => 
      section.fields?.some((field: any) => field.key === 'terms')
    );
  }

  isFormValid(): boolean {
    return this.form.valid;
  }

  onStoreUpdate(store: any): void {
    this.selectedStore = store;
    this.form.get('favoriteStore')?.setValue(store?.partnerId || '');
  }

  getFormControl(key: string): FormControl | undefined {
    const control = this.form.get(key);
    return control instanceof FormControl ? control : undefined;
  }

  submit(): void {
    if (!this.isFormValid()) return;
    
    this.isSubmitting = true;
    const value = { ...this.form.value };
    if (this.selectedStore) {
      (value as any).selectedStore = this.selectedStore;
    }
    this.formSubmit.emit(value);
    this.isSubmitting = false;
  }
}

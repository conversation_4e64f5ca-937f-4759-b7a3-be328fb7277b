import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { ComponentsModule } from '../components.module';

export interface TransactionStatement {
  invoiceNumber?: string;
  transactionType?: string;
  transactionPoints?: number;
  actValue?: number;
  transactionDate?: Date;
  loadDate?: string;
  label?: Date;
  quantity?: number;
  expDate?: Date;
  tierPoints?: number;
}

@Component({
  selector: 'lib-transactions-page',
  templateUrl: './transactions-page.component.html',
  styleUrls: ['./transactions-page.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    IonicModule,
    ComponentsModule
  ]
})
export class TransactionsPageComponent implements OnInit, OnDestroy {
  @Input() profile?: any;
  @Input() memberService?: any;
  @Input() programService?: any;
  @Input() keyCloakService?: any;
  @Input() lssConfig?: any;
  @Input() multiTenantContext?: any;
  @Input() apiBaseUrl?: string;
  @Input() productId?: string;

  @Output() transactionLoaded = new EventEmitter<TransactionStatement[]>();
  @Output() transactionFiltered = new EventEmitter<{type: string, transactions: TransactionStatement[]}>();
  @Output() transactionSelected = new EventEmitter<TransactionStatement>();
  @Output() loadMoreRequested = new EventEmitter<void>();

  searchRender = false;
  statements: TransactionStatement[] = [];
  filteredStatements: TransactionStatement[] = [];
  selectedTransactionType: string = 'All';
  beginDate?: any;
  endDate?: Date;
  loading = false;

  // Transaction type filter options
  filterOptions = [
    {value: 'All', label: 'All', icon: 'list-outline'},
    {value: 'Accrual', label: 'Earned', icon: 'add-circle-outline', color: 'success'},
    {value: 'Redemption', label: 'Spent', icon: 'remove-circle-outline', color: 'danger'},
    {value: 'Refund', label: 'Refund', icon: 'refresh-circle-outline', color: 'warning'},
    {value: 'RefundRedemption', label: 'Refund Redemption', icon: 'refresh-outline', color: 'warning'},
    {value: 'Reversals', label: 'Reversals', icon: 'arrow-undo-outline', color: 'dark'}
  ];

  constructor() {}

  ngOnInit() {
    // Initialize component if needed
    console.log('TransactionsPageComponent initialized');
  }

  ngOnDestroy() {
    // Clean up subscriptions if any
  }

  ionViewDidEnter(): void {
    this.loading = true;
    this.search();
  }

  search(retry?: boolean): void {
    if (!this.profile && !retry) {
      setTimeout(() => this.search(true), 500);
      return;
    }
    let limit = 10;

    this.loadTransactionsUnified(limit);
  }

  private get isMultiTenant(): boolean {
    return this.multiTenantContext?.isMultiTenant || false;
  }

  /**
   * Load transactions using unified API approach
   */
  private loadTransactionsUnified(limit: number): void {
    if (!this.keyCloakService || !this.programService) {
      this.loading = false;
      return;
    }

    const userId = this.keyCloakService.getUserIdForApi ? this.keyCloakService.getUserIdForApi() : null;
    const { productId, mpacc } = this.getProductContext();
    
    if (!userId || !productId || !mpacc) {
      this.loading = false;
      console.warn('Unable to load transactions. Missing required information.');
      return;
    }
    
    // Prepare query options
    const queryOptions = {
      pageSize: limit,
      offset: 0,
      beginDate: this.beginDate ? this.formatDateForAPI(this.beginDate) : '2020-01-01',
      endDate: this.endDate ? this.formatDateForAPI(this.endDate) : '2025-12-31',
      filter: 'award'
    };
    
    this.programService
      .getUserProgramTransactions(userId, productId, mpacc, queryOptions)
      .subscribe({
        error: (error: any) => {
          this.loading = false;
          console.error('Error loading transactions:', error);
        },
        next: (programTransactions: any[]) => {
          if (programTransactions && programTransactions.length > 0) {
            this.statements = programTransactions.map(tx => this.programTxToStatement(tx));
            this.filterTransactions();
            this.transactionLoaded.emit(this.statements);
          } else {
            this.statements = [];
            this.filteredStatements = [];
          }
          this.loading = false;
        },
      });
  }

  /**
   * Get product context based on tenant type
   */
  private getProductContext(): { productId: string | null; mpacc: string | null } {
    if (this.isMultiTenant) {
      const programContext = this.multiTenantContext.currentProgramContext;
      if (programContext) {
        return {
          productId: programContext.programId,
          mpacc: programContext.mpacc
        };
      }
      return { productId: null, mpacc: null };
    } else {
      const productId = String(this.lssConfig?.productId || 'rmic');
      const mpacc = this.profile?.membershipNumber || this.profile?.newMembershipNumber;
      
      return {
        productId: productId || null,
        mpacc: mpacc || null
      };
    }
  }

  /**
   * Converts ProgramTransaction to TransactionStatement
   */
  private programTxToStatement(programTx: any): TransactionStatement {
    return {
      invoiceNumber: programTx.transactionId || `${programTx.productId}-${programTx.mpacc}-${Date.now()}`,
      transactionType: programTx.transactionType,
      transactionPoints: programTx.points,
      actValue: programTx.amount,
      transactionDate: new Date(programTx.transactionDate),
      loadDate: programTx.transactionDate,
      label: new Date(programTx.transactionDate),
      quantity: 1,
      expDate: undefined,
      tierPoints: undefined
    };
  }

  private formatDateForAPI(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  selectTransactionType(type: string): void {
    this.selectedTransactionType = type;
    this.filterTransactions();
  }

  filterTransactions(): void {
    if (this.selectedTransactionType === 'All') {
      this.filteredStatements = [...this.statements];
    } else if (this.selectedTransactionType === 'Reversals') {
      this.filteredStatements = this.statements.filter(statement => 
        this.isReversalTransaction(statement)
      );
    } else {
      this.filteredStatements = this.statements.filter(
        statement => statement.transactionType === this.selectedTransactionType
      );
    }
    
    this.transactionFiltered.emit({
      type: this.selectedTransactionType,
      transactions: this.filteredStatements
    });
  }

  isReversalTransaction(statement: TransactionStatement): boolean {
    const reversalTypes = ['Refund', 'RefundRedemption', 'Reversal', 'Void', 'Cancel'];
    
    if (reversalTypes.includes(statement.transactionType || '')) {
      return true;
    }
    
    const hasNegativePoints = (statement.transactionPoints || 0) < 0;
    const hasNegativeValue = (statement.actValue || 0) < 0;
    
    return hasNegativePoints || hasNegativeValue;
  }

  getTransactionIcon(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return 'add-circle-outline';
      case 'Redemption':
        return 'remove-circle-outline';
      case 'Refund':
      case 'RefundRedemption':
        return 'refresh-circle-outline';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return 'arrow-undo-circle-outline';
      case 'Token':
      case 'TokenRedemption':
        return 'gift-outline';
      default:
        return 'pricetags-outline';
    }
  }

  getTransactionColor(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return 'success';
      case 'Redemption':
        return 'danger';
      case 'Refund':
      case 'RefundRedemption':
        return 'warning';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return 'dark';
      case 'Token':
      case 'TokenRedemption':
        return 'tertiary';
      default:
        return 'medium';
    }
  }

  getTransactionPointsClass(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return 'pcu-earned';
      case 'Redemption':
        return 'pcu-spent';
      case 'Refund':
      case 'RefundRedemption':
        return 'pcu-refund';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return 'pcu-reversal';
      default:
        return 'transaction-points';
    }
  }

  getTransactionPointsDisplay(statement: TransactionStatement): string {
    const points = statement.transactionPoints || 0;
    const transactionType = statement.transactionType;
    
    if (transactionType === 'Accrual' || transactionType === 'Refund') {
      return `+${Math.abs(points)}`;
    } else if (transactionType === 'Redemption' || transactionType === 'RefundRedemption') {
      return `-${Math.abs(points)}`;
    } else if (transactionType === 'Reversal' || transactionType === 'Void' || transactionType === 'Cancel') {
      return points >= 0 ? `+${points}` : `${points}`;
    }
    
    return points >= 0 ? `+${points}` : `${points}`;
  }

  formatDate(dateString: string | Date): string {
    if (!dateString) return '';
    
    let cleanDateString = dateString.toString();
    if (cleanDateString.includes('[UTC]')) {
      cleanDateString = cleanDateString.replace(/\[UTC\]$/, '');
    }
    
    const date = new Date(cleanDateString);
    if (isNaN(date.getTime())) return '';
    
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric'
    };
    return date.toLocaleDateString('en-US', options);
  }

  formatCurrency(value: number): string {
    if (!value && value !== 0) return '0.00';
    return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  getBaseMiles(statement: TransactionStatement): number {
    if (statement.transactionType === 'Accrual') {
      return statement.transactionPoints || 0;
    }
    return 0;
  }

  getBonusMiles(statement: TransactionStatement): number {
    return 0;
  }

  getUnitsRedeemed(statement: TransactionStatement): number {
    if (statement.transactionType === 'Redemption' || 
        statement.transactionType === 'RefundRedemption' || 
        statement.transactionType === 'Reversals') {
      return Math.abs(statement.transactionPoints || 0);
    }
    return 0;
  }

  onTransactionSelected(transaction: TransactionStatement) {
    this.transactionSelected.emit(transaction);
  }

  onLoadMore() {
    this.loadMoreRequested.emit();
  }
}
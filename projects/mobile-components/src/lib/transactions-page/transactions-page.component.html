<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">

  <!-- Transactions Section -->
  <div class="transactions-section">
    <!-- Filter Pills -->
    <lib-filter-pills
      [options]="filterOptions"
      [selected]="selectedTransactionType"
      (selectionChange)="selectTransactionType($event)">
    </lib-filter-pills>
    
    <!-- Transactions List -->
    <div class="transactions-container">
      <!-- Empty State -->
      <lib-empty-state
        *ngIf="filteredStatements.length === 0 && !loading"
        icon="receipt-outline"
        title="No transactions found"
        [message]="selectedTransactionType === 'All' ? 'You haven\'t made any transactions yet' : 'No ' + selectedTransactionType.toLowerCase() + ' transactions found'">
      </lib-empty-state>

      <!-- Loading State -->
      <lib-loading-state
        *ngIf="loading"
        message="Loading transactions...">
      </lib-loading-state>

      <!-- Transactions List -->
      <div class="transactions-list" *ngIf="filteredStatements.length > 0 && !loading">
        <lib-transaction-card
          *ngFor="let statement of filteredStatements; let i = index"
          [transaction]="{
            transactionType: statement.transactionType,
            label: statement.label?.toString(),
            invoiceNumber: statement.invoiceNumber,
            loadDate: statement.loadDate,
            actValue: statement.actValue,
            transactionPoints: statement.transactionPoints,
            baseMiles: getBaseMiles(statement),
            bonusMiles: getBonusMiles(statement),
            unitsRedeemed: getUnitsRedeemed(statement)
          }"
          [animationDelay]="i * 50"
          (click)="onTransactionSelected(statement)">
        </lib-transaction-card>
      </div>
    </div>
  </div>
</lib-page-wrapper>
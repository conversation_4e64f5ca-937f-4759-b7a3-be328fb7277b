import { TestBed } from '@angular/core/testing';
import { ComponentFixture } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { Router } from '@angular/router';
import { AccountPoolComponent } from './account-pool.component';
import { AccountPoolService, SystemService } from 'lp-client-api';
import { ReactiveFormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

class MockAccountPoolService {
  findPoolByMpacc = jasmine.createSpy('findPoolByMpacc').and.returnValue(of({ ENTITYID: 999, MPACC: 'POOL123', POOLNAME: 'Test Pool' }));
  findPool = jasmine.createSpy('findPool').and.returnValue(of({ ENTITYID: 777, MPACC: 'POOLXYZ', POOLNAME: 'X', members: [] }));
  joinPool = jasmine.createSpy('joinPool').and.returnValue(of({ status: 'OK' }));
  processPoolInvite = jasmine.createSpy('processPoolInvite').and.returnValue(of({ status: 'OK' }));
  declineInvite = jasmine.createSpy('declineInvite').and.returnValue(of({}));
  checkInviteStatus = jasmine.createSpy('checkInviteStatus').and.returnValue(of({ status: 'N' }));
}

class MockRouter { navigate = jasmine.createSpy('navigate'); }

describe('AccountPoolComponent (integration wiring)', () => {
  let component: AccountPoolComponent;
  let fixture: ComponentFixture<AccountPoolComponent>;
  let service: MockAccountPoolService;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CommonModule, ReactiveFormsModule, AccountPoolComponent],
      providers: [
        { provide: AccountPoolService, useClass: MockAccountPoolService },
        { provide: SystemService, useValue: { getCodeGroup: () => of({ codeItem: [] }) } },
        { provide: Router, useClass: MockRouter }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AccountPoolComponent);
    component = fixture.componentInstance;
    service = TestBed.inject(AccountPoolService) as unknown as MockAccountPoolService;

    component.membershipNumber = '1000101';
    fixture.detectChanges(); // triggers ngOnInit and form init
  });

  it('should call joinPool with REQS when requesting to join after lookup', () => {
    // Prepare form
    component.joinPoolForm.setValue({ poolMpacc: '********' });

    component.requestToJoinPool();

    expect(service.findPoolByMpacc).toHaveBeenCalledWith('********');
    expect(service.joinPool).toHaveBeenCalledWith(999, '1000101', 'REQS', '1000101');
  });

  it('should call joinPool with INVT when inviting a member', () => {
    component.poolInfo = { ENTITYID: 777, MPACC: 'POOLXYZ', POOLNAME: 'X', members: [] } as any;
    fixture.detectChanges();

    component.inviteMemberForm.setValue({ membershipNumber: '1000202' });

    component.inviteMember();

    expect(service.joinPool).toHaveBeenCalledWith(777, '1000202', 'INVT', '1000101');
  });

  it('should call processPoolInvite with ACCP when accepting an invite', () => {
    component.poolInfo = { ENTITYID: 555, MPACC: 'POOLABC', POOLNAME: 'Y', members: [] } as any;
    fixture.detectChanges();

    component.acceptInvite();

    expect(service.processPoolInvite).toHaveBeenCalledWith(555, '1000101', 'ACCP', '1000101');
  });

  it('should call declineInvite when declining an invitation', () => {
    component.pendingInvitePool = { ENTITYID: 333 } as any;
    fixture.detectChanges();

    component.declineInvite();

    expect(service.declineInvite).toHaveBeenCalledWith(333, '1000101', '1000101');
  });

  it('should call processPoolInvite with EXIT on exitPool when confirmed', () => {
    component.poolInfo = { ENTITYID: 999, MPACC: 'POOLXYZ', POOLNAME: 'Z', members: [] } as any;
    fixture.detectChanges();

    spyOn(window, 'confirm').and.returnValue(true);

    component.exitPool();

    expect(service.processPoolInvite).toHaveBeenCalledWith(999, '1000101', 'EXIT', '1000101');
  });
});


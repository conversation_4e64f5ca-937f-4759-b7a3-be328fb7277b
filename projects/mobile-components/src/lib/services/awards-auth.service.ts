import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface AwardsUser {
  id: string;
  mobile: string;
  password: string;
  name: string;
  surname: string;
  email: string;
  dateOfBirth: string;
  idNumber: string;
  registeredAt: string;
}

@Injectable({
  providedIn: 'root'
})
export class AwardsAuthService {
  private readonly STORAGE_KEY = 'awards_users';
  private readonly CURRENT_USER_KEY = 'awards_current_user';
  
  private currentUserSubject = new BehaviorSubject<AwardsUser | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor() {
    // Load current user from localStorage on init
    const storedUser = localStorage.getItem(this.CURRENT_USER_KEY);
    if (storedUser) {
      this.currentUserSubject.next(JSON.parse(storedUser));
    }
  }

  // Register a new user
  register(userData: {
    mobile: string;
    password: string;
    name: string;
    surname: string;
    email: string;
    dateOfBirth: string;
    idNumber: string;
  }): boolean {
    const users = this.getAllUsers();
    
    // Check if mobile already exists
    if (users.find(u => u.mobile === userData.mobile)) {
      return false; // User already exists
    }
    
    const newUser: AwardsUser = {
      ...userData,
      id: 'USER_' + Date.now(),
      registeredAt: new Date().toISOString()
    };
    
    users.push(newUser);
    this.saveUsers(users);
    
    return true;
  }

  // Login with mobile and password
  login(mobile: string, password: string): AwardsUser | null {
    const users = this.getAllUsers();
    const user = users.find(u => u.mobile === mobile && u.password === password);
    
    if (user) {
      // Set as current user
      localStorage.setItem(this.CURRENT_USER_KEY, JSON.stringify(user));
      this.currentUserSubject.next(user);
      return user;
    }
    
    return null;
  }

  // Logout current user
  logout(): void {
    localStorage.removeItem(this.CURRENT_USER_KEY);
    this.currentUserSubject.next(null);
  }

  // Get current logged in user
  getCurrentUser(): AwardsUser | null {
    return this.currentUserSubject.value;
  }

  // Check if user is logged in
  isAuthenticated(): boolean {
    return !!this.currentUserSubject.value;
  }

  // Get all registered users
  private getAllUsers(): AwardsUser[] {
    const stored = localStorage.getItem(this.STORAGE_KEY);
    return stored ? JSON.parse(stored) : [];
  }

  // Save users to localStorage
  private saveUsers(users: AwardsUser[]): void {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(users));
  }

  // Clear all demo data (for testing)
  clearAllData(): void {
    localStorage.removeItem(this.STORAGE_KEY);
    localStorage.removeItem(this.CURRENT_USER_KEY);
    this.currentUserSubject.next(null);
  }
}
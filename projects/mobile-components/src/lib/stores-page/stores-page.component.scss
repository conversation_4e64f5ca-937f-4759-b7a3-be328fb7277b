/* Modern Stores Styles */

/* Stores Section */
.stores-section {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 100px);
  background: white;
  border-radius: 24px 24px 0 0;
  overflow: hidden;
  animation: slideUp 0.6s ease-out;
}

/* Map Container */
.map-container {
  height: 30vh;
  min-height: 200px;
  position: relative;
  border-radius: 24px 24px 0 0;
  overflow: hidden;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  
  .google-map {
    width: 100%;
    height: 100%;
  }
}

/* Filter Section */
.filter-section {
  background: white;
  border-bottom: 1px solid #e0e0e0;
  animation: fadeIn 0.8s ease-out;
  animation-delay: 0.3s;
  animation-fill-mode: both;
  position: relative;
  z-index: 10;
}

.filter-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background-color: #f5f7fa;
  }
  
  .filter-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    font-weight: 600;
    color: #212121;
    
    ion-icon {
      font-size: 22px;
      color: var(--ion-color-primary, #FF6B35);
    }
  }
  
  .filter-toggle {
    font-size: 24px;
    color: #666;
    transition: transform 0.3s ease;
  }
}

.filter-content {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-in-out;
  
  &.expanded {
    max-height: 600px;
    overflow: visible;
  }
}

.filter-form {
  padding: 0 20px 20px;
}

/* Modern Input Groups */
.input-group {
  position: relative;
  margin-bottom: 16px;
  
  .input-icon {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
    color: #666;
    z-index: 1;
  }
  
  .modern-input,
  .modern-select {
    --padding-start: 48px;
    --padding-end: 16px;
    --padding-top: 12px;
    --padding-bottom: 12px;
    --background: #f5f7fa;
    --border-radius: 12px;
    --color: #212121;
    font-size: 16px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    
    &:focus {
      --background: white;
      border-color: var(--ion-color-primary, #FF6B35);
    }
  }
  
  ion-input {
    --placeholder-color: #999;
    --placeholder-opacity: 1;
    --color: #212121;
    color: #212121;
  }
  
  ion-select {
    --placeholder-color: #999;
    --placeholder-opacity: 1;
    --color: #212121;
    color: #212121;
    
    &[disabled] {
      opacity: 0.5;
    }
  }
}

/* Filter Action Buttons */
.filter-actions {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 12px;
  margin-top: 20px;
  
  .filter-btn {
    --border-radius: 12px;
    font-weight: 600;
    font-size: 14px;
    height: 44px;
    
    &.primary {
      --background: var(--ion-color-primary, #FF6B35);
      --background-hover: var(--ion-color-primary-shade);
      --color: white;
    }
    
    &.secondary {
      --border-color: #e0e0e0;
      --color: #666;
      
      &:hover {
        --background: #f5f7fa;
      }
    }
  }
}

/* Stores List Container */
.stores-list-container {
  flex: 1;
  overflow-y: auto;
  background: #f5f7fa;
  padding: 20px 20px 40px 20px;
  min-height: 40vh;
  -webkit-overflow-scrolling: touch;
}

/* List Header */
.list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  animation: fadeIn 0.6s ease-out;
  animation-delay: 0.4s;
  animation-fill-mode: both;
  
  h3 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: #212121;
  }
  
  .store-count {
    font-size: 14px;
    color: #666;
    background: white;
    padding: 6px 12px;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  }
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 60px 20px;
  animation: fadeIn 0.4s ease-out;
  
  ion-spinner {
    --color: var(--ion-color-primary, #FF6B35);
    width: 48px;
    height: 48px;
    margin-bottom: 16px;
  }
  
  p {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  animation: fadeIn 0.6s ease-out;
  
  .empty-icon {
    width: 100px;
    height: 100px;
    margin: 0 auto 24px;
    background: linear-gradient(135deg, #e8ecf1 0%, #f5f7fa 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    ion-icon {
      font-size: 48px;
      color: #c0c8d0;
    }
  }
  
  h3 {
    font-size: 20px;
    font-weight: 600;
    color: #212121;
    margin: 0 0 8px 0;
  }
  
  p {
    font-size: 16px;
    color: #666;
    margin: 0;
  }
}

/* Stores List */
.stores-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding-bottom: 80px;
}

/* Store Card */
.store-card {
  background: white;
  border-radius: 16px;
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(20px);
  animation: slideIn 0.5s ease-out forwards;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  }
  
  &:active {
    transform: scale(0.98);
  }
}

/* Store Icon */
.store-icon {
  width: 48px;
  height: 48px;
  background: rgba(255, 107, 53, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  
  ion-icon {
    font-size: 24px;
    color: var(--ion-color-primary, #FF6B35);
  }
}

/* Store Info */
.store-info {
  flex: 1;
  min-width: 0;
  
  h4 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: 600;
    color: #212121;
    text-transform: lowercase;
    
    &:first-letter {
      text-transform: uppercase;
    }
  }
  
  .store-address {
    margin: 0;
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: flex-start;
    gap: 6px;
    line-height: 1.4;
    
    ion-icon {
      font-size: 16px;
      color: #999;
      flex-shrink: 0;
      margin-top: 2px;
    }
  }
}

/* Store Arrow */
.store-arrow {
  font-size: 20px;
  color: #c0c8d0;
  flex-shrink: 0;
}

/* Animations */
@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Ion-select popover */
::ng-deep {
  ion-popover.select-popover-upward {
    .popover-content {
      max-height: 250px !important;
      overflow-y: auto !important;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12) !important;
      border-radius: 12px !important;
    }
    
    ion-list {
      max-height: 240px;
      overflow-y: auto;
    }
  }
}

/* Responsive Design */
@media (max-width: 375px) {
  .stores-section {
    height: calc(100vh - 80px);
  }
  
  .map-container {
    height: 25vh;
    min-height: 180px;
  }
  
  .filter-header {
    padding: 14px 16px;
    
    .filter-title {
      font-size: 15px;
      gap: 10px;
      
      ion-icon {
        font-size: 20px;
      }
    }
  }
  
  .filter-form {
    padding: 0 16px 16px;
  }
  
  .input-group {
    margin-bottom: 12px;
    
    .input-icon {
      left: 12px;
      font-size: 18px;
    }
    
    .modern-input,
    .modern-select {
      --padding-start: 40px;
      --padding-top: 10px;
      --padding-bottom: 10px;
      font-size: 15px;
    }
  }
  
  .filter-actions {
    grid-template-columns: 1fr;
    
    .filter-btn {
      font-size: 13px;
      height: 40px;
    }
  }
  
  .stores-list-container {
    padding: 16px;
  }
  
  .list-header {
    margin-bottom: 16px;
    
    h3 {
      font-size: 18px;
    }
    
    .store-count {
      font-size: 13px;
      padding: 4px 10px;
    }
  }
  
  .store-card {
    padding: 14px;
    gap: 12px;
  }
  
  .store-icon {
    width: 40px;
    height: 40px;
    
    ion-icon {
      font-size: 20px;
    }
  }
  
  .store-info {
    h4 {
      font-size: 15px;
    }
    
    .store-address {
      font-size: 13px;
      
      ion-icon {
        font-size: 14px;
      }
    }
  }
}
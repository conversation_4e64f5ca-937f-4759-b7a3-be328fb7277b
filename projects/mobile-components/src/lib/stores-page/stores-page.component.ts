import {
  Component,
  Injector,
  <PERSON><PERSON><PERSON>roy,
  ViewChild,
  ChangeDetectorRef,
  Input,
  Output,
  EventEmitter,
  OnInit,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { GoogleMapsModule, GoogleMap } from '@angular/google-maps';
import { IonicModule } from '@ionic/angular';
import {
  Address,
  CodeItem,
  KeyCloakService,
  Partner,
  PartnerListRequest,
  PartnerService,
  SystemService,
  Telephone,
  MemberProfile,
  MemberService,
  HttpClientService,
  LssConfig,
} from 'lp-client-api';

import { AbstractComponent } from '../abstract.component';
import { BehaviorSubject, Observable, Subscription, map, toArray } from 'rxjs';

@Component({
  selector: 'lib-stores-page',
  templateUrl: 'stores-page.component.html',
  styleUrls: ['stores-page.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    GoogleMapsModule
  ]
})
export class StoresPageComponent extends AbstractComponent implements OnInit, OnDestroy {
  // Inputs for dynamic configuration
  @Input() profile?: MemberProfile;
  @Input() memberService!: MemberService;
  @Input() partnerService!: PartnerService;
  @Input() systemService!: SystemService;
  @Input() keyCloakService!: KeyCloakService;
  @Input() lssConfig!: LssConfig;
  @Input() initialCenter?: google.maps.LatLngLiteral;
  @Input() initialZoom?: number;
  @Input() enableClustering: boolean = true;

  // Output events for navigation and actions
  @Output() storeSelected = new EventEmitter<Partner>();
  @Output() markerSelected = new EventEmitter<Partner>();
  @Output() filtersChanged = new EventEmitter<any>();
  @Output() searchChanged = new EventEmitter<string>();
  @Output() ready = new EventEmitter<void>();

  // Store data
  allStores: Partner[] = [];
  filteredStores: Partner[] = [];
  hashMap = new Map<string, null>();
  storeList: BehaviorSubject<Partner[]> = new BehaviorSubject<Partner[]>([]);
  storeMarkers: any[] = [];
  
  // Map configuration
  provinces!: Observable<CodeItem[]>;
  cities!: Observable<CodeItem[]>;
  @ViewChild(GoogleMap, { static: false }) map?: GoogleMap;
  center!: google.maps.LatLngLiteral;
  options: google.maps.MapOptions = {
    zoomControl: false,
    maxZoom: 18,
    minZoom: 4,
    streetViewControl: false,
  };
  zoom!: number;
  markerClustererImagePath =
    'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m';
  
  // Filter state
  provinceFilter?: string | null = '';
  cityFilter?: string | null = '';
  searchFilter: string = '';
  filterExpanded: boolean = false;

  // Component state - use the inherited loading getter/setter

  constructor(
    injector: Injector,
    private cd: ChangeDetectorRef
  ) {
    super(injector);
  }

  ngOnInit() {
    if (this.profile) {
      this.detectChanges();
    }

    this.addGlobalSubscription(
      this.storeList.subscribe(() => {
        this.detectChanges();
      })
    );
    
    this.getProvinces();
    
    // Initialize immediately if we have required services
    if (this.partnerService && this.systemService) {
      this.init();
    }

    // Emit ready event
    setTimeout(() => this.ready.emit(), 100);
  }

  /* Check if filters must be reset when entering the view */
  override ionViewDidEnter(): void {
    this.loading = true;
    if (this.filteredStores !== undefined && this.filteredStores.length > 0) {
      this.resetFilter();
    } else if (this.allStores.length == 0) {
      this.init();
    }
  }

  /* Initialise the map position and fetch all stores */
  private async init() {
    // Use provided initial center or get user location
    if (this.initialCenter) {
      this.setMapCenter(
        this.initialCenter.lat,
        this.initialCenter.lng,
        this.initialZoom || 12
      );
    } else {
      // User location
      navigator.geolocation.getCurrentPosition((position) => {
        this.setMapCenter(
          position.coords.latitude,
          position.coords.longitude,
          12
        );
      });
    }

    // Default location if user location is not provided
    if (this.center === undefined) {
      const defaultLat = this.lssConfig?.defaultLat ?? -28.83688693522886;
      const defaultLng = this.lssConfig?.defaultLng ?? 25.49975999318031;
      this.setMapCenter(defaultLat, defaultLng, 4);
    }
    
    this.getStores();
  }

  /* Get all available stores */
  private getStores(): void {
    if (!this.partnerService) {
      console.error('PartnerService not provided to StoresPageComponent');
      return;
    }

    this.loading = true;
    
    this.partnerService.getPartnersAll().subscribe({
      next: (result: any) => this.partnerListResponse(result),
      error: (e) => {
        this.loading = false;
        console.error('Error loading Store Details:', e);
      },
    });
  }
  
  private partnerListResponse(result: any) {
    if (result) {
      result.forEach((store: Partner) => {
        if (
          store.getAddressByType('PADR') &&
          store.getPartnerMoreByType('LOCATION')
        ) {
          this.allStores.push(store);
          let city = store.getAddressByType('PADR')?.city;
          this.hashMap.set(city ? city : '', null);
        }
      });
      this.resetFilter();
    }
    this.loading = false;
  }

  /* Add a Store to the list of markers to be displayed on the map */
  private async addMarker(store: Partner) {
    if (
      store.getPartnerMoreByType('LOCATION') !== undefined &&
      store.partnerName !== undefined &&
      store.partnerId !== undefined
    ) {
      this.storeMarkers.push({
        position: {
          lat: Number(
            store.getPartnerMoreByType('LOCATION')?.value.split(',')[0]
          ),
          lng: Number(
            store.getPartnerMoreByType('LOCATION')?.value.split(',')[1]
          ),
        },
        store: store,
      });
    }
  }

  /* Handle click event on map marker */
  markerClick(marker: any) {
    this.setSelectedStore(marker['store']);
    this.markerSelected.emit(marker['store']);
  }

  /* Handle map bound change to list stores that are within the updated bounds */
  boundsChanged() {
    if (
      (this.provinceFilter === undefined || this.provinceFilter === null) &&
      (this.cityFilter === undefined || this.cityFilter === null)
    ) {
      this.filter(true);
    } else {
      return;
    }
  }

  setMapCenter(lat: number, lng: number, zoom: number): void {
    this.center = {
      lat: lat,
      lng: lng,
    };
    this.zoom = zoom;
  }

  /* Navigate to the store detail for the selected store/marker */
  setSelectedStore(partner: Partner) {
    this.storeSelected.emit(partner);
  }

  /* Filter the list of stores */
  filter(filterEmpty: boolean): void {
    this.storeMarkers = [];
    
    if (filterEmpty) {
      // Filter stores within map bounds
      let topBound: any = this.map?.getBounds()?.getNorthEast().lat();
      let bottomBound: any = this.map?.getBounds()?.getSouthWest().lat();
      let leftBound: any = this.map?.getBounds()?.getSouthWest().lng();
      let rightBound: any = this.map?.getBounds()?.getNorthEast().lng();

      this.filteredStores = this.allStores.filter((store) => {
        let locationSplit = store
          .getPartnerMoreByType('LOCATION')
          ?.value?.split(',') || ['', ''];

        var liesWithinLatBounds =
          topBound > Number(locationSplit[0]) &&
          bottomBound <= Number(locationSplit[0]);
        var liesWithinLngBounds =
          leftBound <= Number(locationSplit[1]) &&
          rightBound >= Number(locationSplit[1]);

        return liesWithinLatBounds && liesWithinLngBounds;
      });
    } else {
      this.cityFilter = this.cityFilter || '';
      this.provinceFilter = this.provinceFilter || '';
      
      if (this.provinceFilter.length > 0) {
        this.filteredStores = this.allStores.filter((partner) => {
          const adr: any = partner.getAddressByType('PADR');
          let isFilter = false;
          
          if (
            partner.partnerName
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.provinceDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.cityDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.line1
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.suburbDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase())
          ) {
            isFilter = true;
          }

          if (adr && isFilter) {
            return (
              adr.province === this.provinceFilter &&
              (this.cityFilter === '' || adr.city === this.cityFilter)
            );
          }
          return false;
        });
      } else {
        this.filteredStores = this.allStores.filter((partner) => {
          const adr: any = partner.getAddressByType('PADR');
          let isFilter = false;
          
          if (
            partner.partnerName
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.provinceDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.cityDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.line1
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase()) ||
            adr.suburbDesc
              ?.toLocaleLowerCase()
              .includes(this.searchFilter.toLocaleLowerCase())
          ) {
            isFilter = true;
          }
          
          if (adr && isFilter) {
            return isFilter;
          }
          return false;
        });
      }
      
      if (this.filteredStores.length > 0) {
        this.setMapCenter(
          Number(
            this.filteredStores[0]
              .getPartnerMoreByType('LOCATION')
              ?.value.split(',')[0]
          ),
          Number(
            this.filteredStores[0]
              .getPartnerMoreByType('LOCATION')
              ?.value.split(',')[1]
          ),
          4
        );
      }
    }

    // Set map markers based on filtered stores
    for (var store of this.filteredStores) {
      this.addMarker(store);
    }
    
    this.storeList.next(this.filteredStores);
    
    // Emit filter changed event
    this.filtersChanged.emit({
      province: this.provinceFilter,
      city: this.cityFilter,
      search: this.searchFilter
    });
  }

  /* Reset any filtering of stores */
  resetFilter(): void {
    this.provinceFilter = null;
    this.cityFilter = null;
    this.searchFilter = '';
    
    navigator.geolocation.getCurrentPosition((position) => {
      this.setMapCenter(
        position.coords.latitude,
        position.coords.longitude,
        12
      );
    });
    
    this.filter(true);
  }

  /* Get list of provinces for filtering */
  getProvinces() {
    if (this.systemService) {
      this.provinces = this.systemService.listProvice();
    }
  }

  /* Set selected province filter value */
  setProvinceFilter(filter: string) {
    this.provinceFilter = filter;
    this.getCities(this.provinceFilter);
  }

  /* Get list of cities for filtering */
  getCities(filter: string) {
    if (this.systemService) {
      this.cities = this.systemService
        .listCity('', filter)
        .pipe(
          map((data: CodeItem[]) =>
            data.filter((record) => this.hashMap.has(record.value))
          )
        );
    }
  }

  /* Set selected city filter value */
  setCityFilter(filter: string) {
    this.cityFilter = filter;
  }

  /* Toggle filter expansion */
  toggleFilter() {
    this.filterExpanded = !this.filterExpanded;
  }

  /* Handle search input changes */
  onSearchChange(event: any) {
    this.searchFilter = event.detail.value || '';
    this.searchChanged.emit(this.searchFilter);
  }

  public override detectChanges() {
    this.cd.detectChanges();
  }

  override ngOnDestroy() {
    super.ngOnDestroy();
  }
}
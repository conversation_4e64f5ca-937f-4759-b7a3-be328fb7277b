import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { ComponentsModule } from '../components.module';

export interface SettingsItem {
  title: string;
  subtitle?: string;
  icon: string;
  path?: string;
  action?: string;
  color?: string;
  disabled?: boolean;
  divider?: boolean;
  section?: string;
}

export interface SettingsProfile {
  givenNames?: string;
  surname?: string;
  username?: string;
  membershipNumber?: string;
  avatar?: string;
}

@Component({
  selector: 'lib-settings-page',
  templateUrl: './settings-page.component.html',
  styleUrls: ['./settings-page.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ComponentsModule
  ]
})
export class SettingsPageComponent implements OnInit, OnDestroy {
  @Input() profile?: SettingsProfile;
  @Input() settingsItems?: SettingsItem[];
  @Input() showUserProfile: boolean = true;
  @Input() defaultAvatar: string = 'assets/images/avatar.png';

  @Output() itemSelected = new EventEmitter<SettingsItem>();
  @Output() profileTapped = new EventEmitter<SettingsProfile>();
  @Output() avatarTapped = new EventEmitter<SettingsProfile>();

  // Default settings items if none provided
  defaultSettings: SettingsItem[] = [
    {
      title: 'Profile',
      subtitle: 'Edit your profile information',
      icon: 'person-outline',
      path: '/secure/profile',
      color: 'primary',
      section: 'Account'
    },
    {
      title: 'Notifications',
      subtitle: 'Manage notification preferences',
      icon: 'notifications-outline',
      path: '/secure/notification-settings',
      color: 'primary',
      section: 'Account'
    },
    {
      title: 'Privacy',
      subtitle: 'Privacy settings',
      icon: 'lock-closed-outline',
      path: '/secure/security',
      color: 'warning',
      section: 'Account'
    },
    {
      title: 'Password',
      subtitle: 'Change your password',
      icon: 'key-outline',
      path: '/secure/password',
      color: 'danger',
      section: 'Security'
    },
    {
      title: '2-Step Verification',
      subtitle: 'Enable two-factor authentication',
      icon: 'shield-checkmark-outline',
      action: 'enable-2fa',
      color: 'success',
      section: 'Security'
    },
    {
      title: 'Device History',
      subtitle: 'View login devices',
      icon: 'phone-portrait-outline',
      action: 'device-history',
      color: 'medium',
      section: 'Security'
    },
    {
      title: 'Contact Us',
      subtitle: 'Get help and support',
      icon: 'mail-outline',
      path: '/secure/contactus',
      color: 'success',
      section: 'Support'
    },
    {
      title: 'Terms and Conditions',
      subtitle: 'Read our terms',
      icon: 'document-text-outline',
      action: 'terms',
      color: 'medium',
      section: 'Support'
    },
    {
      title: 'About',
      subtitle: 'App version and info',
      icon: 'information-circle-outline',
      action: 'about',
      color: 'tertiary',
      section: 'Support'
    }
  ];

  constructor(private router: Router) {}

  ngOnInit() {
    console.log('SettingsPageComponent initialized');
  }

  ngOnDestroy() {
    // Clean up subscriptions if any
  }

  get settingsToDisplay(): SettingsItem[] {
    return this.settingsItems || this.defaultSettings;
  }

  get groupedSettings() {
    const grouped: { [key: string]: SettingsItem[] } = {};
    
    this.settingsToDisplay.forEach(item => {
      const section = item.section || 'General';
      if (!grouped[section]) {
        grouped[section] = [];
      }
      grouped[section].push(item);
    });
    
    return grouped;
  }

  get sections(): string[] {
    return Object.keys(this.groupedSettings);
  }

  onItemClick(item: SettingsItem) {
    console.log('Settings item clicked:', item);
    
    // Emit the item selection event
    this.itemSelected.emit(item);
    
    // Handle navigation or actions
    if (item.path) {
      this.navigateToPath(item.path);
    } else if (item.action) {
      this.handleAction(item.action, item);
    }
  }

  private navigateToPath(path: string) {
    try {
      this.router.navigate([path]);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  }

  private handleAction(action: string, item: SettingsItem) {
    switch (action) {
      case 'enable-2fa':
        console.log('Enable 2FA action');
        // Implement 2FA enablement logic
        break;
      case 'device-history':
        console.log('Show device history');
        // Implement device history logic
        break;
      case 'terms':
        console.log('Show terms and conditions');
        // Implement terms display logic
        break;
      case 'about':
        console.log('Show about information');
        // Implement about page logic
        break;
      default:
        console.log('Unknown action:', action);
    }
  }

  onProfileTap() {
    console.log('Profile tapped');
    if (this.profile) {
      this.profileTapped.emit(this.profile);
    }
  }

  onAvatarTap() {
    console.log('Avatar tapped');
    if (this.profile) {
      this.avatarTapped.emit(this.profile);
    }
  }

  get displayName(): string {
    if (this.profile) {
      const firstName = this.profile.givenNames || '';
      const lastName = this.profile.surname || '';
      return `${firstName} ${lastName}`.trim() || this.profile.username || 'User';
    }
    return 'User';
  }

  get displayUsername(): string {
    return this.profile?.username || '';
  }

  get displayMemberNumber(): string {
    return this.profile?.membershipNumber || '';
  }

  get avatarSrc(): string {
    return this.profile?.avatar || this.defaultAvatar;
  }

  getItemColor(item: SettingsItem): string {
    return item.color || 'medium';
  }

  isItemDisabled(item: SettingsItem): boolean {
    return item.disabled === true;
  }
}
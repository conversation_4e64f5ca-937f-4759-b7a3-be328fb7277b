/* Modern Settings Page Styles */

/* Main Container */
.settings-page {
  width: 100%;
  height: 100%;
  padding: 0;
  background: #f8f9fa;
}

/* Profile Section */
.profile-section {
  background: white;
  padding: 32px 24px;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
  margin-bottom: 24px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  
  &:hover {
    background: #f8f9fa;
  }
}

.avatar-container {
  display: inline-block;
  position: relative;
  margin-bottom: 16px;
  
  .profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid #fff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    cursor: pointer;
    
    &:hover {
      transform: scale(1.05);
    }
  }
}

.profile-info {
  .profile-name {
    font-size: 24px;
    font-weight: 600;
    color: #212121;
    margin: 0 0 8px 0;
  }
  
  .profile-username {
    font-size: 16px;
    color: #6c757d;
    margin: 0 0 4px 0;
  }
  
  .profile-member-number {
    font-size: 14px;
    color: #6c757d;
    margin: 0;
  }
}

/* Settings Sections */
.settings-sections {
  padding: 0 16px 32px;
  
  @media (min-width: 768px) {
    padding: 0 24px 32px;
    max-width: 800px;
    margin: 0 auto;
  }
}

.settings-section {
  margin-bottom: 32px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-header {
  margin-bottom: 12px;
  
  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #495057;
    margin: 0;
    padding: 0 8px;
  }
}

/* Settings List */
.settings-list {
  background: white;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

.settings-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0;
  transform: translateY(10px);
  animation: slideInUp 0.4s ease-out forwards;
  
  &:hover {
    background: #f8f9fa;
  }
  
  &:last-child {
    border-bottom: none;
  }
  
  &.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    
    &:hover {
      background: transparent;
    }
  }
}

/* Item Icon */
.item-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
  background: rgba(var(--ion-color-primary-rgb, 255, 107, 53), 0.1);
  
  ion-icon {
    font-size: 20px;
    color: var(--ion-color-primary, #FF6B35);
  }
  
  // Color variants
  &[data-color="primary"] {
    background: rgba(var(--ion-color-primary-rgb, 255, 107, 53), 0.1);
    
    ion-icon {
      color: var(--ion-color-primary, #FF6B35);
    }
  }
  
  &[data-color="secondary"] {
    background: rgba(var(--ion-color-secondary-rgb, 0, 114, 188), 0.1);
    
    ion-icon {
      color: var(--ion-color-secondary, #0072bc);
    }
  }
  
  &[data-color="tertiary"] {
    background: rgba(var(--ion-color-tertiary-rgb, 111, 66, 193), 0.1);
    
    ion-icon {
      color: var(--ion-color-tertiary, #6f42c1);
    }
  }
  
  &[data-color="success"] {
    background: rgba(40, 167, 69, 0.1);
    
    ion-icon {
      color: #28a745;
    }
  }
  
  &[data-color="warning"] {
    background: rgba(255, 193, 7, 0.1);
    
    ion-icon {
      color: #ffc107;
    }
  }
  
  &[data-color="danger"] {
    background: rgba(220, 53, 69, 0.1);
    
    ion-icon {
      color: #dc3545;
    }
  }
  
  &[data-color="medium"] {
    background: rgba(108, 117, 125, 0.1);
    
    ion-icon {
      color: #6c757d;
    }
  }
  
  &[data-color="dark"] {
    background: rgba(52, 58, 64, 0.1);
    
    ion-icon {
      color: #343a40;
    }
  }
}

/* Item Content */
.item-content {
  flex: 1;
  min-width: 0;
  
  .item-title {
    font-size: 16px;
    font-weight: 500;
    color: #212121;
    margin-bottom: 2px;
  }
  
  .item-subtitle {
    font-size: 14px;
    color: #6c757d;
    line-height: 1.4;
  }
}

/* Item Arrow */
.item-arrow {
  margin-left: 12px;
  flex-shrink: 0;
  
  ion-icon {
    font-size: 18px;
    color: #adb5bd;
    transition: transform 0.2s ease;
  }
}

.settings-item:hover .item-arrow ion-icon {
  transform: translateX(2px);
  color: #6c757d;
}

/* Animations */
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-section {
    padding: 24px 16px;
  }
  
  .avatar-container .profile-avatar {
    width: 100px;
    height: 100px;
  }
  
  .profile-info .profile-name {
    font-size: 20px;
  }
  
  .settings-sections {
    padding: 0 12px 24px;
  }
  
  .settings-item {
    padding: 14px 16px;
  }
  
  .item-icon {
    width: 36px;
    height: 36px;
    margin-right: 12px;
    
    ion-icon {
      font-size: 18px;
    }
  }
  
  .item-content {
    .item-title {
      font-size: 15px;
    }
    
    .item-subtitle {
      font-size: 13px;
    }
  }
}

@media (max-width: 480px) {
  .profile-section {
    padding: 20px 12px;
  }
  
  .profile-info .profile-name {
    font-size: 18px;
  }
  
  .section-header .section-title {
    font-size: 16px;
    padding: 0 4px;
  }
  
  .settings-item {
    padding: 12px 12px;
  }
  
  .item-content {
    .item-title {
      font-size: 14px;
    }
    
    .item-subtitle {
      font-size: 12px;
    }
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .settings-page {
    background: #1a1a1a;
  }
  
  .profile-section {
    background: #2d2d2d;
    border-bottom-color: #404040;
    
    &:hover {
      background: #333333;
    }
  }
  
  .profile-info {
    .profile-name {
      color: #ffffff;
    }
    
    .profile-username,
    .profile-member-number {
      color: #b0b0b0;
    }
  }
  
  .settings-list {
    background: #2d2d2d;
  }
  
  .settings-item {
    border-bottom-color: #404040;
    
    &:hover {
      background: #333333;
    }
  }
  
  .item-content {
    .item-title {
      color: #ffffff;
    }
    
    .item-subtitle {
      color: #b0b0b0;
    }
  }
  
  .section-header .section-title {
    color: #e0e0e0;
  }
}
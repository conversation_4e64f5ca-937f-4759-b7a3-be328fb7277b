<!-- Settings Page Component -->
<div class="settings-page">
  <!-- User Profile Section -->
  <div class="profile-section" *ngIf="showUserProfile" (click)="onProfileTap()">
    <div class="avatar-container" (click)="onAvatarTap(); $event.stopPropagation()">
      <img [src]="avatarSrc" [alt]="displayName + ' avatar'" class="profile-avatar">
    </div>
    <div class="profile-info">
      <h2 class="profile-name">{{ displayName }}</h2>
      <p class="profile-username" *ngIf="displayUsername">{{ displayUsername }}</p>
      <p class="profile-member-number" *ngIf="displayMemberNumber">{{ displayMemberNumber }}</p>
    </div>
  </div>

  <!-- Settings Sections -->
  <div class="settings-sections">
    <div class="settings-section" *ngFor="let section of sections">
      <!-- Section Header -->
      <div class="section-header">
        <h3 class="section-title">{{ section }}</h3>
      </div>
      
      <!-- Settings Items -->
      <div class="settings-list">
        <div 
          class="settings-item"
          *ngFor="let item of groupedSettings[section]; let i = index"
          [class.disabled]="isItemDisabled(item)"
          [style.animation-delay.ms]="i * 50"
          (click)="!isItemDisabled(item) && onItemClick(item)">
          
          <!-- Item Icon -->
          <div class="item-icon" [attr.data-color]="getItemColor(item)">
            <ion-icon [name]="item.icon"></ion-icon>
          </div>
          
          <!-- Item Content -->
          <div class="item-content">
            <div class="item-title">{{ item.title }}</div>
            <div class="item-subtitle" *ngIf="item.subtitle">{{ item.subtitle }}</div>
          </div>
          
          <!-- Item Arrow -->
          <div class="item-arrow" *ngIf="!isItemDisabled(item)">
            <ion-icon name="chevron-forward-outline"></ion-icon>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
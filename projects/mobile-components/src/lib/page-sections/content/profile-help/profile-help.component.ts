import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-profile-help',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="profile-help" [ngClass]="config?.class">
      <!-- Section Title -->
      <h2 class="section-title" *ngIf="config?.title">{{ config.title }}</h2>
      
      <!-- Help Categories -->
      <div class="help-container">
        
        <!-- Quick Help Actions -->
        <div class="help-section" *ngIf="config?.showQuickActions">
          <h3 class="section-subtitle">{{ config?.quickActionsTitle || 'Quick Help' }}</h3>
          <div class="quick-actions-grid">
            <div 
              *ngFor="let action of getQuickActions()"
              class="quick-action-item"
              [ngClass]="action.class"
              (click)="onActionClick(action)"
            >
              <div class="action-icon">
                <ion-icon [name]="action.icon"></ion-icon>
              </div>
              <div class="action-content">
                <div class="action-label">{{ action.label }}</div>
                <div class="action-description" *ngIf="action.description">{{ action.description }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Support -->
        <div class="help-section" *ngIf="config?.showContactSupport">
          <h3 class="section-subtitle">{{ config?.contactSupportTitle || 'Contact Support' }}</h3>
          <div class="contact-options">
            <div 
              *ngFor="let contact of getContactOptions()"
              class="contact-item"
              [ngClass]="contact.class"
              (click)="onContactClick(contact)"
            >
              <div class="contact-icon">
                <ion-icon [name]="contact.icon"></ion-icon>
              </div>
              <div class="contact-content">
                <div class="contact-label">{{ contact.label }}</div>
                <div class="contact-description" *ngIf="contact.description">{{ contact.description }}</div>
                <div class="contact-value" *ngIf="contact.value">{{ contact.value }}</div>
              </div>
              <div class="contact-action">
                <ion-icon name="chevron-forward-outline"></ion-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- FAQ Section -->
        <div class="help-section" *ngIf="config?.showFAQ">
          <h3 class="section-subtitle">{{ config?.faqTitle || 'Frequently Asked Questions' }}</h3>
          <div class="faq-list">
            <div 
              *ngFor="let faq of getFAQItems(); let i = index"
              class="faq-item"
              [class.expanded]="faq.expanded"
            >
              <div class="faq-question" (click)="toggleFAQ(i)">
                <span class="question-text">{{ faq.question }}</span>
                <ion-icon 
                  [name]="faq.expanded ? 'chevron-up-outline' : 'chevron-down-outline'"
                  class="expand-icon"
                ></ion-icon>
              </div>
              <div class="faq-answer" *ngIf="faq.expanded">
                <p [innerHTML]="faq.answer"></p>
              </div>
            </div>
          </div>
        </div>

        <!-- App Information -->
        <div class="help-section" *ngIf="config?.showAppInfo">
          <h3 class="section-subtitle">{{ config?.appInfoTitle || 'App Information' }}</h3>
          <div class="app-info-grid">
            <div class="info-item" *ngFor="let info of getAppInfo()">
              <div class="info-label">{{ info.label }}</div>
              <div class="info-value">{{ info.value }}</div>
            </div>
          </div>
        </div>

        <!-- Feedback Section -->
        <div class="help-section" *ngIf="config?.showFeedback">
          <h3 class="section-subtitle">{{ config?.feedbackTitle || 'Send Feedback' }}</h3>
          <div class="feedback-container">
            <p class="feedback-description">
              {{ config?.feedbackDescription || 'Help us improve by sharing your feedback' }}
            </p>
            <button 
              class="feedback-button"
              (click)="onFeedbackClick()"
            >
              <ion-icon name="chatbubble-outline"></ion-icon>
              <span>{{ config?.feedbackButtonLabel || 'Send Feedback' }}</span>
            </button>
          </div>
        </div>

      </div>
    </div>
  `,
  styles: [`
    .profile-help {
      padding: 1rem;
      margin-bottom: 1rem;
    }

    .section-title {
      font-size: 1.3rem;
      font-weight: bold;
      margin-bottom: 1.5rem;
      color: var(--ion-color-dark);
    }

    .help-container {
      display: grid;
      gap: 1.5rem;
    }

    .help-section {
      background: white;
      border-radius: 1rem;
      padding: 1.5rem;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid var(--ion-color-light);
    }

    .section-subtitle {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--ion-color-primary);
      border-bottom: 2px solid var(--ion-color-light);
      padding-bottom: 0.5rem;
    }

    /* Quick Actions */
    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 1rem;
    }

    .quick-action-item {
      display: flex;
      align-items: center;
      padding: 1rem;
      border: 1px solid var(--ion-color-light);
      border-radius: 0.75rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .quick-action-item:hover {
      background: rgba(var(--ion-color-primary-rgb), 0.05);
      border-color: var(--ion-color-primary);
      transform: translateY(-1px);
    }

    .action-icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(var(--ion-color-primary-rgb), 0.1);
      border-radius: 50%;
      margin-right: 0.75rem;
    }

    .action-icon ion-icon {
      font-size: 1.2rem;
      color: var(--ion-color-primary);
    }

    .action-content {
      flex: 1;
    }

    .action-label {
      font-size: 1rem;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin-bottom: 0.25rem;
    }

    .action-description {
      font-size: 0.85rem;
      color: var(--ion-color-medium);
      line-height: 1.4;
    }

    /* Contact Options */
    .contact-options {
      display: grid;
      gap: 0.75rem;
    }

    .contact-item {
      display: flex;
      align-items: center;
      padding: 1rem;
      border: 1px solid var(--ion-color-light);
      border-radius: 0.75rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .contact-item:hover {
      background: rgba(var(--ion-color-primary-rgb), 0.05);
      border-color: var(--ion-color-primary);
    }

    .contact-icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(var(--ion-color-success-rgb), 0.1);
      border-radius: 50%;
      margin-right: 0.75rem;
    }

    .contact-icon ion-icon {
      font-size: 1.2rem;
      color: var(--ion-color-success);
    }

    .contact-content {
      flex: 1;
    }

    .contact-label {
      font-size: 1rem;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin-bottom: 0.25rem;
    }

    .contact-description {
      font-size: 0.85rem;
      color: var(--ion-color-medium);
      margin-bottom: 0.25rem;
    }

    .contact-value {
      font-size: 0.9rem;
      color: var(--ion-color-primary);
      font-weight: 500;
    }

    .contact-action {
      flex-shrink: 0;
      margin-left: 0.75rem;
    }

    .contact-action ion-icon {
      font-size: 1.1rem;
      color: var(--ion-color-medium);
    }

    /* FAQ */
    .faq-list {
      display: grid;
      gap: 0.75rem;
    }

    .faq-item {
      border: 1px solid var(--ion-color-light);
      border-radius: 0.75rem;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .faq-item.expanded {
      border-color: var(--ion-color-primary);
    }

    .faq-question {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 1rem;
      cursor: pointer;
      background: white;
      transition: all 0.3s ease;
    }

    .faq-question:hover {
      background: rgba(var(--ion-color-primary-rgb), 0.05);
    }

    .question-text {
      font-size: 0.95rem;
      font-weight: 600;
      color: var(--ion-color-dark);
    }

    .expand-icon {
      font-size: 1.1rem;
      color: var(--ion-color-medium);
      transition: transform 0.3s ease;
    }

    .faq-answer {
      padding: 0 1rem 1rem 1rem;
      background: rgba(var(--ion-color-primary-rgb), 0.02);
    }

    .faq-answer p {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      line-height: 1.5;
      margin: 0;
    }

    /* App Info */
    .app-info-grid {
      display: grid;
      gap: 0.75rem;
    }

    .info-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid var(--ion-color-light);
    }

    .info-item:last-child {
      border-bottom: none;
    }

    .info-label {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
    }

    .info-value {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--ion-color-dark);
    }

    /* Feedback */
    .feedback-container {
      text-align: center;
    }

    .feedback-description {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      margin-bottom: 1rem;
      line-height: 1.5;
    }

    .feedback-button {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      background: var(--ion-color-primary);
      color: white;
      border: none;
      border-radius: 0.75rem;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .feedback-button:hover {
      background: var(--ion-color-primary-shade);
      transform: translateY(-1px);
    }

    .feedback-button ion-icon {
      font-size: 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .profile-help {
        padding: 0.75rem;
      }

      .help-section {
        padding: 1rem;
      }

      .quick-actions-grid {
        grid-template-columns: 1fr;
      }

      .quick-action-item, .contact-item {
        padding: 0.875rem;
      }

      .action-icon, .contact-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.5rem;
      }

      .action-icon ion-icon, .contact-icon ion-icon {
        font-size: 1.1rem;
      }
    }

    @media (max-width: 480px) {
      .section-title {
        font-size: 1.2rem;
      }

      .section-subtitle {
        font-size: 1rem;
      }

      .quick-action-item, .contact-item {
        padding: 0.75rem;
      }
    }
  `]
})
export class ProfileHelpComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() navigationEvent = new EventEmitter<string>();

  private defaultQuickActions = [
    {
      label: 'Reset Password',
      description: 'Reset your account password',
      icon: 'key-outline',
      action: 'resetPassword'
    },
    {
      label: 'Update Profile',
      description: 'Edit your profile information',
      icon: 'person-outline',
      path: '/secure/profile/edit'
    },
    {
      label: 'Privacy Settings',
      description: 'Manage your privacy preferences',
      icon: 'shield-outline',
      path: '/secure/privacy'
    }
  ];

  private defaultContactOptions = [
    {
      label: 'Email Support',
      description: 'Send us an email',
      value: '<EMAIL>',
      icon: 'mail-outline',
      action: 'emailSupport'
    },
    {
      label: 'Phone Support',
      description: 'Call our support team',
      value: '+****************',
      icon: 'call-outline',
      action: 'phoneSupport'
    },
    {
      label: 'Live Chat',
      description: 'Chat with our support team',
      value: 'Available 24/7',
      icon: 'chatbubble-outline',
      action: 'liveChat'
    }
  ];

  private defaultFAQItems = [
    {
      question: 'How do I update my profile information?',
      answer: 'You can update your profile by going to Settings > Edit Profile and making the necessary changes.',
      expanded: false
    },
    {
      question: 'How do I reset my password?',
      answer: 'Click on "Forgot Password" on the login page or go to Settings > Security > Change Password.',
      expanded: false
    },
    {
      question: 'How do I contact customer support?',
      answer: 'You can contact us via email, phone, or live chat. All contact options are available in the Help section.',
      expanded: false
    }
  ];

  private defaultAppInfo = [
    { label: 'App Version', value: '1.0.0' },
    { label: 'Last Updated', value: 'January 2025' },
    { label: 'Platform', value: 'iOS/Android' },
    { label: 'Support', value: '<EMAIL>' }
  ];

  constructor() {}

  ngOnInit() {
    // Set default configuration if not provided
    if (!this.config) {
      this.config = {
        title: 'Help & Support',
        showQuickActions: true,
        showContactSupport: true,
        showFAQ: true,
        showAppInfo: true,
        showFeedback: true
      };
    }
  }

  getQuickActions() {
    return this.config?.quickActions || this.defaultQuickActions;
  }

  getContactOptions() {
    return this.config?.contactOptions || this.defaultContactOptions;
  }

  getFAQItems() {
    return this.config?.faqItems || this.defaultFAQItems;
  }

  getAppInfo() {
    return this.config?.appInfo || this.defaultAppInfo;
  }

  onActionClick(action: any) {
    if (action.path) {
      this.navigationEvent.emit(action.path);
    } else if (action.action) {
      this.actionEvent.emit(action.action);
    }
  }

  onContactClick(contact: any) {
    if (contact.action) {
      this.actionEvent.emit(contact.action);
    }
  }

  onFeedbackClick() {
    this.actionEvent.emit('sendFeedback');
  }

  toggleFAQ(index: number) {
    const faqItems = this.getFAQItems();
    faqItems[index].expanded = !faqItems[index].expanded;
  }
}

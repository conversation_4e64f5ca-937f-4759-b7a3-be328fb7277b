import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'lib-profile-form',
  standalone: true,
  imports: [CommonModule, IonicModule, FormsModule, ReactiveFormsModule],
  template: `
    <div class="profile-form" [ngClass]="config?.class">
      <!-- Section Title -->
      <h2 class="section-title" *ngIf="config?.title">{{ config.title }}</h2>
      
      <!-- Form Container -->
      <form [formGroup]="profileForm" (ngSubmit)="onSubmit()" class="form-container">
        
        <!-- Personal Information Section -->
        <div class="form-section" *ngIf="config?.showPersonalInfo">
          <h3 class="section-subtitle">{{ config?.personalInfoTitle || 'Personal Information' }}</h3>
          
          <div class="form-row">
            <div class="form-field">
              <label for="givenNames">{{ config?.givenNamesLabel || 'First Name' }}</label>
              <input 
                type="text" 
                id="givenNames"
                formControlName="givenNames"
                [placeholder]="config?.givenNamesPlaceholder || 'Enter your first name'"
                class="form-input"
                [class.error]="profileForm.get('givenNames')?.invalid && profileForm.get('givenNames')?.touched"
              />
              <div class="error-message" *ngIf="profileForm.get('givenNames')?.invalid && profileForm.get('givenNames')?.touched">
                First name is required
              </div>
            </div>

            <div class="form-field">
              <label for="surname">{{ config?.surnameLabel || 'Last Name' }}</label>
              <input 
                type="text" 
                id="surname"
                formControlName="surname"
                [placeholder]="config?.surnamePlaceholder || 'Enter your last name'"
                class="form-input"
                [class.error]="profileForm.get('surname')?.invalid && profileForm.get('surname')?.touched"
              />
              <div class="error-message" *ngIf="profileForm.get('surname')?.invalid && profileForm.get('surname')?.touched">
                Last name is required
              </div>
            </div>
          </div>

          <div class="form-field">
            <label for="email">{{ config?.emailLabel || 'Email Address' }}</label>
            <input 
              type="email" 
              id="email"
              formControlName="email"
              [placeholder]="config?.emailPlaceholder || 'Enter your email address'"
              class="form-input"
              [class.error]="profileForm.get('email')?.invalid && profileForm.get('email')?.touched"
            />
            <div class="error-message" *ngIf="profileForm.get('email')?.invalid && profileForm.get('email')?.touched">
              Please enter a valid email address
            </div>
          </div>

          <div class="form-row">
            <div class="form-field">
              <label for="dateOfBirth">{{ config?.dobLabel || 'Date of Birth' }}</label>
              <input 
                type="date" 
                id="dateOfBirth"
                formControlName="dateOfBirth"
                class="form-input"
                [class.error]="profileForm.get('dateOfBirth')?.invalid && profileForm.get('dateOfBirth')?.touched"
              />
            </div>

            <div class="form-field">
              <label for="gender">{{ config?.genderLabel || 'Gender' }}</label>
              <select 
                id="gender"
                formControlName="gender"
                class="form-select"
              >
                <option value="">Select Gender</option>
                <option value="male">Male</option>
                <option value="female">Female</option>
                <option value="other">Other</option>
                <option value="prefer-not-to-say">Prefer not to say</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Contact Information Section -->
        <div class="form-section" *ngIf="config?.showContactInfo">
          <h3 class="section-subtitle">{{ config?.contactInfoTitle || 'Contact Information' }}</h3>
          
          <div class="form-field">
            <label for="phoneNumber">{{ config?.phoneLabel || 'Phone Number' }}</label>
            <input 
              type="tel" 
              id="phoneNumber"
              formControlName="phoneNumber"
              [placeholder]="config?.phonePlaceholder || 'Enter your phone number'"
              class="form-input"
              [class.error]="profileForm.get('phoneNumber')?.invalid && profileForm.get('phoneNumber')?.touched"
            />
            <div class="error-message" *ngIf="profileForm.get('phoneNumber')?.invalid && profileForm.get('phoneNumber')?.touched">
              Please enter a valid phone number
            </div>
          </div>

          <div class="form-field">
            <label for="address">{{ config?.addressLabel || 'Address' }}</label>
            <textarea 
              id="address"
              formControlName="address"
              [placeholder]="config?.addressPlaceholder || 'Enter your address'"
              class="form-textarea"
              rows="3"
            ></textarea>
          </div>

          <div class="form-row">
            <div class="form-field">
              <label for="city">{{ config?.cityLabel || 'City' }}</label>
              <input 
                type="text" 
                id="city"
                formControlName="city"
                [placeholder]="config?.cityPlaceholder || 'Enter your city'"
                class="form-input"
              />
            </div>

            <div class="form-field">
              <label for="postalCode">{{ config?.postalCodeLabel || 'Postal Code' }}</label>
              <input 
                type="text" 
                id="postalCode"
                formControlName="postalCode"
                [placeholder]="config?.postalCodePlaceholder || 'Enter postal code'"
                class="form-input"
              />
            </div>
          </div>
        </div>

        <!-- Preferences Section -->
        <div class="form-section" *ngIf="config?.showPreferences">
          <h3 class="section-subtitle">{{ config?.preferencesTitle || 'Preferences' }}</h3>
          
          <div class="form-field">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                formControlName="emailNotifications"
                class="form-checkbox"
              />
              <span class="checkbox-text">{{ config?.emailNotificationsLabel || 'Receive email notifications' }}</span>
            </label>
          </div>

          <div class="form-field">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                formControlName="smsNotifications"
                class="form-checkbox"
              />
              <span class="checkbox-text">{{ config?.smsNotificationsLabel || 'Receive SMS notifications' }}</span>
            </label>
          </div>

          <div class="form-field">
            <label class="checkbox-label">
              <input 
                type="checkbox" 
                formControlName="marketingEmails"
                class="form-checkbox"
              />
              <span class="checkbox-text">{{ config?.marketingEmailsLabel || 'Receive marketing emails' }}</span>
            </label>
          </div>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button 
            type="button" 
            class="btn btn-secondary"
            (click)="onCancel()"
            *ngIf="config?.showCancelButton"
          >
            {{ config?.cancelButtonLabel || 'Cancel' }}
          </button>
          
          <button 
            type="submit" 
            class="btn btn-primary"
            [disabled]="profileForm.invalid || isSubmitting"
          >
            <ion-icon name="checkmark-outline" *ngIf="!isSubmitting"></ion-icon>
            <ion-icon name="hourglass-outline" *ngIf="isSubmitting"></ion-icon>
            {{ isSubmitting ? (config?.submittingLabel || 'Saving...') : (config?.submitButtonLabel || 'Save Changes') }}
          </button>
        </div>
      </form>
    </div>
  `,
  styles: [`
    .profile-form {
      padding: 1rem;
      margin-bottom: 1rem;
    }

    .section-title {
      font-size: 1.3rem;
      font-weight: bold;
      margin-bottom: 1.5rem;
      color: var(--ion-color-dark);
    }

    .form-container {
      background: white;
      border-radius: 1rem;
      padding: 1.5rem;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid var(--ion-color-light);
    }

    .form-section {
      margin-bottom: 2rem;
    }

    .form-section:last-of-type {
      margin-bottom: 1.5rem;
    }

    .section-subtitle {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: var(--ion-color-primary);
      border-bottom: 2px solid var(--ion-color-light);
      padding-bottom: 0.5rem;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1rem;
    }

    .form-field {
      margin-bottom: 1rem;
    }

    .form-field label {
      display: block;
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin-bottom: 0.5rem;
    }

    .form-input, .form-select, .form-textarea {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid var(--ion-color-medium);
      border-radius: 0.5rem;
      font-size: 1rem;
      transition: all 0.3s ease;
      background: white;
    }

    .form-input:focus, .form-select:focus, .form-textarea:focus {
      outline: none;
      border-color: var(--ion-color-primary);
      box-shadow: 0 0 0 3px rgba(var(--ion-color-primary-rgb), 0.1);
    }

    .form-input.error, .form-select.error, .form-textarea.error {
      border-color: var(--ion-color-danger);
    }

    .form-textarea {
      resize: vertical;
      min-height: 80px;
    }

    .checkbox-label {
      display: flex;
      align-items: center;
      cursor: pointer;
      font-size: 0.9rem;
    }

    .form-checkbox {
      margin-right: 0.75rem;
      width: 18px;
      height: 18px;
      accent-color: var(--ion-color-primary);
    }

    .checkbox-text {
      color: var(--ion-color-dark);
    }

    .error-message {
      font-size: 0.8rem;
      color: var(--ion-color-danger);
      margin-top: 0.25rem;
    }

    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      margin-top: 2rem;
      padding-top: 1.5rem;
      border-top: 1px solid var(--ion-color-light);
    }

    .btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 0.75rem;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
    }

    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .btn-primary {
      background: var(--ion-color-primary);
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: var(--ion-color-primary-shade);
      transform: translateY(-1px);
    }

    .btn-secondary {
      background: var(--ion-color-light);
      color: var(--ion-color-dark);
      border: 1px solid var(--ion-color-medium);
    }

    .btn-secondary:hover {
      background: var(--ion-color-medium);
    }

    .btn ion-icon {
      font-size: 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .profile-form {
        padding: 0.75rem;
      }

      .form-container {
        padding: 1rem;
      }

      .form-row {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }

      .form-actions {
        flex-direction: column;
      }

      .btn {
        justify-content: center;
      }
    }

    @media (max-width: 480px) {
      .section-title {
        font-size: 1.2rem;
      }

      .section-subtitle {
        font-size: 1rem;
      }

      .form-input, .form-select, .form-textarea {
        padding: 0.6rem;
        font-size: 0.9rem;
      }
    }
  `]
})
export class ProfileFormComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() formSubmitEvent = new EventEmitter<any>();
  @Output() formCancelEvent = new EventEmitter<void>();

  profileForm!: FormGroup;
  isSubmitting = false;

  constructor(private fb: FormBuilder) {}

  ngOnInit() {
    this.initializeForm();
    this.setDefaultConfig();
  }

  private setDefaultConfig() {
    if (!this.config) {
      this.config = {
        title: 'Edit Profile',
        showPersonalInfo: true,
        showContactInfo: true,
        showPreferences: true,
        showCancelButton: true,
        personalInfoTitle: 'Personal Information',
        contactInfoTitle: 'Contact Information',
        preferencesTitle: 'Preferences'
      };
    }
  }

  private initializeForm() {
    this.profileForm = this.fb.group({
      givenNames: [this.profile?.givenNames || '', [Validators.required]],
      surname: [this.profile?.surname || '', [Validators.required]],
      email: [this.profile?.email || '', [Validators.required, Validators.email]],
      dateOfBirth: [this.profile?.dateOfBirth || ''],
      gender: [this.profile?.gender || ''],
      phoneNumber: [this.profile?.phoneNumber || '', [Validators.pattern(/^[\+]?[0-9\s\-\(\)]+$/)]],
      address: [this.profile?.address || ''],
      city: [this.profile?.city || ''],
      postalCode: [this.profile?.postalCode || ''],
      emailNotifications: [this.profile?.emailNotifications ?? true],
      smsNotifications: [this.profile?.smsNotifications ?? false],
      marketingEmails: [this.profile?.marketingEmails ?? false]
    });
  }

  onSubmit() {
    if (this.profileForm.valid) {
      this.isSubmitting = true;
      const formData = this.profileForm.value;
      this.formSubmitEvent.emit(formData);
      
      // Simulate submission delay
      setTimeout(() => {
        this.isSubmitting = false;
      }, 2000);
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.profileForm.controls).forEach(key => {
        this.profileForm.get(key)?.markAsTouched();
      });
    }
  }

  onCancel() {
    this.formCancelEvent.emit();
  }
}

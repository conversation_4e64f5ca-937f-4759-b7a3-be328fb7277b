import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-dashboard-summary',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="dashboard-summary" [ngClass]="config?.class">
      <!-- Section Title -->
      <h2 class="section-title" *ngIf="config?.title">{{ config.title }}</h2>
      
      <!-- Summary Cards -->
      <div class="summary-grid">
        <!-- Balance Card -->
        <div class="summary-card balance-card" *ngIf="config?.showBalance && profile">
          <div class="card-header">
            <div class="card-icon">
              <ion-icon name="wallet-outline"></ion-icon>
            </div>
            <div class="card-title">{{ config?.balanceTitle || 'Current Balance' }}</div>
          </div>
          <div class="card-content">
            <div class="balance-amount">{{ profile?.currentBalance || 0 }}</div>
            <div class="balance-unit">{{ config?.balanceUnit || 'points' }}</div>
          </div>
          <div class="card-footer" *ngIf="profile?.availRands">
            <span class="rand-value">≈ R{{ profile.availRands?.toFixed(2) || '0.00' }}</span>
          </div>
        </div>

        <!-- Membership Card -->
        <div class="summary-card membership-card" *ngIf="config?.showMembership && profile">
          <div class="card-header">
            <div class="card-icon">
              <ion-icon name="card-outline"></ion-icon>
            </div>
            <div class="card-title">{{ config?.membershipTitle || 'Membership' }}</div>
          </div>
          <div class="card-content">
            <div class="membership-tier">{{ profile?.membershipTier || 'Standard' }}</div>
            <div class="membership-number" *ngIf="profile?.newMembershipNumber">
              #{{ profile.newMembershipNumber }}
            </div>
          </div>
        </div>

        <!-- Activity Card -->
        <div class="summary-card activity-card" *ngIf="config?.showActivity">
          <div class="card-header">
            <div class="card-icon">
              <ion-icon name="trending-up-outline"></ion-icon>
            </div>
            <div class="card-title">{{ config?.activityTitle || 'Recent Activity' }}</div>
          </div>
          <div class="card-content">
            <div class="activity-stat">
              <span class="stat-label">This Month</span>
              <span class="stat-value">{{ data?.monthlyTransactions || 0 }} transactions</span>
            </div>
            <div class="activity-stat">
              <span class="stat-label">Points Earned</span>
              <span class="stat-value">{{ data?.monthlyPoints || 0 }} pts</span>
            </div>
          </div>
        </div>

        <!-- Custom Cards -->
        <div 
          *ngFor="let customCard of config?.customCards || []"
          class="summary-card custom-card"
          [ngClass]="customCard.class"
        >
          <div class="card-header">
            <div class="card-icon" *ngIf="customCard.icon">
              <ion-icon [name]="customCard.icon"></ion-icon>
            </div>
            <div class="card-title">{{ customCard.title }}</div>
          </div>
          <div class="card-content">
            <div class="custom-content" [innerHTML]="customCard.content"></div>
          </div>
          <div class="card-footer" *ngIf="customCard.footer">
            <span [innerHTML]="customCard.footer"></span>
          </div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="summary-actions" *ngIf="config?.actions && config.actions.length > 0">
        <button 
          *ngFor="let action of config.actions"
          class="summary-action-btn"
          [ngClass]="action.class"
          (click)="onAction(action)"
        >
          <ion-icon [name]="action.icon" *ngIf="action.icon"></ion-icon>
          <span>{{ action.label }}</span>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-summary {
      padding: 1rem;
      margin-bottom: 1rem;
    }

    .section-title {
      font-size: 1.3rem;
      font-weight: bold;
      margin-bottom: 1rem;
      color: var(--ion-color-dark);
    }

    .summary-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1rem;
      margin-bottom: 1.5rem;
    }

    .summary-card {
      background: white;
      border-radius: 1rem;
      padding: 1.25rem;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid var(--ion-color-light);
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .summary-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--ion-color-primary), var(--ion-color-secondary));
    }

    .summary-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }

    .card-header {
      display: flex;
      align-items: center;
      margin-bottom: 1rem;
    }

    .card-icon {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
      border-radius: 50%;
      margin-right: 0.75rem;
    }

    .card-icon ion-icon {
      font-size: 1.3rem;
      color: white;
    }

    .card-title {
      font-size: 1rem;
      font-weight: 600;
      color: var(--ion-color-dark);
    }

    .card-content {
      margin-bottom: 0.75rem;
    }

    /* Balance Card Specific */
    .balance-card .balance-amount {
      font-size: 2rem;
      font-weight: bold;
      color: var(--ion-color-primary);
      line-height: 1;
    }

    .balance-card .balance-unit {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      margin-top: 0.25rem;
    }

    .balance-card .card-footer {
      padding-top: 0.75rem;
      border-top: 1px solid var(--ion-color-light);
    }

    .balance-card .rand-value {
      font-size: 0.9rem;
      color: var(--ion-color-success);
      font-weight: 600;
    }

    /* Membership Card Specific */
    .membership-card .membership-tier {
      font-size: 1.3rem;
      font-weight: bold;
      color: var(--ion-color-secondary);
      margin-bottom: 0.25rem;
    }

    .membership-card .membership-number {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      font-family: monospace;
    }

    /* Activity Card Specific */
    .activity-card .activity-stat {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .activity-card .stat-label {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
    }

    .activity-card .stat-value {
      font-size: 0.9rem;
      font-weight: 600;
      color: var(--ion-color-dark);
    }

    /* Custom Card */
    .custom-card .custom-content {
      font-size: 0.9rem;
      line-height: 1.4;
    }

    .card-footer {
      font-size: 0.8rem;
      color: var(--ion-color-medium);
    }

    /* Summary Actions */
    .summary-actions {
      display: flex;
      gap: 0.75rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .summary-action-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.25rem;
      background: var(--ion-color-primary);
      color: white;
      border: none;
      border-radius: 0.75rem;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .summary-action-btn:hover {
      background: var(--ion-color-primary-shade);
      transform: translateY(-1px);
    }

    .summary-action-btn ion-icon {
      font-size: 1rem;
    }

    .summary-action-btn.secondary {
      background: var(--ion-color-light);
      color: var(--ion-color-dark);
    }

    .summary-action-btn.secondary:hover {
      background: var(--ion-color-medium);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .dashboard-summary {
        padding: 0.75rem;
      }

      .summary-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }

      .summary-card {
        padding: 1rem;
      }

      .balance-card .balance-amount {
        font-size: 1.75rem;
      }

      .card-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.5rem;
      }

      .card-icon ion-icon {
        font-size: 1.1rem;
      }

      .card-title {
        font-size: 0.9rem;
      }
    }

    @media (max-width: 480px) {
      .summary-actions {
        flex-direction: column;
      }

      .summary-action-btn {
        justify-content: center;
      }
    }
  `]
})
export class DashboardSummaryComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() navigationEvent = new EventEmitter<string>();

  constructor() {}

  ngOnInit() {
    // Set default configuration if not provided
    if (!this.config) {
      this.config = {
        title: 'Account Summary',
        showBalance: true,
        showMembership: true,
        showActivity: true,
        balanceTitle: 'Current Balance',
        balanceUnit: 'points',
        membershipTitle: 'Membership',
        activityTitle: 'Recent Activity'
      };
    }
  }

  onAction(action: any) {
    if (action.path) {
      this.navigationEvent.emit(action.path);
    } else if (action.action) {
      this.actionEvent.emit(action.action);
    }
  }
}

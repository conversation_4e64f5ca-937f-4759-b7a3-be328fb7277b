import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-dashboard-welcome',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="dashboard-welcome" [ngClass]="config?.class">
      <!-- Welcome Message -->
      <div class="welcome-content">
        <div class="welcome-text">
          <h1 class="welcome-title">
            {{ getWelcomeMessage() }}
          </h1>
          <p class="welcome-subtitle" *ngIf="config?.subtitle">
            {{ config.subtitle }}
          </p>
        </div>

        <!-- Time-based greeting icon -->
        <div class="welcome-icon" *ngIf="config?.showIcon">
          <ion-icon [name]="getTimeBasedIcon()"></ion-icon>
        </div>
      </div>

      <!-- Quick Stats -->
      <div class="quick-stats" *ngIf="config?.showQuickStats && profile">
        <div class="stat-item">
          <div class="stat-value">{{ profile?.currentBalance || 0 }}</div>
          <div class="stat-label">{{ config?.balanceLabel || 'Points' }}</div>
        </div>
        <div class="stat-item" *ngIf="profile?.availRands">
          <div class="stat-value">R{{ profile.availRands?.toFixed(2) || '0.00' }}</div>
          <div class="stat-label">{{ config?.randLabel || 'Value' }}</div>
        </div>
        <div class="stat-item" *ngIf="data?.unreadNotifications">
          <div class="stat-value">{{ data.unreadNotifications }}</div>
          <div class="stat-label">{{ config?.notificationLabel || 'Notifications' }}</div>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="welcome-actions" *ngIf="config?.actions && config.actions.length > 0">
        <button 
          *ngFor="let action of config.actions"
          class="welcome-action-btn"
          [ngClass]="action.class"
          (click)="onAction(action)"
        >
          <ion-icon [name]="action.icon" *ngIf="action.icon"></ion-icon>
          <span>{{ action.label }}</span>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-welcome {
      padding: 1.5rem;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, rgba(var(--ion-color-primary-rgb), 0.1), rgba(var(--ion-color-secondary-rgb), 0.1));
      border-radius: 1rem;
      border: 1px solid rgba(var(--ion-color-primary-rgb), 0.2);
    }

    .welcome-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .welcome-text {
      flex: 1;
    }

    .welcome-title {
      font-size: 1.5rem;
      font-weight: bold;
      margin: 0 0 0.5rem 0;
      color: var(--ion-color-dark);
      line-height: 1.2;
    }

    .welcome-subtitle {
      font-size: 1rem;
      margin: 0;
      color: var(--ion-color-medium);
      line-height: 1.4;
    }

    .welcome-icon {
      flex-shrink: 0;
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
      border-radius: 50%;
      margin-left: 1rem;
    }

    .welcome-icon ion-icon {
      font-size: 2rem;
      color: white;
    }

    .quick-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 1rem;
      margin-bottom: 1rem;
      padding: 1rem;
      background: white;
      border-radius: 0.75rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }

    .stat-item {
      text-align: center;
    }

    .stat-value {
      font-size: 1.3rem;
      font-weight: bold;
      color: var(--ion-color-primary);
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.8rem;
      color: var(--ion-color-medium);
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .welcome-actions {
      display: flex;
      gap: 0.75rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .welcome-action-btn {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.75rem 1.25rem;
      background: var(--ion-color-primary);
      color: white;
      border: none;
      border-radius: 0.75rem;
      font-size: 0.9rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .welcome-action-btn:hover {
      background: var(--ion-color-primary-shade);
      transform: translateY(-1px);
    }

    .welcome-action-btn ion-icon {
      font-size: 1rem;
    }

    .welcome-action-btn.secondary {
      background: white;
      color: var(--ion-color-primary);
      border: 1px solid var(--ion-color-primary);
    }

    .welcome-action-btn.secondary:hover {
      background: var(--ion-color-primary);
      color: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .dashboard-welcome {
        padding: 1rem;
      }

      .welcome-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
      }

      .welcome-icon {
        margin-left: 0;
        width: 50px;
        height: 50px;
      }

      .welcome-icon ion-icon {
        font-size: 1.5rem;
      }

      .welcome-title {
        font-size: 1.3rem;
      }

      .welcome-subtitle {
        font-size: 0.9rem;
      }

      .quick-stats {
        grid-template-columns: repeat(2, 1fr);
        padding: 0.75rem;
      }

      .stat-value {
        font-size: 1.1rem;
      }
    }

    @media (max-width: 480px) {
      .quick-stats {
        grid-template-columns: 1fr;
      }

      .welcome-actions {
        flex-direction: column;
      }

      .welcome-action-btn {
        justify-content: center;
      }
    }
  `]
})
export class DashboardWelcomeComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() navigationEvent = new EventEmitter<string>();

  constructor() {}

  ngOnInit() {
    // Set default configuration if not provided
    if (!this.config) {
      this.config = {
        showIcon: true,
        showQuickStats: true,
        subtitle: 'Here\'s what\'s happening with your account today.',
        balanceLabel: 'Points',
        randLabel: 'Value',
        notificationLabel: 'Notifications'
      };
    }
  }

  getWelcomeMessage(): string {
    const userName = this.profile?.givenNames || 'there';
    const timeOfDay = this.getTimeOfDay();
    
    if (this.config?.customMessage) {
      return this.config.customMessage.replace('{name}', userName).replace('{timeOfDay}', timeOfDay);
    }
    
    return `Good ${timeOfDay}, ${userName}!`;
  }

  getTimeOfDay(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'morning';
    if (hour < 17) return 'afternoon';
    return 'evening';
  }

  getTimeBasedIcon(): string {
    const hour = new Date().getHours();
    if (hour < 6) return 'moon-outline';
    if (hour < 12) return 'sunny-outline';
    if (hour < 17) return 'partly-sunny-outline';
    if (hour < 20) return 'sunset-outline';
    return 'moon-outline';
  }

  onAction(action: any) {
    if (action.path) {
      this.navigationEvent.emit(action.path);
    } else if (action.action) {
      this.actionEvent.emit(action.action);
    }
  }
}

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Header Components
import { HomeHeaderComponent } from './headers/home-header/home-header.component';
import { DashboardHeaderComponent } from './headers/dashboard-header/dashboard-header.component';
import { ProfileHeaderComponent } from './headers/profile-header/profile-header.component';

// Navigation Components
import { HomeNavigationComponent } from './navigation/home-navigation/home-navigation.component';
import { ProfileSettingsComponent } from './navigation/profile-settings/profile-settings.component';

// Action Components
import { HomeActionsComponent } from './actions/home-actions/home-actions.component';
import { DashboardQuickActionsComponent } from './actions/dashboard-quick-actions/dashboard-quick-actions.component';

// Content Components
import { DashboardWelcomeComponent } from './content/dashboard-welcome/dashboard-welcome.component';
import { DashboardSummaryComponent } from './content/dashboard-summary/dashboard-summary.component';
import { ProfileFormComponent } from './content/profile-form/profile-form.component';
import { ProfileHelpComponent } from './content/profile-help/profile-help.component';

const PAGE_SECTION_COMPONENTS = [
  // Headers
  HomeHeaderComponent,
  DashboardHeaderComponent,
  ProfileHeaderComponent,

  // Navigation
  HomeNavigationComponent,
  ProfileSettingsComponent,

  // Actions
  HomeActionsComponent,
  DashboardQuickActionsComponent,

  // Content
  DashboardWelcomeComponent,
  DashboardSummaryComponent,
  ProfileFormComponent,
  ProfileHelpComponent,
];

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    IonicModule,
    ...PAGE_SECTION_COMPONENTS
  ],
  exports: [
    ...PAGE_SECTION_COMPONENTS
  ]
})
export class PageSectionsModule { }

// Export all components for direct import
export {
  // Headers
  HomeHeaderComponent,
  DashboardHeaderComponent,
  ProfileHeaderComponent,

  // Navigation
  HomeNavigationComponent,
  ProfileSettingsComponent,

  // Actions
  HomeActionsComponent,
  DashboardQuickActionsComponent,

  // Content
  DashboardWelcomeComponent,
  DashboardSummaryComponent,
  ProfileFormComponent,
  ProfileHelpComponent,
};

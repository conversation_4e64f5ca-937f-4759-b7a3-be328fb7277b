import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-home-navigation',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="home-navigation" [ngClass]="config?.class">
      <!-- Section Title -->
      <h2 class="nav-title" *ngIf="config?.title">{{ config.title }}</h2>
      
      <!-- Quick Actions Grid -->
      <div class="nav-grid" [ngClass]="getGridClass()">
        <div 
          *ngFor="let item of getNavigationItems()"
          class="nav-item"
          [ngClass]="item.class"
          (click)="onNavigate(item)"
        >
          <div class="nav-icon">
            <ion-icon [name]="item.icon"></ion-icon>
          </div>
          <div class="nav-content">
            <h3 class="nav-label">{{ item.label }}</h3>
            <p class="nav-description" *ngIf="item.description">{{ item.description }}</p>
          </div>
          <div class="nav-arrow" *ngIf="config?.showArrows">
            <ion-icon name="chevron-forward-outline"></ion-icon>
          </div>
        </div>
      </div>

      <!-- Featured Section -->
      <div class="featured-section" *ngIf="config?.featured && config.featured.length > 0">
        <h3 class="featured-title">{{ config?.featuredTitle || 'Featured' }}</h3>
        <div class="featured-items">
          <div 
            *ngFor="let item of config.featured"
            class="featured-item"
            (click)="onNavigate(item)"
          >
            <img [src]="item.image" [alt]="item.label" *ngIf="item.image" />
            <div class="featured-content">
              <h4>{{ item.label }}</h4>
              <p *ngIf="item.description">{{ item.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .home-navigation {
      padding: 1rem;
    }

    .nav-title {
      font-size: 1.3rem;
      font-weight: bold;
      margin-bottom: 1rem;
      color: var(--ion-color-dark);
      text-align: center;
    }

    .nav-grid {
      display: grid;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .nav-grid.grid-2 {
      grid-template-columns: repeat(2, 1fr);
    }

    .nav-grid.grid-3 {
      grid-template-columns: repeat(3, 1fr);
    }

    .nav-grid.grid-4 {
      grid-template-columns: repeat(2, 1fr);
    }

    .nav-item {
      display: flex;
      align-items: center;
      padding: 1rem;
      background: white;
      border-radius: 0.75rem;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid var(--ion-color-light);
    }

    .nav-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .nav-icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--ion-color-primary);
      border-radius: 50%;
      margin-right: 0.75rem;
    }

    .nav-icon ion-icon {
      font-size: 1.2rem;
      color: white;
    }

    .nav-content {
      flex: 1;
    }

    .nav-label {
      font-size: 1rem;
      font-weight: 600;
      margin: 0 0 0.25rem 0;
      color: var(--ion-color-dark);
    }

    .nav-description {
      font-size: 0.8rem;
      margin: 0;
      color: var(--ion-color-medium);
      line-height: 1.3;
    }

    .nav-arrow {
      flex-shrink: 0;
      margin-left: 0.5rem;
    }

    .nav-arrow ion-icon {
      font-size: 1rem;
      color: var(--ion-color-medium);
    }

    .featured-section {
      margin-top: 2rem;
    }

    .featured-title {
      font-size: 1.1rem;
      font-weight: bold;
      margin-bottom: 1rem;
      color: var(--ion-color-dark);
    }

    .featured-items {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .featured-item {
      display: flex;
      align-items: center;
      padding: 0.75rem;
      background: white;
      border-radius: 0.5rem;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .featured-item:hover {
      transform: translateX(4px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .featured-item img {
      width: 40px;
      height: 40px;
      border-radius: 0.25rem;
      margin-right: 0.75rem;
      object-fit: cover;
    }

    .featured-content h4 {
      font-size: 0.9rem;
      font-weight: 600;
      margin: 0 0 0.25rem 0;
      color: var(--ion-color-dark);
    }

    .featured-content p {
      font-size: 0.8rem;
      margin: 0;
      color: var(--ion-color-medium);
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .nav-grid.grid-4 {
        grid-template-columns: repeat(2, 1fr);
      }

      .nav-grid.grid-3 {
        grid-template-columns: repeat(2, 1fr);
      }

      .nav-item {
        padding: 0.75rem;
      }

      .nav-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.5rem;
      }

      .nav-label {
        font-size: 0.9rem;
      }

      .nav-description {
        font-size: 0.75rem;
      }
    }

    @media (max-width: 480px) {
      .nav-grid {
        grid-template-columns: 1fr;
      }

      .nav-item {
        padding: 1rem;
      }
    }
  `]
})
export class HomeNavigationComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() navigationEvent = new EventEmitter<string>();

  private defaultNavItems = [
    {
      label: 'Profile',
      description: 'View and edit your profile',
      icon: 'person-outline',
      path: '/secure/profile',
      showWhen: 'authenticated'
    },
    {
      label: 'Transactions',
      description: 'View your transaction history',
      icon: 'card-outline',
      path: '/secure/transactions',
      showWhen: 'authenticated'
    },
    {
      label: 'Virtual Card',
      description: 'Access your loyalty card',
      icon: 'wallet-outline',
      path: '/secure/virtualcard',
      showWhen: 'authenticated'
    },
    {
      label: 'Games',
      description: 'Play games and earn points',
      icon: 'game-controller-outline',
      path: '/public/games/home',
      showWhen: 'always'
    }
  ];

  constructor() {}

  ngOnInit() {
    // Set default configuration if not provided
    if (!this.config) {
      this.config = {
        title: 'Quick Actions',
        showArrows: true,
        gridColumns: 2
      };
    }
  }

  getNavigationItems() {
    const items = this.config?.items || this.defaultNavItems;
    const isAuthenticated = !!this.profile;

    return items.filter((item: any) => {
      if (item.showWhen === 'authenticated') return isAuthenticated;
      if (item.showWhen === 'anonymous') return !isAuthenticated;
      return true; // showWhen === 'always' or undefined
    });
  }

  getGridClass() {
    const columns = this.config?.gridColumns || 2;
    return `grid-${columns}`;
  }

  onNavigate(item: any) {
    if (item.path) {
      this.navigationEvent.emit(item.path);
    } else if (item.action) {
      this.actionEvent.emit(item.action);
    }
  }
}

import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-profile-settings',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="profile-settings" [ngClass]="config?.class">
      <!-- Section Title -->
      <h2 class="section-title" *ngIf="config?.title">{{ config.title }}</h2>
      
      <!-- Settings Grid -->
      <div class="settings-grid">
        <!-- Account Settings -->
        <div class="settings-category" *ngIf="config?.showAccountSettings">
          <h3 class="category-title">{{ config?.accountSettingsTitle || 'Account Settings' }}</h3>
          <div class="settings-list">
            <div 
              *ngFor="let setting of getAccountSettings()"
              class="setting-item"
              [ngClass]="setting.class"
              (click)="onSettingClick(setting)"
            >
              <div class="setting-icon">
                <ion-icon [name]="setting.icon"></ion-icon>
              </div>
              <div class="setting-content">
                <div class="setting-label">{{ setting.label }}</div>
                <div class="setting-description" *ngIf="setting.description">{{ setting.description }}</div>
              </div>
              <div class="setting-action">
                <ion-icon name="chevron-forward-outline" *ngIf="setting.type === 'navigation'"></ion-icon>
                <ion-toggle 
                  *ngIf="setting.type === 'toggle'"
                  [checked]="setting.value"
                  (ionChange)="onToggleChange(setting, $event)"
                ></ion-toggle>
                <span class="setting-value" *ngIf="setting.type === 'value'">{{ setting.value }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Privacy Settings -->
        <div class="settings-category" *ngIf="config?.showPrivacySettings">
          <h3 class="category-title">{{ config?.privacySettingsTitle || 'Privacy & Security' }}</h3>
          <div class="settings-list">
            <div 
              *ngFor="let setting of getPrivacySettings()"
              class="setting-item"
              [ngClass]="setting.class"
              (click)="onSettingClick(setting)"
            >
              <div class="setting-icon">
                <ion-icon [name]="setting.icon"></ion-icon>
              </div>
              <div class="setting-content">
                <div class="setting-label">{{ setting.label }}</div>
                <div class="setting-description" *ngIf="setting.description">{{ setting.description }}</div>
              </div>
              <div class="setting-action">
                <ion-icon name="chevron-forward-outline" *ngIf="setting.type === 'navigation'"></ion-icon>
                <ion-toggle 
                  *ngIf="setting.type === 'toggle'"
                  [checked]="setting.value"
                  (ionChange)="onToggleChange(setting, $event)"
                ></ion-toggle>
                <span class="setting-value" *ngIf="setting.type === 'value'">{{ setting.value }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Notification Settings -->
        <div class="settings-category" *ngIf="config?.showNotificationSettings">
          <h3 class="category-title">{{ config?.notificationSettingsTitle || 'Notifications' }}</h3>
          <div class="settings-list">
            <div 
              *ngFor="let setting of getNotificationSettings()"
              class="setting-item"
              [ngClass]="setting.class"
              (click)="onSettingClick(setting)"
            >
              <div class="setting-icon">
                <ion-icon [name]="setting.icon"></ion-icon>
              </div>
              <div class="setting-content">
                <div class="setting-label">{{ setting.label }}</div>
                <div class="setting-description" *ngIf="setting.description">{{ setting.description }}</div>
              </div>
              <div class="setting-action">
                <ion-icon name="chevron-forward-outline" *ngIf="setting.type === 'navigation'"></ion-icon>
                <ion-toggle 
                  *ngIf="setting.type === 'toggle'"
                  [checked]="setting.value"
                  (ionChange)="onToggleChange(setting, $event)"
                ></ion-toggle>
                <span class="setting-value" *ngIf="setting.type === 'value'">{{ setting.value }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- App Settings -->
        <div class="settings-category" *ngIf="config?.showAppSettings">
          <h3 class="category-title">{{ config?.appSettingsTitle || 'App Settings' }}</h3>
          <div class="settings-list">
            <div 
              *ngFor="let setting of getAppSettings()"
              class="setting-item"
              [ngClass]="setting.class"
              (click)="onSettingClick(setting)"
            >
              <div class="setting-icon">
                <ion-icon [name]="setting.icon"></ion-icon>
              </div>
              <div class="setting-content">
                <div class="setting-label">{{ setting.label }}</div>
                <div class="setting-description" *ngIf="setting.description">{{ setting.description }}</div>
              </div>
              <div class="setting-action">
                <ion-icon name="chevron-forward-outline" *ngIf="setting.type === 'navigation'"></ion-icon>
                <ion-toggle 
                  *ngIf="setting.type === 'toggle'"
                  [checked]="setting.value"
                  (ionChange)="onToggleChange(setting, $event)"
                ></ion-toggle>
                <span class="setting-value" *ngIf="setting.type === 'value'">{{ setting.value }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .profile-settings {
      padding: 1rem;
      margin-bottom: 1rem;
    }

    .section-title {
      font-size: 1.3rem;
      font-weight: bold;
      margin-bottom: 1.5rem;
      color: var(--ion-color-dark);
    }

    .settings-grid {
      display: grid;
      gap: 1.5rem;
    }

    .settings-category {
      background: white;
      border-radius: 1rem;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      border: 1px solid var(--ion-color-light);
    }

    .category-title {
      font-size: 1.1rem;
      font-weight: 600;
      padding: 1rem 1.25rem;
      margin: 0;
      background: var(--ion-color-light);
      color: var(--ion-color-dark);
      border-bottom: 1px solid var(--ion-color-medium);
    }

    .settings-list {
      padding: 0;
    }

    .setting-item {
      display: flex;
      align-items: center;
      padding: 1rem 1.25rem;
      cursor: pointer;
      transition: all 0.3s ease;
      border-bottom: 1px solid var(--ion-color-light);
      position: relative;
    }

    .setting-item:last-child {
      border-bottom: none;
    }

    .setting-item:hover {
      background: rgba(var(--ion-color-primary-rgb), 0.05);
    }

    .setting-item.disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    .setting-item.warning {
      border-left: 4px solid var(--ion-color-warning);
    }

    .setting-item.danger {
      border-left: 4px solid var(--ion-color-danger);
    }

    .setting-icon {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(var(--ion-color-primary-rgb), 0.1);
      border-radius: 50%;
      margin-right: 1rem;
    }

    .setting-icon ion-icon {
      font-size: 1.2rem;
      color: var(--ion-color-primary);
    }

    .setting-item.warning .setting-icon {
      background: rgba(var(--ion-color-warning-rgb), 0.1);
    }

    .setting-item.warning .setting-icon ion-icon {
      color: var(--ion-color-warning);
    }

    .setting-item.danger .setting-icon {
      background: rgba(var(--ion-color-danger-rgb), 0.1);
    }

    .setting-item.danger .setting-icon ion-icon {
      color: var(--ion-color-danger);
    }

    .setting-content {
      flex: 1;
    }

    .setting-label {
      font-size: 1rem;
      font-weight: 600;
      color: var(--ion-color-dark);
      margin-bottom: 0.25rem;
    }

    .setting-description {
      font-size: 0.85rem;
      color: var(--ion-color-medium);
      line-height: 1.4;
    }

    .setting-action {
      flex-shrink: 0;
      margin-left: 1rem;
      display: flex;
      align-items: center;
    }

    .setting-action ion-icon {
      font-size: 1.1rem;
      color: var(--ion-color-medium);
    }

    .setting-value {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      font-weight: 500;
    }

    ion-toggle {
      --background: var(--ion-color-light);
      --background-checked: var(--ion-color-primary);
      --handle-background: white;
      --handle-background-checked: white;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .profile-settings {
        padding: 0.75rem;
      }

      .setting-item {
        padding: 0.875rem 1rem;
      }

      .setting-icon {
        width: 35px;
        height: 35px;
        margin-right: 0.75rem;
      }

      .setting-icon ion-icon {
        font-size: 1.1rem;
      }

      .setting-label {
        font-size: 0.95rem;
      }

      .setting-description {
        font-size: 0.8rem;
      }

      .category-title {
        font-size: 1rem;
        padding: 0.875rem 1rem;
      }
    }

    @media (max-width: 480px) {
      .setting-item {
        padding: 0.75rem;
      }

      .setting-icon {
        width: 32px;
        height: 32px;
        margin-right: 0.5rem;
      }

      .setting-icon ion-icon {
        font-size: 1rem;
      }

      .setting-action {
        margin-left: 0.5rem;
      }
    }
  `]
})
export class ProfileSettingsComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() navigationEvent = new EventEmitter<string>();
  @Output() settingChangeEvent = new EventEmitter<{setting: any, value: any}>();

  private defaultAccountSettings = [
    {
      id: 'edit-profile',
      label: 'Edit Profile',
      description: 'Update your personal information',
      icon: 'person-outline',
      type: 'navigation',
      path: '/secure/profile/edit'
    },
    {
      id: 'change-password',
      label: 'Change Password',
      description: 'Update your account password',
      icon: 'lock-closed-outline',
      type: 'navigation',
      path: '/secure/security/password'
    },
    {
      id: 'two-factor',
      label: 'Two-Factor Authentication',
      description: 'Enable additional security',
      icon: 'shield-checkmark-outline',
      type: 'toggle',
      value: false
    }
  ];

  private defaultPrivacySettings = [
    {
      id: 'profile-visibility',
      label: 'Profile Visibility',
      description: 'Control who can see your profile',
      icon: 'eye-outline',
      type: 'navigation',
      path: '/secure/privacy/visibility'
    },
    {
      id: 'data-sharing',
      label: 'Data Sharing',
      description: 'Manage data sharing preferences',
      icon: 'share-outline',
      type: 'toggle',
      value: true
    },
    {
      id: 'activity-tracking',
      label: 'Activity Tracking',
      description: 'Allow activity tracking for better experience',
      icon: 'analytics-outline',
      type: 'toggle',
      value: true
    }
  ];

  private defaultNotificationSettings = [
    {
      id: 'email-notifications',
      label: 'Email Notifications',
      description: 'Receive notifications via email',
      icon: 'mail-outline',
      type: 'toggle',
      value: true
    },
    {
      id: 'push-notifications',
      label: 'Push Notifications',
      description: 'Receive push notifications',
      icon: 'notifications-outline',
      type: 'toggle',
      value: true
    },
    {
      id: 'sms-notifications',
      label: 'SMS Notifications',
      description: 'Receive notifications via SMS',
      icon: 'chatbubble-outline',
      type: 'toggle',
      value: false
    }
  ];

  private defaultAppSettings = [
    {
      id: 'language',
      label: 'Language',
      description: 'Choose your preferred language',
      icon: 'language-outline',
      type: 'value',
      value: 'English'
    },
    {
      id: 'theme',
      label: 'Theme',
      description: 'Choose light or dark theme',
      icon: 'contrast-outline',
      type: 'value',
      value: 'Light'
    },
    {
      id: 'auto-sync',
      label: 'Auto Sync',
      description: 'Automatically sync data',
      icon: 'sync-outline',
      type: 'toggle',
      value: true
    }
  ];

  constructor() {}

  ngOnInit() {
    // Set default configuration if not provided
    if (!this.config) {
      this.config = {
        title: 'Settings',
        showAccountSettings: true,
        showPrivacySettings: true,
        showNotificationSettings: true,
        showAppSettings: true
      };
    }
  }

  getAccountSettings() {
    return this.config?.accountSettings || this.defaultAccountSettings;
  }

  getPrivacySettings() {
    return this.config?.privacySettings || this.defaultPrivacySettings;
  }

  getNotificationSettings() {
    return this.config?.notificationSettings || this.defaultNotificationSettings;
  }

  getAppSettings() {
    return this.config?.appSettings || this.defaultAppSettings;
  }

  onSettingClick(setting: any) {
    if (setting.disabled) return;

    if (setting.type === 'navigation' && setting.path) {
      this.navigationEvent.emit(setting.path);
    } else if (setting.action) {
      this.actionEvent.emit(setting.action);
    }
  }

  onToggleChange(setting: any, event: any) {
    const newValue = event.detail.checked;
    setting.value = newValue;
    this.settingChangeEvent.emit({ setting, value: newValue });
  }
}

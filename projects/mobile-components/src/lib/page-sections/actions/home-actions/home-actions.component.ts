import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-home-actions',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="home-actions" [ngClass]="config?.class">
      <!-- Primary Actions -->
      <div class="primary-actions" *ngIf="getPrimaryActions().length > 0">
        <h3 class="actions-title" *ngIf="config?.primaryTitle">{{ config.primaryTitle }}</h3>
        <div class="actions-grid primary">
          <button 
            *ngFor="let action of getPrimaryActions()"
            class="action-button primary"
            [ngClass]="action.class"
            (click)="onAction(action)"
          >
            <div class="action-icon">
              <ion-icon [name]="action.icon"></ion-icon>
            </div>
            <div class="action-content">
              <span class="action-label">{{ action.label }}</span>
              <span class="action-description" *ngIf="action.description">{{ action.description }}</span>
            </div>
          </button>
        </div>
      </div>

      <!-- Secondary Actions -->
      <div class="secondary-actions" *ngIf="getSecondaryActions().length > 0">
        <h3 class="actions-title" *ngIf="config?.secondaryTitle">{{ config.secondaryTitle }}</h3>
        <div class="actions-grid secondary">
          <button 
            *ngFor="let action of getSecondaryActions()"
            class="action-button secondary"
            [ngClass]="action.class"
            (click)="onAction(action)"
          >
            <ion-icon [name]="action.icon" *ngIf="action.icon"></ion-icon>
            <span>{{ action.label }}</span>
          </button>
        </div>
      </div>

      <!-- Authentication Actions (for anonymous users) -->
      <div class="auth-actions" *ngIf="getAuthActions().length > 0">
        <h3 class="actions-title" *ngIf="config?.authTitle">{{ config.authTitle }}</h3>
        <div class="actions-grid auth">
          <button 
            *ngFor="let action of getAuthActions()"
            class="action-button auth"
            [ngClass]="action.class"
            (click)="onAction(action)"
          >
            <ion-icon [name]="action.icon" *ngIf="action.icon"></ion-icon>
            <span>{{ action.label }}</span>
          </button>
        </div>
      </div>

      <!-- Social Actions -->
      <div class="social-actions" *ngIf="getSocialActions().length > 0">
        <h3 class="actions-title" *ngIf="config?.socialTitle">{{ config.socialTitle }}</h3>
        <div class="social-buttons">
          <button 
            *ngFor="let action of getSocialActions()"
            class="social-button"
            [ngClass]="action.class"
            (click)="onAction(action)"
          >
            <ion-icon [name]="action.icon"></ion-icon>
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .home-actions {
      padding: 1rem;
    }

    .actions-title {
      font-size: 1.1rem;
      font-weight: bold;
      margin-bottom: 1rem;
      color: var(--ion-color-dark);
      text-align: center;
    }

    .actions-grid {
      display: grid;
      gap: 1rem;
      margin-bottom: 2rem;
    }

    .actions-grid.primary {
      grid-template-columns: 1fr;
    }

    .actions-grid.secondary {
      grid-template-columns: repeat(2, 1fr);
    }

    .actions-grid.auth {
      grid-template-columns: 1fr;
    }

    .action-button {
      display: flex;
      align-items: center;
      padding: 1rem;
      border: none;
      border-radius: 0.75rem;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: left;
      font-family: inherit;
    }

    .action-button.primary {
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
      color: white;
      box-shadow: 0 4px 16px rgba(var(--ion-color-primary-rgb), 0.3);
    }

    .action-button.primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(var(--ion-color-primary-rgb), 0.4);
    }

    .action-button.secondary {
      background: white;
      color: var(--ion-color-dark);
      border: 1px solid var(--ion-color-light);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .action-button.secondary:hover {
      background: var(--ion-color-light);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .action-button.auth {
      background: var(--ion-color-tertiary);
      color: white;
      justify-content: center;
      font-weight: 600;
    }

    .action-button.auth:hover {
      background: var(--ion-color-tertiary-shade);
      transform: translateY(-1px);
    }

    .action-icon {
      flex-shrink: 0;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      margin-right: 1rem;
    }

    .action-icon ion-icon {
      font-size: 1.5rem;
      color: white;
    }

    .action-content {
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .action-label {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: 0.25rem;
    }

    .action-description {
      font-size: 0.9rem;
      opacity: 0.8;
      line-height: 1.3;
    }

    .secondary .action-button {
      flex-direction: column;
      text-align: center;
      padding: 1.5rem 1rem;
    }

    .secondary .action-button ion-icon {
      font-size: 2rem;
      margin-bottom: 0.5rem;
      color: var(--ion-color-primary);
    }

    .auth .action-button ion-icon {
      font-size: 1.2rem;
      margin-right: 0.5rem;
    }

    .social-buttons {
      display: flex;
      justify-content: center;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .social-button {
      width: 50px;
      height: 50px;
      border: none;
      border-radius: 50%;
      background: var(--ion-color-medium);
      color: white;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .social-button:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    .social-button ion-icon {
      font-size: 1.5rem;
    }

    /* Social platform specific colors */
    .social-button.facebook {
      background: #1877f2;
    }

    .social-button.twitter {
      background: #1da1f2;
    }

    .social-button.linkedin {
      background: #0077b5;
    }

    .social-button.instagram {
      background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
    }

    .social-button.youtube {
      background: #ff0000;
    }

    .social-button.pinterest {
      background: #bd081c;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .actions-grid.secondary {
        grid-template-columns: 1fr;
      }

      .action-button {
        padding: 0.75rem;
      }

      .action-icon {
        width: 40px;
        height: 40px;
        margin-right: 0.75rem;
      }

      .action-icon ion-icon {
        font-size: 1.2rem;
      }

      .action-label {
        font-size: 1rem;
      }

      .action-description {
        font-size: 0.8rem;
      }
    }

    @media (max-width: 480px) {
      .social-buttons {
        gap: 0.75rem;
      }

      .social-button {
        width: 45px;
        height: 45px;
      }

      .social-button ion-icon {
        font-size: 1.3rem;
      }
    }
  `]
})
export class HomeActionsComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() navigationEvent = new EventEmitter<string>();

  private defaultActions = {
    primary: [
      {
        label: 'Get Started',
        description: 'Begin your loyalty journey today',
        icon: 'rocket-outline',
        action: 'getStarted',
        showWhen: 'anonymous'
      },
      {
        label: 'View Dashboard',
        description: 'See your account overview',
        icon: 'speedometer-outline',
        path: '/secure/dashboard',
        showWhen: 'authenticated'
      }
    ],
    secondary: [
      {
        label: 'Games',
        icon: 'game-controller-outline',
        path: '/public/games/home',
        showWhen: 'always'
      },
      {
        label: 'Stores',
        icon: 'location-outline',
        path: '/public/stores',
        showWhen: 'always'
      }
    ],
    auth: [
      {
        label: 'Sign In',
        icon: 'log-in-outline',
        path: '/public/login',
        showWhen: 'anonymous'
      },
      {
        label: 'Create Account',
        icon: 'person-add-outline',
        path: '/public/register',
        showWhen: 'anonymous'
      }
    ],
    social: [
      {
        label: 'Facebook',
        icon: 'logo-facebook',
        action: 'socialLogin:facebook',
        class: 'facebook',
        showWhen: 'anonymous'
      },
      {
        label: 'Twitter',
        icon: 'logo-twitter',
        action: 'socialLogin:twitter',
        class: 'twitter',
        showWhen: 'anonymous'
      },
      {
        label: 'LinkedIn',
        icon: 'logo-linkedin',
        action: 'socialLogin:linkedin',
        class: 'linkedin',
        showWhen: 'anonymous'
      }
    ]
  };

  constructor() {}

  ngOnInit() {
    // Set default configuration if not provided
    if (!this.config) {
      this.config = {
        primaryTitle: 'Get Started',
        secondaryTitle: 'Explore',
        authTitle: 'Join Us',
        socialTitle: 'Connect With Us'
      };
    }
  }

  getPrimaryActions() {
    return this.filterActionsByAuth(this.config?.primaryActions || this.defaultActions.primary);
  }

  getSecondaryActions() {
    return this.filterActionsByAuth(this.config?.secondaryActions || this.defaultActions.secondary);
  }

  getAuthActions() {
    return this.filterActionsByAuth(this.config?.authActions || this.defaultActions.auth);
  }

  getSocialActions() {
    return this.filterActionsByAuth(this.config?.socialActions || this.defaultActions.social);
  }

  private filterActionsByAuth(actions: any[]) {
    const isAuthenticated = !!this.profile;

    return actions.filter((action: any) => {
      if (action.showWhen === 'authenticated') return isAuthenticated;
      if (action.showWhen === 'anonymous') return !isAuthenticated;
      return true; // showWhen === 'always' or undefined
    });
  }

  onAction(action: any) {
    if (action.path) {
      this.navigationEvent.emit(action.path);
    } else if (action.action) {
      this.actionEvent.emit(action.action);
    }
  }
}

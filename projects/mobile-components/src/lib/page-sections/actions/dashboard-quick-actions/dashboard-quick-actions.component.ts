import { Component, Input, Output, EventEmitter, OnInit, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Types for dashboard quick actions
export interface QuickAction {
  id: string;
  label: string;
  description?: string;
  icon: string;
  path?: string;
  action?: string;
  class?: string;
  disabled?: boolean;
  visible?: boolean;
  showWhen?: 'always' | 'authenticated' | 'anonymous';
  badge?: {
    text: string;
    variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  };
  priority?: 'high' | 'medium' | 'low';
}

export interface QuickActionsConfig {
  title?: string;
  showTitle?: boolean;
  showArrows?: boolean;
  showDescriptions?: boolean;
  gridColumns?: 1 | 2 | 3 | 4;
  layout?: 'grid' | 'list' | 'horizontal';
  class?: string;
  additionalInfo?: string;
  showAdditionalInfo?: boolean;
  actions?: QuickAction[];
}

@Component({
  selector: 'lib-dashboard-quick-actions',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div 
      [class]="computedClasses()" 
      [attr.aria-label]="'Quick actions section'"
      role="region"
      [style.display]="hidden ? 'none' : 'block'"
    >
      <!-- Section Title -->
      <h2 
        class="section-title" 
        *ngIf="resolvedConfig().showTitle && resolvedConfig().title"
        [id]="componentId() + '-title'"
      >
        {{ resolvedConfig().title }}
      </h2>
      
      <!-- Actions Grid/List -->
      <div 
        [class]="actionsContainerClass()"
        [attr.aria-labelledby]="resolvedConfig().showTitle ? componentId() + '-title' : undefined"
        role="list"
      >
        <div 
          *ngFor="let action of resolvedActions(); trackBy: trackAction"
          [class]="actionItemClass(action)"
          [style.display]="action.visible === false ? 'none' : 'flex'"
          (click)="onAction(action)"
          (keydown.enter)="onAction(action)"
          (keydown.space)="onAction(action)"
          tabindex="0"
          role="listitem"
          [attr.aria-label]="getActionAriaLabel(action)"
          [attr.aria-disabled]="action.disabled"
        >
          <!-- Action Icon -->
          <div class="action-icon" [class]="getIconClass(action)">
            <ion-icon [name]="action.icon" aria-hidden="true"></ion-icon>
            <!-- Badge -->
            <div 
              *ngIf="action.badge" 
              [class]="getBadgeClass(action.badge)"
              [attr.aria-label]="'Badge: ' + action.badge.text"
            >
              {{ action.badge.text }}
            </div>
          </div>

          <!-- Action Content -->
          <div class="action-content">
            <h3 class="action-label">{{ action.label }}</h3>
            <p 
              class="action-description" 
              *ngIf="action.description && resolvedConfig().showDescriptions"
            >
              {{ action.description }}
            </p>
          </div>

          <!-- Action Arrow -->
          <div class="action-arrow" *ngIf="resolvedConfig().showArrows && !action.disabled">
            <ion-icon name="chevron-forward-outline" aria-hidden="true"></ion-icon>
          </div>
        </div>
      </div>

      <!-- Additional Info -->
      <div 
        class="additional-info" 
        *ngIf="resolvedConfig().showAdditionalInfo && resolvedConfig().additionalInfo"
      >
        <p class="info-text">{{ resolvedConfig().additionalInfo }}</p>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-quick-actions {
      transition: all 0.3s ease;
    }

    .section-title {
      font-weight: bold;
      margin-bottom: 1rem;
      color: var(--ion-color-dark);
      text-align: left;
    }

    .actions-container {
      margin-bottom: 1rem;
    }

    .actions-grid {
      display: grid;
      gap: 1rem;
    }

    .actions-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;
    }

    .actions-horizontal {
      display: flex;
      gap: 1rem;
      overflow-x: auto;
      padding-bottom: 0.5rem;
      scroll-snap-type: x mandatory;
    }

    .actions-horizontal .action-item {
      flex-shrink: 0;
      min-width: 200px;
      scroll-snap-align: start;
    }

    .action-item {
      display: flex;
      align-items: center;
      background: white;
      border-radius: 1rem;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid var(--ion-color-light);
      position: relative;
      overflow: hidden;
    }

    .action-item::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, var(--ion-color-primary), var(--ion-color-secondary));
      transform: scaleX(0);
      transition: transform 0.3s ease;
    }

    .action-item:hover:not(.disabled) {
      transform: translateY(-3px);
      box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
    }

    .action-item:hover:not(.disabled)::before {
      transform: scaleX(1);
    }

    .action-item:focus {
      outline: 2px solid var(--ion-color-primary);
      outline-offset: 2px;
    }

    .action-item.disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .action-icon {
      flex-shrink: 0;
      width: 50px;
      height: 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
      border-radius: 50%;
      margin-right: 1rem;
      position: relative;
    }

    .action-icon::after {
      content: '';
      position: absolute;
      inset: 2px;
      background: white;
      border-radius: 50%;
      z-index: 0;
    }

    .action-icon ion-icon {
      font-size: 1.5rem;
      color: var(--ion-color-primary);
      position: relative;
      z-index: 1;
    }

    .action-content {
      flex: 1;
    }

    .action-label {
      font-weight: 600;
      margin: 0 0 0.25rem 0;
      color: var(--ion-color-dark);
    }

    .action-description {
      font-size: 0.85rem;
      margin: 0;
      color: var(--ion-color-medium);
      line-height: 1.4;
    }

    .action-arrow {
      flex-shrink: 0;
      margin-left: 0.75rem;
      opacity: 0.6;
      transition: all 0.3s ease;
    }

    .action-item:hover .action-arrow {
      opacity: 1;
      transform: translateX(3px);
    }

    .action-arrow ion-icon {
      font-size: 1.2rem;
      color: var(--ion-color-medium);
    }

    .additional-info {
      text-align: center;
      margin-top: 1rem;
    }

    .info-text {
      font-size: 0.9rem;
      color: var(--ion-color-medium);
      margin: 0;
      font-style: italic;
    }

    /* Badge styles */
    .action-badge {
      position: absolute;
      top: -5px;
      right: -5px;
      min-width: 18px;
      height: 18px;
      border-radius: 9px;
      font-size: 0.7rem;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      z-index: 2;
    }

    .badge-default { background: var(--ion-color-medium); }
    .badge-primary { background: var(--ion-color-primary); }
    .badge-secondary { background: var(--ion-color-secondary); }
    .badge-success { background: var(--ion-color-success); }
    .badge-warning { background: var(--ion-color-warning); }
    .badge-danger { background: var(--ion-color-danger); }

    /* Action item variants */
    .action-item.primary {
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
      color: white;
    }

    .action-item.primary .action-label,
    .action-item.primary .action-description {
      color: white;
    }

    .action-item.primary .action-icon {
      background: rgba(255, 255, 255, 0.2);
    }

    .action-item.primary .action-icon::after {
      background: rgba(255, 255, 255, 0.9);
    }

    .action-item.secondary {
      background: var(--ion-color-light);
      border-color: var(--ion-color-medium);
    }

    .action-item.success {
      border-left: 4px solid var(--ion-color-success);
    }

    .action-item.warning {
      border-left: 4px solid var(--ion-color-warning);
    }

    .action-item.danger {
      border-left: 4px solid var(--ion-color-danger);
    }

    /* Priority styles */
    .action-item.priority-high {
      order: -2;
      border-left: 4px solid var(--ion-color-primary);
    }

    .action-item.priority-medium {
      order: -1;
    }

    .action-item.priority-low {
      order: 1;
      opacity: 0.8;
    }

    /* Size variations */
    .size-xs .action-item { padding: 0.75rem; }
    .size-sm .action-item { padding: 1rem; }
    .size-md .action-item { padding: 1.25rem; }
    .size-lg .action-item { padding: 1.5rem; }
    .size-xl .action-item { padding: 2rem; }

    .size-xs .action-icon { width: 35px; height: 35px; }
    .size-sm .action-icon { width: 40px; height: 40px; }
    .size-md .action-icon { width: 50px; height: 50px; }
    .size-lg .action-icon { width: 60px; height: 60px; }
    .size-xl .action-icon { width: 70px; height: 70px; }

    .size-xs .section-title { font-size: 1rem; }
    .size-sm .section-title { font-size: 1.1rem; }
    .size-md .section-title { font-size: 1.3rem; }
    .size-lg .section-title { font-size: 1.5rem; }
    .size-xl .section-title { font-size: 1.7rem; }
  `]
})
export class DashboardQuickActionsComponent implements OnInit {
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() sticky: boolean = false;
  @Input() fixed: boolean = false;
  @Input() hidden: boolean = false;

  // Dashboard quick actions specific inputs
  @Input() config: Partial<QuickActionsConfig> = {};
  @Input() actions: QuickAction[] = [];
  @Input() profile: any = null;
  @Input() title: string = 'Quick Actions';
  @Input() showTitle: boolean = true;
  @Input() showArrows: boolean = true;
  @Input() showDescriptions: boolean = true;
  @Input() gridColumns: 1 | 2 | 3 | 4 = 2;
  @Input() layout: 'grid' | 'list' | 'horizontal' = 'grid';
  @Input() additionalInfo: string = '';
  @Input() showAdditionalInfo: boolean = false;

  // Event outputs
  @Output() actionClicked = new EventEmitter<QuickAction>();
  @Output() actionNavigated = new EventEmitter<string>();

  // Component state
  public readonly componentId = signal<string>(`quick-actions-${Math.random().toString(36).substr(2, 9)}`);

  // Default configuration
  private readonly defaultConfig: QuickActionsConfig = {
    title: 'Quick Actions',
    showTitle: true,
    showArrows: true,
    showDescriptions: true,
    gridColumns: 2,
    layout: 'grid',
    class: '',
    additionalInfo: '',
    showAdditionalInfo: false,
    actions: []
  };

  // Default actions
  private readonly defaultActions: QuickAction[] = [
    {
      id: 'profile',
      label: 'Profile',
      description: 'View and edit your profile information',
      icon: 'person-outline',
      path: '/secure/profile',
      showWhen: 'authenticated',
      priority: 'high'
    },
    {
      id: 'security',
      label: 'Security',
      description: 'Manage your account security settings',
      icon: 'shield-checkmark-outline',
      path: '/secure/security',
      showWhen: 'authenticated',
      priority: 'high'
    },
    {
      id: 'transactions',
      label: 'Transactions',
      description: 'View your transaction history',
      icon: 'card-outline',
      path: '/secure/transactions',
      showWhen: 'authenticated',
      priority: 'medium'
    },
    {
      id: 'virtual-card',
      label: 'Virtual Card',
      description: 'Access your digital loyalty card',
      icon: 'wallet-outline',
      path: '/secure/virtualcard',
      showWhen: 'authenticated',
      priority: 'medium',
      badge: { text: 'New', variant: 'primary' }
    },
    {
      id: 'help',
      label: 'Help & Support',
      description: 'Get help and contact support',
      icon: 'help-circle-outline',
      path: '/help',
      showWhen: 'always',
      priority: 'low'
    },
    {
      id: 'settings',
      label: 'Settings',
      description: 'Customize your app preferences',
      icon: 'settings-outline',
      path: '/settings',
      showWhen: 'always',
      priority: 'low'
    }
  ];

  // Computed properties
  public readonly resolvedConfig = computed(() => ({
    ...this.defaultConfig,
    title: this.title,
    showTitle: this.showTitle,
    showArrows: this.showArrows,
    showDescriptions: this.showDescriptions,
    gridColumns: this.gridColumns,
    layout: this.layout,
    additionalInfo: this.additionalInfo,
    showAdditionalInfo: this.showAdditionalInfo,
    ...this.config
  }));

  public readonly resolvedActions = computed(() => {
    const actions = this.actions.length > 0 ? this.actions : this.defaultActions;
    const isAuthenticated = !!this.profile;

    return actions
      .filter(action => {
        if (action.showWhen === 'authenticated') return isAuthenticated;
        if (action.showWhen === 'anonymous') return !isAuthenticated;
        return true; // 'always' or undefined
      })
      .sort((a, b) => {
        const priorityOrder = { high: 0, medium: 1, low: 2 };
        const aPriority = priorityOrder[a.priority || 'medium'];
        const bPriority = priorityOrder[b.priority || 'medium'];
        return aPriority - bPriority;
      });
  });

  public readonly computedClasses = computed(() => {
    const baseClasses = [
      'dashboard-quick-actions',
      'transition-all duration-300'
    ];

    // Size classes
    const sizeClasses = {
      xs: ['p-2', 'text-xs', 'size-xs'],
      sm: ['p-3', 'text-sm', 'size-sm'],
      md: ['p-4', 'text-base', 'size-md'],
      lg: ['p-6', 'text-lg', 'size-lg'],
      xl: ['p-8', 'text-xl', 'size-xl']
    };

    // Variant classes
    const variantClasses = {
      default: ['bg-white'],
      primary: ['bg-blue-50', 'border-blue-200'],
      secondary: ['bg-gray-50', 'border-gray-200'],
      success: ['bg-green-50', 'border-green-200'],
      warning: ['bg-yellow-50', 'border-yellow-200'],
      danger: ['bg-red-50', 'border-red-200']
    };

    // Rounded classes
    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-lg'],
      lg: ['rounded-xl'],
      full: ['rounded-3xl']
    };

    // Position classes
    const positionClasses = [];
    if (this.sticky) positionClasses.push('sticky', 'top-0', 'z-50');
    if (this.fixed) positionClasses.push('fixed', 'top-0', 'left-0', 'right-0', 'z-50');

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...roundedClasses[this.rounded],
      ...positionClasses,
      this.className
    ].filter(Boolean).join(' ');
  });

  public readonly actionsContainerClass = computed(() => {
    const config = this.resolvedConfig();
    const baseClass = 'actions-container';
    
    if (config.layout === 'list') {
      return `${baseClass} actions-list`;
    } else if (config.layout === 'horizontal') {
      return `${baseClass} actions-horizontal`;
    } else {
      return `${baseClass} actions-grid grid-cols-${config.gridColumns}`;
    }
  });

  constructor() {}

  ngOnInit() {
    // Component initialization
  }

  onAction(action: QuickAction) {
    if (action.disabled) return;
    
    this.actionClicked.emit(action);
    
    if (action.path) {
      this.actionNavigated.emit(action.path);
    }
  }

  trackAction(index: number, action: QuickAction): string {
    return action.id;
  }

  actionItemClass(action: QuickAction): string {
    const baseClasses = ['action-item'];
    
    if (action.disabled) baseClasses.push('disabled');
    if (action.class) baseClasses.push(action.class);
    if (action.priority) baseClasses.push(`priority-${action.priority}`);
    
    return baseClasses.join(' ');
  }

  getIconClass(action: QuickAction): string {
    return 'action-icon';
  }

  getBadgeClass(badge: QuickAction['badge']): string {
    if (!badge) return '';
    return `action-badge badge-${badge.variant}`;
  }

  getActionAriaLabel(action: QuickAction): string {
    let label = action.label;
    if (action.description) {
      label += `: ${action.description}`;
    }
    if (action.disabled) {
      label += ' (disabled)';
    }
    return label;
  }
}
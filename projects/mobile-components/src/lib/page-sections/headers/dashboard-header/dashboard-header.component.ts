import { Component, Input, Output, EventEmitter, OnInit, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

// Types for dashboard header
export interface HeaderAction {
  id: string;
  label: string;
  icon?: string;
  action?: string;
  navigation?: string;
  class?: string;
  disabled?: boolean;
  visible?: boolean;
}

export interface HeaderConfig {
  showLogo?: boolean;
  showBalance?: boolean;
  showUserInfo?: boolean;
  showStats?: boolean;
  showActions?: boolean;
  logoUrl?: string;
  logoAlt?: string;
  balanceLabel?: string;
  balanceUnit?: string;
  class?: string;
  backgroundClass?: string;
  actions?: HeaderAction[];
}

export interface UserProfile {
  givenNames?: string;
  surname?: string;
  newMembershipNumber?: string;
  currentBalance?: number;
  availRands?: number;
  availUnits?: number;
  avatar?: string;
  email?: string;
}

@Component({
  selector: 'lib-dashboard-header',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div 
      [class]="computedClasses()" 
      [attr.aria-label]="'Dashboard header for ' + (resolvedProfile().givenNames || 'user')"
      role="banner"
      [style.display]="hidden ? 'none' : 'block'"
    >
      <!-- Background -->
      <div class="header-background" [ngClass]="resolvedConfig().backgroundClass">
        <div class="animated-bg"></div>
      </div>

      <!-- Header Content -->
      <div class="header-content">
        <!-- Logo and Balance Section -->
        <div class="header-top" [class.flex-col]="size === 'sm'">
          <div class="logo-section" *ngIf="resolvedConfig().showLogo">
            <img 
              [src]="resolvedConfig().logoUrl" 
              [alt]="resolvedConfig().logoAlt"
              class="header-logo"
              [class.h-8]="size === 'sm'"
              [class.h-10]="size === 'md'"
              [class.h-12]="size === 'lg'"
              loading="lazy"
              (click)="onLogoClick()"
              tabindex="0"
              role="button"
              [attr.aria-label]="'Logo: ' + resolvedConfig().logoAlt"
            />
          </div>

          <div class="balance-section" *ngIf="resolvedProfile() && resolvedConfig().showBalance">
            <div 
              class="balance-container"
              (click)="onBalanceClick()"
              tabindex="0"
              role="button"
              [attr.aria-label]="'Balance: ' + (resolvedProfile().currentBalance || 0) + ' ' + resolvedConfig().balanceUnit"
            >
              <div class="balance-label">{{ resolvedConfig().balanceLabel }}</div>
              <div class="balance-value">{{ resolvedProfile().currentBalance || 0 | number:'1.0-0' }}</div>
              <div class="balance-unit">{{ resolvedConfig().balanceUnit }}</div>
            </div>
          </div>
        </div>

        <!-- User Info Section -->
        <div class="user-info" *ngIf="resolvedProfile() && resolvedConfig().showUserInfo">
          <div 
            class="user-details"
            (click)="onProfileClick()"
            tabindex="0"
            role="button"
            [attr.aria-label]="'User profile: ' + resolvedProfile().givenNames + ' ' + resolvedProfile().surname"
          >
            <div class="user-avatar" *ngIf="resolvedProfile().avatar">
              <img 
                [src]="resolvedProfile().avatar" 
                [alt]="resolvedProfile().givenNames + ' avatar'"
                class="w-12 h-12 rounded-full object-cover"
                loading="lazy"
              />
            </div>
            <div class="user-text">
              <div class="user-name">
                {{ resolvedProfile().givenNames || 'User' }} {{ resolvedProfile().surname || '' }}
              </div>
              <div class="user-email" *ngIf="resolvedProfile().email">
                {{ resolvedProfile().email }}
              </div>
              <div class="membership-info" *ngIf="resolvedProfile().newMembershipNumber">
                <span class="membership-label">Member ID:</span>
                <span class="membership-number">{{ resolvedProfile().newMembershipNumber }}</span>
              </div>
            </div>
          </div>

          <div class="user-stats" *ngIf="resolvedConfig().showStats">
            <div class="stat-item" *ngIf="resolvedProfile().availRands !== undefined">
              <div class="stat-label">Rand Value</div>
              <div class="stat-value">R {{ resolvedProfile().availRands?.toFixed(2) || '0.00' }}</div>
            </div>
            <div class="stat-item" *ngIf="resolvedProfile().availUnits !== undefined">
              <div class="stat-label">Available Units</div>
              <div class="stat-value">{{ resolvedProfile().availUnits || 0 | number }}</div>
            </div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="header-actions" *ngIf="resolvedActions().length > 0 && resolvedConfig().showActions">
          <button 
            *ngFor="let action of resolvedActions(); trackBy: trackAction"
            type="button"
            class="header-action-btn"
            [ngClass]="action.class"
            [disabled]="action.disabled"
            [style.display]="action.visible === false ? 'none' : 'flex'"
            (click)="onAction(action)"
            [attr.aria-label]="action.label"
          >
            <ion-icon [name]="action.icon" *ngIf="action.icon" aria-hidden="true"></ion-icon>
            <span>{{ action.label }}</span>
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dashboard-header {
      position: relative;
      overflow: hidden;
      transition: all 0.3s ease;
    }

    .header-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 0;
    }

    .animated-bg {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(-45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05), rgba(255,255,255,0.1), rgba(255,255,255,0.05));
      background-size: 400% 400%;
      animation: gradientShift 15s ease infinite;
    }

    @keyframes gradientShift {
      0% { background-position: 0% 50%; }
      50% { background-position: 100% 50%; }
      100% { background-position: 0% 50%; }
    }

    .header-content {
      position: relative;
      z-index: 1;
      color: white;
    }

    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .logo-section {
      cursor: pointer;
    }

    .header-logo {
      width: auto;
      max-width: 120px;
      transition: all 0.2s ease;
      cursor: pointer;
    }

    .header-logo:hover {
      transform: scale(1.05);
    }

    .balance-section {
      text-align: right;
    }

    .balance-container {
      background: rgba(255, 255, 255, 0.15);
      padding: 0.75rem 1rem;
      border-radius: 0.75rem;
      backdrop-filter: blur(10px);
      transition: all 0.2s ease;
      cursor: pointer;
    }

    .balance-container:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.02);
    }

    .balance-label {
      font-size: 0.8rem;
      opacity: 0.9;
      margin-bottom: 0.25rem;
    }

    .balance-value {
      font-size: 1.5rem;
      font-weight: bold;
      line-height: 1;
    }

    .balance-unit {
      font-size: 0.8rem;
      opacity: 0.8;
    }

    .user-info {
      background: rgba(255, 255, 255, 0.1);
      padding: 1rem;
      border-radius: 0.75rem;
      backdrop-filter: blur(10px);
      margin-bottom: 1rem;
      transition: all 0.2s ease;
    }

    .user-details {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 0.75rem;
      cursor: pointer;
      transition: all 0.2s ease;
      padding: 0.5rem;
      border-radius: 0.5rem;
    }

    .user-details:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .user-text {
      flex: 1;
    }

    .user-name {
      font-size: 1.2rem;
      font-weight: bold;
      margin-bottom: 0.25rem;
    }

    .user-email {
      font-size: 0.85rem;
      opacity: 0.8;
      margin-bottom: 0.25rem;
    }

    .membership-info {
      font-size: 0.9rem;
      opacity: 0.9;
    }

    .membership-label {
      margin-right: 0.5rem;
    }

    .membership-number {
      font-weight: 600;
    }

    .user-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 1rem;
    }

    .stat-item {
      text-align: center;
      padding: 0.5rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 0.5rem;
      transition: all 0.2s ease;
    }

    .stat-item:hover {
      background: rgba(255, 255, 255, 0.15);
    }

    .stat-label {
      font-size: 0.8rem;
      opacity: 0.8;
      margin-bottom: 0.25rem;
    }

    .stat-value {
      font-size: 1rem;
      font-weight: bold;
    }

    .header-actions {
      display: flex;
      gap: 0.5rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .header-action-btn {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.5rem 1rem;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 1rem;
      color: white;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .header-action-btn:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    .header-action-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .header-action-btn ion-icon {
      font-size: 1rem;
    }

    /* Focus styles for accessibility */
    .header-action-btn:focus,
    .balance-container:focus,
    .user-details:focus,
    .logo-section:focus,
    .header-logo:focus {
      outline: 2px solid rgba(255, 255, 255, 0.8);
      outline-offset: 2px;
    }

    /* Position classes */
    .sticky {
      position: sticky;
      top: 0;
      z-index: 1000;
    }

    .fixed {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      z-index: 1000;
    }

    /* Responsive adjustments built into Tailwind classes */
  `]
})
export class DashboardHeaderComponent implements OnInit {
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  @Input() sticky: boolean = false;
  @Input() fixed: boolean = false;
  @Input() hidden: boolean = false;

  // Dashboard header specific inputs
  @Input() config: Partial<HeaderConfig> = {};
  @Input() profile: UserProfile | null = null;
  @Input() actions: HeaderAction[] = [];
  @Input() showLogo: boolean = true;
  @Input() showBalance: boolean = true;
  @Input() showUserInfo: boolean = true;
  @Input() showStats: boolean = true;
  @Input() showActions: boolean = true;
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() logoUrl: string = 'assets/images/logo.png';
  @Input() logoAlt: string = 'Logo';
  @Input() balanceLabel: string = 'Current Balance';
  @Input() balanceUnit: string = 'points';
  @Input() backgroundGradient: string = 'linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary))';

  // Event outputs
  @Output() actionClicked = new EventEmitter<HeaderAction>();
  @Output() profileClicked = new EventEmitter<UserProfile>();
  @Output() logoClicked = new EventEmitter<void>();
  @Output() balanceClicked = new EventEmitter<number>();

  // Default configuration
  private readonly defaultConfig: HeaderConfig = {
    showLogo: true,
    showBalance: true,
    showUserInfo: true,
    showStats: true,
    showActions: true,
    logoUrl: 'assets/images/logo.png',
    logoAlt: 'Logo',
    balanceLabel: 'Current Balance',
    balanceUnit: 'points',
    class: '',
    backgroundClass: '',
    actions: []
  };

  // Default profile
  private readonly defaultProfile: UserProfile = {
    givenNames: 'John',
    surname: 'Doe',
    email: '<EMAIL>',
    newMembershipNumber: 'LP123456',
    currentBalance: 1250,
    availRands: 125.50,
    availUnits: 50,
    avatar: 'assets/images/avatar.png'
  };

  // Default actions
  private readonly defaultActions: HeaderAction[] = [
    {
      id: 'notifications',
      label: 'Notifications',
      icon: 'notifications-outline',
      action: 'notifications',
      class: 'text-white hover:bg-white/20'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: 'settings-outline',
      navigation: '/settings',
      class: 'text-white hover:bg-white/20'
    },
    {
      id: 'help',
      label: 'Help',
      icon: 'help-circle-outline',
      action: 'help',
      class: 'text-white hover:bg-white/20'
    }
  ];

  // Computed properties
  public readonly resolvedConfig = computed(() => ({
    ...this.defaultConfig,
    showLogo: this.showLogo,
    showBalance: this.showBalance,
    showUserInfo: this.showUserInfo,
    showStats: this.showStats,
    showActions: this.showActions,
    logoUrl: this.logoUrl,
    logoAlt: this.logoAlt,
    balanceLabel: this.balanceLabel,
    balanceUnit: this.balanceUnit,
    ...this.config
  }));

  public readonly resolvedProfile = computed(() => 
    this.profile || this.defaultProfile
  );

  public readonly resolvedActions = computed(() => 
    this.actions.length > 0 ? this.actions : this.defaultActions
  );

  public readonly computedClasses = computed(() => {
    const baseClasses = [
      'dashboard-header',
      'relative',
      'overflow-hidden',
      'transition-all duration-300'
    ];

    // Size classes
    const sizeClasses = {
      xs: ['p-2', 'text-xs'],
      sm: ['p-3', 'text-sm'],
      md: ['p-4', 'text-base'],
      lg: ['p-6', 'text-lg'],
      xl: ['p-8', 'text-xl']
    };

    // Variant classes
    const variantClasses = {
      default: ['bg-gradient-to-br', 'from-blue-500', 'to-blue-700'],
      primary: ['bg-gradient-to-br', 'from-indigo-500', 'to-purple-700'],
      secondary: ['bg-gradient-to-br', 'from-gray-500', 'to-gray-700'],
      success: ['bg-gradient-to-br', 'from-green-500', 'to-green-700'],
      warning: ['bg-gradient-to-br', 'from-yellow-500', 'to-orange-700'],
      danger: ['bg-gradient-to-br', 'from-red-500', 'to-red-700']
    };

    // Rounded classes
    const roundedClasses = {
      none: ['rounded-none'],
      sm: ['rounded-sm'],
      md: ['rounded-lg'],
      lg: ['rounded-xl'],
      full: ['rounded-3xl']
    };

    // Position classes
    const positionClasses = [];
    if (this.sticky) positionClasses.push('sticky', 'top-0', 'z-50');
    if (this.fixed) positionClasses.push('fixed', 'top-0', 'left-0', 'right-0', 'z-50');

    return [
      ...baseClasses,
      ...sizeClasses[this.size],
      ...variantClasses[this.variant],
      ...roundedClasses[this.rounded],
      ...positionClasses,
      this.className
    ].filter(Boolean).join(' ');
  });

  constructor() {}

  ngOnInit() {
    // Component initialization
  }

  onAction(action: HeaderAction) {
    if (action.disabled) return;
    
    this.actionClicked.emit(action);
  }

  onProfileClick() {
    if (this.resolvedProfile()) {
      this.profileClicked.emit(this.resolvedProfile());
    }
  }

  onLogoClick() {
    this.logoClicked.emit();
  }

  onBalanceClick() {
    const balance = this.resolvedProfile().currentBalance;
    if (balance !== undefined) {
      this.balanceClicked.emit(balance);
    }
  }

  trackAction(index: number, action: HeaderAction): string {
    return action.id;
  }
}
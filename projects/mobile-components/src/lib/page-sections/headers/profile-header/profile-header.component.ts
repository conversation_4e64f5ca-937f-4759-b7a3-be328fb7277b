import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-profile-header',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="profile-header" [ngClass]="config?.class">
      <!-- Header Content -->
      <div class="header-content">
        <!-- Logo and Balance Section -->
        <div class="header-top">
          <div class="logo-section" *ngIf="config?.showLogo">
            <img 
              [src]="config?.logoUrl || 'assets/images/logo.png'" 
              [alt]="config?.logoAlt || 'Logo'"
              class="header-logo"
            />
          </div>

          <div class="balance-section" *ngIf="profile && config?.showBalance">
            <div class="balance-container">
              <div class="balance-label">{{ config?.balanceLabel || 'Current Balance' }}</div>
              <div class="balance-value">{{ profile?.currentBalance || 0 }}</div>
              <div class="balance-unit">{{ config?.balanceUnit || 'points' }}</div>
            </div>
          </div>
        </div>

        <!-- Profile Info Section -->
        <div class="profile-info" *ngIf="profile && config?.showProfileInfo">
          <div class="profile-avatar" *ngIf="config?.showAvatar">
            <div class="avatar-container">
              <img 
                [src]="profile?.avatarUrl || config?.defaultAvatarUrl || 'assets/images/default-avatar.png'" 
                [alt]="profile?.givenNames + ' ' + profile?.surname"
                class="avatar-image"
                (error)="onAvatarError($event)"
              />
              <div class="avatar-overlay" *ngIf="config?.allowAvatarEdit">
                <ion-icon name="camera-outline"></ion-icon>
              </div>
            </div>
          </div>

          <div class="profile-details">
            <div class="profile-name">
              {{ profile?.givenNames || 'User' }} {{ profile?.surname || '' }}
            </div>
            <div class="profile-email" *ngIf="profile?.email">
              {{ profile.email }}
            </div>
            <div class="membership-info" *ngIf="profile?.newMembershipNumber">
              <span class="membership-label">Member ID:</span>
              <span class="membership-number">{{ profile.newMembershipNumber }}</span>
            </div>
            <div class="membership-tier" *ngIf="profile?.membershipTier">
              <span class="tier-label">Tier:</span>
              <span class="tier-value">{{ profile.membershipTier }}</span>
            </div>
          </div>
        </div>

        <!-- Quick Stats -->
        <div class="quick-stats" *ngIf="config?.showQuickStats && profile">
          <div class="stat-item">
            <div class="stat-value">{{ profile?.currentBalance || 0 }}</div>
            <div class="stat-label">{{ config?.balanceLabel || 'Points' }}</div>
          </div>
          <div class="stat-item" *ngIf="profile?.availRands">
            <div class="stat-value">R{{ profile.availRands?.toFixed(2) || '0.00' }}</div>
            <div class="stat-label">{{ config?.randLabel || 'Value' }}</div>
          </div>
          <div class="stat-item" *ngIf="data?.profileCompleteness">
            <div class="stat-value">{{ data.profileCompleteness }}%</div>
            <div class="stat-label">{{ config?.completenessLabel || 'Complete' }}</div>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="header-actions" *ngIf="config?.actions && config.actions.length > 0">
          <button 
            *ngFor="let action of config.actions"
            class="header-action-btn"
            [ngClass]="action.class"
            (click)="onAction(action.action || action.navigation)"
          >
            <ion-icon [name]="action.icon" *ngIf="action.icon"></ion-icon>
            <span>{{ action.label }}</span>
          </button>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .profile-header {
      position: relative;
      padding: 1rem;
      margin-bottom: 1rem;
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
      color: white;
      border-radius: 0 0 1rem 1rem;
    }

    .header-content {
      position: relative;
      z-index: 1;
    }

    .header-top {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .header-logo {
      height: 40px;
      width: auto;
      max-width: 120px;
    }

    .balance-section {
      text-align: right;
    }

    .balance-container {
      background: rgba(255, 255, 255, 0.15);
      padding: 0.75rem 1rem;
      border-radius: 0.75rem;
      backdrop-filter: blur(10px);
    }

    .balance-label {
      font-size: 0.8rem;
      opacity: 0.9;
      margin-bottom: 0.25rem;
    }

    .balance-value {
      font-size: 1.5rem;
      font-weight: bold;
      line-height: 1;
    }

    .balance-unit {
      font-size: 0.8rem;
      opacity: 0.8;
    }

    .profile-info {
      display: flex;
      align-items: center;
      background: rgba(255, 255, 255, 0.1);
      padding: 1rem;
      border-radius: 0.75rem;
      backdrop-filter: blur(10px);
      margin-bottom: 1rem;
    }

    .profile-avatar {
      margin-right: 1rem;
    }

    .avatar-container {
      position: relative;
      width: 80px;
      height: 80px;
    }

    .avatar-image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      object-fit: cover;
      border: 3px solid rgba(255, 255, 255, 0.3);
    }

    .avatar-overlay {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 28px;
      height: 28px;
      background: var(--ion-color-primary);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 2px solid white;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .avatar-overlay:hover {
      background: var(--ion-color-primary-shade);
      transform: scale(1.1);
    }

    .avatar-overlay ion-icon {
      font-size: 0.9rem;
      color: white;
    }

    .profile-details {
      flex: 1;
    }

    .profile-name {
      font-size: 1.3rem;
      font-weight: bold;
      margin-bottom: 0.25rem;
    }

    .profile-email {
      font-size: 0.9rem;
      opacity: 0.9;
      margin-bottom: 0.5rem;
    }

    .membership-info, .membership-tier {
      font-size: 0.85rem;
      margin-bottom: 0.25rem;
    }

    .membership-label, .tier-label {
      opacity: 0.8;
      margin-right: 0.5rem;
    }

    .membership-number, .tier-value {
      font-weight: 600;
    }

    .quick-stats {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      gap: 1rem;
      margin-bottom: 1rem;
      padding: 1rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 0.75rem;
      backdrop-filter: blur(10px);
    }

    .stat-item {
      text-align: center;
    }

    .stat-value {
      font-size: 1.2rem;
      font-weight: bold;
      margin-bottom: 0.25rem;
    }

    .stat-label {
      font-size: 0.8rem;
      opacity: 0.8;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .header-actions {
      display: flex;
      gap: 0.5rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .header-action-btn {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.5rem 1rem;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 1rem;
      color: white;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .header-action-btn:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    .header-action-btn ion-icon {
      font-size: 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .profile-header {
        padding: 0.75rem;
      }

      .header-top {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
      }

      .balance-section {
        text-align: center;
      }

      .profile-info {
        flex-direction: column;
        text-align: center;
      }

      .profile-avatar {
        margin-right: 0;
        margin-bottom: 1rem;
      }

      .avatar-container {
        width: 70px;
        height: 70px;
      }

      .profile-name {
        font-size: 1.2rem;
      }

      .balance-value {
        font-size: 1.3rem;
      }

      .quick-stats {
        grid-template-columns: repeat(2, 1fr);
      }
    }

    @media (max-width: 480px) {
      .quick-stats {
        grid-template-columns: 1fr;
      }

      .header-action-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
      }
    }
  `]
})
export class ProfileHeaderComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() navigationEvent = new EventEmitter<string>();

  constructor() {}

  ngOnInit() {
    // Set default configuration if not provided
    if (!this.config) {
      this.config = {
        showLogo: true,
        showBalance: true,
        showProfileInfo: true,
        showAvatar: true,
        showQuickStats: true,
        allowAvatarEdit: true,
        balanceLabel: 'Current Balance',
        balanceUnit: 'points',
        randLabel: 'Value',
        completenessLabel: 'Complete'
      };
    }
  }

  onAction(action: string) {
    if (action?.startsWith('/')) {
      this.navigationEvent.emit(action);
    } else {
      this.actionEvent.emit(action);
    }
  }

  onAvatarError(event: any) {
    // Fallback to default avatar on error
    event.target.src = this.config?.defaultAvatarUrl || 'assets/images/default-avatar.png';
  }
}

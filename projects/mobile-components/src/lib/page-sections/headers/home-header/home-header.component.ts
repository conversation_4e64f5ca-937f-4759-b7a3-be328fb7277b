import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'lib-home-header',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="home-header" [ngClass]="config?.class">
      <!-- Logo Section -->
      <div class="header-logo" *ngIf="config?.showLogo">
        <img 
          [src]="config?.logoUrl || 'assets/images/logo.png'" 
          [alt]="config?.logoAlt || 'Logo'"
          class="logo-image"
        />
      </div>

      <!-- Title Section -->
      <div class="header-content" *ngIf="config?.title || config?.subtitle">
        <h1 class="header-title" *ngIf="config?.title" [ngClass]="config?.titleClass">
          {{ config.title }}
        </h1>
        <p class="header-subtitle" *ngIf="config?.subtitle" [ngClass]="config?.subtitleClass">
          {{ config.subtitle }}
        </p>
      </div>

      <!-- User Info Section (for authenticated users) -->
      <div class="header-user-info" *ngIf="profile && config?.showUserInfo">
        <div class="user-greeting">
          <span class="greeting-text">Welcome back,</span>
          <span class="user-name">{{ profile?.givenNames || 'User' }}</span>
        </div>
        <div class="user-balance" *ngIf="profile?.currentBalance !== undefined">
          <span class="balance-label">Balance:</span>
          <span class="balance-value">{{ profile.currentBalance || 0 }} points</span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="header-actions" *ngIf="config?.actions && config.actions.length > 0">
        <button 
          *ngFor="let action of config.actions"
          class="action-button"
          [ngClass]="action.class"
          (click)="onAction(action.action || action.navigation)"
        >
          <ion-icon [name]="action.icon" *ngIf="action.icon"></ion-icon>
          <span>{{ action.label }}</span>
        </button>
      </div>
    </div>
  `,
  styles: [`
    .home-header {
      padding: 1rem;
      text-align: center;
      background: linear-gradient(135deg, var(--ion-color-primary), var(--ion-color-secondary));
      color: white;
      border-radius: 0 0 1rem 1rem;
      margin-bottom: 1rem;
    }

    .header-logo {
      margin-bottom: 1rem;
    }

    .logo-image {
      height: 60px;
      width: auto;
      max-width: 200px;
    }

    .header-content {
      margin-bottom: 1rem;
    }

    .header-title {
      font-size: 1.8rem;
      font-weight: bold;
      margin: 0 0 0.5rem 0;
      color: white;
    }

    .header-subtitle {
      font-size: 1rem;
      margin: 0;
      opacity: 0.9;
      color: white;
    }

    .header-user-info {
      margin-bottom: 1rem;
      padding: 0.75rem;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 0.5rem;
      backdrop-filter: blur(10px);
    }

    .user-greeting {
      margin-bottom: 0.5rem;
    }

    .greeting-text {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .user-name {
      font-size: 1.1rem;
      font-weight: bold;
      margin-left: 0.25rem;
    }

    .user-balance {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 0.5rem;
    }

    .balance-label {
      font-size: 0.9rem;
      opacity: 0.8;
    }

    .balance-value {
      font-size: 1rem;
      font-weight: bold;
    }

    .header-actions {
      display: flex;
      gap: 0.5rem;
      justify-content: center;
      flex-wrap: wrap;
    }

    .action-button {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.5rem 1rem;
      background: rgba(255, 255, 255, 0.2);
      border: none;
      border-radius: 1rem;
      color: white;
      font-size: 0.9rem;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .action-button:hover {
      background: rgba(255, 255, 255, 0.3);
      transform: translateY(-1px);
    }

    .action-button ion-icon {
      font-size: 1rem;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .home-header {
        padding: 0.75rem;
      }

      .header-title {
        font-size: 1.5rem;
      }

      .header-subtitle {
        font-size: 0.9rem;
      }

      .action-button {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
      }
    }
  `]
})
export class HomeHeaderComponent implements OnInit {
  @Input() config: any = {};
  @Input() profile: any;
  @Input() data: any;

  @Output() actionEvent = new EventEmitter<string>();
  @Output() navigationEvent = new EventEmitter<string>();

  constructor() {}

  ngOnInit() {
    // Set default configuration if not provided
    if (!this.config) {
      this.config = {
        showLogo: true,
        title: 'Welcome to Loyalty Plus',
        subtitle: 'Your rewards journey starts here',
        showUserInfo: true
      };
    }
  }

  onAction(action: string) {
    if (action?.startsWith('/')) {
      this.navigationEvent.emit(action);
    } else {
      this.actionEvent.emit(action);
    }
  }
}

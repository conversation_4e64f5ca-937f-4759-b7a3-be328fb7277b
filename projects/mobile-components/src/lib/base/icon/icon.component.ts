import { Component, Input, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { computed } from '@angular/core';
import { CommonModule } from '@angular/common';

export type IconSource = 'ionic' | 'lucide' | 'material' | 'heroicons';

@Component({
  selector: 'base-icon',
  templateUrl: './icon.component.html',
  styleUrls: ['./icon.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class IconComponent implements OnInit {
  // Standard inputs for all components
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'none';

  // Icon-specific inputs
  @Input() name: string = 'home';
  @Input() source: IconSource = 'ionic';
  @Input() color: string = '';
  @Input() spin: boolean = false;
  @Input() pulse: boolean = false;
  @Input() flip: 'horizontal' | 'vertical' | 'both' | 'none' = 'none';
  @Input() rotate: number = 0;
  @Input() stroke: string = '';
  @Input() fill: string = '';
  @Input() alt: string = '';
  @Input() ariaLabel: string = '';
  @Input() clickable: boolean = false;

  // Legacy compatibility
  @Input() icon: string = 'home'; // Legacy prop name
  @Input() class: string = ''; // Legacy prop name
  @Input() item_id: string = Math.random().toString(36).substring(7);

  get effectiveName(): string {
    return this.name || this.icon || 'home';
  }

  get effectiveAriaLabel(): string {
    return this.ariaLabel || this.alt || this.effectiveName;
  }

  get computedClasses(): string {
    const classes: string[] = [];

    // Base icon class
    classes.push('base-icon');

    // Standard size classes
    const sizeClasses = {
      xs: 'h-3 w-3',
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6',
      xl: 'h-8 w-8'
    };
    if (sizeClasses[this.size]) {
      classes.push(sizeClasses[this.size]);
    }

    // Variant color classes
    const variantClasses = {
      default: 'text-gray-600 dark:text-gray-300',
      primary: 'text-blue-600 dark:text-blue-400',
      secondary: 'text-gray-500 dark:text-gray-400',
      success: 'text-green-600 dark:text-green-400',
      warning: 'text-yellow-600 dark:text-yellow-500',
      danger: 'text-red-600 dark:text-red-400'
    };
    if (variantClasses[this.variant]) {
      classes.push(variantClasses[this.variant]);
    }

    // Rounded classes (for icon containers/backgrounds)
    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };
    if (this.rounded !== 'none' && roundedClasses[this.rounded]) {
      classes.push(roundedClasses[this.rounded]);
    }

    // Animation classes
    if (this.spin) {
      classes.push('animate-spin');
    }
    if (this.pulse) {
      classes.push('animate-pulse');
    }

    // Transform classes
    if (this.flip !== 'none') {
      const flipClasses = {
        horizontal: 'scale-x-[-1]',
        vertical: 'scale-y-[-1]',
        both: 'scale-x-[-1] scale-y-[-1]',
        none: ''
      };
      if (flipClasses[this.flip]) {
        classes.push(flipClasses[this.flip]);
      }
    }

    // Rotation
    if (this.rotate !== 0) {
      const rotateClass = `rotate-[${this.rotate}deg]`;
      classes.push(rotateClass);
    }

    // Clickable styling
    if (this.clickable) {
      classes.push('cursor-pointer hover:scale-110 transition-transform duration-200');
    }

    // Custom color override
    if (this.color) {
      classes.push(this.color);
    }

    // Custom className
    if (this.className) {
      classes.push(this.className);
    }

    // Legacy class support
    if (this.class) {
      classes.push(this.class);
    }

    return classes.filter(Boolean).join(' ');
  }

  get iconStyles(): { [key: string]: string } {
    const styles: { [key: string]: string } = {};

    if (this.stroke) {
      styles['--ion-color-base'] = this.stroke;
    }
    if (this.fill) {
      styles['--ion-color-contrast'] = this.fill;
    }

    return styles;
  }

  get iconName(): string {
    // Handle different icon source formats
    switch (this.source) {
      case 'lucide':
        return this.effectiveName.startsWith('lucide:') ? this.effectiveName : `lucide:${this.effectiveName}`;
      case 'material':
        return this.effectiveName.startsWith('material:') ? this.effectiveName : `material:${this.effectiveName}`;
      case 'heroicons':
        return this.effectiveName.startsWith('heroicons:') ? this.effectiveName : `heroicons:${this.effectiveName}`;
      case 'ionic':
      default:
        // For ionic icons, use the name directly (ion-icon will handle it)
        return this.effectiveName.replace(/^(ionic:|ion-)?/, '');
    }
  }

  // Legacy classes computed property for backward compatibility
  classes = computed(() => [
    this.class || 'h-4 w-4',
    this.className
  ].filter(Boolean));

  ngOnInit() {
    // This is where you would typically put initialization logic
  }
}

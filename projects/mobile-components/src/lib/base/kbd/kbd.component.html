<!-- Enhanced Kbd component for keyboard shortcuts -->
<span class="inline-flex items-center space-x-1" [attr.title]="tooltip">
  <ng-container *ngFor="let key of keyArray; let i = index">
    <!-- Individual key -->
    <kbd
      [ngClass]="computedClasses"
      [attr.aria-label]="'Key: ' + key"
      [tabindex]="interactive && !disabled ? 0 : -1"
      (click)="onKeyClick(key)"
      (mouseenter)="onKeyHover(key)"
      (keydown.enter)="onKeyClick(key)"
      (keydown.space)="onKeyClick(key)"
    >
      {{ key }}
    </kbd>
    
    <!-- Separator between keys -->
    <span *ngIf="i < keyArray.length - 1" class="text-gray-500 text-sm font-normal">
      {{ separator }}
    </span>
  </ng-container>
  
  <!-- Content projection for additional elements -->
  <ng-content></ng-content>
</span>

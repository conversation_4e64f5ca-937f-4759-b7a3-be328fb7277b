import { Component, Input, Output, EventEmitter, OnInit, Inject, Optional, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-kbd',
  templateUrl: './kbd.component.html',
  styleUrls: ['./kbd.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class KbdComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() keys: string | string[] = 'Ctrl';
  @Input() separator: string = '+';
  @Input() disabled: boolean = false;
  @Input() interactive: boolean = false;
  @Input() keyStyle: 'flat' | 'raised' | 'outlined' = 'raised';
  @Input() tooltip: string = '';

  // Events
  @Output() keyClick = new EventEmitter<string>();
  @Output() keyHover = new EventEmitter<string>();
  
  constructor(@Optional() @Inject('componentProperties') public properties: any) {}
  
  ngOnInit(): void {
    if (this.properties) {
      // Map properties from injected properties
      this.className = this.properties.className || this.className;
      this.size = this.properties.size || this.size;
      this.variant = this.properties.variant || this.variant;
      this.rounded = this.properties.rounded || this.rounded;
      this.keys = this.properties.keys || this.keys;
      this.separator = this.properties.separator || this.separator;
      this.disabled = this.properties.disabled || this.disabled;
      this.interactive = this.properties.interactive || this.interactive;
      this.keyStyle = this.properties.keyStyle || this.keyStyle;
      this.tooltip = this.properties.tooltip || this.tooltip;
    }
  }

  get computedClasses(): string {
    const baseClasses = 'inline-flex items-center justify-center font-mono font-semibold transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs px-1 py-0.5 min-w-[1.25rem] h-5',
      sm: 'text-sm px-1.5 py-1 min-w-[1.5rem] h-6',
      md: 'text-sm px-2 py-1.5 min-w-[2rem] h-8',
      lg: 'text-base px-2.5 py-2 min-w-[2.5rem] h-10',
      xl: 'text-lg px-3 py-2.5 min-w-[3rem] h-12'
    };

    const variantClasses = {
      default: this.getStyleClasses('default'),
      primary: this.getStyleClasses('primary'),
      secondary: this.getStyleClasses('secondary'),
      success: this.getStyleClasses('success'),
      warning: this.getStyleClasses('warning'),
      danger: this.getStyleClasses('danger')
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const stateClasses = [];
    if (this.disabled) stateClasses.push('opacity-50 cursor-not-allowed');
    if (this.interactive && !this.disabled) stateClasses.push('cursor-pointer hover:scale-105 active:scale-95');

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      ...stateClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  private getStyleClasses(variant: string): string {
    const styleMap: Record<string, Record<string, string>> = {
      flat: {
        default: 'bg-gray-100 text-gray-900 border border-gray-200',
        primary: 'bg-blue-100 text-blue-900 border border-blue-200',
        secondary: 'bg-gray-100 text-gray-900 border border-gray-300',
        success: 'bg-green-100 text-green-900 border border-green-200',
        warning: 'bg-yellow-100 text-yellow-900 border border-yellow-200',
        danger: 'bg-red-100 text-red-900 border border-red-200'
      },
      raised: {
        default: 'bg-white text-gray-900 border border-gray-300 shadow-sm hover:shadow-md',
        primary: 'bg-blue-50 text-blue-900 border border-blue-300 shadow-sm hover:shadow-md shadow-blue-100',
        secondary: 'bg-gray-50 text-gray-900 border border-gray-400 shadow-sm hover:shadow-md',
        success: 'bg-green-50 text-green-900 border border-green-300 shadow-sm hover:shadow-md shadow-green-100',
        warning: 'bg-yellow-50 text-yellow-900 border border-yellow-300 shadow-sm hover:shadow-md shadow-yellow-100',
        danger: 'bg-red-50 text-red-900 border border-red-300 shadow-sm hover:shadow-md shadow-red-100'
      },
      outlined: {
        default: 'bg-transparent text-gray-900 border-2 border-gray-300 hover:bg-gray-50',
        primary: 'bg-transparent text-blue-900 border-2 border-blue-300 hover:bg-blue-50',
        secondary: 'bg-transparent text-gray-900 border-2 border-gray-400 hover:bg-gray-50',
        success: 'bg-transparent text-green-900 border-2 border-green-300 hover:bg-green-50',
        warning: 'bg-transparent text-yellow-900 border-2 border-yellow-300 hover:bg-yellow-50',
        danger: 'bg-transparent text-red-900 border-2 border-red-300 hover:bg-red-50'
      }
    };

    const style = styleMap[this.keyStyle];
    return style ? (style[variant] || style['default']) : styleMap['raised']['default'];
  }

  get keyArray(): string[] {
    return Array.isArray(this.keys) ? this.keys : [this.keys];
  }

  onKeyClick(key: string): void {
    if (!this.disabled && this.interactive) {
      this.keyClick.emit(key);
    }
  }

  onKeyHover(key: string): void {
    if (!this.disabled) {
      this.keyHover.emit(key);
    }
  }
}

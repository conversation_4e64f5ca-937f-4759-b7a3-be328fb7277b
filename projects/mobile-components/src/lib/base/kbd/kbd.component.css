/* Enhanced Kbd Component Styles */
:host {
  display: inline-block;
}

/* Interactive keyboard key effects */
kbd {
  user-select: none;
  white-space: nowrap;
}

/* Hover effects for interactive keys */
kbd.cursor-pointer:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Active/pressed state */
kbd.cursor-pointer:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Focus styles for accessibility */
kbd:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Special key styling */
kbd[aria-label*="Ctrl"],
kbd[aria-label*="Alt"],
kbd[aria-label*="Shift"],
kbd[aria-label*="Cmd"],
kbd[aria-label*="Meta"] {
  font-weight: 700;
}

/* Disabled state */
kbd.opacity-50 {
  pointer-events: none;
}

/* Animation for interactive elements */
@keyframes key-press {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

kbd.cursor-pointer:active {
  animation: key-press 0.1s ease-in-out;
}
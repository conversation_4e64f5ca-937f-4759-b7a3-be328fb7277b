import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'base-switch-thin',
  templateUrl: './switch-thin.component.html',
  styleUrls: ['./switch-thin.component.css'],
  standalone: true,
  imports: [NgClass, NgIf],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SwitchThinComponent),
      multi: true
    }
  ]
})
export class SwitchThinComponent implements ControlValueAccessor {
  // Standard inputs (MANDATORY)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'full';

  // Component-specific inputs
  @Input() label: string = 'Slim toggle';
  @Input() checked: boolean = false;
  @Input() disabled: boolean = false;
  @Input() loading: boolean = false;
  @Input() required: boolean = false;
  @Input() name: string = '';
  @Input() value: any = true;
  @Input() uncheckedValue: any = false;

  // Events
  @Output() checkedChange = new EventEmitter<boolean>();
  @Output() valueChange = new EventEmitter<any>();

  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  get computedClasses(): string {
    const baseClasses = 'relative inline-flex items-center cursor-pointer transition-all duration-200 ease-in-out focus:outline-none focus:ring-1 focus:ring-offset-1';
    
    const sizeClasses = {
      xs: 'h-3 w-6',
      sm: 'h-3 w-7', 
      md: 'h-4 w-8',
      lg: 'h-4 w-9',
      xl: 'h-5 w-10'
    };

    const variantClasses = {
      default: this.checked ? 'bg-gray-500 focus:ring-gray-400' : 'bg-gray-300 focus:ring-gray-300',
      primary: this.checked ? 'bg-blue-500 focus:ring-blue-400' : 'bg-gray-300 focus:ring-blue-300',
      secondary: this.checked ? 'bg-gray-500 focus:ring-gray-400' : 'bg-gray-300 focus:ring-gray-300',
      success: this.checked ? 'bg-green-500 focus:ring-green-400' : 'bg-gray-300 focus:ring-green-300',
      warning: this.checked ? 'bg-yellow-400 focus:ring-yellow-300' : 'bg-gray-300 focus:ring-yellow-300',
      danger: this.checked ? 'bg-red-500 focus:ring-red-400' : 'bg-gray-300 focus:ring-red-300'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md', 
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const disabledClasses = this.disabled ? 'opacity-50 cursor-not-allowed' : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      disabledClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get indicatorClasses(): string {
    const baseClasses = 'absolute left-0 inline-block bg-white shadow transform transition ease-in-out duration-200';
    
    const sizeClasses = {
      xs: 'h-2 w-2 top-0.5',
      sm: 'h-2 w-2 top-0.5', 
      md: 'h-3 w-3 top-0.5',
      lg: 'h-3 w-3 top-0.5',
      xl: 'h-4 w-4 top-0.5'
    };

    const positionClasses = {
      xs: this.checked ? 'translate-x-3' : 'translate-x-0.5',
      sm: this.checked ? 'translate-x-4' : 'translate-x-0.5', 
      md: this.checked ? 'translate-x-4' : 'translate-x-0.5',
      lg: this.checked ? 'translate-x-5' : 'translate-x-0.5',
      xl: this.checked ? 'translate-x-5' : 'translate-x-0.5'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md', 
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      baseClasses,
      sizeClasses[this.size],
      positionClasses[this.size],
      roundedClasses[this.rounded]
    ].filter(Boolean).join(' ');
  }

  onToggle(): void {
    if (this.disabled || this.loading) return;
    
    this.checked = !this.checked;
    this.onTouched();
    
    const newValue = this.checked ? this.value : this.uncheckedValue;
    this.onChange(newValue);
    
    this.checkedChange.emit(this.checked);
    this.valueChange.emit(newValue);
  }

  // ControlValueAccessor methods
  writeValue(value: any): void {
    this.checked = value === this.value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}

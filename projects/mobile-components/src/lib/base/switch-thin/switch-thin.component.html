<label [class]="'flex items-center justify-between gap-3 ' + (disabled ? 'cursor-not-allowed' : 'cursor-pointer')">
  <!-- Label text -->
  <span 
    *ngIf="label"
    [class]="'text-sm font-medium select-none ' + (disabled ? 'text-gray-400' : 'text-gray-900')"
  >
    {{ label }}
    <span *ngIf="required" class="text-red-500 ml-1">*</span>
  </span>

  <!-- Switch toggle -->
  <div class="relative">
    <!-- Hidden input for form integration -->
    <input
      type="checkbox"
      [name]="name"
      [checked]="checked"
      [disabled]="disabled || loading"
      [required]="required"
      [attr.aria-label]="label || 'Thin toggle switch'"
      [attr.aria-checked]="checked"
      [attr.aria-disabled]="disabled || loading"
      (change)="onToggle()"
      class="sr-only"
    />
    
    <!-- Visual switch container -->
    <div 
      [ngClass]="computedClasses"
      (click)="onToggle()"
      role="switch"
      [attr.aria-checked]="checked"
      [attr.aria-disabled]="disabled || loading"
      tabindex="0"
      (keydown.enter)="onToggle()"
      (keydown.space)="$event.preventDefault(); onToggle()"
    >
      <!-- Switch indicator -->
      <span [ngClass]="indicatorClasses">
        <!-- Loading spinner -->
        <svg 
          *ngIf="loading" 
          class="animate-spin h-2 w-2 text-gray-400 absolute inset-0 m-auto" 
          fill="none" 
          viewBox="0 0 24 24"
        >
          <circle 
            class="opacity-25" 
            cx="12" 
            cy="12" 
            r="10" 
            stroke="currentColor" 
            stroke-width="4"
          ></circle>
          <path 
            class="opacity-75" 
            fill="currentColor" 
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
      </span>
    </div>
  </div>
</label>

import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { NgClass, NgIf, NgSwitch, NgSwitchCase } from '@angular/common';
import { RouterModule } from '@angular/router';
import { BasePlaceloadComponent } from '../placeload/placeload.component';

@Component({
  selector: 'base-button-icon',
  templateUrl: './button-icon.component.html',
  styleUrls: ['./button-icon.component.css'],
  standalone: true,
  imports: [NgClass, NgIf, NgSwitch, NgSwitchCase, RouterModule, BasePlaceloadComponent],
})
export class ButtonIconComponent implements OnInit {
  // Standard inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost' | 'outline' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() icon: string = 'heroicons:star';
  @Input() ariaLabel: string = 'Icon button';
  @Input() to?: string;
  @Input() href?: string;
  @Input() rel: string = '';
  @Input() target: string = '';
  @Input() type: 'button' | 'submit' | 'reset' = 'button';
  @Input() loading: boolean = false;
  @Input() disabled: boolean = false;

  // Legacy inputs for backward compatibility
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() color?: 'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'light' | 'dark' | 'black' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none';

  // Events
  @Output() clicked = new EventEmitter<Event>();

  is: string = 'button';

  ngOnInit() {
    this.determineElementType();
  }

  get computedClasses(): string {
    const baseClasses = 'inline-flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed select-none';
    
    const sizeClasses = {
      xs: 'h-6 w-6 text-xs',
      sm: 'h-8 w-8 text-sm',
      md: 'h-10 w-10 text-base',
      lg: 'h-12 w-12 text-lg',
      xl: 'h-14 w-14 text-xl'
    };

    const variantClasses = {
      default: 'bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-500',
      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
      success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
      warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500',
      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
      ghost: 'bg-transparent text-gray-600 hover:bg-gray-100 focus:ring-gray-500',
      outline: 'border border-gray-300 bg-transparent text-gray-700 hover:bg-gray-50 focus:ring-gray-500'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const loadingClasses = this.loading ? 'opacity-75 cursor-wait' : '';

    // Legacy class support
    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      loadingClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  private getLegacyClasses(): string {
    // Support legacy color input
    const legacyColors = {
      'default': 'text-gray-700 hover:text-gray-900',
      'default-contrast': 'text-gray-900 hover:text-gray-700',
      'muted': 'text-gray-500 hover:text-gray-700',
      'muted-contrast': 'text-gray-700 hover:text-gray-900',
      'light': 'text-gray-400 hover:text-gray-600',
      'dark': 'text-gray-800 hover:text-gray-900',
      'black': 'text-black hover:text-gray-800',
      'primary': 'text-blue-600 hover:text-blue-700',
      'info': 'text-blue-500 hover:text-blue-600',
      'success': 'text-green-600 hover:text-green-700',
      'warning': 'text-yellow-600 hover:text-yellow-700',
      'danger': 'text-red-600 hover:text-red-700',
      'none': ''
    };

    return this.color ? legacyColors[this.color] || '' : '';
  }

  private determineElementType() {
    if (this.to) {
      this.is = 'router-link';
    } else if (this.href) {
      this.is = 'a';
    } else {
      this.is = 'button';
    }
  }

  onClick(event: Event): void {
    if (!this.disabled && !this.loading) {
      this.clicked.emit(event);
    }
  }
}

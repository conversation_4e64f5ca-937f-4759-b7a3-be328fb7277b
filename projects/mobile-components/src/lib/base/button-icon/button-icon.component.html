<ng-container [ngSwitch]="is">
  <!-- Regular button -->
  <button
    *ngSwitchCase="'button'"
    [type]="type"
    [ngClass]="computedClasses"
    [disabled]="disabled || loading"
    (click)="onClick($event)"
    [attr.aria-label]="ariaLabel"
    [attr.aria-disabled]="disabled || loading">
    
    <ng-container *ngIf="!loading">
      <ng-content>
        <!-- Default star icon if no content provided -->
        <svg class="w-full h-full" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      </ng-content>
    </ng-container>
    
    <base-placeload *ngIf="loading" class="w-4 h-4 rounded-md"></base-placeload>
  </button>

  <!-- Router link -->
  <a 
    *ngSwitchCase="'router-link'"
    [routerLink]="to"
    [ngClass]="computedClasses"
    [attr.aria-label]="ariaLabel"
    role="button">
    
    <ng-container *ngIf="!loading">
      <ng-content>
        <!-- Default star icon if no content provided -->
        <svg class="w-full h-full" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      </ng-content>
    </ng-container>
    
    <base-placeload *ngIf="loading" class="w-4 h-4 rounded-md"></base-placeload>
  </a>

  <!-- External link -->
  <a 
    *ngSwitchCase="'a'"
    [href]="href"
    [target]="target"
    [rel]="rel"
    [ngClass]="computedClasses"
    [attr.aria-label]="ariaLabel"
    role="button">
    
    <ng-container *ngIf="!loading">
      <ng-content>
        <!-- Default star icon if no content provided -->
        <svg class="w-full h-full" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
        </svg>
      </ng-content>
    </ng-container>
    
    <base-placeload *ngIf="loading" class="w-4 h-4 rounded-md"></base-placeload>
  </a>
</ng-container>

<!-- Enhanced Text Component Template -->
<ng-container [ngSwitch]="effectiveElement">
  <!-- Paragraph -->
  <p *ngSwitchCase="'p'" [class]="computedClasses">
    {{ effectiveContent }}
  </p>
  
  <!-- Span -->
  <span *ngSwitchCase="'span'" [class]="computedClasses">
    {{ effectiveContent }}
  </span>
  
  <!-- Div -->
  <div *ngSwitchCase="'div'" [class]="computedClasses">
    {{ effectiveContent }}
  </div>
  
  <!-- Label -->
  <label *ngSwitchCase="'label'" [class]="computedClasses">
    {{ effectiveContent }}
  </label>
  
  <!-- Small -->
  <small *ngSwitchCase="'small'" [class]="computedClasses">
    {{ effectiveContent }}
  </small>
  
  <!-- Strong -->
  <strong *ngSwitchCase="'strong'" [class]="computedClasses">
    {{ effectiveContent }}
  </strong>
  
  <!-- Emphasis -->
  <em *ngSwitchCase="'em'" [class]="computedClasses">
    {{ effectiveContent }}
  </em>
  
  <!-- Mark -->
  <mark *ngSwitchCase="'mark'" [class]="computedClasses">
    {{ effectiveContent }}
  </mark>
  
  <!-- Deleted -->
  <del *ngSwitchCase="'del'" [class]="computedClasses">
    {{ effectiveContent }}
  </del>
  
  <!-- Inserted -->
  <ins *ngSwitchCase="'ins'" [class]="computedClasses">
    {{ effectiveContent }}
  </ins>
  
  <!-- Subscript -->
  <sub *ngSwitchCase="'sub'" [class]="computedClasses">
    {{ effectiveContent }}
  </sub>
  
  <!-- Superscript -->
  <sup *ngSwitchCase="'sup'" [class]="computedClasses">
    {{ effectiveContent }}
  </sup>
  
  <!-- Default fallback -->
  <p *ngSwitchDefault [class]="computedClasses">
    {{ effectiveContent }}
  </p>
</ng-container>

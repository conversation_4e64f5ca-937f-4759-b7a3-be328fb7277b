import { Component, Input, Inject, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Optional } from '@angular/core';
import { CommonModule } from '@angular/common';

export type TextElement = 'p' | 'span' | 'div' | 'label' | 'small' | 'strong' | 'em' | 'mark' | 'del' | 'ins' | 'sub' | 'sup';
export type TextWeight = 'thin' | 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold' | 'black';
export type TextSize = 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | '8xl' | '9xl';
export type TextAlign = 'left' | 'center' | 'right' | 'justify';
export type TextTransform = 'none' | 'uppercase' | 'lowercase' | 'capitalize';
export type TextDecoration = 'none' | 'underline' | 'overline' | 'line-through';
export type TextLineHeight = 'none' | 'tight' | 'snug' | 'normal' | 'relaxed' | 'loose';

@Component({
  selector: 'base-text',
  templateUrl: './text.component.html',
  styleUrls: ['./text.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class TextComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'none';

  // Component-specific inputs
  @Input() content: string = 'Sample text content for preview';
  @Input() element: TextElement = 'p';
  @Input() textSize: TextSize = 'base';
  @Input() weight: TextWeight = 'normal';
  @Input() align: TextAlign = 'left';
  @Input() transform: TextTransform = 'none';
  @Input() decoration: TextDecoration = 'none';
  @Input() lineHeight: TextLineHeight = 'normal';
  @Input() color: string = '';
  @Input() italic: boolean = false;
  @Input() truncate: boolean = false;
  @Input() maxLines?: number;
  @Input() spacing: boolean = true;

  // Legacy inputs for backward compatibility
  @Input() text?: string; // Legacy content prop
  @Input() as?: TextElement; // Legacy element prop
  @Input() item_id: string = Math.random().toString(36).substring(7);

  constructor(@Optional() @Inject('componentProperties') private properties: any) {}

  ngOnInit() {
    if (this.properties) {
      this.content = this.properties.content || this.content;
      this.className = this.properties.className || this.className;
      this.size = this.properties.size || this.size;
      this.variant = this.properties.variant || this.variant;
      this.element = this.properties.element || this.element;
      this.textSize = this.properties.textSize || this.textSize;
      this.weight = this.properties.weight || this.weight;
      this.align = this.properties.align || this.align;
      this.color = this.properties.color || this.color;
    }
  }

  get effectiveContent(): string {
    return this.content || this.text || 'Sample text content for preview';
  }

  get effectiveElement(): TextElement {
    return this.element || this.as || 'p';
  }

  get computedClasses(): string {
    const baseClasses = 'block transition-colors duration-200';
    
    // Standard size classes (kept for consistency with other components)
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    // Text-specific size classes (more comprehensive)
    const textSizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      base: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl',
      '2xl': 'text-2xl',
      '3xl': 'text-3xl',
      '4xl': 'text-4xl',
      '5xl': 'text-5xl',
      '6xl': 'text-6xl',
      '7xl': 'text-7xl',
      '8xl': 'text-8xl',
      '9xl': 'text-9xl'
    };

    // Variant color classes
    const variantClasses = {
      default: 'text-gray-900 dark:text-gray-100',
      primary: 'text-blue-600 dark:text-blue-400',
      secondary: 'text-gray-600 dark:text-gray-300',
      success: 'text-green-600 dark:text-green-400',
      warning: 'text-yellow-600 dark:text-yellow-400',
      danger: 'text-red-600 dark:text-red-400'
    };

    // Font weight classes
    const weightClasses = {
      thin: 'font-thin',
      light: 'font-light',
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
      extrabold: 'font-extrabold',
      black: 'font-black'
    };

    // Text alignment classes
    const alignClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify'
    };

    // Text transform classes
    const transformClasses = {
      none: '',
      uppercase: 'uppercase',
      lowercase: 'lowercase',
      capitalize: 'capitalize'
    };

    // Text decoration classes
    const decorationClasses = {
      none: 'no-underline',
      underline: 'underline',
      overline: 'overline',
      'line-through': 'line-through'
    };

    // Line height classes
    const lineHeightClasses = {
      none: 'leading-none',
      tight: 'leading-tight',
      snug: 'leading-snug',
      normal: 'leading-normal',
      relaxed: 'leading-relaxed',
      loose: 'leading-loose'
    };

    const classes = [
      baseClasses,
      textSizeClasses[this.textSize],
      !this.color ? variantClasses[this.variant] : '', // Only apply variant if no custom color
      weightClasses[this.weight],
      alignClasses[this.align],
      transformClasses[this.transform],
      decorationClasses[this.decoration],
      lineHeightClasses[this.lineHeight],
      this.italic ? 'italic' : '',
      this.truncate ? 'truncate' : '',
      this.maxLines ? `line-clamp-${this.maxLines}` : '',
      this.spacing ? 'mb-2' : '',
      this.color || '', // Custom color override
      this.className
    ];

    return classes.filter(Boolean).join(' ');
  }
}

import { Component, Input, Output, EventEmitter, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'base-switch-ball',
  templateUrl: './switch-ball.component.html',
  styleUrls: ['./switch-ball.component.css'],
  standalone: true,
  imports: [NgClass, NgIf],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SwitchBallComponent),
      multi: true
    }
  ]
})
export class SwitchBallComponent implements ControlValueAccessor {
  // Standard inputs (MANDATORY)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'full';

  // Component-specific inputs
  @Input() label: string = 'Toggle switch';
  @Input() checked: boolean = false;
  @Input() disabled: boolean = false;
  @Input() loading: boolean = false;
  @Input() required: boolean = false;
  @Input() name: string = '';
  @Input() value: any = true;
  @Input() uncheckedValue: any = false;

  // Events
  @Output() checkedChange = new EventEmitter<boolean>();
  @Output() valueChange = new EventEmitter<any>();

  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  get computedClasses(): string {
    const baseClasses = 'relative inline-flex items-center cursor-pointer transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    const sizeClasses = {
      xs: 'h-4 w-7',
      sm: 'h-5 w-9', 
      md: 'h-6 w-11',
      lg: 'h-7 w-12',
      xl: 'h-8 w-14'
    };

    const variantClasses = {
      default: this.checked ? 'bg-gray-600 focus:ring-gray-500' : 'bg-gray-200 focus:ring-gray-300',
      primary: this.checked ? 'bg-blue-600 focus:ring-blue-500' : 'bg-gray-200 focus:ring-blue-300',
      secondary: this.checked ? 'bg-gray-600 focus:ring-gray-500' : 'bg-gray-200 focus:ring-gray-300',
      success: this.checked ? 'bg-green-600 focus:ring-green-500' : 'bg-gray-200 focus:ring-green-300',
      warning: this.checked ? 'bg-yellow-500 focus:ring-yellow-400' : 'bg-gray-200 focus:ring-yellow-300',
      danger: this.checked ? 'bg-red-600 focus:ring-red-500' : 'bg-gray-200 focus:ring-red-300'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md', 
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const disabledClasses = this.disabled ? 'opacity-50 cursor-not-allowed' : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      disabledClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get ballClasses(): string {
    const baseClasses = 'pointer-events-none inline-block bg-white shadow transform ring-0 transition ease-in-out duration-200';
    
    const sizeClasses = {
      xs: 'h-3 w-3',
      sm: 'h-4 w-4', 
      md: 'h-5 w-5',
      lg: 'h-6 w-6',
      xl: 'h-7 w-7'
    };

    const positionClasses = {
      xs: this.checked ? 'translate-x-3' : 'translate-x-0',
      sm: this.checked ? 'translate-x-4' : 'translate-x-0', 
      md: this.checked ? 'translate-x-5' : 'translate-x-0',
      lg: this.checked ? 'translate-x-5' : 'translate-x-0',
      xl: this.checked ? 'translate-x-6' : 'translate-x-0'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md', 
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      baseClasses,
      sizeClasses[this.size],
      positionClasses[this.size],
      roundedClasses[this.rounded]
    ].filter(Boolean).join(' ');
  }

  onToggle(): void {
    if (this.disabled || this.loading) return;
    
    this.checked = !this.checked;
    this.onTouched();
    
    const newValue = this.checked ? this.value : this.uncheckedValue;
    this.onChange(newValue);
    
    this.checkedChange.emit(this.checked);
    this.valueChange.emit(newValue);
  }

  // ControlValueAccessor methods
  writeValue(value: any): void {
    this.checked = value === this.value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }
}

import { Component, Input, OnInit, computed, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';
import { DropdownComponent } from '../dropdown/dropdown.component';
import { DropdownItemComponent } from '../dropdown-item/dropdown-item.component';

export interface BreadcrumbItem {
  to?: any;
  label?: string;
  hideLabel?: boolean;
  icon?: string;
  iconClasses?: string | string[];
}

@Component({
  selector: 'base-breadcrumb',
  templateUrl: './breadcrumb.component.html',
  styleUrls: ['./breadcrumb.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    IconComponent,
    DropdownComponent,
    DropdownItemComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})

export class BreadcrumbComponent implements OnInit {
  // Standard inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  
  // Component-specific inputs
  @Input() items: BreadcrumbItem[] = [
    { to: '/', label: 'Home', icon: 'lucide:home' },
    { to: '/products', label: 'Products' },
    { to: '/products/category', label: 'Category' },
    { label: 'Current Page' }
  ];
  @Input() separator: string = '/';
  @Input() showIcons: boolean = true;
  @Input() maxItems: number = 0; // 0 means no limit
  @Input() showDropdown: boolean = true;
  
  // Legacy inputs for backward compatibility
  @Input() color?: 'primary' | 'dark' | 'black' | 'default' = 'default';
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() classes?: {
    wrapper?: string | string[];
    list?: string | string[];
    dropdown?: string | string[];
    item?: string | string[];
  } = {};

  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'flex items-center space-x-1';
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };
    const variantClasses = {
      default: 'text-gray-600',
      primary: 'text-blue-600',
      secondary: 'text-gray-500',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      danger: 'text-red-600'
    };
    
    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      this.className
    ].filter(Boolean).join(' ');
  }

  // Legacy color classes
  colors: Record<string, string> = {
    primary: 'nui-breadcrumb-primary',
    dark: 'nui-breadcrumb-dark',
    black: 'nui-breadcrumb-black',
  };

  // Get visible items based on maxItems
  get visibleItems(): BreadcrumbItem[] {
    const items = this.computedItems();
    if (this.maxItems === 0 || items.length <= this.maxItems) {
      return items;
    }
    
    // Show first item, ellipsis, and last few items
    const lastItems = items.slice(-(this.maxItems - 1));
    return [items[0], ...lastItems];
  }

  // Get collapsed items for dropdown
  get collapsedItems(): BreadcrumbItem[] {
    const items = this.computedItems();
    if (this.maxItems === 0 || items.length <= this.maxItems) {
      return [];
    }
    
    return items.slice(1, -(this.maxItems - 1));
  }

  constructor(private route: ActivatedRoute, private router: Router) {}

  ngOnInit() {
    // No changes needed here
  }

  computedItems = computed(() => {
    if (this.items) {
      return this.items;
    }

    const breadcrumbItems: any[] = [];
    const indexRoute = this.router.config.find((route) => route.path === '');

    if (
      indexRoute &&
      indexRoute.data &&
      indexRoute.data['breadcrumb'] !== false
    ) {
      if (indexRoute.data['breadcrumb']) {
        breadcrumbItems.push({
          to: '/',
          ...indexRoute.data['breadcrumb'],
        });
      } else if (indexRoute.data['title']) {
        breadcrumbItems.push({
          label: indexRoute.data['title'] as string,
          to: '/',
        });
      }
    }

    let currentRoute = this.route.snapshot;
    while (currentRoute.firstChild) {
      currentRoute = currentRoute.firstChild;
      if (currentRoute.data['breadcrumb'] === false) {
        continue;
      }
      if (currentRoute.data['breadcrumb']) {
        breadcrumbItems.push({
          to: currentRoute.url.join('/'),
          ...currentRoute.data['breadcrumb'],
        });
      } else if (currentRoute.data['title']) {
        breadcrumbItems.push({
          label: currentRoute.data['title'] as string,
          to: currentRoute.url.join('/'),
        });
      }
    }

    return breadcrumbItems;
  });
}

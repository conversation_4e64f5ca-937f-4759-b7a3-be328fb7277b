<!-- Enhanced Input with modern computed classes -->
<div class="space-y-2">
  <!-- Label (non-floating) -->
  <label
    *ngIf="(label && !labelFloat) || (!labelFloat && labelTemplate)"
    class="block text-sm font-medium text-gray-700"
    [for]="id || item_id"
    [ngClass]="classes?.label"
  >
    <ng-container *ngIf="labelTemplate; else defaultLabel">
      <ng-container *ngTemplateOutlet="labelTemplate"></ng-container>
    </ng-container>
    <ng-template #defaultLabel>
      {{ label }}
      <span *ngIf="required" class="text-red-500 ml-1">*</span>
    </ng-template>
  </label>

  <!-- Input wrapper with relative positioning for icons and floating labels -->
  <div class="relative">
    <!-- Main input field -->
    <input
      [id]="id || item_id"
      #inputRef
      [(ngModel)]="modelValue"
      [type]="type"
      [placeholder]="placeholder"
      [disabled]="disabled"
      [readonly]="readonly"
      [required]="required"
      [attr.maxlength]="maxlength || null"
      [attr.minlength]="minlength || null"
      [attr.pattern]="pattern || null"
      [autocomplete]="autocomplete"
      [ngClass]="computedClasses"
      [attr.aria-label]="label || placeholder"
      [attr.aria-required]="required"
      [attr.aria-invalid]="!!error"
      [attr.aria-describedby]="error && isErrorString() ? (item_id + '-error') : null"
      class="focus:outline-none focus:ring-2 focus:ring-offset-0"
      [class.pr-10]="icon || loading"
      [class.pl-10]="icon && !loading && type !== 'number'"
      (input)="onModelChange($any($event.target).value)"
      (focus)="onFocus($event)"
      (blur)="onBlur($event)"
      (change)="onInputChange($event)">

    <!-- Floating label -->
    <label
      *ngIf="(label && labelFloat) || (labelFloat && labelTemplate)"
      class="absolute left-3 transition-all duration-200 pointer-events-none"
      [ngClass]="{
        'text-xs -top-2 bg-white px-1 text-blue-600': modelValue || inputRef.value,
        'text-gray-500 top-3': !modelValue && !inputRef.value
      }"
      [for]="id || item_id"
    >
      <ng-container *ngIf="labelTemplate; else defaultFloatLabel">
        <ng-container *ngTemplateOutlet="labelTemplate"></ng-container>
      </ng-container>
      <ng-template #defaultFloatLabel>
        {{ label }}
        <span *ngIf="required" class="text-red-500 ml-1">*</span>
      </ng-template>
    </label>

    <!-- Loading indicator -->
    <div *ngIf="loading" class="absolute inset-y-0 right-0 flex items-center pr-3">
      <svg class="animate-spin h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
    </div>

    <!-- Icon -->
    <div *ngIf="icon && !loading" class="absolute inset-y-0 right-0 flex items-center pr-3" [ngClass]="classes?.icon">
      <ng-container *ngIf="iconTemplate; else defaultIcon">
        <ng-container *ngTemplateOutlet="iconTemplate"></ng-container>
      </ng-container>
      <ng-template #defaultIcon>
        <base-icon [icon]="icon!" class="h-5 w-5 text-gray-400"></base-icon>
      </ng-template>
    </div>

    <!-- Action content projection -->
    <ng-content select="[action]"></ng-content>
  </div>

  <!-- Error message -->
  <span
    *ngIf="isErrorString()"
    [id]="item_id + '-error'"
    [ngClass]="classes?.error"
    class="text-sm text-red-600 block"
  >
    {{ error }}
  </span>

  <!-- Help text content projection -->
  <ng-content select="[slot=help]"></ng-content>
</div>

<!-- Legacy wrapper for backward compatibility (disabled by default) -->
<div
  *ngIf="false"
  [ngClass]="wrapperClasses"
>
  <label
    *ngIf="(label && !labelFloat) || (!labelFloat && labelTemplate)"
    class="nui-input-label"
    [for]="id"
    [ngClass]="classes?.label"
  >
    <ng-container *ngIf="labelTemplate; else defaultLegacyLabel">
      <ng-container *ngTemplateOutlet="labelTemplate"></ng-container>
    </ng-container>
    <ng-template #defaultLegacyLabel>{{ label }}</ng-template>
  </label>
  <div class="nui-input-outer" [ngClass]="classes?.outer">
    <div>
      <input
        [id]="id"
        #inputRef
        [(ngModel)]="modelValue"
        [type]="type"
        class="nui-input"
        [ngClass]="classes?.input"
        [placeholder]="placeholder"
      />
      <label
        *ngIf="(label && labelFloat) || (labelFloat && labelTemplate)"
        class="nui-label-float"
        [for]="id"
        [ngClass]="classes?.label"
      >
        <ng-container *ngIf="labelTemplate; else defaultFloatLegacyLabel">
          <ng-container *ngTemplateOutlet="labelTemplate"></ng-container>
        </ng-container>
        <ng-template #defaultFloatLegacyLabel>{{ label }}</ng-template>
      </label>
      <div *ngIf="loading" class="nui-input-placeload">
        <base-placeload class="nui-placeload"></base-placeload>
      </div>
      <div *ngIf="icon" class="nui-input-icon" [ngClass]="classes?.icon">
        <ng-container *ngIf="iconTemplate; else defaultLegacyIcon">
          <ng-container *ngTemplateOutlet="iconTemplate"></ng-container>
        </ng-container>
        <ng-template #defaultLegacyIcon>
          <base-icon [icon]="icon!" class="nui-input-icon-inner"></base-icon>
        </ng-template>
      </div>
      <ng-content select="[action]"></ng-content>
    </div>
    <span
      *ngIf="isErrorString()"
      [ngClass]="classes?.error"
      class="nui-input-error-text"
    >
      {{ error }}
    </span>
  </div>
</div>

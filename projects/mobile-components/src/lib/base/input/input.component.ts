import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ViewChild,
  ElementRef,
  TemplateRef,
  Inject,
  Optional,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';

import { CommonModule } from '@angular/common';
import { FormsModule, ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

import { BasePlaceloadComponent } from '../placeload/placeload.component';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'base-input',
  templateUrl: './input.component.html',
  styleUrls: ['./input.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    BasePlaceloadComponent,
    IconComponent
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: InputComponent,
      multi: true
    }
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class InputComponent implements OnInit, ControlValueAccessor {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Legacy inputs (preserved for backward compatibility)
  @Input() id?: string;
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() type: string = 'text';
  @Input() contrast?: 'default' | 'default-contrast' | 'muted' | 'muted-contrast';
  @Input() label: string = 'Sample Input Field';
  @Input() labelFloat?: boolean;
  @Input() icon?: string = 'user';
  @Input() placeholder: string = 'Enter some text here...';
  @Input() error?: string | boolean;
  @Input() colorFocus?: boolean;
  @Input() loading?: boolean;
  @Input() classes?: {
    wrapper?: string | string[];
    outer?: string | string[];
    label?: string | string[];
    input?: string | string[];
    addon?: string | string[];
    error?: string | string[];
    icon?: string | string[];
  };
  @Input() labelTemplate: TemplateRef<any> | null = null;
  @Input() iconTemplate: TemplateRef<any> | null = null;

  // Enhanced inputs
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() readonly: boolean = false;
  @Input() maxlength?: number | null = null;
  @Input() minlength?: number | null = null;
  @Input() pattern?: string | null = null;
  @Input() autocomplete?: string = 'off';
  @Input() value: string | number = '';

  // Events
  @Output() valueChange = new EventEmitter<string | number>();
  @Output() modelChange = new EventEmitter<string | number>();
  @Output() inputFocus = new EventEmitter<FocusEvent>();
  @Output() inputBlur = new EventEmitter<FocusEvent>();
  @Output() inputChange = new EventEmitter<Event>();

  @ViewChild('inputRef') inputElementRef!: ElementRef<HTMLInputElement>;

  modelValue: string | number = '';
  
  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  
  public radiuses: Record<string, string> = {
    none: '',
    sm: 'nui-input-rounded-sm',
    md: 'nui-input-rounded-md',
    lg: 'nui-input-rounded-lg',
    full: 'nui-input-rounded-full',
  };

  public sizes: Record<string, string> = {
    xs: 'nui-input-xs',
    sm: 'nui-input-sm',
    md: 'nui-input-md',
    lg: 'nui-input-lg',
    xl: 'nui-input-xl',
  };

  public contrasts: Record<string, string> = {
    default: 'nui-input-default',
    'default-contrast': 'nui-input-default-contrast',
    muted: 'nui-input-muted',
    'muted-contrast': 'nui-input-muted-contrast',
  };

  constructor(@Optional() @Inject('componentProperties') public properties: any) {}

  ngOnInit() {
    // Handle legacy property injection
    if (this.properties) {
      this.className = this.properties.className || this.className;
      this.size = this.properties.size || this.size;
      this.variant = this.properties.variant || this.variant;
      this.rounded = this.properties.rounded || this.rounded;
      this.type = this.properties.type || this.type;
      this.label = this.properties.label || this.label;
      this.placeholder = this.properties.placeholder || this.placeholder;
      this.disabled = this.properties.disabled || this.disabled;
      this.required = this.properties.required || this.required;
      this.value = this.properties.value || this.value;
    }

    // Initialize modelValue
    this.modelValue = this.value;

    if (this.labelFloat && this.label) {
      console.warn(
        '[ninja-ui][base-input] The "label-float" property is not compatible with the label slot, use the label property instead.'
      );
    }
  }

  get computedClasses(): string {
    const baseClasses = 'relative block w-full transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs px-2 py-1 min-h-[32px]',
      sm: 'text-sm px-3 py-2 min-h-[36px]',
      md: 'text-base px-4 py-3 min-h-[44px]', // Mobile-optimized touch target
      lg: 'text-lg px-5 py-4 min-h-[48px]',
      xl: 'text-xl px-6 py-5 min-h-[52px]'
    };

    const variantClasses = {
      default: 'border border-gray-300 bg-white text-gray-900 focus:border-blue-500 focus:ring-blue-500',
      primary: 'border border-blue-300 bg-blue-50 text-blue-900 focus:border-blue-600 focus:ring-blue-600',
      secondary: 'border border-gray-400 bg-gray-50 text-gray-900 focus:border-gray-600 focus:ring-gray-600',
      success: 'border border-green-300 bg-green-50 text-green-900 focus:border-green-600 focus:ring-green-600',
      warning: 'border border-yellow-300 bg-yellow-50 text-yellow-900 focus:border-yellow-600 focus:ring-yellow-600',
      danger: 'border border-red-300 bg-red-50 text-red-900 focus:border-red-600 focus:ring-red-600'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const stateClasses = [];
    if (this.disabled) stateClasses.push('opacity-50 cursor-not-allowed');
    if (this.error && !this.loading) stateClasses.push('border-red-500 focus:border-red-500 focus:ring-red-500');
    if (this.readonly) stateClasses.push('bg-gray-100 cursor-default');

    // Legacy support
    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      ...stateClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get wrapperClasses(): string {
    const baseClasses = 'nui-input-wrapper';
    const legacyClasses = [
      this.contrast && this.contrasts[this.contrast],
      this.size && this.sizes[this.size],
      this.rounded && this.radiuses[this.rounded],
      this.error && !this.loading ? 'nui-input-error' : '',
      this.loading ? 'nui-input-loading' : '',
      this.labelFloat ? 'nui-input-label-float' : '',
      this.icon ? 'nui-has-icon' : '',
      this.colorFocus ? 'nui-input-focus' : '',
      this.classes?.wrapper
    ].filter(Boolean);

    return [baseClasses, ...legacyClasses].join(' ');
  }

  private getLegacyClasses(): string {
    return [
      this.contrast && this.contrasts[this.contrast],
      this.error && !this.loading ? 'nui-input-error' : '',
      this.loading ? 'nui-input-loading' : '',
      this.colorFocus ? 'nui-input-focus' : ''
    ].filter(Boolean).join(' ');
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    this.modelValue = value || '';
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onModelChange(value: string | number) {
    this.modelValue = value;
    this.onChange(value);
    this.modelChange.emit(this.modelValue);
    this.valueChange.emit(this.modelValue);
  }

  onFocus(event: FocusEvent) {
    this.inputFocus.emit(event);
  }

  onBlur(event: FocusEvent) {
    this.onTouched();
    this.inputBlur.emit(event);
  }

  onInputChange(event: Event) {
    this.inputChange.emit(event);
  }

  looseToNumber(val: any): number | any {
    const n = Number.parseFloat(val);
    return Number.isNaN(n) ? val : n;
  }

  isErrorString(): boolean {
    return typeof this.error === 'string';
  }
}

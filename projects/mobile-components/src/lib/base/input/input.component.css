/* Enhanced Input Component Styles */
:host {
  display: block;
}

/* Enhanced focus styles */
.enhanced-input:focus {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Floating label animation */
.floating-label {
  transition: all 0.2s ease-in-out;
  transform-origin: left top;
}

.floating-label.active {
  transform: translateY(-1.5rem) scale(0.75);
  color: #3b82f6;
}

/* Invalid state styling */
input[aria-invalid="true"] {
  border-color: #ef4444 !important;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Disabled state styling */
input:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Read-only state styling */
input:read-only {
  cursor: default;
  background-color: #f9fafb;
}

/* Icon positioning adjustments */
.input-with-icon {
  padding-right: 2.5rem;
}

.input-with-left-icon {
  padding-left: 2.5rem;
}

/* Loading state animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading-input {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Enhanced accessibility focus ring */
input:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}
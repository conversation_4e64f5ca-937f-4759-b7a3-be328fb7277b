import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  OnInit, 
  OnChanges,
  ChangeDetectionStrategy,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-autocomplete-item',
  templateUrl: './autocomplete-item.component.html',
  styleUrls: ['./autocomplete-item.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class AutocompleteItemComponent implements OnInit, OnChanges {
  // Standard inputs as required by the story
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs for autocomplete item functionality
  @Input() text: string = 'Autocomplete Item';
  @Input() value: any = '';
  @Input() icon?: string;
  @Input() iconRight?: string;
  @Input() subtitle?: string;
  @Input() selected: boolean = false;
  @Input() disabled: boolean = false;
  @Input() highlighted: boolean = false;
  @Input() divider: boolean = false;
  @Input() clickable: boolean = true;

  // Event outputs for user interaction
  @Output() itemClick = new EventEmitter<any>();
  @Output() itemSelect = new EventEmitter<any>();

  // Internal computed classes
  classes: string = '';

  // Style mappings for different properties
  private sizes: Record<string, string> = {
    xs: 'text-xs py-1 px-2',
    sm: 'text-sm py-1.5 px-3',
    md: 'text-base py-2 px-4',
    lg: 'text-lg py-2.5 px-5',
    xl: 'text-xl py-3 px-6'
  };

  private variants: Record<string, string> = {
    default: 'text-gray-700 hover:bg-gray-100',
    primary: 'text-blue-700 hover:bg-blue-50',
    secondary: 'text-gray-600 hover:bg-gray-50',
    success: 'text-green-700 hover:bg-green-50',
    warning: 'text-yellow-700 hover:bg-yellow-50',
    danger: 'text-red-700 hover:bg-red-50'
  };

  private roundeds: Record<string, string> = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    full: 'rounded-full'
  };

  ngOnInit(): void {
    this.computeClasses();
  }

  ngOnChanges(): void {
    this.computeClasses();
  }

  private computeClasses(): void {
    const baseClasses = [
      'block w-full text-left transition-colors duration-200',
      this.clickable ? 'cursor-pointer' : 'cursor-default',
      this.disabled ? 'opacity-50 cursor-not-allowed' : '',
      this.selected ? 'bg-blue-100 text-blue-900' : '',
      this.highlighted ? 'bg-gray-100' : '',
      this.divider ? 'border-b border-gray-200' : '',
      this.sizes[this.size] || this.sizes['md'],
      this.variants[this.variant] || this.variants['default'],
      this.roundeds[this.rounded] || this.roundeds['md'],
      this.className
    ];

    this.classes = baseClasses
      .filter(Boolean)
      .join(' ');
  }

  onItemClick(event: Event): void {
    if (this.disabled || !this.clickable) {
      event.preventDefault();
      return;
    }

    this.itemClick.emit({
      value: this.value,
      text: this.text,
      event: event
    });

    if (!this.selected) {
      this.itemSelect.emit({
        value: this.value,
        text: this.text,
        event: event
      });
    }
  }
}

<!-- Enhanced Table Component Template -->
<div [class]="tableWrapperClasses">
  <!-- Loading State -->
  <div *ngIf="loading" class="flex justify-center items-center py-8">
    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
    <span class="ml-2 text-gray-600">Loading...</span>
  </div>

  <!-- Table -->
  <table *ngIf="!loading" [class]="computedClasses">
    <!-- Header -->
    <thead>
      <tr [class]="headerRowClasses">
        <th *ngFor="let column of columns; trackBy: trackByColumn"
            [class]="getHeaderCellClasses(column)"
            [style.width]="column.width"
            (click)="onHeaderClick(column)">
          <div class="flex items-center justify-between">
            <span>{{ column.label }}</span>
            <span *ngIf="column.sortable" class="ml-1 text-gray-400">
              {{ getSortIcon(column) || '↕' }}
            </span>
          </div>
        </th>
      </tr>
    </thead>

    <!-- Body -->
    <tbody>
      <!-- Data Rows -->
      <tr *ngFor="let row of data; let i = index; trackBy: trackByRow"
          [class]="getRowClasses(row, i)"
          (click)="onRowClick(row)">
        <td *ngFor="let column of columns; trackBy: trackByColumn"
            [class]="getCellClasses(column)">
          <ng-container [ngSwitch]="column.type">
            <!-- Boolean Type -->
            <span *ngSwitchCase="'boolean'" 
                  [class]="getCellValue(row, column) ? 'text-green-600' : 'text-red-600'">
              {{ getCellValue(row, column) ? 'Yes' : 'No' }}
            </span>
            
            <!-- Number Type -->
            <span *ngSwitchCase="'number'" class="font-mono">
              {{ getCellValue(row, column) | number }}
            </span>
            
            <!-- Date Type -->
            <span *ngSwitchCase="'date'">
              {{ getCellValue(row, column) | date:'mediumDate' }}
            </span>
            
            <!-- Default/Text Type -->
            <span *ngSwitchDefault>
              {{ getCellValue(row, column) }}
            </span>
          </ng-container>
        </td>
      </tr>

      <!-- Empty State -->
      <tr *ngIf="!data || data.length === 0">
        <td [attr.colspan]="columns.length" class="px-4 py-8 text-center text-gray-500">
          {{ emptyMessage }}
        </td>
      </tr>
    </tbody>
  </table>
</div>

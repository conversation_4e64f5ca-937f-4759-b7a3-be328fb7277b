import { Component, Input, OnInit, Type, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { computed } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-paragraph',
  templateUrl: './paragraph.component.html',
  styleUrls: ['./paragraph.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ParagraphComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'none';

  // Component-specific inputs
  @Input() text: string = 'This is a sample paragraph demonstrating the enhanced paragraph component with full Tailwind customization support.';
  @Input() align: 'left' | 'center' | 'right' | 'justify' = 'left';
  @Input() weight: 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold' = 'normal';
  @Input() leading: 'none' | 'tight' | 'snug' | 'normal' | 'relaxed' | 'loose' = 'normal';
  @Input() spacing: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() truncate: boolean = false;
  @Input() italic: boolean = false;
  @Input() underline: boolean = false;
  @Input() element: 'p' | 'div' | 'span' = 'p';
  @Input() maxLines?: number;

  // Legacy support
  @Input() as?: string;
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() lead?: 'none' | 'tight' | 'snug' | 'normal' | 'relaxed' | 'loose';
  @Input() innerText: string = '';

  componentType: Type<any> = 'p' as any;

  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'text-gray-900 dark:text-gray-100',
      primary: 'text-blue-600 dark:text-blue-400',
      secondary: 'text-gray-600 dark:text-gray-400',
      success: 'text-green-600 dark:text-green-400',
      warning: 'text-yellow-600 dark:text-yellow-400',
      danger: 'text-red-600 dark:text-red-400'
    };

    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const alignClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify'
    };

    const weightClasses = {
      light: 'font-light',
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
      extrabold: 'font-extrabold'
    };

    const leadingClasses = {
      none: 'leading-none',
      tight: 'leading-tight',
      snug: 'leading-snug',
      normal: 'leading-normal',
      relaxed: 'leading-relaxed',
      loose: 'leading-loose'
    };

    const spacingClasses = {
      none: '',
      sm: 'mb-2',
      md: 'mb-4',
      lg: 'mb-6',
      xl: 'mb-8'
    };

    const additionalClasses = [
      this.truncate ? 'truncate' : '',
      this.italic ? 'italic' : '',
      this.underline ? 'underline' : '',
      this.maxLines ? `line-clamp-${this.maxLines}` : ''
    ];

    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      alignClasses[this.align],
      weightClasses[this.weight],
      leadingClasses[this.leading],
      spacingClasses[this.spacing],
      ...additionalClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  // Legacy support method
  getLegacyClasses(): string {
    const legacyClasses = [];
    
    if (this.sizes[this.size as keyof typeof this.sizes]) {
      legacyClasses.push(this.sizes[this.size as keyof typeof this.sizes]);
    }
    
    if (this.weight && this.weights[this.weight as keyof typeof this.weights]) {
      legacyClasses.push(this.weights[this.weight as keyof typeof this.weights]);
    }
    
    if (this.lead && this.leads[this.lead as keyof typeof this.leads]) {
      legacyClasses.push(this.leads[this.lead as keyof typeof this.leads]);
    }
    
    return legacyClasses.join(' ');
  }

  // Get the content to display
  get displayText(): string {
    return this.text || this.innerText || '';
  }

  // Get the element tag to use
  get elementTag(): string {
    return this.as || this.element;
  }

  sizes: Record<string, string> = {
    xs: 'nui-paragraph-xs',
    sm: 'nui-paragraph-sm',
    md: 'nui-paragraph-md',
    lg: 'nui-paragraph-lg',
    xl: 'nui-paragraph-xl',
    '2xl': 'nui-paragraph-2xl',
    '3xl': 'nui-paragraph-3xl',
    '4xl': 'nui-paragraph-4xl',
    '5xl': 'nui-paragraph-5xl',
    '6xl': 'nui-paragraph-6xl',
    '7xl': 'nui-paragraph-7xl',
    '8xl': 'nui-paragraph-8xl',
    '9xl': 'nui-paragraph-9xl',
  };

  weights: Record<string, string> = {
    light: 'nui-weight-light',
    normal: 'nui-weight-normal',
    medium: 'nui-weight-medium',
    semibold: 'nui-weight-semibold',
    bold: 'nui-weight-bold',
    extrabold: 'nui-weight-extrabold',
  };

  leads: Record<string, string> = {
    none: 'nui-lead-none',
    tight: 'nui-lead-tight',
    snug: 'nui-lead-snug',
    normal: 'nui-lead-normal',
    relaxed: 'nui-lead-relaxed',
    loose: 'nui-lead-loose',
  };

  classes: string[] = [];

  ngOnInit() {
    this.updateClasses();
  }

  private updateClasses() {
    // Legacy support - maintain the old classes array for backward compatibility
    this.classes = [
      'nui-paragraph',
      this.size && this.sizes[this.size],
      this.weight && this.weights[this.weight],
      this.lead && this.leads[this.lead],
    ].filter((cls): cls is string => !!cls);
  }
}

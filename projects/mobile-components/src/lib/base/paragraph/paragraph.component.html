<!-- Modern Tailwind Implementation -->
<ng-container [ngSwitch]="elementTag">
  <p *ngSwitchCase="'p'" [ngClass]="computedClasses" [attr.id]="item_id">
    {{ displayText }}
    <ng-content></ng-content>
  </p>
  
  <div *ngSwitchCase="'div'" [ngClass]="computedClasses" [attr.id]="item_id">
    {{ displayText }}
    <ng-content></ng-content>
  </div>
  
  <span *ngSwitchCase="'span'" [ngClass]="computedClasses" [attr.id]="item_id">
    {{ displayText }}
    <ng-content></ng-content>
  </span>
  
  <!-- Default fallback -->
  <p *ngSwitchDefault [ngClass]="computedClasses" [attr.id]="item_id">
    {{ displayText }}
    <ng-content></ng-content>
  </p>
</ng-container>

<!-- Legacy Template (Hidden, for backward compatibility) -->
<div class="hidden legacy-paragraph-wrapper">
  <div [ngClass]="classes">
    {{ innerText }}
    <ng-content></ng-content>
  </div>
</div>

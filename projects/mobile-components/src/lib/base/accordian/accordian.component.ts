import { Component, Input, Output, EventEmitter, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { HeadingComponent } from '../heading/heading.component';
import { ParagraphComponent } from '../paragraph/paragraph.component';

// Type definitions for component inputs
export interface AccordianItem {
  title: string;
  content: string;
}

export interface AccordianClassConfig {
  wrapper?: string | string[];
  details?: string | string[];
  summary?: string | string[];
  header?: string | string[];
  content?: string | string[];
}

@Component({
  selector: 'base-accordian',
  templateUrl: './accordian.component.html',
  styleUrls: ['./accordian.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class AccordianComponent {
  // Standard Tailwind inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';
  
  // Component-specific inputs
  @Input() items: AccordianItem[] = [
    { title: 'First Section', content: 'This is the content of the first accordion section. It can contain any text or HTML content.' },
    { title: 'Second Section', content: 'This is the content of the second accordion section. You can expand multiple sections or configure it to show only one at a time.' },
    { title: 'Third Section', content: 'This is the content of the third accordion section. Perfect for organizing content in a collapsible format.' }
  ];
  @Input() openItems: number[] = [0];
  @Input() exclusive: boolean = false;
  @Input() action: 'dot' | 'chevron' | 'plus' = 'chevron';
  @Input() dotColor: 'default' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'dark' | 'black' = 'primary';
  
  // Legacy support
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() color: 'default' | 'default-contrast' | 'muted' | 'muted-contrast' = 'default';
  @Input() classes: AccordianClassConfig = {};
  @Output() open = new EventEmitter<AccordianItem>();

  // Base classes for the component
  private readonly baseClasses = 'nui-accordion border border-muted-200 dark:border-muted-700';

  // Size-based classes
  private readonly sizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl'
  };

  // Variant-based classes
  private readonly variantClasses = {
    default: 'bg-white dark:bg-muted-800',
    primary: 'bg-primary-50 dark:bg-primary-900 border-primary-200 dark:border-primary-700',
    secondary: 'bg-muted-50 dark:bg-muted-900',
    success: 'bg-success-50 dark:bg-success-900 border-success-200 dark:border-success-700',
    warning: 'bg-warning-50 dark:bg-warning-900 border-warning-200 dark:border-warning-700',
    danger: 'bg-danger-50 dark:bg-danger-900 border-danger-200 dark:border-danger-700'
  };

  // Rounded classes
  private readonly roundedClasses = {
    none: 'rounded-none',
    sm: 'rounded-sm',
    md: 'rounded-md',
    lg: 'rounded-lg',
    full: 'rounded-full'
  };

  // Computed classes getter
  get computedClasses(): string {
    return [
      this.baseClasses,
      this.sizeClasses[this.size],
      this.variantClasses[this.variant],
      this.roundedClasses[this.rounded],
      this.className
    ].filter(Boolean).join(' ');
  }

  colors = {
    default: 'nui-accordion-default',
    'default-contrast': 'nui-accordion-default-contrast',
    muted: 'nui-accordion-muted',
    'muted-contrast': 'nui-accordion-muted-contrast',
  };

  dotColors = {
    default: 'nui-dot-default',
    primary: 'nui-dot-primary',
    info: 'nui-dot-info',
    success: 'nui-dot-success',
    warning: 'nui-dot-warning',
    danger: 'nui-dot-danger',
    dark: 'nui-dot-dark',
    black: 'nui-dot-black',
  };

  radiuses = {
    none: '',
    sm: 'nui-accordion-rounded-sm',
    md: 'nui-accordion-rounded-md',
    lg: 'nui-accordion-rounded-lg',
  };

  actions = {
    dot: 'nui-accordion-dot',
    chevron: 'nui-accordion-chevron',
    plus: 'nui-accordion-plus',
  };

  get internalOpenItems() {
    return this.openItems;
  }

  toggle(index: number) {
    const isOpen = this.openItems.includes(index);

    if (this.exclusive) {
      this.openItems = isOpen ? [] : [index];
    } else {
      if (isOpen) {
        this.openItems = this.openItems.filter((i) => i !== index);
      } else {
        this.openItems = [...this.openItems, index];
      }
    }

    if (!isOpen) {
      this.open.emit(this.items[index]);
    }
  }
}

import { ComponentFixture, TestBed } from '@angular/core/testing';
import { AccordianComponent } from './accordian.component';

describe('AccordianComponent', () => {
  let component: AccordianComponent;
  let fixture: ComponentFixture<AccordianComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [AccordianComponent] // Use imports for standalone component
    });
    fixture = TestBed.createComponent(AccordianComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have default values', () => {
    expect(component.size).toBe('md');
    expect(component.variant).toBe('default');
    expect(component.rounded).toBe('md');
    expect(component.className).toBe('');
    expect(component.items.length).toBe(3);
  });

  it('should apply computed classes correctly', () => {
    component.size = 'lg';
    component.variant = 'primary';
    component.rounded = 'lg';
    component.className = 'custom-class';
    
    const computedClasses = component.computedClasses;
    expect(computedClasses).toContain('text-lg');
    expect(computedClasses).toContain('bg-primary-50');
    expect(computedClasses).toContain('rounded-lg');
    expect(computedClasses).toContain('custom-class');
  });
});

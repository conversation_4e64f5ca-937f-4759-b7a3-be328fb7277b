<div class="nui-accordion-wrapper" [ngClass]="className">
  <div
    *ngFor="let item of items; let key = index"
    [ngClass]="[
      computedClasses,
      color && colors[color],
      dotColor && dotColors[dotColor],
      action && actions[action],
      classes.wrapper
    ]"
  >
    <details
      [open]="internalOpenItems.includes(key)"
      class="nui-accordion-detail"
      [ngClass]="classes.details"
    >
      <summary
        class="nui-accordion-summary"
        [ngClass]="classes.summary"
        tabindex="0"
        (click)="$event.preventDefault(); toggle(key)"
      >
        <ng-container
          *ngTemplateOutlet="
            accordionItemSummary;
            context: { $implicit: item, index: key }
          "
        ></ng-container>
      </summary>
      <div class="nui-accordion-content" [ngClass]="classes.content">
        <ng-container
          *ngTemplateOutlet="
            accordionItemContent;
            context: { $implicit: item, index: key }
          "
        ></ng-container>
      </div>
    </details>
  </div>
</div>

<ng-template #accordionItemSummary let-item let-index="index">
  <div class="nui-accordion-header" [ngClass]="classes.header">
    <base-heading
      as="h4"
      size="sm"
      weight="medium"
      lead="none"
      class="nui-accordion-header-inner"
    >
      {{ item.title }}
    </base-heading>

    <div *ngIf="action === 'dot'" class="nui-accordion-dot"></div>
    <div *ngIf="action === 'chevron'" class="nui-icon-outer">
      <ion-icon name="chevron-down-outline" class="nui-chevron-icon"></ion-icon>
    </div>
    <div *ngIf="action === 'plus'" class="nui-icon-outer">
      <ion-icon name="add-outline" class="nui-plus-icon"></ion-icon>
    </div>
  </div>
</ng-template>

<ng-template #accordionItemContent let-item let-index="index">
  <base-paragraph size="sm" lead="tight">
    {{ item.content }}
  </base-paragraph>
</ng-template>

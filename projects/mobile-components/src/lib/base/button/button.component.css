@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .nui-button {
    @apply inline-flex relative justify-center items-center space-x-1 font-sans font-normal leading-5 no-underline transition-all duration-300 disabled:opacity-60 disabled:cursor-not-allowed hover:shadow-none;
  }

  /* Colors */
  .nui-button-primary {
    @apply text-white bg-primary-500 hover:bg-primary-400 active:bg-primary-500 focus:bg-primary-400;
  }

  .nui-button-secondary {
    @apply text-white bg-secondary-500 hover:bg-secondary-400 active:bg-secondary-500 focus:bg-secondary-400;
  }

  .nui-button-success {
    @apply text-white bg-success-500 hover:bg-success-400 active:bg-success-500 focus:bg-success-400;
  }

  .nui-button-danger {
    @apply text-white bg-danger-500 hover:bg-danger-400 active:bg-danger-500 focus:bg-danger-400;
  }

  .nui-button-warning {
    @apply text-white bg-warning-500 hover:bg-warning-400 active:bg-warning-500 focus:bg-warning-400;
  }

  .nui-button-info {
    @apply text-white bg-info-500 hover:bg-info-400 active:bg-info-500 focus:bg-info-400;
  }

  /* Sizes */
  .nui-button-sm {
    @apply px-3 py-1 h-8 text-sm;
  }

  .nui-button-md {
    @apply px-5 py-2 h-10 text-sm;
  }

  .nui-button-lg {
    @apply px-6 py-2 h-12 text-base;
  }

  .nui-button-xl {
    @apply px-10 py-4 h-14 text-base;
  }

  /* Rounded variants */
  .nui-button-rounded-none {
    @apply rounded-none;
  }

  .nui-button-rounded-sm {
    @apply rounded-md;
  }

  .nui-button-rounded-md {
    @apply rounded-lg;
  }

  .nui-button-rounded-lg {
    @apply rounded-xl;
  }

  .nui-button-rounded-full {
    @apply rounded-full;
  }

  /* Style variants */
  .nui-button-solid {
    @apply border-0;
  }

  .nui-button-outline {
    @apply bg-transparent border-2;
  }

  .nui-button-outline.nui-button-primary {
    @apply border-primary-500 text-primary-500 hover:bg-primary-500 hover:text-white;
  }

  .nui-button-outline.nui-button-secondary {
    @apply border-secondary-500 text-secondary-500 hover:bg-secondary-500 hover:text-white;
  }

  .nui-button-outline.nui-button-success {
    @apply border-success-500 text-success-500 hover:bg-success-500 hover:text-white;
  }

  .nui-button-outline.nui-button-danger {
    @apply border-danger-500 text-danger-500 hover:bg-danger-500 hover:text-white;
  }

  .nui-button-outline.nui-button-warning {
    @apply border-warning-500 text-warning-500 hover:bg-warning-500 hover:text-white;
  }

  .nui-button-outline.nui-button-info {
    @apply border-info-500 text-info-500 hover:bg-info-500 hover:text-white;
  }

  .nui-button-pastel {
    @apply bg-opacity-10 border-0;
  }

  .nui-button-pastel.nui-button-primary {
    @apply bg-primary-500 text-primary-500 hover:bg-opacity-20;
  }

  .nui-button-pastel.nui-button-secondary {
    @apply bg-secondary-500 text-secondary-500 hover:bg-opacity-20;
  }

  .nui-button-pastel.nui-button-success {
    @apply bg-success-500 text-success-500 hover:bg-opacity-20;
  }

  .nui-button-pastel.nui-button-danger {
    @apply bg-danger-500 text-danger-500 hover:bg-opacity-20;
  }

  .nui-button-pastel.nui-button-warning {
    @apply bg-warning-500 text-warning-500 hover:bg-opacity-20;
  }

  .nui-button-pastel.nui-button-info {
    @apply bg-info-500 text-info-500 hover:bg-opacity-20;
  }
}

import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'mobile-swipe-card',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div [ngClass]="computedClasses">
      <!-- Left Swipe Action -->
      <div 
        *ngIf="leftAction"
        class="absolute inset-y-0 left-0 flex items-center justify-center w-20 bg-red-500 text-white"
      >
        <i [class]="leftAction.icon || 'fas fa-trash'"></i>
      </div>

      <!-- Right Swipe Action -->
      <div 
        *ngIf="rightAction"
        class="absolute inset-y-0 right-0 flex items-center justify-center w-20 bg-green-500 text-white"
      >
        <i [class]="rightAction.icon || 'fas fa-check'"></i>
      </div>

      <!-- Card Content -->
      <div 
        class="relative bg-white z-10 transition-transform duration-200 touch-manipulation"
        [style.transform]="'translateX(' + translateX + 'px)'"
        (touchstart)="onTouchStart($event)"
        (touchmove)="onTouchMove($event)"
        (touchend)="onTouchEnd($event)"
        (click)="onCardClick()"
      >
        <!-- Card Header -->
        <div *ngIf="title || subtitle" class="p-4 border-b border-gray-100">
          <h3 *ngIf="title" class="text-lg font-semibold text-gray-900">{{ title }}</h3>
          <p *ngIf="subtitle" class="text-sm text-gray-600">{{ subtitle }}</p>
        </div>

        <!-- Card Body -->
        <div class="p-4">
          <ng-content></ng-content>
        </div>

        <!-- Card Footer -->
        <div *ngIf="hasActions" class="p-4 border-t border-gray-100">
          <ng-content select="[slot=actions]"></ng-content>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .swipe-card {
      position: relative;
      overflow: hidden;
      cursor: grab;
    }
    
    .swipe-card:active {
      cursor: grabbing;
    }
    
    .swipe-threshold {
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }
  `]
})
export class MobileSwipeCardComponent {
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() swipeEnabled: boolean = true;
  @Input() swipeThreshold: number = 80;
  @Input() leftAction?: { icon?: string; label?: string; action: () => void };
  @Input() rightAction?: { icon?: string; label?: string; action: () => void };
  @Input() className: string = '';

  @Output() cardClick = new EventEmitter<void>();
  @Output() leftSwipe = new EventEmitter<void>();
  @Output() rightSwipe = new EventEmitter<void>();

  translateX: number = 0;
  startX: number = 0;
  currentX: number = 0;
  isDragging: boolean = false;
  hasActions: boolean = false;

  get computedClasses(): string {
    const baseClasses = 'swipe-card bg-white border border-gray-200 rounded-lg shadow-sm';
    const thresholdClasses = Math.abs(this.translateX) > this.swipeThreshold ? 'swipe-threshold' : '';

    return [
      baseClasses,
      thresholdClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  onTouchStart(event: TouchEvent): void {
    if (!this.swipeEnabled) return;
    
    this.isDragging = true;
    this.startX = event.touches[0].clientX;
    this.currentX = this.startX;
  }

  onTouchMove(event: TouchEvent): void {
    if (!this.isDragging || !this.swipeEnabled) return;
    
    event.preventDefault();
    this.currentX = event.touches[0].clientX;
    this.translateX = this.currentX - this.startX;
    
    // Limit the swipe distance
    const maxSwipe = this.swipeThreshold * 1.5;
    this.translateX = Math.max(-maxSwipe, Math.min(maxSwipe, this.translateX));
  }

  onTouchEnd(event: TouchEvent): void {
    if (!this.isDragging || !this.swipeEnabled) return;
    
    this.isDragging = false;
    
    // Check if swipe threshold was reached
    if (Math.abs(this.translateX) > this.swipeThreshold) {
      if (this.translateX > 0 && this.rightAction) {
        this.rightSwipe.emit();
        this.rightAction.action();
      } else if (this.translateX < 0 && this.leftAction) {
        this.leftSwipe.emit();
        this.leftAction.action();
      }
    }
    
    // Reset position
    this.translateX = 0;
  }

  onCardClick(): void {
    if (!this.isDragging && Math.abs(this.translateX) < 10) {
      this.cardClick.emit();
    }
  }

  resetPosition(): void {
    this.translateX = 0;
    this.isDragging = false;
  }
}

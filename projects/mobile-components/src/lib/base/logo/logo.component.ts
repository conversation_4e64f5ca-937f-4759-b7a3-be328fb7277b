import { Component, Input, Output, EventEmitter, OnInit, Inject, Optional, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'base-logo',
  templateUrl: './logo.component.html',
  styleUrls: ['./logo.component.css'],
  standalone: true,
  imports: [CommonModule, IconComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class LogoComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Logo-specific inputs
  @Input() src: string = 'https://via.placeholder.com/120x120/3B82F6/FFFFFF?text=LOGO';
  @Input() alt: string = 'Company Logo';
  @Input() name: string = 'Brand Name';
  @Input() tagline: string = '';
  @Input() showName: boolean = true;
  @Input() showTagline: boolean = false;
  @Input() layout: 'horizontal' | 'vertical' | 'icon-only' = 'horizontal';
  @Input() textPosition: 'right' | 'bottom' | 'overlay' = 'right';
  @Input() alignment: 'left' | 'center' | 'right' = 'left';
  @Input() clickable: boolean = false;
  @Input() href: string = '';
  @Input() target: '_blank' | '_self' | '_parent' | '_top' = '_self';
  @Input() loading: boolean = false;
  @Input() fallbackIcon: string = 'building-office';
  @Input() badge: string = '';
  @Input() badgeVariant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'primary';

  // Events
  @Output() logoClick = new EventEmitter<Event>();
  @Output() imageLoad = new EventEmitter<Event>();
  @Output() imageError = new EventEmitter<Event>();

  // Internal state
  imageLoadError: boolean = false;
  imageLoaded: boolean = false;

  constructor(@Optional() @Inject('componentProperties') public properties: any) {}

  ngOnInit(): void {
    if (this.properties) {
      // Map properties from injected properties
      this.className = this.properties.className || this.className;
      this.size = this.properties.size || this.size;
      this.variant = this.properties.variant || this.variant;
      this.rounded = this.properties.rounded || this.rounded;
      this.src = this.properties.src || this.src;
      this.alt = this.properties.alt || this.alt;
      this.name = this.properties.name || this.name;
      this.tagline = this.properties.tagline || this.tagline;
      this.showName = this.properties.showName ?? this.showName;
      this.showTagline = this.properties.showTagline ?? this.showTagline;
      this.layout = this.properties.layout || this.layout;
      this.textPosition = this.properties.textPosition || this.textPosition;
      this.alignment = this.properties.alignment || this.alignment;
      this.clickable = this.properties.clickable ?? this.clickable;
      this.href = this.properties.href || this.href;
      this.target = this.properties.target || this.target;
      this.loading = this.properties.loading ?? this.loading;
      this.fallbackIcon = this.properties.fallbackIcon || this.fallbackIcon;
      this.badge = this.properties.badge || this.badge;
      this.badgeVariant = this.properties.badgeVariant || this.badgeVariant;
    }
  }

  get computedClasses(): string {
    const baseClasses = 'inline-flex items-center transition-all duration-200';
    
    const sizeClasses = {
      xs: 'gap-2',
      sm: 'gap-2',
      md: 'gap-3',
      lg: 'gap-4',
      xl: 'gap-4'
    };

    const variantClasses = {
      default: '',
      primary: 'text-blue-600',
      secondary: 'text-gray-600',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      danger: 'text-red-600'
    };

    const layoutClasses = {
      horizontal: 'flex-row',
      vertical: 'flex-col',
      'icon-only': 'flex-row'
    };

    const alignmentClasses = {
      left: 'justify-start text-left',
      center: 'justify-center text-center',
      right: 'justify-end text-right'
    };

    const interactiveClasses = this.clickable || this.href ? 
      'cursor-pointer hover:opacity-80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500' : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      layoutClasses[this.layout],
      alignmentClasses[this.alignment],
      interactiveClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get imageClasses(): string {
    const baseClasses = 'flex-shrink-0 object-cover transition-all duration-200';
    
    const sizeClasses = {
      xs: 'w-6 h-6',
      sm: 'w-8 h-8',
      md: 'w-12 h-12',
      lg: 'w-16 h-16',
      xl: 'w-20 h-20'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const loadingClasses = this.loading ? 'animate-pulse bg-gray-200' : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      roundedClasses[this.rounded],
      loadingClasses
    ].filter(Boolean).join(' ');
  }

  get textClasses(): string {
    const baseClasses = 'transition-colors duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    return [
      baseClasses,
      sizeClasses[this.size]
    ].filter(Boolean).join(' ');
  }

  get taglineClasses(): string {
    const baseClasses = 'transition-colors duration-200 opacity-75';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-xs',
      md: 'text-sm',
      lg: 'text-base',
      xl: 'text-lg'
    };

    return [
      baseClasses,
      sizeClasses[this.size]
    ].filter(Boolean).join(' ');
  }

  get badgeClasses(): string {
    const baseClasses = 'absolute -top-1 -right-1 px-1.5 py-0.5 text-xs font-medium rounded-full border';
    
    const variantClasses = {
      primary: 'bg-blue-50 text-blue-700 border-blue-200',
      secondary: 'bg-gray-50 text-gray-700 border-gray-200',
      success: 'bg-green-50 text-green-700 border-green-200',
      warning: 'bg-yellow-50 text-yellow-700 border-yellow-200',
      danger: 'bg-red-50 text-red-700 border-red-200'
    };

    return [
      baseClasses,
      variantClasses[this.badgeVariant]
    ].filter(Boolean).join(' ');
  }

  get initials(): string {
    if (!this.name) return '';
    return this.name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .substring(0, 2)
      .toUpperCase();
  }

  get shouldShowFallback(): boolean {
    return this.imageLoadError || !this.src || this.loading;
  }

  onClick(event: Event): void {
    if (!this.clickable && !this.href) return;
    
    this.logoClick.emit(event);
    
    if (this.href) {
      if (this.target === '_blank') {
        window.open(this.href, this.target);
      } else {
        window.location.href = this.href;
      }
    }
  }

  onImageLoad(event: Event): void {
    this.imageLoaded = true;
    this.imageLoadError = false;
    this.imageLoad.emit(event);
  }

  onImageError(event: Event): void {
    this.imageLoadError = true;
    this.imageLoaded = false;
    this.imageError.emit(event);
  }
}

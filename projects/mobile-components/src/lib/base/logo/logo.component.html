<!-- Logo Component Template -->
<div 
  [class]="computedClasses"
  (click)="onClick($event)"
  [attr.role]="clickable || href ? 'button' : null"
  [attr.tabindex]="clickable || href ? '0' : null"
  [attr.aria-label]="alt + (name ? ' - ' + name : '')"
>
  <!-- Image/Icon Container -->
  <div class="relative">
    <!-- Actual Image -->
    <img
      *ngIf="!shouldShowFallback"
      [src]="src"
      [alt]="alt"
      [class]="imageClasses"
      (load)="onImageLoad($event)"
      (error)="onImageError($event)"
      loading="lazy"
    />
    
    <!-- Fallback Icon -->
    <div
      *ngIf="shouldShowFallback"
      [class]="imageClasses + ' bg-gray-100 flex items-center justify-center'"
    >
      <!-- Loading State -->
      <div 
        *ngIf="loading" 
        class="animate-spin rounded-full border-2 border-gray-300 border-t-blue-600"
        [style.width.px]="size === 'xs' ? 16 : size === 'sm' ? 20 : size === 'md' ? 28 : size === 'lg' ? 36 : 44"
        [style.height.px]="size === 'xs' ? 16 : size === 'sm' ? 20 : size === 'md' ? 28 : size === 'lg' ? 36 : 44"
      ></div>
      
      <!-- Icon Fallback -->
      <base-icon 
        *ngIf="!loading && fallbackIcon"
        [name]="fallbackIcon"
        [size]="size === 'xs' ? 'sm' : size === 'sm' ? 'md' : size === 'md' ? 'lg' : 'xl'"
        class="text-gray-400"
      ></base-icon>
      
      <!-- Text Initials Fallback -->
      <span 
        *ngIf="!loading && !fallbackIcon && initials"
        [class]="textClasses + ' font-semibold text-gray-600'"
      >
        {{ initials }}
      </span>
    </div>
    
    <!-- Badge -->
    <span 
      *ngIf="badge"
      [class]="badgeClasses"
    >
      {{ badge }}
    </span>
  </div>
  
  <!-- Text Content -->
  <div 
    *ngIf="(showName && name) || (showTagline && tagline)"
    class="flex flex-col"
    [class.items-center]="alignment === 'center'"
    [class.items-end]="alignment === 'right'"
  >
    <!-- Brand Name -->
    <div 
      *ngIf="showName && name"
      [class]="textClasses + ' font-semibold leading-tight'"
    >
      {{ name }}
    </div>
    
    <!-- Tagline -->
    <div 
      *ngIf="showTagline && tagline"
      [class]="taglineClasses + ' leading-tight'"
    >
      {{ tagline }}
    </div>
  </div>
</div>

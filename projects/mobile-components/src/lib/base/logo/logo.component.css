/* Logo Component Styles */

/* Smooth focus transitions */
:host {
  display: inline-block;
}

/* Enhanced hover effects for clickable logos */
[role="button"]:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Focus styles for accessibility */
[role="button"]:focus {
  outline: none;
  transform: translateY(-1px);
}

/* Loading animation */
@keyframes logoLoad {
  0% { opacity: 0; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1); }
}

/* Image load animation */
img {
  animation: logoLoad 0.3s ease-out;
}

/* Badge pulse animation */
.badge-animate {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Responsive text sizing */
@media (max-width: 640px) {
  .responsive-text {
    font-size: 0.875rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .bg-gray-100 {
    background-color: rgb(31 41 55);
  }
  
  .text-gray-400 {
    color: rgb(156 163 175);
  }
  
  .text-gray-600 {
    color: rgb(209 213 219);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  [role="button"]:focus {
    outline: 2px solid currentColor;
    outline-offset: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
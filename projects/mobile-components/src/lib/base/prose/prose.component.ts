import { Component, Input, Output, EventEmitter, OnInit, Injectable, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class NuiDefaultPropertyService {
  private defaults: Record<string, Record<string, any>> = {
    BaseProse: {
      rounded: 'md',
    },
    // Add more component defaults here as needed
  };

  getDefaultProperty<T>(
    componentName: string,
    propertyName: string,
    currentValue: T | undefined
  ): T {
    if (currentValue !== undefined) {
      return currentValue;
    }
    return this.defaults[componentName]?.[propertyName] ?? undefined;
  }
}

@Component({
  selector: 'base-prose',
  templateUrl: './prose.component.html',
  styleUrls: ['./prose.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ProseComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() content?: string; // HTML content to display
  @Input() maxWidth: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl' | '6xl' = '4xl';
  @Input() align: 'left' | 'center' | 'right' | 'justify' = 'left';
  @Input() spacing: 'tight' | 'normal' | 'relaxed' = 'normal';
  @Input() headingColor: 'inherit' | 'primary' | 'secondary' | 'accent' = 'inherit';
  @Input() linkColor: 'inherit' | 'primary' | 'secondary' | 'accent' = 'primary';
  @Input() codeTheme: 'light' | 'dark' | 'auto' = 'auto';
  @Input() tableStyle: 'simple' | 'striped' | 'bordered' = 'simple';
  @Input() listStyle: 'disc' | 'decimal' | 'none' = 'disc';
  @Input() enableSyntaxHighlighting: boolean = true;
  @Input() enableTypography: boolean = true;
  @Input() sanitizeHtml: boolean = true;

  // Legacy support
  @Input() item_id: string = Math.random().toString(36).substring(7);

  // Events
  @Output() linkClick = new EventEmitter<{ url: string; event: MouseEvent }>();
  @Output() imageLoad = new EventEmitter<{ src: string; event: Event }>();

  radiuses: Record<string, string> = {
    none: 'nui-prose-rounded-none',
    sm: 'nui-prose-rounded-sm',
    md: 'nui-prose-rounded-md',
    lg: 'nui-prose-rounded-lg',
  };

  // Sample content for preview
  defaultContent = `
    <h2>Welcome to the Prose Component</h2>
    <p>This is a comprehensive rich text component that supports <strong>bold text</strong>, <em>italic text</em>, and <a href="#">links</a>.</p>
    
    <h3>Features Include:</h3>
    <ul>
      <li>Typography styling with Tailwind classes</li>
      <li>Code syntax highlighting</li>
      <li>Table formatting</li>
      <li>Image handling</li>
    </ul>
    
    <blockquote>
      "Great typography is the foundation of great design."
    </blockquote>
    
    <pre><code>const example = {
  component: 'prose',
  enhanced: true
};</code></pre>
    
    <p>The component is fully customizable and supports various content types for rich document display.</p>
  `;

  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'prose transition-all duration-200';
    
    const sizeClasses = {
      xs: 'prose-sm',
      sm: 'prose-sm',
      md: 'prose-base',
      lg: 'prose-lg',
      xl: 'prose-xl'
    };

    const variantClasses = {
      default: 'prose-gray dark:prose-invert',
      primary: 'prose-blue dark:prose-blue',
      secondary: 'prose-gray dark:prose-gray',
      success: 'prose-green dark:prose-green',
      warning: 'prose-yellow dark:prose-yellow',
      danger: 'prose-red dark:prose-red'
    };

    const maxWidthClasses = {
      none: 'max-w-none',
      xs: 'max-w-xs',
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      '2xl': 'max-w-2xl',
      '4xl': 'max-w-4xl',
      '6xl': 'max-w-6xl'
    };

    const alignClasses = {
      left: 'text-left',
      center: 'text-center mx-auto',
      right: 'text-right ml-auto',
      justify: 'text-justify'
    };

    const spacingClasses = {
      tight: 'prose-tight',
      normal: '',
      relaxed: 'prose-relaxed'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const themeClasses = [
      this.enableTypography ? 'prose' : '',
      this.codeTheme === 'dark' ? 'prose-invert' : '',
      this.tableStyle === 'striped' ? 'prose-table-striped' : '',
      this.tableStyle === 'bordered' ? 'prose-table-bordered' : ''
    ];

    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      this.enableTypography ? sizeClasses[this.size] : '',
      this.enableTypography ? variantClasses[this.variant] : '',
      maxWidthClasses[this.maxWidth],
      alignClasses[this.align],
      spacingClasses[this.spacing],
      ...themeClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedContentClasses(): string {
    const baseContentClasses = '';
    
    const headingColorClasses = {
      inherit: '',
      primary: '[&_h1]:text-blue-600 [&_h2]:text-blue-600 [&_h3]:text-blue-600 [&_h4]:text-blue-600 [&_h5]:text-blue-600 [&_h6]:text-blue-600 dark:[&_h1]:text-blue-400 dark:[&_h2]:text-blue-400 dark:[&_h3]:text-blue-400 dark:[&_h4]:text-blue-400 dark:[&_h5]:text-blue-400 dark:[&_h6]:text-blue-400',
      secondary: '[&_h1]:text-gray-600 [&_h2]:text-gray-600 [&_h3]:text-gray-600 [&_h4]:text-gray-600 [&_h5]:text-gray-600 [&_h6]:text-gray-600 dark:[&_h1]:text-gray-400 dark:[&_h2]:text-gray-400 dark:[&_h3]:text-gray-400 dark:[&_h4]:text-gray-400 dark:[&_h5]:text-gray-400 dark:[&_h6]:text-gray-400',
      accent: '[&_h1]:text-purple-600 [&_h2]:text-purple-600 [&_h3]:text-purple-600 [&_h4]:text-purple-600 [&_h5]:text-purple-600 [&_h6]:text-purple-600 dark:[&_h1]:text-purple-400 dark:[&_h2]:text-purple-400 dark:[&_h3]:text-purple-400 dark:[&_h4]:text-purple-400 dark:[&_h5]:text-purple-400 dark:[&_h6]:text-purple-400'
    };

    const linkColorClasses = {
      inherit: '',
      primary: '[&_a]:text-blue-600 [&_a:hover]:text-blue-800 dark:[&_a]:text-blue-400 dark:[&_a:hover]:text-blue-300',
      secondary: '[&_a]:text-gray-600 [&_a:hover]:text-gray-800 dark:[&_a]:text-gray-400 dark:[&_a:hover]:text-gray-300',
      accent: '[&_a]:text-purple-600 [&_a:hover]:text-purple-800 dark:[&_a]:text-purple-400 dark:[&_a:hover]:text-purple-300'
    };

    const listStyleClasses = {
      disc: '[&_ul]:list-disc [&_ol]:list-decimal',
      decimal: '[&_ul]:list-decimal [&_ol]:list-decimal',
      none: '[&_ul]:list-none [&_ol]:list-none'
    };

    return [
      baseContentClasses,
      headingColorClasses[this.headingColor],
      linkColorClasses[this.linkColor],
      listStyleClasses[this.listStyle]
    ].filter(Boolean).join(' ');
  }

  // Legacy support method
  getLegacyClasses(): string {
    const legacyClasses = [];
    
    if (this.rounded && this.radiuses[this.rounded as keyof typeof this.radiuses]) {
      legacyClasses.push(this.radiuses[this.rounded as keyof typeof this.radiuses]);
    }
    
    return legacyClasses.join(' ');
  }

  get displayContent(): string {
    return this.content || this.defaultContent;
  }

  constructor(private nuiDefaultPropertyService: NuiDefaultPropertyService) {}

  ngOnInit() {
    // Legacy support for default property service
    this.rounded = this.nuiDefaultPropertyService.getDefaultProperty(
      'BaseProse',
      'rounded',
      this.rounded
    ) as 'none' | 'sm' | 'md' | 'lg' | 'full';
  }

  onLinkClick(event: MouseEvent, url: string) {
    this.linkClick.emit({ url, event });
  }

  onImageLoad(event: Event, src: string) {
    this.imageLoad.emit({ src, event });
  }

  // Utility method to sanitize HTML if needed
  sanitizeContent(content: string): string {
    if (!this.sanitizeHtml) return content;
    
    // Basic sanitization - in production, use a proper sanitization library
    return content
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '');
  }
}

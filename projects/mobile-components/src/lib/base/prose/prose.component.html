<div 
  [class]="computedClasses"
  [attr.id]="item_id"
  role="article"
  [attr.aria-label]="'Rich text content'"
>
  <!-- Content wrapper with typography and styling -->
  <div [class]="computedContentClasses">
    <!-- Dynamic content display -->
    <ng-container *ngIf="content; else defaultContentTemplate">
      <div [innerHTML]="sanitizeContent(displayContent)"></div>
    </ng-container>
    
    <!-- Default content template -->
    <ng-template #defaultContentTemplate>
      <div [innerHTML]="sanitizeContent(defaultContent)"></div>
    </ng-template>
    
    <!-- Slotted content -->
    <ng-content></ng-content>
  </div>
</div>

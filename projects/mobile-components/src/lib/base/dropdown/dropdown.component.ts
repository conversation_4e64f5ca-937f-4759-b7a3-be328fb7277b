import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ElementRef, ViewChild, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

export interface DropdownItem {
  label: string;
  value: any;
  icon?: string;
  disabled?: boolean;
  divider?: boolean;
  children?: DropdownItem[];
}

@Component({
  selector: 'base-dropdown',
  templateUrl: './dropdown.component.html',
  styleUrls: ['./dropdown.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class DropdownComponent implements OnInit, OnDestroy {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() label: string = 'Select Option';
  @Input() placeholder: string = 'Choose an option';
  @Input() items: DropdownItem[] = [
    { label: 'Dashboard', value: 'dashboard', icon: '📊' },
    { label: 'Profile', value: 'profile', icon: '👤' },
    { label: 'Settings', value: 'settings', icon: '⚙️' },
    { divider: true, label: '', value: null },
    { label: 'Logout', value: 'logout', icon: '🚪' }
  ];
  @Input() selectedValue: any = null;
  @Input() disabled: boolean = false;
  @Input() loading: boolean = false;
  @Input() searchable: boolean = false;
  @Input() clearable: boolean = false;
  @Input() multiple: boolean = false;
  @Input() position: 'bottom' | 'top' | 'left' | 'right' = 'bottom';
  @Input() fullWidth: boolean = true;
  @Input() maxHeight: string = '200px';

  // Events
  @Output() selectionChange = new EventEmitter<any>();
  @Output() itemClick = new EventEmitter<DropdownItem>();
  @Output() open = new EventEmitter<void>();
  @Output() close = new EventEmitter<void>();

  @ViewChild('dropdownRef') dropdownRef!: ElementRef;
  @ViewChild('menuRef') menuRef!: ElementRef;

  isOpen: boolean = false;
  searchTerm: string = '';
  selectedItems: any[] = [];

  ngOnInit() {
    if (this.multiple && this.selectedValue) {
      this.selectedItems = Array.isArray(this.selectedValue) ? this.selectedValue : [this.selectedValue];
    }
  }

  ngOnDestroy() {
    // Cleanup if needed
  }

  get computedClasses(): string {
    const baseClasses = 'relative inline-block text-left transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm', 
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: '',
      primary: '',
      secondary: '',
      success: '',
      warning: '',
      danger: ''
    };

    const roundedClasses = {
      none: '',
      sm: '',
      md: '', 
      lg: '',
      full: ''
    };

    const widthClasses = this.fullWidth ? 'w-full' : 'w-auto';
    const disabledClasses = this.disabled ? 'opacity-50 pointer-events-none' : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      widthClasses,
      disabledClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedButtonClasses(): string {
    const baseButtonClasses = 'inline-flex justify-between items-center px-4 py-2 border border-gray-300 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200';
    
    const sizeButtonClasses = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-5 py-2.5 text-lg',
      xl: 'px-6 py-3 text-xl'
    };

    const variantButtonClasses = {
      default: 'focus:ring-gray-500 border-gray-300',
      primary: 'focus:ring-blue-500 border-blue-300 text-blue-700',
      secondary: 'focus:ring-gray-500 border-gray-300',
      success: 'focus:ring-green-500 border-green-300 text-green-700',
      warning: 'focus:ring-yellow-500 border-yellow-300 text-yellow-700',
      danger: 'focus:ring-red-500 border-red-300 text-red-700'
    };

    const roundedButtonClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const widthButtonClasses = this.fullWidth ? 'w-full' : '';

    return [
      baseButtonClasses,
      sizeButtonClasses[this.size],
      variantButtonClasses[this.variant],
      roundedButtonClasses[this.rounded],
      widthButtonClasses
    ].filter(Boolean).join(' ');
  }

  get computedMenuClasses(): string {
    const baseMenuClasses = 'absolute z-50 mt-1 bg-white border border-gray-300 shadow-lg focus:outline-none';
    
    const positionClasses = {
      bottom: 'top-full left-0',
      top: 'bottom-full left-0',
      left: 'right-full top-0',
      right: 'left-full top-0'
    };

    const roundedMenuClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-xl'
    };

    const widthMenuClasses = this.fullWidth ? 'w-full min-w-full' : 'min-w-48';

    return [
      baseMenuClasses,
      positionClasses[this.position],
      roundedMenuClasses[this.rounded],
      widthMenuClasses
    ].filter(Boolean).join(' ');
  }

  get filteredItems(): DropdownItem[] {
    if (!this.searchable || !this.searchTerm) {
      return this.items;
    }
    return this.items.filter(item => 
      item.label.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  get selectedLabel(): string {
    if (this.multiple && this.selectedItems.length > 0) {
      return `${this.selectedItems.length} item(s) selected`;
    }
    
    const selectedItem = this.items.find(item => item.value === this.selectedValue);
    return selectedItem ? selectedItem.label : this.placeholder;
  }

  toggleDropdown() {
    if (this.disabled || this.loading) return;
    
    this.isOpen = !this.isOpen;
    if (this.isOpen) {
      this.open.emit();
    } else {
      this.close.emit();
    }
  }

  selectItem(item: DropdownItem) {
    if (item.disabled || item.divider) return;

    this.itemClick.emit(item);

    if (this.multiple) {
      const index = this.selectedItems.indexOf(item.value);
      if (index > -1) {
        this.selectedItems.splice(index, 1);
      } else {
        this.selectedItems.push(item.value);
      }
      this.selectionChange.emit([...this.selectedItems]);
    } else {
      this.selectedValue = item.value;
      this.selectionChange.emit(item.value);
      this.isOpen = false;
      this.close.emit();
    }
  }

  clearSelection() {
    if (this.multiple) {
      this.selectedItems = [];
      this.selectionChange.emit([]);
    } else {
      this.selectedValue = null;
      this.selectionChange.emit(null);
    }
  }

  isSelected(item: DropdownItem): boolean {
    if (this.multiple) {
      return this.selectedItems.includes(item.value);
    }
    return this.selectedValue === item.value;
  }

  getItemClasses(item: DropdownItem): string {
    const baseItemClasses = 'hover:bg-gray-100 focus:bg-gray-100 focus:outline-none';
    const selectedClasses = this.isSelected(item) ? 'bg-blue-50 text-blue-700' : 'text-gray-900';
    const disabledClasses = item.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';
    
    return [baseItemClasses, selectedClasses, disabledClasses].filter(Boolean).join(' ');
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    if (this.dropdownRef && !this.dropdownRef.nativeElement.contains(event.target as Node)) {
      if (this.isOpen) {
        this.isOpen = false;
        this.close.emit();
      }
    }
  }

  @HostListener('keydown.escape')
  onEscapeKey() {
    if (this.isOpen) {
      this.isOpen = false;
      this.close.emit();
    }
  }
}

<div #dropdownRef [ngClass]="computedClasses">
  <!-- Label -->
  <label *ngIf="label" class="block text-sm font-medium text-gray-700 mb-1">
    {{ label }}
  </label>

  <!-- Dropdown Button -->
  <button
    type="button"
    [ngClass]="computedButtonClasses"
    [disabled]="disabled"
    [attr.aria-expanded]="isOpen"
    [attr.aria-haspopup]="true"
    [attr.aria-label]="label || placeholder"
    (click)="toggleDropdown()"
  >
    <span class="flex items-center truncate">
      <!-- Loading spinner -->
      <svg *ngIf="loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      
      <!-- Selected content -->
      <span *ngIf="!loading" class="truncate">{{ selectedLabel }}</span>
    </span>

    <!-- Clear button -->
    <button
      *ngIf="clearable && (selectedValue !== null || selectedItems.length > 0) && !loading"
      type="button"
      class="ml-2 flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors"
      [attr.aria-label]="'Clear selection'"
      (click)="clearSelection(); $event.stopPropagation()"
    >
      <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
      </svg>
    </button>

    <!-- Dropdown arrow -->
    <svg 
      class="ml-2 flex-shrink-0 h-4 w-4 text-gray-400 transition-transform duration-200"
      [class.rotate-180]="isOpen"
      fill="none" 
      viewBox="0 0 24 24" 
      stroke="currentColor"
    >
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
    </svg>
  </button>

  <!-- Dropdown Menu -->
  <div
    #menuRef
    *ngIf="isOpen"
    [ngClass]="computedMenuClasses"
    [style.max-height]="maxHeight"
    class="overflow-auto"
    role="menu"
    [attr.aria-labelledby]="label"
  >
    <!-- Search input -->
    <div *ngIf="searchable" class="p-2 border-b border-gray-200">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        placeholder="Search..."
        [attr.aria-label]="'Search items'"
        (click)="$event.stopPropagation()"
      />
    </div>

    <!-- Menu items -->
    <div class="py-1">
      <ng-container *ngFor="let item of filteredItems; let i = index">
        <!-- Divider -->
        <div 
          *ngIf="item.divider" 
          class="border-t border-gray-200 my-1"
          role="separator"
        ></div>

        <!-- Menu item -->
        <button
          *ngIf="!item.divider"
          type="button"
          class="w-full text-left px-4 py-2 text-sm transition-colors duration-150 flex items-center justify-between"
          [class]="getItemClasses(item)"
          [disabled]="item.disabled"
          [attr.aria-selected]="isSelected(item)"
          [attr.aria-disabled]="item.disabled"
          role="menuitem"
          (click)="selectItem(item)"
        >
          <div class="flex items-center">
            <!-- Icon -->
            <span *ngIf="item.icon" class="mr-2">{{ item.icon }}</span>
            
            <!-- Label -->
            <span [class.line-through]="item.disabled">{{ item.label }}</span>
          </div>

          <!-- Selection indicator -->
          <svg 
            *ngIf="isSelected(item) && !multiple" 
            class="h-4 w-4 text-blue-600" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>

          <!-- Checkbox for multiple selection -->
          <input
            *ngIf="multiple"
            type="checkbox"
            [checked]="isSelected(item)"
            class="h-4 w-4 text-blue-600 rounded focus:ring-blue-500 border-gray-300"
            [attr.aria-label]="'Select ' + item.label"
            tabindex="-1"
            (click)="$event.stopPropagation()"
          />
        </button>
      </ng-container>

      <!-- No results message -->
      <div 
        *ngIf="filteredItems.length === 0" 
        class="px-4 py-2 text-sm text-gray-500 text-center"
      >
        No items found
      </div>
    </div>
  </div>
</div>

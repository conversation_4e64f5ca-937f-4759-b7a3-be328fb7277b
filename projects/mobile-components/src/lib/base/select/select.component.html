<div 
  [class]="computedClasses"
  [attr.id]="item_id + '-wrapper'"
>
  <!-- Label -->
  <label 
    *ngIf="label"
    [for]="id"
    class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
  >
    {{ label }}
    <span *ngIf="required" class="text-red-500 ml-1">*</span>
  </label>

  <!-- Description -->
  <p 
    *ngIf="description"
    class="text-sm text-gray-600 dark:text-gray-400 mb-2"
  >
    {{ description }}
  </p>

  <!-- Select Container -->
  <div class="relative">
    <!-- Simple Select (non-searchable) -->
    <select
      *ngIf="!searchable"
      #selectRef
      [id]="id"
      [class]="computedSelectClasses"
      [disabled]="disabled"
      [required]="required"
      [multiple]="multiple"
      [(ngModel)]="selectedValue"
      (change)="onSelectChange($event)"
      (focus)="onSelectFocus($event)"
      (blur)="onSelectBlur($event)"
      [attr.aria-describedby]="description ? id + '-description' : null"
      [attr.aria-invalid]="hasError"
    >
      <option value="" disabled>{{ placeholder }}</option>
      
      <!-- Grouped Options -->
      <ng-container *ngIf="hasGroups">
        <optgroup 
          *ngFor="let group of groupedOptions | keyvalue" 
          [label]="group.key"
        >
          <option 
            *ngFor="let option of group.value"
            [value]="option.value"
            [disabled]="option.disabled"
          >
            {{ option.label }}
          </option>
        </optgroup>
      </ng-container>
      
      <!-- Regular Options -->
      <ng-container *ngIf="!hasGroups">
        <option 
          *ngFor="let option of options"
          [value]="option.value"
          [disabled]="option.disabled"
        >
          {{ option.label }}
        </option>
      </ng-container>
    </select>

    <!-- Custom Dropdown (searchable) -->
    <div *ngIf="searchable" class="relative">
      <!-- Trigger Button -->
      <button
        type="button"
        [class]="computedSelectClasses + ' flex items-center justify-between cursor-pointer'"
        [disabled]="disabled"
        (click)="toggleDropdown()"
        (focus)="onSelectFocus($event)"
        (blur)="onSelectBlur($event)"
        [attr.aria-expanded]="isOpen"
        [attr.aria-haspopup]="true"
      >
        <span 
          class="block truncate"
          [ngClass]="{ 'text-gray-500': !selectedValue }"
        >
          {{ displayValue }}
        </span>
        
        <!-- Loading Spinner -->
        <svg 
          *ngIf="loading"
          class="w-4 h-4 animate-spin"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        
        <!-- Clear Button -->
        <button
          *ngIf="clearable && selectedValue && !loading"
          type="button"
          class="ml-2 text-gray-400 hover:text-gray-600"
          (click)="clearSelection(); $event.stopPropagation()"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
        
        <!-- Chevron -->
        <svg 
          *ngIf="!loading"
          class="w-4 h-4 text-gray-400 ml-2 transition-transform duration-200"
          [ngClass]="{ 'transform rotate-180': isOpen }"
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
        </svg>
      </button>

      <!-- Dropdown Panel -->
      <div
        *ngIf="isOpen"
        class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg"
        [style.max-height]="maxHeight"
        role="listbox"
      >
        <!-- Search Input -->
        <div class="p-2 border-b border-gray-200 dark:border-gray-700">
          <input
            #searchRef
            type="text"
            class="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            [placeholder]="searchPlaceholder"
            [(ngModel)]="searchTerm"
            (input)="onSearchInput($event)"
          />
        </div>

        <!-- Options List -->
        <div class="max-h-60 overflow-auto">
          <!-- Empty State -->
          <div 
            *ngIf="filteredOptions.length === 0"
            class="px-3 py-2 text-sm text-gray-500 dark:text-gray-400"
          >
            {{ emptyMessage }}
          </div>

          <!-- Grouped Options -->
          <ng-container *ngIf="hasGroups && filteredOptions.length > 0">
            <div *ngFor="let group of groupedOptions | keyvalue">
              <div class="px-3 py-2 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider bg-gray-50 dark:bg-gray-700">
                {{ group.key }}
              </div>
              <button
                *ngFor="let option of group.value"
                type="button"
                class="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700 flex items-center justify-between"
                [ngClass]="{
                  'bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300': isOptionSelected(option),
                  'text-gray-400 cursor-not-allowed': option.disabled
                }"
                [disabled]="option.disabled"
                (click)="selectOption(option)"
                role="option"
                [attr.aria-selected]="isOptionSelected(option)"
              >
                <span>{{ option.label }}</span>
                <svg 
                  *ngIf="isOptionSelected(option)"
                  class="w-4 h-4"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                </svg>
              </button>
            </div>
          </ng-container>

          <!-- Regular Options -->
          <ng-container *ngIf="!hasGroups && filteredOptions.length > 0">
            <button
              *ngFor="let option of filteredOptions"
              type="button"
              class="w-full px-3 py-2 text-left text-sm hover:bg-gray-100 dark:hover:bg-gray-700 focus:outline-none focus:bg-gray-100 dark:focus:bg-gray-700 flex items-center justify-between"
              [ngClass]="{
                'bg-blue-50 dark:bg-blue-900 text-blue-700 dark:text-blue-300': isOptionSelected(option),
                'text-gray-400 cursor-not-allowed': option.disabled
              }"
              [disabled]="option.disabled"
              (click)="selectOption(option)"
              role="option"
              [attr.aria-selected]="isOptionSelected(option)"
            >
              <span>{{ option.label }}</span>
              <svg 
                *ngIf="isOptionSelected(option)"
                class="w-4 h-4"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
              </svg>
            </button>
          </ng-container>
        </div>
      </div>
    </div>

    <!-- Dropdown Arrow (for simple select) -->
    <div 
      *ngIf="!searchable"
      class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none"
    >
      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
      </svg>
    </div>
  </div>

  <!-- Help Text -->
  <p 
    *ngIf="helpText && !hasError"
    class="mt-1 text-sm text-gray-500 dark:text-gray-400"
  >
    {{ helpText }}
  </p>

  <!-- Error Message -->
  <p 
    *ngIf="hasError"
    class="mt-1 text-sm text-red-600 dark:text-red-400"
    role="alert"
  >
    {{ errorText }}
  </p>

  <!-- Slotted Content -->
  <ng-content></ng-content>
</div>

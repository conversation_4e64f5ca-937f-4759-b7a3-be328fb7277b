import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ViewChild,
  ElementRef,
  forwardRef,
  Injectable,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

export interface SelectOption {
  value: any;
  label: string;
  disabled?: boolean;
  group?: string;
}

@Injectable({
  providedIn: 'root',
})
export class NuiDefaultPropertyService {
  private defaults: Record<string, Record<string, any>> = {
    BaseSelect: {
      rounded: 'md',
      size: 'md',
    },
    // Add more component defaults here as needed
  };

  getDefaultProperty<T>(
    componentName: string,
    propertyName: string,
    currentValue: T | undefined
  ): T {
    if (currentValue !== undefined) {
      return currentValue;
    }
    return this.defaults[componentName]?.[propertyName] ?? undefined;
  }
}

@Component({
  selector: 'base-select',
  templateUrl: './select.component.html',
  styleUrls: ['./select.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SelectComponent),
      multi: true,
    },
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class SelectComponent implements OnInit, ControlValueAccessor {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() options: SelectOption[] = [
    { value: 'option1', label: 'First Option' },
    { value: 'option2', label: 'Second Option' },
    { value: 'option3', label: 'Third Option', disabled: true },
    { value: 'option4', label: 'Fourth Option' }
  ];
  @Input() placeholder: string = 'Choose an option...';
  @Input() label?: string;
  @Input() description?: string;
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() multiple: boolean = false;
  @Input() searchable: boolean = false;
  @Input() clearable: boolean = false;
  @Input() loading: boolean = false;
  @Input() maxHeight: string = '200px';
  @Input() emptyMessage: string = 'No options available';
  @Input() searchPlaceholder: string = 'Search options...';
  @Input() groupBy?: string; // Property to group options by
  @Input() errorText?: string;
  @Input() helpText?: string;
  @Input() allowCustomValues: boolean = false;

  // Legacy support
  @Input() id?: string;
  @Input() item_id: string = Math.random().toString(36).substring(7);

  // Events
  @Output() selectionChange = new EventEmitter<any>();
  @Output() optionSelect = new EventEmitter<SelectOption>();
  @Output() searchChange = new EventEmitter<string>();
  @Output() selectOpen = new EventEmitter<void>();
  @Output() selectClose = new EventEmitter<void>();
  @Output() selectFocus = new EventEmitter<FocusEvent>();
  @Output() selectBlur = new EventEmitter<FocusEvent>();

  @ViewChild('selectRef') selectRef!: ElementRef<HTMLSelectElement>;
  @ViewChild('searchRef') searchRef!: ElementRef<HTMLInputElement>;

  // Internal state
  selectedValue: any = null;
  isOpen: boolean = false;
  searchTerm: string = '';
  filteredOptions: SelectOption[] = [];

  // Form control implementation
  onChange: any = () => {};
  onTouched: any = () => {};

  constructor(private nuiDefaultPropertyService: NuiDefaultPropertyService) {}

  ngOnInit() {
    this.size = this.nuiDefaultPropertyService.getDefaultProperty(
      'BaseSelect',
      'size',
      this.size
    ) as 'xs' | 'sm' | 'md' | 'lg' | 'xl';
    
    this.rounded = this.nuiDefaultPropertyService.getDefaultProperty(
      'BaseSelect',
      'rounded',
      this.rounded
    ) as 'none' | 'sm' | 'md' | 'lg' | 'full';
    
    if (!this.id) {
      this.id = `select-${this.item_id}`;
    }

    this.filteredOptions = [...this.options];
  }

  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'relative w-full';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: '',
      primary: 'select-primary',
      secondary: 'select-secondary',
      success: 'select-success',
      warning: 'select-warning',
      danger: 'select-danger'
    };

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedSelectClasses(): string {
    const baseSelectClasses = 'w-full border-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 appearance-none bg-white dark:bg-gray-800';
    
    const sizeSelectClasses = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-3 text-base',
      lg: 'px-5 py-4 text-lg',
      xl: 'px-6 py-5 text-xl'
    };

    const variantSelectClasses = {
      default: 'border-gray-300 text-gray-900 focus:ring-gray-500 focus:border-gray-500',
      primary: 'border-blue-300 text-blue-900 focus:ring-blue-500 focus:border-blue-500',
      secondary: 'border-gray-400 text-gray-700 focus:ring-gray-500 focus:border-gray-500',
      success: 'border-green-300 text-green-900 focus:ring-green-500 focus:border-green-500',
      warning: 'border-yellow-300 text-yellow-900 focus:ring-yellow-500 focus:border-yellow-500',
      danger: 'border-red-300 text-red-900 focus:ring-red-500 focus:border-red-500'
    };

    const roundedSelectClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const disabledClasses = this.disabled ? 'opacity-50 cursor-not-allowed bg-gray-100 dark:bg-gray-700' : '';
    const errorClasses = this.hasError ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : '';

    return [
      baseSelectClasses,
      sizeSelectClasses[this.size],
      variantSelectClasses[this.variant],
      roundedSelectClasses[this.rounded],
      disabledClasses,
      errorClasses
    ].filter(Boolean).join(' ');
  }

  get hasError(): boolean {
    return !!this.errorText;
  }

  get selectedOption(): SelectOption | null {
    return this.options.find(option => option.value === this.selectedValue) || null;
  }

  get displayValue(): string {
    if (this.multiple && Array.isArray(this.selectedValue)) {
      const selectedOptions = this.options.filter(option => 
        this.selectedValue.includes(option.value)
      );
      return selectedOptions.map(opt => opt.label).join(', ');
    }
    
    const option = this.selectedOption;
    return option ? option.label : this.placeholder;
  }

  // Form control methods
  writeValue(value: any): void {
    this.selectedValue = value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Event handlers
  onSelectChange(event: Event): void {
    if (this.disabled) return;
    
    const target = event.target as HTMLSelectElement;
    const value = target.value;
    
    this.selectedValue = value;
    this.onChange(value);
    this.selectionChange.emit(value);
    
    const selectedOption = this.options.find(opt => opt.value === value);
    if (selectedOption) {
      this.optionSelect.emit(selectedOption);
    }
  }

  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value;
    this.filterOptions();
    this.searchChange.emit(this.searchTerm);
  }

  onSelectFocus(event: FocusEvent): void {
    this.onTouched();
    this.selectFocus.emit(event);
  }

  onSelectBlur(event: FocusEvent): void {
    this.selectBlur.emit(event);
  }

  // Utility methods
  filterOptions(): void {
    if (!this.searchTerm) {
      this.filteredOptions = [...this.options];
      return;
    }

    this.filteredOptions = this.options.filter(option =>
      option.label.toLowerCase().includes(this.searchTerm.toLowerCase())
    );
  }

  selectOption(option: SelectOption): void {
    if (option.disabled || this.disabled) return;

    if (this.multiple) {
      if (!Array.isArray(this.selectedValue)) {
        this.selectedValue = [];
      }
      
      const index = this.selectedValue.indexOf(option.value);
      if (index > -1) {
        this.selectedValue.splice(index, 1);
      } else {
        this.selectedValue.push(option.value);
      }
    } else {
      this.selectedValue = option.value;
      this.toggleDropdown();
    }

    this.onChange(this.selectedValue);
    this.selectionChange.emit(this.selectedValue);
    this.optionSelect.emit(option);
  }

  clearSelection(): void {
    if (this.disabled) return;
    
    this.selectedValue = this.multiple ? [] : null;
    this.onChange(this.selectedValue);
    this.selectionChange.emit(this.selectedValue);
  }

  toggleDropdown(): void {
    if (this.disabled) return;
    
    this.isOpen = !this.isOpen;
    
    if (this.isOpen) {
      this.selectOpen.emit();
      // Focus search input if searchable
      if (this.searchable) {
        setTimeout(() => {
          this.searchRef?.nativeElement?.focus();
        });
      }
    } else {
      this.selectClose.emit();
      this.searchTerm = '';
      this.filterOptions();
    }
  }

  isOptionSelected(option: SelectOption): boolean {
    if (this.multiple && Array.isArray(this.selectedValue)) {
      return this.selectedValue.includes(option.value);
    }
    return this.selectedValue === option.value;
  }

  get groupedOptions(): { [key: string]: SelectOption[] } {
    if (!this.groupBy) return {};
    
    return this.filteredOptions.reduce((groups, option) => {
      const group = (option as any)[this.groupBy!] || 'Other';
      if (!groups[group]) {
        groups[group] = [];
      }
      groups[group].push(option);
      return groups;
    }, {} as { [key: string]: SelectOption[] });
  }

  get hasGroups(): boolean {
    return !!this.groupBy && Object.keys(this.groupedOptions).length > 0;
  }
}

import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'mobile-modal',
  standalone: true,
  imports: [CommonModule],
  template: `
    <!-- Backdrop -->
    <div 
      *ngIf="isOpen"
      class="fixed inset-0 bg-black bg-opacity-50 z-50 transition-opacity"
      [class.opacity-100]="isOpen"
      (click)="onBackdropClick()"
    >
      <!-- Modal Container -->
      <div class="fixed inset-x-0 bottom-0 z-50 transform transition-transform duration-300"
           [class.translate-y-full]="!isOpen"
           [class.translate-y-0]="isOpen">
        
        <!-- Drag Handle -->
        <div *ngIf="draggable" class="flex justify-center p-2 bg-white">
          <div class="w-8 h-1 bg-gray-300 rounded-full"></div>
        </div>
        
        <!-- Modal Content -->
        <div [ngClass]="computedClasses">
          <!-- Header -->
          <div *ngIf="title || closable" class="flex items-center justify-between p-4 border-b border-gray-200">
            <h3 *ngIf="title" class="text-lg font-semibold text-gray-900">{{ title }}</h3>
            <button 
              *ngIf="closable"
              type="button"
              class="p-2 text-gray-400 hover:text-gray-600 touch-manipulation"
              (click)="close()"
            >
              <i class="fas fa-times text-xl"></i>
            </button>
          </div>
          
          <!-- Body -->
          <div class="p-4" [style.max-height]="maxHeight" [class.overflow-y-auto]="scrollable">
            <ng-content></ng-content>
          </div>
          
          <!-- Footer -->
          <div *ngIf="hasFooterContent" class="p-4 border-t border-gray-200">
            <ng-content select="[slot=footer]"></ng-content>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .mobile-modal {
      background: white;
      border-top-left-radius: 1rem;
      border-top-right-radius: 1rem;
      max-height: 90vh;
      min-height: 200px;
    }
    
    .mobile-modal.fullscreen {
      height: 100vh;
      border-radius: 0;
    }
    
    @media (min-width: 768px) {
      .mobile-modal {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 90%;
        max-width: 500px;
        max-height: 80vh;
        border-radius: 0.75rem;
      }
    }
  `]
})
export class MobileModalComponent implements OnInit, OnDestroy {
  @Input() isOpen: boolean = false;
  @Input() title: string = '';
  @Input() closable: boolean = true;
  @Input() backdrop: boolean = true;
  @Input() draggable: boolean = true;
  @Input() scrollable: boolean = true;
  @Input() fullscreen: boolean = false;
  @Input() maxHeight: string = '70vh';
  @Input() className: string = '';

  @Output() opened = new EventEmitter<void>();
  @Output() closed = new EventEmitter<void>();
  @Output() backdropClick = new EventEmitter<void>();

  hasFooterContent: boolean = false;

  ngOnInit(): void {
    // Check if footer content is projected
    // This is a simplified check - in a real implementation you might use ContentChild
    this.hasFooterContent = true; // You would implement proper content projection detection
    
    if (this.isOpen) {
      this.preventBodyScroll();
      this.opened.emit();
    }
  }

  ngOnDestroy(): void {
    this.restoreBodyScroll();
  }

  get computedClasses(): string {
    const baseClasses = 'mobile-modal w-full bg-white shadow-xl';
    const fullscreenClasses = this.fullscreen ? 'fullscreen' : '';

    return [
      baseClasses,
      fullscreenClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  onBackdropClick(): void {
    if (this.backdrop) {
      this.backdropClick.emit();
      this.close();
    }
  }

  open(): void {
    this.isOpen = true;
    this.preventBodyScroll();
    this.opened.emit();
  }

  close(): void {
    this.isOpen = false;
    this.restoreBodyScroll();
    this.closed.emit();
  }

  toggle(): void {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }

  private preventBodyScroll(): void {
    document.body.style.overflow = 'hidden';
  }

  private restoreBodyScroll(): void {
    document.body.style.overflow = '';
  }
}

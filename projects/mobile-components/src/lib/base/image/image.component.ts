import { Component, Input, Output, EventEmitter, OnInit, Inject, Optional, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-image',
  templateUrl: './image.component.html',
  styleUrls: ['./image.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ImageComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() src: string = 'https://images.unsplash.com/photo-1682687220742-aba13b6e50ba?w=800&h=600&fit=crop&crop=entropy&auto=format&q=80';
  @Input() alt: string = 'Sample image showcasing the Image component functionality';
  @Input() loading: boolean = false;
  @Input() aspectRatio: 'square' | 'video' | 'wide' | 'tall' | 'auto' = 'auto';
  @Input() objectFit: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down' = 'cover';
  @Input() fallbackSrc: string = 'https://via.placeholder.com/400x300/e5e7eb/6b7280?text=Image+Not+Found';
  @Input() lazy: boolean = true;
  @Input() placeholder: string = '';
  @Input() overlay: boolean = false;
  @Input() overlayText: string = '';

  // Events
  @Output() imageLoad = new EventEmitter<Event>();
  @Output() imageError = new EventEmitter<Event>();
  @Output() imageClick = new EventEmitter<Event>();
  
  hasError = false;
  isLoading = true;
  
  constructor(@Optional() @Inject('componentProperties') public properties: any) {}
  
  ngOnInit(): void {
    if (this.properties) {
      // Map properties from injected properties
      this.src = this.properties.src || this.src;
      this.alt = this.properties.alt || this.alt;
      this.className = this.properties.className || this.className;
      this.size = this.properties.size || this.size;
      this.variant = this.properties.variant || this.variant;
      this.rounded = this.properties.rounded || this.rounded;
      this.loading = this.properties.loading || this.loading;
      this.aspectRatio = this.properties.aspectRatio || this.aspectRatio;
      this.objectFit = this.properties.objectFit || this.objectFit;
      this.fallbackSrc = this.properties.fallbackSrc || this.fallbackSrc;
      this.lazy = this.properties.lazy || this.lazy;
      this.placeholder = this.properties.placeholder || this.placeholder;
      this.overlay = this.properties.overlay || this.overlay;
      this.overlayText = this.properties.overlayText || this.overlayText;
    }
  }

  get computedClasses(): string {
    const baseClasses = 'relative overflow-hidden transition-all duration-200';
    
    const sizeClasses = {
      xs: 'w-16 h-16',
      sm: 'w-24 h-24',
      md: 'w-32 h-32',
      lg: 'w-48 h-48',
      xl: 'w-64 h-64'
    };

    const variantClasses = {
      default: 'border border-gray-200',
      primary: 'border-2 border-blue-500 shadow-md',
      secondary: 'border-2 border-gray-500 shadow-sm',
      success: 'border-2 border-green-500 shadow-md',
      warning: 'border-2 border-yellow-500 shadow-md',
      danger: 'border-2 border-red-500 shadow-md'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const aspectRatioClasses = {
      square: 'aspect-square',
      video: 'aspect-video',
      wide: 'aspect-[16/9]',
      tall: 'aspect-[3/4]',
      auto: ''
    };

    return [
      baseClasses,
      this.aspectRatio !== 'auto' ? aspectRatioClasses[this.aspectRatio] : sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      this.className
    ].filter(Boolean).join(' ');
  }

  get imageClasses(): string {
    const objectFitClasses = {
      contain: 'object-contain',
      cover: 'object-cover',
      fill: 'object-fill',
      none: 'object-none',
      'scale-down': 'object-scale-down'
    };

    return [
      'w-full h-full transition-opacity duration-300',
      objectFitClasses[this.objectFit],
      this.isLoading ? 'opacity-0' : 'opacity-100'
    ].filter(Boolean).join(' ');
  }
  
  onImageError(event: Event): void {
    this.hasError = true;
    this.isLoading = false;
    this.imageError.emit(event);
    
    // Set fallback image
    const img = event.target as HTMLImageElement;
    if (img && this.fallbackSrc && img.src !== this.fallbackSrc) {
      img.src = this.fallbackSrc;
      this.hasError = false;
    }
  }
  
  onImageLoad(event: Event): void {
    this.hasError = false;
    this.isLoading = false;
    this.imageLoad.emit(event);
  }

  onClick(event: Event): void {
    this.imageClick.emit(event);
  }
}

<div [ngClass]="computedClasses" (click)="onClick($event)">
  <!-- Loading placeholder -->
  <div *ngIf="isLoading && placeholder" 
       class="absolute inset-0 flex items-center justify-center bg-gray-100 text-gray-500 text-sm">
    {{ placeholder }}
  </div>
  
  <!-- Loading spinner -->
  <div *ngIf="isLoading && !placeholder" 
       class="absolute inset-0 flex items-center justify-center bg-gray-100">
    <svg class="animate-spin h-6 w-6 text-gray-400" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
  </div>

  <!-- Main image -->
  <img 
    [src]="src" 
    [alt]="alt" 
    [ngClass]="imageClasses"
    [loading]="lazy ? 'lazy' : 'eager'"
    [attr.aria-label]="alt"
    (error)="onImageError($event)"
    (load)="onImageLoad($event)">

  <!-- Overlay -->
  <div *ngIf="overlay" 
       class="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center text-white">
    <span *ngIf="overlayText" class="text-center px-4">{{ overlayText }}</span>
    <ng-content select="[slot=overlay]"></ng-content>
  </div>

  <!-- Error message -->
  <div *ngIf="hasError" 
       class="absolute inset-0 flex flex-col items-center justify-center bg-gray-100 text-gray-500 text-sm">
    <svg class="w-8 h-8 mb-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
    </svg>
    <span>Image failed to load</span>
  </div>

  <!-- Content projection -->
  <ng-content></ng-content>
</div>

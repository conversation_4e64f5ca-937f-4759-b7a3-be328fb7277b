/* Enhanced Image Component Styles */
:host {
  display: inline-block;
}

/* Ensure proper sizing for aspect ratio containers */
.aspect-square {
  aspect-ratio: 1 / 1;
}

.aspect-video {
  aspect-ratio: 16 / 9;
}

/* Animation classes for smooth transitions */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Interactive hover effects */
:host:hover img {
  transform: scale(1.02);
  transition: transform 0.2s ease-in-out;
}

/* Focus styles for accessibility */
:host:focus-within {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

import { Component, Input, OnInit, ViewContainerRef, Injector, ViewChild, ChangeDetectorRef, Type, OnChanges, SimpleChanges, ChangeDetectionStrategy, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BaseModule } from '../../base/base.module';

@Component({
  selector: 'base-container',
  standalone: true,
  imports: [CommonModule, BaseModule],
  templateUrl: './container.component.html',
  styleUrls: ['./container.component.css'],
  changeDetection: ChangeDetectionStrategy.Default
})
export class ContainerComponent implements OnInit, OnChanges, AfterViewInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() title: string = 'Content Container';
  @Input() subtitle: string = 'A flexible container for dynamic content';
  @Input() padding: boolean = true;
  @Input() fullWidth: boolean = true;
  @Input() maxWidth: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' = 'none';
  @Input() centerContent: boolean = false;
  @Input() shadow: boolean = false;
  @Input() border: boolean = false;

  // Dynamic loading inputs (preserved for backward compatibility)
  @Input() childrenConfig: any[] = []; // Configuration for child components
  @Input() resolveComponentFn!: (type: string) => Type<any> | null; // Function to resolve component types

  @ViewChild('childContainer', { read: ViewContainerRef, static: false }) childContainer!: ViewContainerRef;

  constructor(
    private injector: Injector,
    private cdr: ChangeDetectorRef
  ) {}

  get computedClasses(): string {
    const baseClasses = 'relative transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm', 
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'bg-white',
      primary: 'bg-blue-50 border-blue-200',
      secondary: 'bg-gray-50 border-gray-200',
      success: 'bg-green-50 border-green-200',
      warning: 'bg-yellow-50 border-yellow-200',
      danger: 'bg-red-50 border-red-200'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md', 
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const widthClasses = this.fullWidth ? 'w-full' : '';
    
    const maxWidthClasses = {
      none: '',
      xs: 'max-w-xs',
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl',
      '2xl': 'max-w-2xl',
      '3xl': 'max-w-3xl',
      '4xl': 'max-w-4xl',
      '5xl': 'max-w-5xl',
      '6xl': 'max-w-6xl',
      '7xl': 'max-w-7xl'
    };

    const paddingClasses = this.padding ? 'p-4 md:p-6' : '';
    const centerClasses = this.centerContent ? 'mx-auto' : '';
    const shadowClasses = this.shadow ? 'shadow-lg' : '';
    const borderClasses = this.border ? 'border' : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      widthClasses,
      maxWidthClasses[this.maxWidth],
      paddingClasses,
      centerClasses,
      shadowClasses,
      borderClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  ngOnInit(): void {
    console.log('[Container] ngOnInit');
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('[Container] ngOnChanges - ResolverAvailable:', !!this.resolveComponentFn, 'ConfigAvailable:', !!this.childrenConfig, 'Changes:', changes);
    // Logic moved to ngAfterViewInit
  }

  ngAfterViewInit(): void {
    console.log('[Container] ngAfterViewInit - View initialized, childContainer available:', !!this.childContainer);
    const resolverAvailable = this.resolveComponentFn !== undefined && this.resolveComponentFn !== null;
    const configAvailable = this.childrenConfig && this.childrenConfig.length > 0;
    console.log('[Container] ngAfterViewInit - ResolverAvailable:', resolverAvailable, 'ConfigAvailable:', configAvailable);
    console.log('[Container] ngAfterViewInit - Current resolveComponentFn:', this.resolveComponentFn);

    if (resolverAvailable && configAvailable) {
      console.log('[Container] ngAfterViewInit - Triggering loadChildren...');
      this.loadChildren();
    } else if (configAvailable) {
      console.log('[Container] ngAfterViewInit - Resolver not available yet, retrying after delay...');
      setTimeout(() => {
        console.log('[Container] Retry - Checking resolver again... ResolverAvailable:', this.resolveComponentFn !== undefined && this.resolveComponentFn !== null);
        if (this.resolveComponentFn !== undefined && this.resolveComponentFn !== null && this.childrenConfig !== undefined && this.childrenConfig !== null && this.childrenConfig.length > 0) {
          console.log('[Container] Retry - Resolver now available, triggering loadChildren...');
          this.loadChildren();
        } else {
          console.error('[Container] Retry - Resolver still not available or config missing. Cannot load children.');
        }
      }, 100); // Retry after a short delay
    }
    this.cdr.detectChanges();
  }

  private loadChildren(): void {
    console.log('[Container] loadChildren called. Config:', this.childrenConfig);
    // Defensive check still useful here
    if (!this.resolveComponentFn) {
      console.error("[Container] loadChildren - 'resolveComponentFn' is missing.");
      return;
    }
    if (!this.childrenConfig || this.childrenConfig.length === 0) {
      console.log('[Container] loadChildren - No children config provided.');
      return;
    }
    this.childContainer.clear();

    this.childrenConfig.forEach(compConfig => {
      console.log(`[Container] loadChildren - Processing child: ${compConfig.type}`); // Log child type
      const componentType = this.resolveComponentFn(compConfig.type);
      console.log(`[Container] loadChildren - Resolved component type for ${compConfig.type}:`, componentType); // Debug resolved type
      if (componentType) {
        try {
          const componentRef = this.childContainer.createComponent(componentType, {
            injector: this.injector
          });
          if (compConfig.inputs) {
            Object.keys(compConfig.inputs).forEach(key => {
              componentRef.instance[key] = compConfig.inputs[key];
            });
          }
          componentRef.changeDetectorRef.detectChanges();
        } catch (error) {
          console.error(`[Container] Error creating component ${compConfig.type}:`, error);
        }
      } else {
        console.warn(`[Container] Component type '${compConfig.type}' not found via resolver.`);
      }
    });
    console.log('[Container] loadChildren finished.');
    this.cdr.detectChanges(); // Ensure container's view is updated after adding children
  }
}

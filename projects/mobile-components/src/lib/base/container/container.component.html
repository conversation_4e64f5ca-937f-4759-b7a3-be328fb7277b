<div [ngClass]="computedClasses" style="min-height: 50px;">
  <div *ngIf="title || subtitle" class="mb-4">
    <h3 *ngIf="title" class="text-lg font-semibold mb-2 text-gray-700">{{ title }}</h3>
    <p *ngIf="subtitle" class="text-sm text-gray-600">{{ subtitle }}</p>
  </div>
  
  <div class="container-content">
    <ng-template #childContainer></ng-template> <!-- Anchor for dynamic children -->
    
    <!-- Default content when no dynamic children are loaded -->
    <div *ngIf="!childrenConfig || childrenConfig.length === 0" class="text-gray-500 text-center py-8">
      <ng-content></ng-content>
      <div *ngIf="!title && !subtitle" class="text-sm">
        Container ready for content
      </div>
    </div>
  </div>
</div>

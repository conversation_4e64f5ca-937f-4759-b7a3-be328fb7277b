import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

@Component({
  selector: 'base-dropdown-item',
  templateUrl: './dropdown-item.component.html',
  styleUrls: ['./dropdown-item.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule]
})
export class DropdownItemComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'none';

  // Component-specific inputs
  @Input() label: string = 'Menu Item';
  @Input() value: any = null;
  @Input() icon: string = '';
  @Input() disabled: boolean = false;
  @Input() selected: boolean = false;
  @Input() href: string = '';
  @Input() routerLink: string | any[] = '';
  @Input() target: '_blank' | '_self' | '_parent' | '_top' = '_self';
  @Input() type: 'button' | 'link' | 'router' = 'button';
  @Input() shortcut: string = '';
  @Input() description: string = '';

  // Events
  @Output() itemClick = new EventEmitter<any>();
  @Output() itemSelect = new EventEmitter<any>();

  ngOnInit() {
    // Component initialization if needed
  }

  get computedClasses(): string {
    const baseClasses = 'flex items-center w-full px-4 py-2 text-left transition-all duration-200 focus:outline-none';
    
    const sizeClasses = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-5 py-2.5 text-lg',
      xl: 'px-6 py-3 text-xl'
    };

    const variantClasses = {
      default: 'text-gray-900 hover:bg-gray-100 focus:bg-gray-100',
      primary: 'text-blue-900 hover:bg-blue-100 focus:bg-blue-100',
      secondary: 'text-gray-700 hover:bg-gray-100 focus:bg-gray-100',
      success: 'text-green-900 hover:bg-green-100 focus:bg-green-100',
      warning: 'text-yellow-900 hover:bg-yellow-100 focus:bg-yellow-100',
      danger: 'text-red-900 hover:bg-red-100 focus:bg-red-100'
    };

    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const stateClasses = this.getStateClasses();
    const disabledClasses = this.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      stateClasses,
      disabledClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedIconClasses(): string {
    const baseIconClasses = 'flex-shrink-0';
    
    const sizeIconClasses = {
      xs: 'h-3 w-3 mr-2',
      sm: 'h-4 w-4 mr-2',
      md: 'h-4 w-4 mr-3',
      lg: 'h-5 w-5 mr-3',
      xl: 'h-6 w-6 mr-4'
    };

    return [baseIconClasses, sizeIconClasses[this.size]].filter(Boolean).join(' ');
  }

  get computedShortcutClasses(): string {
    const baseShortcutClasses = 'ml-auto text-xs text-gray-400 font-mono';
    
    const sizeShortcutClasses = {
      xs: 'text-xs',
      sm: 'text-xs',
      md: 'text-xs',
      lg: 'text-sm',
      xl: 'text-sm'
    };

    return [baseShortcutClasses, sizeShortcutClasses[this.size]].filter(Boolean).join(' ');
  }

  private getStateClasses(): string {
    if (this.selected) {
      const selectedClasses = {
        default: 'bg-gray-100 text-gray-900',
        primary: 'bg-blue-100 text-blue-900',
        secondary: 'bg-gray-100 text-gray-900',
        success: 'bg-green-100 text-green-900',
        warning: 'bg-yellow-100 text-yellow-900',
        danger: 'bg-red-100 text-red-900'
      };
      return selectedClasses[this.variant];
    }
    return '';
  }

  onClick(event: Event) {
    if (this.disabled) {
      event.preventDefault();
      event.stopPropagation();
      return;
    }

    this.itemClick.emit({
      event,
      value: this.value,
      label: this.label,
      item: this
    });

    if (!this.selected) {
      this.selected = true;
      this.itemSelect.emit({
        value: this.value,
        label: this.label,
        item: this
      });
    }
  }

  get isExternalLink(): boolean {
    return this.type === 'link' && !!this.href && (this.href.startsWith('http') || this.href.startsWith('//'));
  }

  get hasIcon(): boolean {
    return !!this.icon;
  }

  get hasShortcut(): boolean {
    return !!this.shortcut;
  }

  get hasDescription(): boolean {
    return !!this.description;
  }
}

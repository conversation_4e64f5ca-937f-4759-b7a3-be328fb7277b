<!-- Button type dropdown item -->
<button
  *ngIf="type === 'button'"
  type="button"
  [ngClass]="computedClasses"
  [disabled]="disabled"
  [attr.aria-selected]="selected"
  [attr.aria-disabled]="disabled"
  role="menuitem"
  (click)="onClick($event)"
>
  <div class="flex items-center flex-1">
    <!-- Icon -->
    <span *ngIf="hasIcon" [ngClass]="computedIconClasses">{{ icon }}</span>
    
    <!-- Content -->
    <div class="flex-1">
      <div class="flex items-center justify-between">
        <span class="font-medium">{{ label }}</span>
        <span *ngIf="hasShortcut" [ngClass]="computedShortcutClasses">{{ shortcut }}</span>
      </div>
      <div *ngIf="hasDescription" class="text-sm text-gray-500 mt-1">{{ description }}</div>
    </div>
  </div>
  
  <!-- Selected indicator -->
  <svg 
    *ngIf="selected" 
    class="ml-2 h-4 w-4 text-blue-600 flex-shrink-0" 
    fill="none" 
    viewBox="0 0 24 24" 
    stroke="currentColor"
  >
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
  </svg>
</button>

<!-- Link type dropdown item -->
<a
  *ngIf="type === 'link' && href"
  [href]="href"
  [target]="target"
  [ngClass]="computedClasses"
  [attr.aria-selected]="selected"
  [attr.aria-disabled]="disabled"
  [class.pointer-events-none]="disabled"
  role="menuitem"
  (click)="onClick($event)"
>
  <div class="flex items-center flex-1">
    <!-- Icon -->
    <span *ngIf="hasIcon" [ngClass]="computedIconClasses">{{ icon }}</span>
    
    <!-- Content -->
    <div class="flex-1">
      <div class="flex items-center justify-between">
        <span class="font-medium">{{ label }}</span>
        <div class="flex items-center">
          <span *ngIf="hasShortcut" [ngClass]="computedShortcutClasses">{{ shortcut }}</span>
          <!-- External link indicator -->
          <svg 
            *ngIf="isExternalLink" 
            class="ml-1 h-3 w-3 text-gray-400" 
            fill="none" 
            viewBox="0 0 24 24" 
            stroke="currentColor"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
          </svg>
        </div>
      </div>
      <div *ngIf="hasDescription" class="text-sm text-gray-500 mt-1">{{ description }}</div>
    </div>
  </div>
  
  <!-- Selected indicator -->
  <svg 
    *ngIf="selected" 
    class="ml-2 h-4 w-4 text-blue-600 flex-shrink-0" 
    fill="none" 
    viewBox="0 0 24 24" 
    stroke="currentColor"
  >
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
  </svg>
</a>

<!-- Router link type dropdown item -->
<a
  *ngIf="type === 'router' && routerLink"
  [routerLink]="routerLink"
  [ngClass]="computedClasses"
  [attr.aria-selected]="selected"
  [attr.aria-disabled]="disabled"
  [class.pointer-events-none]="disabled"
  role="menuitem"
  (click)="onClick($event)"
>
  <div class="flex items-center flex-1">
    <!-- Icon -->
    <span *ngIf="hasIcon" [ngClass]="computedIconClasses">{{ icon }}</span>
    
    <!-- Content -->
    <div class="flex-1">
      <div class="flex items-center justify-between">
        <span class="font-medium">{{ label }}</span>
        <span *ngIf="hasShortcut" [ngClass]="computedShortcutClasses">{{ shortcut }}</span>
      </div>
      <div *ngIf="hasDescription" class="text-sm text-gray-500 mt-1">{{ description }}</div>
    </div>
  </div>
  
  <!-- Selected indicator -->
  <svg 
    *ngIf="selected" 
    class="ml-2 h-4 w-4 text-blue-600 flex-shrink-0" 
    fill="none" 
    viewBox="0 0 24 24" 
    stroke="currentColor"
  >
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
  </svg>
</a>

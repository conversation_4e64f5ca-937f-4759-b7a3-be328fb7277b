import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  OnDestroy,
  Injectable,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { CommonModule } from '@angular/common';

export interface SnackAction {
  label: string;
  action: () => void;
  color?: 'primary' | 'secondary' | 'accent';
}

@Injectable({
  providedIn: 'root',
})
export class NuiDefaultPropertyService {
  private defaults: Record<string, Record<string, any>> = {
    BaseSnack: {
      rounded: 'md',
      size: 'md',
      position: 'bottom-center',
    },
    // Add more component defaults here as needed
  };

  getDefaultProperty<T>(
    componentName: string,
    propertyName: string,
    currentValue: T | undefined
  ): T {
    if (currentValue !== undefined) {
      return currentValue;
    }
    return this.defaults[componentName]?.[propertyName] ?? undefined;
  }
}

@Component({
  selector: 'base-snack',
  templateUrl: './snack.component.html',
  styleUrls: ['./snack.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class SnackComponent implements OnInit, OnDestroy {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() message: string = 'This is a sample snackbar notification message!';
  @Input() title?: string;
  @Input() visible: boolean = true;
  @Input() duration: number = 4000; // Auto-dismiss duration in ms (0 = no auto-dismiss)
  @Input() position: 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right' | 'center' = 'bottom-center';
  @Input() closable: boolean = true;
  @Input() actions: SnackAction[] = [];
  @Input() icon?: string; // Icon name or path
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() showProgress: boolean = false; // Show auto-dismiss progress bar
  @Input() pauseOnHover: boolean = true; // Pause auto-dismiss on hover
  @Input() persistent: boolean = false; // Don't auto-dismiss (overrides duration)
  @Input() multiline: boolean = false; // Allow message wrapping
  @Input() elevation: 'none' | 'sm' | 'md' | 'lg' | 'xl' = 'md'; // Shadow elevation
  @Input() backdrop: boolean = false; // Show backdrop overlay
  @Input() backdropClass: string = 'bg-black bg-opacity-25';

  // Legacy support
  @Input() item_id: string = Math.random().toString(36).substring(7);

  // Events
  @Output() dismiss = new EventEmitter<void>();
  @Output() actionClick = new EventEmitter<SnackAction>();
  @Output() snackShow = new EventEmitter<void>();
  @Output() snackHide = new EventEmitter<void>();

  // Internal state
  private autoHideTimer?: number;
  private progressInterval?: number;
  progressWidth: number = 100;
  isHovered: boolean = false;

  constructor(private nuiDefaultPropertyService: NuiDefaultPropertyService) {}

  ngOnInit() {
    this.size = this.nuiDefaultPropertyService.getDefaultProperty(
      'BaseSnack',
      'size',
      this.size
    ) as 'xs' | 'sm' | 'md' | 'lg' | 'xl';
    
    this.rounded = this.nuiDefaultPropertyService.getDefaultProperty(
      'BaseSnack',
      'rounded',
      this.rounded
    ) as 'none' | 'sm' | 'md' | 'lg' | 'full';

    this.position = this.nuiDefaultPropertyService.getDefaultProperty(
      'BaseSnack',
      'position',
      this.position
    ) as 'top-left' | 'top-center' | 'top-right' | 'bottom-left' | 'bottom-center' | 'bottom-right' | 'center';

    if (this.visible) {
      this.snackShow.emit();
      this.startAutoHide();
    }
  }

  ngOnDestroy() {
    this.clearTimers();
  }

  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'pointer-events-auto transform transition-all duration-300 ease-in-out';
    
    const sizeClasses = {
      xs: 'text-xs px-3 py-2',
      sm: 'text-sm px-4 py-3',
      md: 'text-base px-6 py-4',
      lg: 'text-lg px-8 py-5',
      xl: 'text-xl px-10 py-6'
    };

    const variantClasses = {
      default: 'bg-gray-800 text-white border-gray-700',
      primary: 'bg-blue-600 text-white border-blue-500',
      secondary: 'bg-gray-600 text-white border-gray-500',
      success: 'bg-green-600 text-white border-green-500',
      warning: 'bg-yellow-600 text-black border-yellow-500',
      danger: 'bg-red-600 text-white border-red-500'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const elevationClasses = {
      none: '',
      sm: 'shadow-sm',
      md: 'shadow-md',
      lg: 'shadow-lg',
      xl: 'shadow-xl'
    };

    const multilineClasses = this.multiline ? 'whitespace-normal' : 'whitespace-nowrap';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      elevationClasses[this.elevation],
      multilineClasses,
      'border',
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedContainerClasses(): string {
    const baseContainerClasses = 'fixed z-50 pointer-events-none transition-all duration-300';
    
    const positionClasses = {
      'top-left': 'top-4 left-4',
      'top-center': 'top-4 left-1/2 transform -translate-x-1/2',
      'top-right': 'top-4 right-4',
      'bottom-left': 'bottom-4 left-4',
      'bottom-center': 'bottom-4 left-1/2 transform -translate-x-1/2',
      'bottom-right': 'bottom-4 right-4',
      'center': 'top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2'
    };

    const visibilityClasses = this.visible 
      ? 'opacity-100 scale-100 translate-y-0' 
      : 'opacity-0 scale-95 translate-y-2';

    return [
      baseContainerClasses,
      positionClasses[this.position],
      visibilityClasses
    ].filter(Boolean).join(' ');
  }

  get computedProgressClasses(): string {
    const baseProgressClasses = 'absolute bottom-0 left-0 h-1 transition-all duration-100 ease-linear';
    
    const variantProgressClasses = {
      default: 'bg-white bg-opacity-30',
      primary: 'bg-white bg-opacity-30',
      secondary: 'bg-white bg-opacity-30',
      success: 'bg-white bg-opacity-30',
      warning: 'bg-black bg-opacity-30',
      danger: 'bg-white bg-opacity-30'
    };

    return [
      baseProgressClasses,
      variantProgressClasses[this.variant]
    ].filter(Boolean).join(' ');
  }

  get hasIcon(): boolean {
    return !!this.icon;
  }

  get hasActions(): boolean {
    return this.actions.length > 0;
  }

  get shouldShowProgress(): boolean {
    return this.showProgress && this.duration > 0 && !this.persistent;
  }

  // Event handlers
  onMouseEnter(): void {
    if (this.pauseOnHover) {
      this.isHovered = true;
      this.clearTimers();
    }
  }

  onMouseLeave(): void {
    if (this.pauseOnHover && this.isHovered) {
      this.isHovered = false;
      this.startAutoHide();
    }
  }

  onClose(): void {
    this.close();
  }

  onActionClick(action: SnackAction): void {
    this.actionClick.emit(action);
    action.action();
  }

  // Public methods
  show(): void {
    this.visible = true;
    this.snackShow.emit();
    this.startAutoHide();
  }

  close(): void {
    this.visible = false;
    this.snackHide.emit();
    this.dismiss.emit();
    this.clearTimers();
  }

  toggle(): void {
    if (this.visible) {
      this.close();
    } else {
      this.show();
    }
  }

  // Private methods
  private startAutoHide(): void {
    if (this.duration <= 0 || this.persistent) return;

    this.clearTimers();
    
    if (this.shouldShowProgress) {
      this.startProgressAnimation();
    }

    this.autoHideTimer = window.setTimeout(() => {
      this.close();
    }, this.duration);
  }

  private startProgressAnimation(): void {
    const interval = 50; // Update every 50ms
    const steps = this.duration / interval;
    const decrement = 100 / steps;
    
    this.progressWidth = 100;
    
    this.progressInterval = window.setInterval(() => {
      this.progressWidth -= decrement;
      if (this.progressWidth <= 0) {
        this.progressWidth = 0;
        this.clearProgressAnimation();
      }
    }, interval);
  }

  private clearTimers(): void {
    if (this.autoHideTimer) {
      clearTimeout(this.autoHideTimer);
      this.autoHideTimer = undefined;
    }
    this.clearProgressAnimation();
  }

  private clearProgressAnimation(): void {
    if (this.progressInterval) {
      clearInterval(this.progressInterval);
      this.progressInterval = undefined;
    }
  }

  // Utility methods
  getIconClasses(): string {
    const baseIconClasses = 'flex-shrink-0';
    
    const sizeIconClasses = {
      xs: 'w-4 h-4',
      sm: 'w-5 h-5',
      md: 'w-6 h-6',
      lg: 'w-7 h-7',
      xl: 'w-8 h-8'
    };

    const positionIconClasses = this.iconPosition === 'left' ? 'mr-3' : 'ml-3';

    return [
      baseIconClasses,
      sizeIconClasses[this.size],
      positionIconClasses
    ].filter(Boolean).join(' ');
  }

  getActionButtonClasses(action: SnackAction): string {
    const baseActionClasses = 'ml-3 px-3 py-1 text-sm font-medium rounded transition-colors duration-200 hover:bg-white hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50';
    
    const colorActionClasses = {
      primary: 'text-blue-200 hover:text-blue-100',
      secondary: 'text-gray-200 hover:text-gray-100',
      accent: 'text-purple-200 hover:text-purple-100'
    };

    return [
      baseActionClasses,
      action.color ? colorActionClasses[action.color] : 'text-white'
    ].filter(Boolean).join(' ');
  }
}

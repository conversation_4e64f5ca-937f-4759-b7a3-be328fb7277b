<!-- Backdrop Overlay -->
<div 
  *ngIf="backdrop && visible"
  [class]="backdropClass + ' fixed inset-0 z-40'"
  (click)="onClose()"
></div>

<!-- Snackbar Container -->
<div 
  [class]="computedContainerClasses"
  [attr.id]="item_id"
  role="alert"
  [attr.aria-live]="variant === 'danger' ? 'assertive' : 'polite'"
  [attr.aria-atomic]="true"
>
  <!-- Snackbar Content -->
  <div
    [class]="computedClasses"
    (mouseenter)="onMouseEnter()"
    (mouseleave)="onMouseLeave()"
    [style.max-width]="position.includes('center') ? '90vw' : '400px'"
  >
    <!-- Main Content Area -->
    <div class="flex items-start">
      <!-- Left Icon -->
      <div 
        *ngIf="hasIcon && iconPosition === 'left'"
        [class]="getIconClasses()"
      >
        <!-- Icon Slot -->
        <ng-container *ngIf="icon">
          <!-- Default Icons by Variant -->
          <svg 
            *ngIf="variant === 'success'"
            class="w-full h-full"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
          
          <svg 
            *ngIf="variant === 'warning'"
            class="w-full h-full"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
          </svg>
          
          <svg 
            *ngIf="variant === 'danger'"
            class="w-full h-full"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
          </svg>
          
          <svg 
            *ngIf="variant === 'primary'"
            class="w-full h-full"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
          
          <!-- Default/Secondary Icon -->
          <svg 
            *ngIf="variant === 'default' || variant === 'secondary'"
            class="w-full h-full"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
          </svg>
        </ng-container>
      </div>

      <!-- Message Content -->
      <div class="flex-1 min-w-0">
        <!-- Title -->
        <h4 
          *ngIf="title"
          class="font-semibold mb-1"
          [ngClass]="{ 'text-sm': size === 'xs' || size === 'sm' }"
        >
          {{ title }}
        </h4>
        
        <!-- Message -->
        <p 
          class="leading-relaxed"
          [ngClass]="{ 
            'text-sm': size === 'xs',
            'text-base': size === 'sm',
            'break-words': multiline
          }"
        >
          {{ message }}
        </p>
        
        <!-- Slotted Content -->
        <ng-content></ng-content>
      </div>

      <!-- Right Icon -->
      <div 
        *ngIf="hasIcon && iconPosition === 'right'"
        [class]="getIconClasses()"
      >
        <!-- Same icon logic as left side -->
        <ng-container *ngIf="icon">
          <svg 
            *ngIf="variant === 'success'"
            class="w-full h-full"
            fill="currentColor"
            viewBox="0 0 20 20"
          >
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
          </svg>
        </ng-container>
      </div>

      <!-- Actions -->
      <div *ngIf="hasActions" class="flex items-center ml-3">
        <button
          *ngFor="let action of actions"
          type="button"
          [class]="getActionButtonClasses(action)"
          (click)="onActionClick(action)"
        >
          {{ action.label }}
        </button>
      </div>

      <!-- Close Button -->
      <button
        *ngIf="closable"
        type="button"
        class="ml-3 flex-shrink-0 p-1 rounded-md hover:bg-white hover:bg-opacity-20 focus:outline-none focus:ring-2 focus:ring-white focus:ring-opacity-50 transition-colors duration-200"
        (click)="onClose()"
        [attr.aria-label]="'Close notification'"
      >
        <svg 
          class="w-4 h-4" 
          fill="currentColor" 
          viewBox="0 0 20 20"
        >
          <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
        </svg>
      </button>
    </div>

    <!-- Progress Bar -->
    <div 
      *ngIf="shouldShowProgress"
      class="absolute bottom-0 left-0 right-0 h-1 bg-white bg-opacity-20 rounded-b-md overflow-hidden"
    >
      <div 
        [class]="computedProgressClasses"
        [style.width.%]="progressWidth"
      ></div>
    </div>
  </div>
</div>

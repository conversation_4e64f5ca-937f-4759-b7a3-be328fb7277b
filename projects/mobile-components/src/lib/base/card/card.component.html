<div 
  [ngClass]="computedClasses"
  (click)="onClick($event)"
  [attr.role]="clickable ? 'button' : null"
  [attr.tabindex]="clickable ? 0 : null">
  
  <!-- Card Header -->
  <div *ngIf="title || subtitle" [ngClass]="headerClasses">
    <h3 *ngIf="title" class="text-lg font-semibold text-gray-900">{{ title }}</h3>
    <p *ngIf="subtitle" class="text-sm text-gray-600 mt-1">{{ subtitle }}</p>
  </div>
  
  <!-- Card Content -->
  <div [ngClass]="contentClasses">
    <ng-content></ng-content>
  </div>
</div>

import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'base-card',
  templateUrl: './card.component.html',
  styleUrls: ['./card.component.css'],
  standalone: true,
  imports: [NgClass, NgIf]
})
export class CardComponent implements OnInit {
  // Standard inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'glass' | 'outlined' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full' = 'lg';

  // Component-specific inputs
  @Input() title: string = 'Card Title';
  @Input() subtitle: string = 'This is a sample card description that shows the card\'s purpose and content.';
  @Input() padding: boolean = true;
  @Input() shadow: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'hover' = 'md';
  @Input() border: boolean = true;
  @Input() hoverable: boolean = false;
  @Input() clickable: boolean = false;

  // Legacy inputs for backward compatibility
  @Input() color?: 'default' | 'primary' | 'info' | 'muted' | 'success' | 'warning' | 'danger';

  // Events
  @Output() cardClicked = new EventEmitter<Event>();

  ngOnInit(): void {
    // Component initialization
  }

  get computedClasses(): string {
    const baseClasses = 'block transition-all duration-200';
    
    const sizeClasses = {
      xs: 'max-w-xs',
      sm: 'max-w-sm',
      md: 'max-w-md',
      lg: 'max-w-lg',
      xl: 'max-w-xl'
    };

    const variantClasses = {
      default: 'bg-white border-gray-200',
      primary: 'bg-blue-50 border-blue-200',
      secondary: 'bg-gray-50 border-gray-200',
      success: 'bg-green-50 border-green-200',
      warning: 'bg-yellow-50 border-yellow-200',
      danger: 'bg-red-50 border-red-200',
      glass: 'bg-white/70 backdrop-blur-sm border-white/20',
      outlined: 'bg-transparent border-gray-300'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      xl: 'rounded-xl',
      '2xl': 'rounded-2xl',
      '3xl': 'rounded-3xl',
      full: 'rounded-full'
    };

    const shadowClasses = {
      none: '',
      sm: 'shadow-sm',
      md: 'shadow-md',
      lg: 'shadow-lg',
      xl: 'shadow-xl',
      '2xl': 'shadow-2xl',
      hover: 'shadow-md hover:shadow-lg'
    };

    const borderClass = this.border ? 'border' : '';
    const hoverableClass = this.hoverable ? 'hover:scale-105' : '';
    const clickableClass = this.clickable ? 'cursor-pointer' : '';

    // Legacy color support
    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      shadowClasses[this.shadow],
      borderClass,
      hoverableClass,
      clickableClass,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get contentClasses(): string {
    return this.padding ? 'p-6' : '';
  }

  get headerClasses(): string {
    const baseClasses = 'border-b';
    const paddingClasses = this.padding ? 'px-6 pt-6 pb-4' : 'p-4';
    
    const variantBorderClasses = {
      default: 'border-gray-200',
      primary: 'border-blue-200',
      secondary: 'border-gray-200',
      success: 'border-green-200',
      warning: 'border-yellow-200',
      danger: 'border-red-200',
      glass: 'border-white/20',
      outlined: 'border-gray-300'
    };

    return [
      baseClasses,
      paddingClasses,
      variantBorderClasses[this.variant]
    ].join(' ');
  }

  private getLegacyClasses(): string {
    // Support legacy color input
    const legacyColors = {
      'default': 'bg-white border-gray-200',
      'primary': 'bg-blue-50 border-blue-200',
      'info': 'bg-blue-50 border-blue-200',
      'muted': 'bg-gray-50 border-gray-200',
      'success': 'bg-green-50 border-green-200',
      'warning': 'bg-yellow-50 border-yellow-200',
      'danger': 'bg-red-50 border-red-200'
    };

    return this.color ? legacyColors[this.color] || '' : '';
  }

  onClick(event: Event): void {
    if (this.clickable) {
      this.cardClicked.emit(event);
    }
  }
}
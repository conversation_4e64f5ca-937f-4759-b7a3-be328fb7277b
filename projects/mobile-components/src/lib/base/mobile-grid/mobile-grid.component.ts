import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'mobile-grid',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div [ngClass]="computedClasses">
      <ng-content></ng-content>
    </div>
  `,
  styles: [`
    .mobile-grid {
      display: grid;
      gap: 1rem;
      width: 100%;
    }
    
    @media (max-width: 640px) {
      .mobile-grid {
        gap: 0.75rem;
      }
    }
  `]
})
export class MobileGridComponent {
  @Input() columns: number = 1;
  @Input() gap: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() className: string = '';

  get computedClasses(): string {
    const baseClasses = 'mobile-grid';
    
    const gapClasses = {
      xs: 'gap-1',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8'
    };

    const gridClasses = this.columns > 0 ? `grid-cols-${this.columns}` : 'grid-cols-1';
    const responsiveClasses = 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6';

    return [
      baseClasses,
      this.columns > 0 ? gridClasses : responsiveClasses,
      gapClasses[this.gap],
      this.className
    ].filter(Boolean).join(' ');
  }
}

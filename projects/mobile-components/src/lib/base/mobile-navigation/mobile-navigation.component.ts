import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

export interface NavItem {
  label: string;
  icon?: string;
  route?: string;
  action?: () => void;
  active?: boolean;
  disabled?: boolean;
  badge?: string | number;
}

@Component({
  selector: 'mobile-navigation',
  standalone: true,
  imports: [CommonModule, RouterModule],
  template: `
    <nav [ngClass]="computedClasses">
      <div class="flex justify-around items-center h-full">
        <button
          *ngFor="let item of items; trackBy: trackByIndex"
          type="button"
          [disabled]="item.disabled"
          [class]="getItemClasses(item)"
          (click)="onItemClick(item)"
          [routerLink]="item.route"
          routerLinkActive="active"
          [routerLinkActiveOptions]="{ exact: exactMatch }"
        >
          <!-- Icon -->
          <div class="relative mb-1" *ngIf="item.icon">
            <i [class]="item.icon + ' text-lg'"></i>
            <!-- Badge -->
            <span 
              *ngIf="item.badge" 
              class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full min-w-[16px] h-4 flex items-center justify-center px-1"
            >
              {{ item.badge }}
            </span>
          </div>
          
          <!-- Label -->
          <span class="text-xs font-medium">{{ item.label }}</span>
        </button>
      </div>
    </nav>
  `,
  styles: [`
    .mobile-nav {
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 50;
    }
    
    .mobile-nav-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      min-height: 48px;
      min-width: 48px;
      transition: all 0.2s ease;
      touch-action: manipulation;
    }
    
    .mobile-nav-item:active {
      transform: scale(0.95);
    }
    
    .mobile-nav-item.active {
      color: #3b82f6;
    }
    
    .mobile-nav-item:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  `]
})
export class MobileNavigationComponent {
  @Input() items: NavItem[] = [
    { label: 'Home', icon: 'fas fa-home', route: '/home' },
    { label: 'Search', icon: 'fas fa-search', route: '/search' },
    { label: 'Profile', icon: 'fas fa-user', route: '/profile', badge: '3' },
    { label: 'Settings', icon: 'fas fa-cog', route: '/settings' }
  ];
  @Input() variant: 'default' | 'primary' | 'dark' = 'default';
  @Input() position: 'bottom' | 'top' = 'bottom';
  @Input() shadow: boolean = true;
  @Input() exactMatch: boolean = false;
  @Input() className: string = '';

  @Output() itemClick = new EventEmitter<NavItem>();

  get computedClasses(): string {
    const baseClasses = 'mobile-nav w-full border-t';
    
    const variantClasses = {
      default: 'bg-white border-gray-200 text-gray-600',
      primary: 'bg-blue-600 border-blue-700 text-white',
      dark: 'bg-gray-900 border-gray-800 text-gray-300'
    };

    const positionClasses = this.position === 'bottom' ? 'bottom-0' : 'top-0';
    const shadowClasses = this.shadow ? 'shadow-lg' : '';

    return [
      baseClasses,
      variantClasses[this.variant],
      positionClasses,
      shadowClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  getItemClasses(item: NavItem): string {
    const baseClasses = 'mobile-nav-item flex-1 p-2 text-center transition-colors';
    const stateClasses = item.active ? 'active' : '';
    const disabledClasses = item.disabled ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100';

    return [baseClasses, stateClasses, disabledClasses].filter(Boolean).join(' ');
  }

  onItemClick(item: NavItem): void {
    if (item.disabled) return;
    
    if (item.action) {
      item.action();
    }
    
    this.itemClick.emit(item);
  }

  trackByIndex(index: number): number {
    return index;
  }
}

import { Component, Input, Output, EventEmitter, OnInit, Inject, Optional, forwardRef, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR, FormsModule } from '@angular/forms';
import { IconComponent } from '../icon/icon.component';

export interface ListboxOption {
  id: string | number;
  label: string;
  value: any;
  disabled?: boolean;
  icon?: string;
  description?: string;
  group?: string;
}

@Component({
  selector: 'base-listbox',
  templateUrl: './listbox.component.html',
  styleUrls: ['./listbox.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule, IconComponent],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ListboxComponent),
      multi: true
    }
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ListboxComponent implements OnInit, ControlValueAccessor {
  // Make Object available in template
  Object = Object;
  
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() options: ListboxOption[] = [
    { id: 1, label: 'Option 1', value: 'option1', description: 'First sample option', icon: 'star' },
    { id: 2, label: 'Option 2', value: 'option2', description: 'Second sample option', icon: 'heart' },
    { id: 3, label: 'Option 3', value: 'option3', description: 'Third sample option', icon: 'bookmark' },
    { id: 4, label: 'Disabled Option', value: 'disabled', description: 'This option is disabled', disabled: true, icon: 'lock' }
  ];
  @Input() multiple: boolean = false;
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() height: string = 'auto';
  @Input() maxHeight: string = '300px';
  @Input() searchable: boolean = false;
  @Input() searchPlaceholder: string = 'Search options...';
  @Input() emptyText: string = 'No options available';
  @Input() loadingText: string = 'Loading options...';
  @Input() loading: boolean = false;
  @Input() groupBy: boolean = false;

  // Events
  @Output() selectionChange = new EventEmitter<any>();
  @Output() optionClick = new EventEmitter<ListboxOption>();
  @Output() searchChange = new EventEmitter<string>();

  // Internal state
  selectedValues: any[] = [];
  searchTerm: string = '';
  focusedIndex: number = -1;

  // ControlValueAccessor
  private onChange = (value: any) => {};
  private onTouched = () => {};
  
  constructor(@Optional() @Inject('componentProperties') public properties: any) {}
  
  ngOnInit(): void {
    if (this.properties) {
      // Map properties from injected properties
      this.className = this.properties.className || this.className;
      this.size = this.properties.size || this.size;
      this.variant = this.properties.variant || this.variant;
      this.rounded = this.properties.rounded || this.rounded;
      this.options = this.properties.options || this.options;
      this.multiple = this.properties.multiple || this.multiple;
      this.disabled = this.properties.disabled || this.disabled;
      this.required = this.properties.required || this.required;
      this.searchable = this.properties.searchable || this.searchable;
      this.loading = this.properties.loading || this.loading;
      this.groupBy = this.properties.groupBy || this.groupBy;
    }
  }

  get computedClasses(): string {
    const baseClasses = 'border focus-within:ring-2 focus-within:ring-offset-2 transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'border-gray-300 focus-within:ring-blue-500 focus-within:border-blue-500',
      primary: 'border-blue-300 focus-within:ring-blue-500 focus-within:border-blue-500',
      secondary: 'border-gray-400 focus-within:ring-gray-500 focus-within:border-gray-500',
      success: 'border-green-300 focus-within:ring-green-500 focus-within:border-green-500',
      warning: 'border-yellow-300 focus-within:ring-yellow-500 focus-within:border-yellow-500',
      danger: 'border-red-300 focus-within:ring-red-500 focus-within:border-red-500'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const stateClasses = [];
    if (this.disabled) stateClasses.push('opacity-50 cursor-not-allowed bg-gray-50');
    else stateClasses.push('bg-white');

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      ...stateClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get filteredOptions(): ListboxOption[] {
    if (!this.searchTerm) return this.options;
    
    return this.options.filter(option =>
      option.label.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
      (option.description && option.description.toLowerCase().includes(this.searchTerm.toLowerCase()))
    );
  }

  get groupedOptions(): Record<string, ListboxOption[]> {
    if (!this.groupBy) return {};
    
    return this.filteredOptions.reduce((groups, option) => {
      const group = option.group || 'Other';
      if (!groups[group]) groups[group] = [];
      groups[group].push(option);
      return groups;
    }, {} as Record<string, ListboxOption[]>);
  }

  // ControlValueAccessor implementation
  writeValue(value: any): void {
    if (this.multiple) {
      this.selectedValues = Array.isArray(value) ? value : [];
    } else {
      this.selectedValues = value ? [value] : [];
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  isSelected(option: ListboxOption): boolean {
    return this.selectedValues.includes(option.value);
  }

  onOptionClick(option: ListboxOption): void {
    if (this.disabled || option.disabled) return;

    this.onTouched();
    this.optionClick.emit(option);

    if (this.multiple) {
      const index = this.selectedValues.indexOf(option.value);
      if (index > -1) {
        this.selectedValues.splice(index, 1);
      } else {
        this.selectedValues.push(option.value);
      }
      this.onChange([...this.selectedValues]);
      this.selectionChange.emit([...this.selectedValues]);
    } else {
      this.selectedValues = this.isSelected(option) ? [] : [option.value];
      this.onChange(this.selectedValues[0] || null);
      this.selectionChange.emit(this.selectedValues[0] || null);
    }
  }

  onSearchInput(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value;
    this.searchChange.emit(this.searchTerm);
    this.focusedIndex = -1;
  }

  onKeyDown(event: KeyboardEvent): void {
    if (this.disabled) return;

    const visibleOptions = this.filteredOptions.filter(opt => !opt.disabled);
    
    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault();
        this.focusedIndex = Math.min(this.focusedIndex + 1, visibleOptions.length - 1);
        break;
      case 'ArrowUp':
        event.preventDefault();
        this.focusedIndex = Math.max(this.focusedIndex - 1, 0);
        break;
      case 'Enter':
      case ' ':
        event.preventDefault();
        if (this.focusedIndex >= 0 && visibleOptions[this.focusedIndex]) {
          this.onOptionClick(visibleOptions[this.focusedIndex]);
        }
        break;
      case 'Escape':
        this.focusedIndex = -1;
        break;
    }
  }

  getOptionClasses(option: ListboxOption, index: number): string {
    const baseClasses = 'px-4 py-2 cursor-pointer transition-colors duration-150 flex items-center gap-3';
    const hoverClasses = 'hover:bg-gray-50';
    const selectedClasses = this.isSelected(option) ? 'bg-blue-50 text-blue-900 border-l-4 border-blue-500' : '';
    const disabledClasses = option.disabled ? 'opacity-50 cursor-not-allowed' : '';
    const focusedClasses = this.focusedIndex === index ? 'bg-gray-100' : '';

    return [
      baseClasses,
      !option.disabled ? hoverClasses : '',
      selectedClasses,
      disabledClasses,
      focusedClasses
    ].filter(Boolean).join(' ');
  }

  trackByOption(index: number, option: ListboxOption): any {
    return option.id;
  }
}

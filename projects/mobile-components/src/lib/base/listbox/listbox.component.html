<!-- Enhanced Listbox component for single/multi selection -->
<div 
  [ngClass]="computedClasses"
  [attr.aria-label]="'Listbox with ' + options.length + ' options'"
  [attr.aria-disabled]="disabled"
  (keydown)="onKeyDown($event)"
  tabindex="0"
  role="listbox"
  [attr.aria-multiselectable]="multiple"
>
  <!-- Search input -->
  <div *ngIf="searchable" class="p-3 border-b border-gray-200">
    <div class="relative">
      <base-icon icon="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"></base-icon>
      <input
        type="text"
        [placeholder]="searchPlaceholder"
        [value]="searchTerm"
        [disabled]="disabled"
        class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        (input)="onSearchInput($event)"
      >
    </div>
  </div>

  <!-- Loading state -->
  <div *ngIf="loading" class="flex items-center justify-center py-8">
    <svg class="animate-spin h-6 w-6 text-gray-400 mr-2" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    <span class="text-gray-500">{{ loadingText }}</span>
  </div>

  <!-- Empty state -->
  <div *ngIf="!loading && filteredOptions.length === 0" class="flex flex-col items-center justify-center py-8 text-gray-500">
    <base-icon icon="inbox" class="h-12 w-12 mb-2 text-gray-300"></base-icon>
    <span>{{ emptyText }}</span>
  </div>

  <!-- Options list -->
  <div 
    *ngIf="!loading && filteredOptions.length > 0"
    class="overflow-y-auto"
    [style.height]="height !== 'auto' ? height : 'auto'"
    [style.max-height]="maxHeight"
  >
    <!-- Grouped options -->
    <ng-container *ngIf="groupBy && Object.keys(groupedOptions).length > 0">
      <div *ngFor="let group of Object.keys(groupedOptions)" class="group">
        <!-- Group header -->
        <div class="px-4 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider bg-gray-50 border-b border-gray-200">
          {{ group }}
        </div>
        
        <!-- Group options -->
        <div
          *ngFor="let option of groupedOptions[group]; let i = index; trackBy: trackByOption"
          [ngClass]="getOptionClasses(option, i)"
          [attr.aria-selected]="isSelected(option)"
          [attr.aria-disabled]="option.disabled"
          role="option"
          (click)="onOptionClick(option)"
        >
          <ng-container *ngTemplateOutlet="optionTemplate; context: { $implicit: option }"></ng-container>
        </div>
      </div>
    </ng-container>

    <!-- Non-grouped options -->
    <ng-container *ngIf="!groupBy">
      <div
        *ngFor="let option of filteredOptions; let i = index; trackBy: trackByOption"
        [ngClass]="getOptionClasses(option, i)"
        [attr.aria-selected]="isSelected(option)"
        [attr.aria-disabled]="option.disabled"
        role="option"
        (click)="onOptionClick(option)"
      >
        <ng-container *ngTemplateOutlet="optionTemplate; context: { $implicit: option }"></ng-container>
      </div>
    </ng-container>
  </div>

  <!-- Content projection for custom options -->
  <ng-content></ng-content>
</div>

<!-- Option template -->
<ng-template #optionTemplate let-option>
  <div class="flex items-center w-full">
    <!-- Selection indicator -->
    <div class="flex items-center mr-3">
      <input
        *ngIf="multiple"
        type="checkbox"
        [checked]="isSelected(option)"
        [disabled]="option.disabled"
        class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        (click)="$event.stopPropagation()"
        readonly
      >
      <div
        *ngIf="!multiple"
        class="w-4 h-4 rounded-full border-2 flex items-center justify-center"
        [class.border-blue-500]="isSelected(option)"
        [class.border-gray-300]="!isSelected(option)"
        [class.bg-blue-500]="isSelected(option)"
      >
        <div *ngIf="isSelected(option)" class="w-2 h-2 bg-white rounded-full"></div>
      </div>
    </div>

    <!-- Icon -->
    <base-icon 
      *ngIf="option.icon" 
      [icon]="option.icon" 
      class="h-5 w-5 text-gray-400 mr-3"
      [class.text-blue-500]="isSelected(option)"
    ></base-icon>

    <!-- Content -->
    <div class="flex-1 min-w-0">
      <div class="font-medium" [class.text-blue-900]="isSelected(option)">
        {{ option.label }}
      </div>
      <div *ngIf="option.description" class="text-sm text-gray-500 truncate">
        {{ option.description }}
      </div>
    </div>

    <!-- Selected indicator -->
    <base-icon 
      *ngIf="isSelected(option)" 
      icon="check" 
      class="h-4 w-4 text-blue-500 ml-2"
    ></base-icon>
  </div>
</ng-template>

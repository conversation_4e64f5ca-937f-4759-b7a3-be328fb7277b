import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-divider',
  templateUrl: './divider.component.html',
  styleUrls: ['./divider.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class DividerComponent {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'none';

  // Component-specific inputs
  @Input() text: string = '';
  @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';
  @Input() spacing: 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() thickness: 'thin' | 'medium' | 'thick' = 'thin';
  @Input() style: 'solid' | 'dashed' | 'dotted' = 'solid';
  @Input() element: 'div' | 'hr' | 'li' = 'div';

  get computedClasses(): string {
    const baseClasses = this.orientation === 'horizontal' 
      ? 'w-full flex items-center' 
      : 'h-full flex flex-col items-center';
    
    const sizeClasses = {
      xs: this.orientation === 'horizontal' ? 'text-xs' : 'text-xs',
      sm: this.orientation === 'horizontal' ? 'text-sm' : 'text-sm',
      md: this.orientation === 'horizontal' ? 'text-base' : 'text-base',
      lg: this.orientation === 'horizontal' ? 'text-lg' : 'text-lg',
      xl: this.orientation === 'horizontal' ? 'text-xl' : 'text-xl'
    };

    const variantClasses = {
      default: 'border-muted-200 dark:border-muted-700 text-muted-500',
      primary: 'border-blue-200 dark:border-blue-700 text-blue-500',
      secondary: 'border-gray-200 dark:border-gray-700 text-gray-500',
      success: 'border-green-200 dark:border-green-700 text-green-500',
      warning: 'border-yellow-200 dark:border-yellow-700 text-yellow-500',
      danger: 'border-red-200 dark:border-red-700 text-red-500'
    };

    const spacingClasses = {
      none: this.orientation === 'horizontal' ? 'my-0' : 'mx-0',
      xs: this.orientation === 'horizontal' ? 'my-1' : 'mx-1',
      sm: this.orientation === 'horizontal' ? 'my-2' : 'mx-2',
      md: this.orientation === 'horizontal' ? 'my-3' : 'mx-3',
      lg: this.orientation === 'horizontal' ? 'my-4' : 'mx-4',
      xl: this.orientation === 'horizontal' ? 'my-6' : 'mx-6'
    };

    const thicknessClasses = {
      thin: this.orientation === 'horizontal' ? 'h-px' : 'w-px',
      medium: this.orientation === 'horizontal' ? 'h-0.5' : 'w-0.5',
      thick: this.orientation === 'horizontal' ? 'h-1' : 'w-1'
    };

    const styleClasses = {
      solid: 'border-solid',
      dashed: 'border-dashed',
      dotted: 'border-dotted'
    };

    const borderDirection = this.orientation === 'horizontal' ? 'border-t' : 'border-l';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      spacingClasses[this.spacing],
      thicknessClasses[this.thickness],
      styleClasses[this.style],
      borderDirection,
      this.className
    ].filter(Boolean).join(' ');
  }

  get lineClasses(): string {
    const baseLineClasses = 'flex-1';
    
    const variantLineClasses = {
      default: 'border-muted-200 dark:border-muted-700',
      primary: 'border-blue-200 dark:border-blue-700',
      secondary: 'border-gray-200 dark:border-gray-700',
      success: 'border-green-200 dark:border-green-700',
      warning: 'border-yellow-200 dark:border-yellow-700',
      danger: 'border-red-200 dark:border-red-700'
    };

    const thicknessLineClasses = {
      thin: this.orientation === 'horizontal' ? 'h-px' : 'w-px',
      medium: this.orientation === 'horizontal' ? 'h-0.5' : 'w-0.5',
      thick: this.orientation === 'horizontal' ? 'h-1' : 'w-1'
    };

    const styleLineClasses = {
      solid: 'border-solid',
      dashed: 'border-dashed',
      dotted: 'border-dotted'
    };

    const borderDirection = this.orientation === 'horizontal' ? 'border-t' : 'border-l';

    return [
      baseLineClasses,
      variantLineClasses[this.variant],
      thicknessLineClasses[this.thickness],
      styleLineClasses[this.style],
      borderDirection
    ].filter(Boolean).join(' ');
  }

  get textClasses(): string {
    const baseTextClasses = this.orientation === 'horizontal' ? 'px-3' : 'py-3';
    
    const sizeTextClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-sm',
      lg: 'text-base',
      xl: 'text-lg'
    };

    const variantTextClasses = {
      default: 'text-muted-500 bg-white dark:bg-gray-900',
      primary: 'text-blue-500 bg-white dark:bg-gray-900',
      secondary: 'text-gray-500 bg-white dark:bg-gray-900',
      success: 'text-green-500 bg-white dark:bg-gray-900',
      warning: 'text-yellow-500 bg-white dark:bg-gray-900',
      danger: 'text-red-500 bg-white dark:bg-gray-900'
    };

    return [
      baseTextClasses,
      sizeTextClasses[this.size],
      variantTextClasses[this.variant]
    ].filter(Boolean).join(' ');
  }
}

<!-- Simple divider without text -->
<div *ngIf="!text && element === 'div'" [ngClass]="computedClasses" role="separator" aria-orientation="horizontal"></div>
<hr *ngIf="!text && element === 'hr'" [ngClass]="computedClasses" />
<li *ngIf="!text && element === 'li'" [ngClass]="computedClasses" role="separator" aria-orientation="horizontal"></li>

<!-- Divider with text -->
<div *ngIf="text && orientation === 'horizontal'" class="flex items-center" [ngClass]="className">
  <div [ngClass]="lineClasses"></div>
  <span [ngClass]="textClasses">{{ text }}</span>
  <div [ngClass]="lineClasses"></div>
</div>

<div *ngIf="text && orientation === 'vertical'" class="flex flex-col items-center" [ngClass]="className">
  <div [ngClass]="lineClasses"></div>
  <span [ngClass]="textClasses">{{ text }}</span>
  <div [ngClass]="lineClasses"></div>
</div>

<div 
  [ngClass]="computedClasses"
  [ngStyle]="inlineStyles"
  (click)="onPictureClick($event)"
  role="img"
  [attr.aria-label]="alt"
>
  <!-- Picture Element for Responsive Images -->
  <picture class="w-full h-full">
    <!-- Source elements for different media conditions -->
    <source 
      *ngFor="let source of sources"
      [srcset]="source.src"
      [media]="source.media"
      [type]="source.type"
      [sizes]="source.sizes"
    />
    
    <!-- Fallback img element -->
    <img
      [src]="currentSrc || src"
      [alt]="alt"
      [ngClass]="computedImageClasses"
      [ngStyle]="inlineStyles"
      [loading]="loading"
      (load)="onImageLoad($event)"
      (error)="onImageError($event)"
    />
  </picture>

  <!-- Placeholder overlay -->
  <div 
    *ngIf="showPlaceholder"
    [ngClass]="computedPlaceholderClasses"
  >
    <div class="flex flex-col items-center justify-center text-center p-4">
      <!-- Placeholder icon -->
      <svg 
        *ngIf="!imageLoaded || hasImageError"
        class="w-8 h-8 mb-2 opacity-50"
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
        [attr.aria-hidden]="true"
      >
        <path 
          stroke-linecap="round" 
          stroke-linejoin="round" 
          stroke-width="2" 
          d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
        ></path>
      </svg>
      
      <!-- Error message -->
      <span 
        *ngIf="hasImageError" 
        class="text-xs opacity-75"
      >
        Failed to load image
      </span>
      
      <!-- Loading indicator -->
      <div 
        *ngIf="!imageLoaded && !hasImageError"
        class="animate-pulse text-xs opacity-75"
      >
        Loading...
      </div>
    </div>
  </div>

  <!-- Content projection for overlay content -->
  <ng-content></ng-content>
</div>

import { Component, Input, Output, EventEmitter, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

export interface PictureSource {
  src: string;
  media?: string;
  type?: string;
  sizes?: string;
}

@Component({
  selector: 'base-picture',
  templateUrl: './picture.component.html',
  styleUrls: ['./picture.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PictureComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() src: string = 'https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=800&h=600&fit=crop';
  @Input() alt: string = 'Sample image demonstrating responsive picture component';
  @Input() sources: PictureSource[] = [
    {
      src: 'https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=400&h=300&fit=crop',
      media: '(max-width: 640px)'
    },
    {
      src: 'https://images.unsplash.com/photo-1472214103451-9374bd1c798e?w=800&h=600&fit=crop',
      media: '(max-width: 1024px)'
    }
  ];
  @Input() width?: number | string;
  @Input() height?: number | string;
  @Input() loading: 'lazy' | 'eager' = 'lazy';
  @Input() aspectRatio: 'square' | 'video' | 'photo' | 'wide' | 'tall' | 'auto' = 'auto';
  @Input() objectFit: 'contain' | 'cover' | 'fill' | 'scale-down' | 'none' = 'cover';
  @Input() objectPosition: string = 'center';
  @Input() fallbackSrc?: string;
  @Input() showPlaceholder: boolean = true;
  @Input() placeholderIcon: string = 'lucide:image';
  @Input() disabled: boolean = false;
  @Input() clickable: boolean = false;

  // Events
  @Output() imageLoad = new EventEmitter<Event>();
  @Output() imageError = new EventEmitter<Event>();
  @Output() pictureClick = new EventEmitter<MouseEvent>();

  // State
  imageLoaded: boolean = false;
  hasImageError: boolean = false;
  currentSrc: string = '';

  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'relative overflow-hidden transition-all duration-200';
    
    const sizeClasses = {
      xs: 'w-16 h-16',
      sm: 'w-24 h-24',
      md: 'w-32 h-32',
      lg: 'w-48 h-48',
      xl: 'w-64 h-64'
    };

    const variantClasses = {
      default: 'bg-gray-100 dark:bg-gray-800',
      primary: 'bg-blue-50 dark:bg-blue-900/20 ring-1 ring-blue-200 dark:ring-blue-800',
      secondary: 'bg-gray-50 dark:bg-gray-900 ring-1 ring-gray-200 dark:ring-gray-700',
      success: 'bg-green-50 dark:bg-green-900/20 ring-1 ring-green-200 dark:ring-green-800',
      warning: 'bg-yellow-50 dark:bg-yellow-900/20 ring-1 ring-yellow-200 dark:ring-yellow-800',
      danger: 'bg-red-50 dark:bg-red-900/20 ring-1 ring-red-200 dark:ring-red-800'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const aspectRatioClasses = {
      square: 'aspect-square',
      video: 'aspect-video',
      photo: 'aspect-[4/3]',
      wide: 'aspect-[16/9]',
      tall: 'aspect-[3/4]',
      auto: ''
    };

    const interactionClasses = [
      this.clickable ? 'cursor-pointer hover:shadow-lg' : '',
      this.disabled ? 'opacity-50 cursor-not-allowed' : ''
    ];

    return [
      baseClasses,
      this.width === undefined && this.height === undefined ? sizeClasses[this.size] : '',
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      aspectRatioClasses[this.aspectRatio],
      ...interactionClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedImageClasses(): string {
    const baseImageClasses = 'w-full h-full transition-opacity duration-300';
    
    const objectFitClasses = {
      contain: 'object-contain',
      cover: 'object-cover',
      fill: 'object-fill',
      'scale-down': 'object-scale-down',
      none: 'object-none'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      baseImageClasses,
      objectFitClasses[this.objectFit],
      roundedClasses[this.rounded],
      this.imageLoaded ? 'opacity-100' : 'opacity-0'
    ].filter(Boolean).join(' ');
  }

  get computedPlaceholderClasses(): string {
    const basePlaceholderClasses = 'absolute inset-0 flex items-center justify-center transition-opacity duration-300';
    
    const variantPlaceholderClasses = {
      default: 'text-gray-400 dark:text-gray-500',
      primary: 'text-blue-400 dark:text-blue-500',
      secondary: 'text-gray-400 dark:text-gray-500',
      success: 'text-green-400 dark:text-green-500',
      warning: 'text-yellow-400 dark:text-yellow-500',
      danger: 'text-red-400 dark:text-red-500'
    };

    return [
      basePlaceholderClasses,
      variantPlaceholderClasses[this.variant],
      this.imageLoaded && !this.hasImageError ? 'opacity-0 pointer-events-none' : 'opacity-100'
    ].filter(Boolean).join(' ');
  }

  get inlineStyles(): { [key: string]: string } {
    const styles: { [key: string]: string } = {};
    
    if (this.width !== undefined) {
      styles['width'] = typeof this.width === 'number' ? `${this.width}px` : this.width;
    }
    
    if (this.height !== undefined) {
      styles['height'] = typeof this.height === 'number' ? `${this.height}px` : this.height;
    }

    if (this.objectPosition) {
      styles['object-position'] = this.objectPosition;
    }

    return styles;
  }

  onImageLoad(event: Event) {
    this.imageLoaded = true;
    this.hasImageError = false;
    this.imageLoad.emit(event);
  }

  onImageError(event: Event) {
    this.hasImageError = true;
    this.imageLoad.emit(event);
    
    if (this.fallbackSrc && this.currentSrc !== this.fallbackSrc) {
      this.currentSrc = this.fallbackSrc;
    }
    
    this.imageError.emit(event);
  }

  onPictureClick(event: MouseEvent) {
    if (this.disabled || !this.clickable) return;
    this.pictureClick.emit(event);
  }

  ngOnInit() {
    this.currentSrc = this.src;
  }
}

import { Component, Input, Output, EventEmitter, OnInit, Inject, Optional, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'base-link',
  templateUrl: './link.component.html',
  styleUrls: ['./link.component.css'],
  standalone: true,
  imports: [CommonModule, RouterModule, IconComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class LinkComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Legacy inputs (preserved for backward compatibility)
  @Input() to?: string;
  @Input() href?: string;
  @Input() external?: boolean;
  @Input() replace?: boolean;
  @Input() custom?: boolean;
  @Input() target?: '_blank' | '_parent' | '_self' | '_top' | string | null | undefined;
  @Input() rel?: string | null;
  @Input() noRel?: boolean;
  @Input() prefetch?: boolean;
  @Input() noPrefetch?: boolean;
  @Input() activeClass?: string;
  @Input() exactActiveClass?: string;
  @Input() ariaCurrentValue?: string;
  @Input() item_id?: string;
  @Input() routerLink: string | any[] | null | undefined;
  @Input() classes: string = '';

  // Enhanced inputs
  @Input() text: string = 'Sample Navigation Link';
  @Input() disabled: boolean = false;
  @Input() underline: 'none' | 'hover' | 'always' = 'hover';
  @Input() icon?: string;
  @Input() iconPosition: 'left' | 'right' = 'left';
  @Input() loading: boolean = false;
  @Input() badge?: string | number;
  @Input() tooltip?: string;

  // Events
  @Output() linkClick = new EventEmitter<Event>();
  @Output() linkHover = new EventEmitter<Event>();
  @Output() linkFocus = new EventEmitter<Event>();
  
  constructor(@Optional() @Inject('componentProperties') public properties: any) {}
  
  ngOnInit(): void {
    if (this.properties) {
      // Map properties from injected properties
      this.className = this.properties.className || this.className;
      this.size = this.properties.size || this.size;
      this.variant = this.properties.variant || this.variant;
      this.rounded = this.properties.rounded || this.rounded;
      this.text = this.properties.text || this.text;
      this.disabled = this.properties.disabled || this.disabled;
      this.underline = this.properties.underline || this.underline;
      this.icon = this.properties.icon || this.icon;
      this.iconPosition = this.properties.iconPosition || this.iconPosition;
      this.loading = this.properties.loading || this.loading;
      this.badge = this.properties.badge || this.badge;
      this.tooltip = this.properties.tooltip || this.tooltip;
      this.to = this.properties.to || this.to;
      this.href = this.properties.href || this.href;
      this.routerLink = this.properties.routerLink || this.routerLink;
      this.target = this.properties.target || this.target;
      this.external = this.properties.external || this.external;
    }

    // Auto-detect external links
    if (this.href && !this.external) {
      this.external = this.isExternalLink(this.href);
    }

    // Set default routerLink if not provided
    if (!this.routerLink && this.to) {
      this.routerLink = this.to;
    }
  }

  get computedClasses(): string {
    const baseClasses = 'inline-flex items-center gap-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'text-gray-700 hover:text-gray-900 focus:ring-gray-500',
      primary: 'text-blue-600 hover:text-blue-800 focus:ring-blue-500',
      secondary: 'text-gray-600 hover:text-gray-800 focus:ring-gray-500',
      success: 'text-green-600 hover:text-green-800 focus:ring-green-500',
      warning: 'text-yellow-600 hover:text-yellow-800 focus:ring-yellow-500',
      danger: 'text-red-600 hover:text-red-800 focus:ring-red-500'
    };

    const underlineClasses = {
      none: 'no-underline',
      hover: 'no-underline hover:underline',
      always: 'underline'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const stateClasses = [];
    if (this.disabled) stateClasses.push('opacity-50 cursor-not-allowed pointer-events-none');
    if (this.loading) stateClasses.push('cursor-wait');

    // Legacy support
    const legacyClasses = this.classes || '';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      underlineClasses[this.underline],
      roundedClasses[this.rounded],
      ...stateClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get finalHref(): string | null {
    return this.href || (this.external ? (this.to || null) : null);
  }

  get finalTarget(): string | null {
    if (this.target !== undefined) return this.target;
    return this.external ? '_blank' : null;
  }

  get finalRel(): string | null {
    if (this.noRel) return null;
    if (this.rel) return this.rel;
    return this.external ? 'noopener noreferrer' : null;
  }

  get isExternal(): boolean {
    return !!this.external || (this.href ? this.isExternalLink(this.href) : false);
  }

  private isExternalLink(url: string): boolean {
    return url.startsWith('http://') || url.startsWith('https://') || url.startsWith('//');
  }

  onClick(event: Event): void {
    if (this.disabled || this.loading) {
      event.preventDefault();
      return;
    }
    this.linkClick.emit(event);
  }

  onHover(event: Event): void {
    if (!this.disabled) {
      this.linkHover.emit(event);
    }
  }

  onFocus(event: Event): void {
    if (!this.disabled) {
      this.linkFocus.emit(event);
    }
  }
}

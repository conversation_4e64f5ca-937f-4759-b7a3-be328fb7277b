<!-- Enhanced Link component with modern features -->
<ng-container *ngIf="!isExternal && routerLink; else externalLink">
  <!-- Internal router link -->
  <a
    [routerLink]="routerLink"
    [ngClass]="computedClasses"
    [class.nui-link]="true"
    [attr.aria-label]="tooltip || text"
    [attr.title]="tooltip"
    [attr.aria-disabled]="disabled"
    [replaceUrl]="replace"
    [queryParamsHandling]="'preserve'"
    (click)="onClick($event)"
    (mouseenter)="onHover($event)"
    (focus)="onFocus($event)"
  >
    <!-- Loading spinner -->
    <svg *ngIf="loading" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>

    <!-- Left icon -->
    <base-icon *ngIf="icon && iconPosition === 'left' && !loading" [icon]="icon" class="h-4 w-4"></base-icon>

    <!-- Link text content -->
    <span class="flex items-center gap-1">
      <ng-content>{{ text }}</ng-content>
      
      <!-- Badge -->
      <span *ngIf="badge" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        {{ badge }}
      </span>
    </span>

    <!-- Right icon -->
    <base-icon *ngIf="icon && iconPosition === 'right' && !loading" [icon]="icon" class="h-4 w-4"></base-icon>

    <!-- External link indicator -->
    <svg *ngIf="isExternal" class="h-3 w-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
    </svg>
  </a>
</ng-container>

<ng-template #externalLink>
  <!-- External link -->
  <a
    [href]="finalHref"
    [target]="finalTarget"
    [rel]="finalRel"
    [ngClass]="computedClasses"
    [class.nui-link]="true"
    [attr.aria-label]="tooltip || text"
    [attr.title]="tooltip"
    [attr.aria-disabled]="disabled"
    (click)="onClick($event)"
    (mouseenter)="onHover($event)"
    (focus)="onFocus($event)"
  >
    <!-- Loading spinner -->
    <svg *ngIf="loading" class="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>

    <!-- Left icon -->
    <base-icon *ngIf="icon && iconPosition === 'left' && !loading" [icon]="icon" class="h-4 w-4"></base-icon>

    <!-- Link text content -->
    <span class="flex items-center gap-1">
      <ng-content>{{ text }}</ng-content>
      
      <!-- Badge -->
      <span *ngIf="badge" class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
        {{ badge }}
      </span>
    </span>

    <!-- Right icon -->
    <base-icon *ngIf="icon && iconPosition === 'right' && !loading" [icon]="icon" class="h-4 w-4"></base-icon>

    <!-- External link indicator -->
    <svg class="h-3 w-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
    </svg>
  </a>
</ng-template>

/* Enhanced Link Component Styles */
:host {
  display: inline-block;
}

/* Link hover effects */
a {
  text-decoration-thickness: 2px;
  text-underline-offset: 2px;
  transition: all 0.2s ease-in-out;
}

/* Smooth hover transitions */
a:hover {
  transform: translateY(-1px);
}

/* Focus styles for accessibility */
a:focus-visible {
  outline: 2px solid currentColor;
  outline-offset: 2px;
  border-radius: 4px;
}

/* External link indicator animation */
a:hover svg {
  transform: translate(1px, -1px);
  transition: transform 0.2s ease-in-out;
}

/* Badge styling enhancements */
.badge {
  transition: all 0.2s ease-in-out;
}

a:hover .badge {
  transform: scale(1.05);
}

/* Loading state */
a.cursor-wait {
  pointer-events: none;
}

/* Disabled state */
a[aria-disabled="true"] {
  pointer-events: none;
  opacity: 0.6;
}

/* Icon transitions */
.icon {
  transition: transform 0.2s ease-in-out;
}

a:hover .icon {
  transform: scale(1.1);
}
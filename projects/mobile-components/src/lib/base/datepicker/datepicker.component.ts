import { Component, Input, Output, EventEmitter, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

@Component({
  selector: 'base-datepicker',
  templateUrl: './datepicker.component.html',
  styleUrls: ['./datepicker.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class DatepickerComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'primary';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'lg';

  // Component-specific inputs
  @Input() label: string = 'Select Date';
  @Input() placeholder: string = 'Choose a date';
  @Input() selectedDate: string = '';
  @Input() dateFormat: string = 'DD-MM-YYYY';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() minDate: string = '';
  @Input() maxDate: string = '';
  @Input() expanded: boolean = true;
  @Input() showInput: boolean = false;
  @Input() inline: boolean = true;

  // Legacy inputs (preserved for backward compatibility)
  @Input() item_id: string = Math.random().toString(36).substring(7);

  // Events
  @Output() dateChange = new EventEmitter<string>();
  @Output() dateSelect = new EventEmitter<Date>();

  MONTH_NAMES = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  MONTH_SHORT_NAMES = [
    'Jan',
    'Feb',
    'Mar',
    'Apr',
    'May',
    'Jun',
    'Jul',
    'Aug',
    'Sep',
    'Oct',
    'Nov',
    'Dec',
  ];

  DAYS = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  showDatepicker: boolean = false;
  datepickerValue: string = '';
  internalSelectedDate: string = '';
  month!: number;
  year!: number;
  numberOfDays: number[] = [];
  blankDays: number[] = [];

  get computedClasses(): string {
    const baseClasses = 'relative mx-auto w-full pb-5 font-sans transition-all duration-200';
    
    const sizeClasses = {
      xs: 'max-w-[200px] text-xs',
      sm: 'max-w-[240px] text-sm', 
      md: this.expanded ? 'max-w-[310px]' : 'max-w-[240px]',
      lg: this.expanded ? 'max-w-[350px]' : 'max-w-[280px]',
      xl: this.expanded ? 'max-w-[400px]' : 'max-w-[320px]'
    };

    const variantClasses = {
      default: '',
      primary: '',
      secondary: '',
      success: '',
      warning: '',
      danger: ''
    };

    const roundedClasses = {
      none: '',
      sm: '',
      md: '', 
      lg: '',
      full: ''
    };

    const disabledClasses = this.disabled ? 'opacity-50 pointer-events-none' : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      disabledClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedInputClasses(): string {
    const baseInputClasses = 'w-full rounded-lg py-3 pe-10 ps-4 font-medium leading-none shadow-sm focus:outline-none focus:ring transition-all duration-200';
    
    const sizeInputClasses = {
      xs: 'py-1 px-2 text-xs',
      sm: 'py-2 px-3 text-sm',
      md: 'py-3 px-4 text-base',
      lg: 'py-4 px-5 text-lg',
      xl: 'py-5 px-6 text-xl'
    };

    const variantInputClasses = {
      default: 'text-muted-600 focus:ring-gray-600/50',
      primary: 'text-muted-600 focus:ring-blue-600/50',
      secondary: 'text-muted-600 focus:ring-gray-600/50',
      success: 'text-muted-600 focus:ring-green-600/50',
      warning: 'text-muted-600 focus:ring-yellow-600/50',
      danger: 'text-muted-600 focus:ring-red-600/50'
    };

    const roundedInputClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const visibilityClasses = this.showInput ? 'block' : 'hidden';

    return [
      baseInputClasses,
      sizeInputClasses[this.size],
      variantInputClasses[this.variant],
      roundedInputClasses[this.rounded],
      visibilityClasses
    ].filter(Boolean).join(' ');
  }

  ngOnInit() {
    // Use selectedDate input or default to today
    this.internalSelectedDate = this.selectedDate || new Date().toISOString().split('T')[0];
    this.initDate();
    this.getNoOfDays();
  }

  initDate() {
    let today;
    if (this.internalSelectedDate) {
      today = new Date(Date.parse(this.internalSelectedDate));
    } else {
      today = new Date();
    }
    this.month = today.getMonth();
    this.year = today.getFullYear();
    this.datepickerValue = this.formatDateForDisplay(today);
  }

  formatDateForDisplay(date: Date): string {
    let formattedDay = this.DAYS[date.getDay()];
    let formattedDate = ('0' + date.getDate()).slice(-2);
    let formattedMonth = this.MONTH_NAMES[date.getMonth()];
    let formattedMonthShortName = this.MONTH_SHORT_NAMES[date.getMonth()];
    let formattedMonthInNumber = ('0' + (date.getMonth() + 1)).slice(-2);
    let formattedYear = date.getFullYear();

    if (this.dateFormat === 'DD-MM-YYYY') {
      return `${formattedDate}-${formattedMonthInNumber}-${formattedYear}`;
    }
    if (this.dateFormat === 'YYYY-MM-DD') {
      return `${formattedYear}-${formattedMonthInNumber}-${formattedDate}`;
    }
    if (this.dateFormat === 'D d M, Y') {
      return `${formattedDay} ${formattedDate} ${formattedMonthShortName} ${formattedYear}`;
    }
    return `${formattedDay} ${formattedDate} ${formattedMonth} ${formattedYear}`;
  }

  isSelectedDate(date: number): boolean {
    const d = new Date(this.year, this.month, date);
    return this.datepickerValue === this.formatDateForDisplay(d);
  }

  isToday(date: number): boolean {
    const today = new Date();
    const d = new Date(this.year, this.month, date);
    return today.toDateString() === d.toDateString();
  }

  getDateValue(date: number) {
    if (this.disabled) return;
    
    let selectedDate = new Date(this.year, this.month, date);
    this.datepickerValue = this.formatDateForDisplay(selectedDate);
    this.internalSelectedDate = selectedDate.toISOString().split('T')[0];
    this.isSelectedDate(date);
    
    // Emit events
    this.dateChange.emit(this.datepickerValue);
    this.dateSelect.emit(selectedDate);
  }

  getNoOfDays() {
    let daysInMonth = new Date(this.year, this.month + 1, 0).getDate();
    let dayOfWeek = new Date(this.year, this.month).getDay();
    let blankdaysArray = [];
    for (let i = 1; i <= dayOfWeek; i++) {
      blankdaysArray.push(i);
    }
    let daysArray = [];
    for (let i = 1; i <= daysInMonth; i++) {
      daysArray.push(i);
    }
    this.blankDays = blankdaysArray;
    this.numberOfDays = daysArray;
  }

  incrementDays() {
    if (this.disabled) return;
    
    if (this.month == 0) {
      this.year--;
      this.month = 12;
    } else {
      this.month--;
    }
    this.getNoOfDays();
  }

  decrementDays() {
    if (this.disabled) return;
    
    if (this.month == 11) {
      this.month = 0;
      this.year++;
    } else {
      this.month++;
    }
    this.getNoOfDays();
  }

  toggleDatepicker() {
    if (!this.disabled) {
      this.showDatepicker = !this.showDatepicker;
    }
  }
}

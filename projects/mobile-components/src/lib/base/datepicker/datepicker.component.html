<div [ngClass]="computedClasses">
  <input type="hidden" name="date" [value]="datepickerValue" />
  <input
    [(ngModel)]="datepickerValue"
    type="text"
    [ngClass]="computedInputClasses"
    [placeholder]="placeholder"
    [disabled]="disabled"
    [required]="required"
    [attr.aria-label]="label"
    readonly
    (click)="toggleDatepicker()"
  />

  <div class="w-full">
    <div class="mb-6 flex items-center justify-between">
      <div
        class="
          text-muted-800
          dark:text-muted-100
          space-x-2
          text-base
          font-medium
        "
      >
        <span>{{ MONTH_NAMES[month] }}</span>
        <span>{{ year }}</span>
      </div>
      <div>
        <button
          type="button"
          class="
            focus:shadow-outline
            hover:bg-muted-100
            inline-flex
            cursor-pointer
            rounded-full
            p-1
            transition
            duration-100
            ease-in-out
            focus:outline-none
          "
          (click)="incrementDays()"
        >
          <svg
            class="text-muted-400 inline-flex size-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>
        <button
          type="button"
          class="
            focus:shadow-outline
            hover:bg-muted-100
            inline-flex
            cursor-pointer
            rounded-full
            p-1
            transition
            duration-100
            ease-in-out
            focus:outline-none
          "
          (click)="decrementDays()"
        >
          <svg
            class="text-muted-400 inline-flex size-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>
      </div>
    </div>

    <div class="-mx-1 mb-3 flex flex-wrap">
      <ng-container *ngFor="let day of DAYS; let index = index">
        <div style="width: 14.26%" class="px-0.5">
          <div
            class="
              text-muted-400
              dark:text-muted-300
              text-center text-xs
              font-medium
            "
          >
            {{ day }}
          </div>
        </div>
      </ng-container>
    </div>

    <div class="-mx-1 flex flex-wrap">
      <ng-container *ngFor="let blankday of blankDays">
        <div
          style="width: 14.28%"
          class="border border-transparent p-1 text-center text-sm"
        ></div>
      </ng-container>
      <ng-container *ngFor="let date of numberOfDays; let dateIndex = index">
        <div style="width: 14.28%" class="flex items-center justify-center">
          <div
            class="
              mx-auto
              flex
              size-8
              cursor-pointer
              items-center
              justify-center
              rounded-full
              text-center text-sm
              leading-none
              transition
              duration-100
              ease-in-out
            "
            [ngClass]="{
              'bg-primary-100 dark:bg-primary-500/20 text-primary-500':
                isToday(date),
              'text-muted-500 dark:text-muted-400 hover:text-primary-500 hover:bg-primary-100 dark:hover:bg-primary-500/20':
                !isToday(date) && !isSelectedDate(date),
              'bg-primary-500 hover:bg-primary-500/75 text-white':
                isSelectedDate(date)
            }"
            (click)="getDateValue(date)"
          >
            {{ date }}
          </div>
        </div>
      </ng-container>
    </div>
  </div>
</div>

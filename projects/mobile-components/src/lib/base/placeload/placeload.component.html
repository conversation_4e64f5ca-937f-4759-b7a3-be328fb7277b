<div *ngIf="visible" [ngStyle]="inlineStyles">
  <!-- Line Type -->
  <div 
    *ngIf="type === 'line'"
    [ngClass]="computedClasses"
    [ngStyle]="inlineStyles"
  ></div>

  <!-- Text Type (Multiple Lines) -->
  <div 
    *ngIf="type === 'text'"
    [ngClass]="computedContainerClasses"
  >
    <div 
      *ngFor="let line of lineArray; let i = index"
      [ngClass]="computedClasses"
      [style.width]="getLineWidth(i)"
    ></div>
  </div>

  <!-- Avatar Type -->
  <div 
    *ngIf="type === 'avatar'"
    [ngClass]="[computedClasses, 'aspect-square']"
    [ngStyle]="inlineStyles"
  ></div>

  <!-- Image Type -->
  <div 
    *ngIf="type === 'image'"
    [ngClass]="[computedClasses, 'w-full']"
    [ngStyle]="inlineStyles"
  ></div>

  <!-- Button Type -->
  <div 
    *ngIf="type === 'button'"
    [ngClass]="[computedClasses, 'inline-block']"
    [ngStyle]="inlineStyles"
  ></div>

  <!-- Card Type (Complex Layout) -->
  <div 
    *ngIf="type === 'card'"
    [ngClass]="computedClasses"
    [ngStyle]="inlineStyles"
  >
    <div class="flex items-start space-x-4">
      <!-- Avatar -->
      <div [ngClass]="getElementClasses('avatar')" style="width: 3rem; height: 3rem;"></div>
      
      <!-- Content -->
      <div class="flex-1 space-y-2">
        <!-- Title -->
        <div [ngClass]="getElementClasses('title')" style="width: 60%; height: 1.25rem;"></div>
        
        <!-- Subtitle -->
        <div [ngClass]="getElementClasses('subtitle')" style="width: 40%; height: 1rem;"></div>
      </div>
    </div>
    
    <!-- Main Content -->
    <div [ngClass]="getElementClasses('content')" style="width: 100%; height: 4rem; margin-top: 1rem;"></div>
    
    <!-- Action Button -->
    <div class="flex justify-end mt-4">
      <div [ngClass]="getElementClasses('button')" style="width: 5rem; height: 2.5rem;"></div>
    </div>
  </div>

  <!-- Custom Type (Content Projection) -->
  <div 
    *ngIf="type === 'custom'"
    [ngClass]="computedClasses"
    [ngStyle]="inlineStyles"
  >
    <ng-content></ng-content>
  </div>

  <!-- Legacy Template (Hidden, for backward compatibility) -->
  <div class="hidden legacy-placeload-wrapper">
    <div class="nui-placeload animate-nui-placeload"></div>
  </div>
</div>

import { Component, Input, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-placeload',
  templateUrl: './placeload.component.html',
  styleUrls: ['./placeload.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class BasePlaceloadComponent {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() type: 'line' | 'text' | 'card' | 'avatar' | 'image' | 'button' | 'custom' = 'line';
  @Input() width?: string | number;
  @Input() height?: string | number;
  @Input() lines: number = 3;
  @Input() spacing: 'none' | 'sm' | 'md' | 'lg' = 'md';
  @Input() animated: boolean = true;
  @Input() pulsing: boolean = false;
  @Input() shimmer: boolean = true;
  @Input() visible: boolean = true;
  @Input() duration: number = 1500;

  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'overflow-hidden';
    
    const sizeClasses = {
      xs: 'h-3',
      sm: 'h-4',
      md: 'h-5',
      lg: 'h-6',
      xl: 'h-8'
    };

    const variantClasses = {
      default: 'bg-gray-200 dark:bg-gray-700',
      primary: 'bg-blue-100 dark:bg-blue-900',
      secondary: 'bg-gray-100 dark:bg-gray-800',
      success: 'bg-green-100 dark:bg-green-900',
      warning: 'bg-yellow-100 dark:bg-yellow-900',
      danger: 'bg-red-100 dark:bg-red-900'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const animationClasses = [
      this.animated && this.shimmer ? 'animate-pulse' : '',
      this.pulsing ? 'animate-pulse' : '',
      !this.visible ? 'opacity-0' : 'opacity-100'
    ];

    // Type-specific base classes
    const typeBaseClasses = this.getTypeBaseClasses();

    return [
      baseClasses,
      typeBaseClasses,
      this.type === 'line' || this.type === 'text' ? sizeClasses[this.size] : '',
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      ...animationClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedContainerClasses(): string {
    const baseContainerClasses = 'w-full';
    
    const spacingClasses = {
      none: 'space-y-0',
      sm: 'space-y-1',
      md: 'space-y-2',
      lg: 'space-y-3'
    };

    return [
      baseContainerClasses,
      spacingClasses[this.spacing]
    ].filter(Boolean).join(' ');
  }

  get inlineStyles(): { [key: string]: string } {
    const styles: { [key: string]: string } = {};
    
    if (this.width !== undefined) {
      styles['width'] = typeof this.width === 'number' ? `${this.width}px` : this.width;
    }
    
    if (this.height !== undefined) {
      styles['height'] = typeof this.height === 'number' ? `${this.height}px` : this.height;
    }

    if (this.animated && this.duration) {
      styles['animation-duration'] = `${this.duration}ms`;
    }

    return styles;
  }

  get lineArray(): number[] {
    return Array.from({ length: this.lines }, (_, i) => i);
  }

  get cardElements(): any[] {
    return [
      { type: 'avatar', width: '3rem', height: '3rem' },
      { type: 'title', width: '60%', height: '1.25rem' },
      { type: 'subtitle', width: '40%', height: '1rem' },
      { type: 'content', width: '100%', height: '4rem' },
      { type: 'button', width: '5rem', height: '2.5rem' }
    ];
  }

  private getTypeBaseClasses(): string {
    const typeClasses: Record<string, string> = {
      line: 'w-full',
      text: 'w-full',
      card: 'p-4 border border-gray-200 dark:border-gray-700',
      avatar: 'rounded-full',
      image: 'aspect-video',
      button: 'px-4 py-2',
      custom: ''
    };

    return typeClasses[this.type] || '';
  }

  getLineWidth(index: number): string {
    // Vary line widths for more realistic skeleton
    const widths = ['100%', '95%', '85%', '90%', '80%'];
    return widths[index % widths.length];
  }

  getElementClasses(elementType: string): string {
    const baseClasses = 'bg-gray-200 dark:bg-gray-700';
    
    const elementTypeClasses: Record<string, string> = {
      avatar: 'rounded-full flex-shrink-0',
      title: 'rounded h-5',
      subtitle: 'rounded h-4',
      content: 'rounded',
      button: 'rounded px-4 py-2'
    };

    const animationClasses = this.animated ? 'animate-pulse' : '';

    return [
      baseClasses,
      elementTypeClasses[elementType] || 'rounded',
      animationClasses
    ].filter(Boolean).join(' ');
  }
}

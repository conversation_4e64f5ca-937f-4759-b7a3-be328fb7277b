/* Object Display Component Styles */

/* Host element */
:host {
  display: block;
}

/* Table enhancements */
.table-container {
  overflow-x: auto;
  border-radius: 0.5rem;
}

/* Row hover effects */
tbody tr:hover {
  background-color: rgb(249 250 251);
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Property value styling */
.property-value {
  word-break: break-all;
  max-width: 300px;
}

.property-value.string {
  color: rgb(34 197 94); /* green-500 */
}

.property-value.number {
  color: rgb(59 130 246); /* blue-500 */
}

.property-value.boolean {
  color: rgb(168 85 247); /* purple-500 */
}

.property-value.date {
  color: rgb(245 158 11); /* amber-500 */
}

.property-value.null,
.property-value.undefined {
  color: rgb(156 163 175); /* gray-400 */
  font-style: italic;
}

/* Expandable row animations */
.expandable-row {
  transition: all 0.2s ease-in-out;
}

.expandable-row.expanded {
  background-color: rgb(***********); /* gray-100 */
}

/* Search highlight */
.search-highlight {
  background-color: rgb(***********); /* yellow-200 */
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

/* Copy button animations */
.copy-button {
  transition: all 0.2s ease-in-out;
  opacity: 0;
}

tr:hover .copy-button,
.property-item:hover .copy-button {
  opacity: 1;
}

.copy-button:hover {
  transform: scale(1.1);
}

/* JSON syntax highlighting */
.json-container {
  position: relative;
}

.json-container pre {
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: #00ff41;
  border: 1px solid #333;
}

/* Tree view indentation */
.tree-level-0 { margin-left: 0; }
.tree-level-1 { margin-left: 1rem; }
.tree-level-2 { margin-left: 2rem; }
.tree-level-3 { margin-left: 3rem; }
.tree-level-4 { margin-left: 4rem; }

/* Loading animation */
.loading-container {
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Sort indicators */
.sort-indicator {
  transition: transform 0.2s ease-in-out;
}

.sort-indicator.asc {
  transform: rotate(0deg);
}

.sort-indicator.desc {
  transform: rotate(180deg);
}

/* Property type badges */
.type-badge {
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Expansion button animations */
.expand-button {
  transition: transform 0.2s ease-in-out;
}

.expand-button.expanded {
  transform: rotate(90deg);
}

/* Card display mode */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1rem;
}

.property-card {
  border: 1px solid rgb(229 231 235);
  border-radius: 0.5rem;
  padding: 1rem;
  transition: all 0.2s ease-in-out;
}

.property-card:hover {
  border-color: rgb(59 130 246);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

/* Empty state styling */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: rgb(107 114 128);
}

.empty-state svg {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
}

/* Responsive design */
@media (max-width: 640px) {
  .table-container {
    font-size: 0.875rem;
  }
  
  .property-value {
    max-width: 200px;
  }
  
  .card-grid {
    grid-template-columns: 1fr;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  tbody tr:hover {
    background-color: rgb(31 41 55);
  }
  
  .property-card {
    background-color: rgb(31 41 55);
    border-color: rgb(75 85 99);
  }
  
  .property-card:hover {
    border-color: rgb(59 130 246);
    background-color: rgb(55 65 81);
  }
  
  .expandable-row.expanded {
    background-color: rgb(55 65 81);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .property-value {
    font-weight: 600;
  }
  
  .type-badge {
    border: 1px solid currentColor;
  }
  
  tbody tr:hover {
    outline: 2px solid currentColor;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .copy-button,
  .expand-button,
  .search-container {
    display: none !important;
  }
  
  .property-card,
  tbody tr {
    break-inside: avoid;
  }
  
  .json-container pre {
    background: white !important;
    color: black !important;
    border: 1px solid black;
  }
}
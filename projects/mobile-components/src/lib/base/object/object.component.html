<!-- Object Display Component Template -->
<div [class]="computedClasses">
  <!-- Header -->
  <div [class]="headerClasses">
    <div class="flex items-center justify-between">
      <h3 class="text-lg font-semibold">{{ title }}</h3>
      
      <div class="flex items-center gap-2">
        <!-- Expand/Collapse All -->
        <div *ngIf="expandable" class="flex gap-1">
          <button
            type="button"
            class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
            (click)="expandAll()"
          >
            Expand All
          </button>
          <button
            type="button"
            class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
            (click)="collapseAll()"
          >
            Collapse All
          </button>
        </div>
        
        <!-- Property Count -->
        <span class="text-sm text-gray-500">
          {{ filteredProperties.length }} {{ filteredProperties.length === 1 ? 'property' : 'properties' }}
        </span>
      </div>
    </div>
    
    <!-- Search Bar -->
    <div *ngIf="searchable" class="mt-3">
      <div class="relative">
        <base-icon 
          name="magnifying-glass"
          size="sm"
          class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
        ></base-icon>
        <input
          type="text"
          placeholder="Search properties..."
          class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          (input)="onSearch($event)"
        />
      </div>
    </div>
  </div>

  <!-- Content -->
  <div [class]="contentClasses">
    <!-- Loading State -->
    <div *ngIf="loading" class="flex items-center justify-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      <span class="ml-3 text-gray-600">Loading...</span>
    </div>

    <!-- Empty State -->
    <div *ngIf="!loading && filteredProperties.length === 0" class="text-center py-8">
      <base-icon 
        name="cube-transparent"
        size="xl"
        class="text-gray-400 mb-4"
      ></base-icon>
      <p class="text-gray-600">{{ emptyMessage }}</p>
    </div>

    <!-- Table Display Mode -->
    <div *ngIf="!loading && filteredProperties.length > 0 && displayMode === 'table'">
      <table [class]="tableClasses">
        <thead class="bg-gray-50">
          <tr>
            <th 
              *ngIf="sortable"
              class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
              (click)="onSort('key')"
            >
              Key
              <base-icon 
                *ngIf="sortBy === 'key'"
                [name]="sortDirection === 'asc' ? 'chevron-up' : 'chevron-down'"
                size="sm"
                class="inline ml-1"
              ></base-icon>
            </th>
            <th 
              *ngIf="!sortable"
              class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Key
            </th>
            
            <th 
              *ngIf="showTypes && sortable"
              class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
              (click)="onSort('type')"
            >
              Type
              <base-icon 
                *ngIf="sortBy === 'type'"
                [name]="sortDirection === 'asc' ? 'chevron-up' : 'chevron-down'"
                size="sm"
                class="inline ml-1"
              ></base-icon>
            </th>
            <th 
              *ngIf="showTypes && !sortable"
              class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Type
            </th>
            
            <th 
              *ngIf="sortable"
              class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
              (click)="onSort('value')"
            >
              Value
              <base-icon 
                *ngIf="sortBy === 'value'"
                [name]="sortDirection === 'asc' ? 'chevron-up' : 'chevron-down'"
                size="sm"
                class="inline ml-1"
              ></base-icon>
            </th>
            <th 
              *ngIf="!sortable"
              class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              Value
            </th>
            
            <th *ngIf="showPaths" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Path
            </th>
            
            <th *ngIf="copyable || expandable" class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
              Actions
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr 
            *ngFor="let property of filteredProperties; trackBy: trackByProperty"
            class="hover:bg-gray-50 cursor-pointer transition-colors"
            (click)="onPropertyClick(property)"
          >
            <td class="px-4 py-3 whitespace-nowrap">
              <div class="flex items-center" [style.padding-left]="getIndentation(property.level)">
                <button
                  *ngIf="property.expandable"
                  type="button"
                  class="mr-2 p-1 rounded hover:bg-gray-200"
                  (click)="toggleExpansion(property); $event.stopPropagation()"
                >
                  <base-icon 
                    [name]="property.expanded ? 'chevron-down' : 'chevron-right'"
                    size="sm"
                  ></base-icon>
                </button>
                <span class="font-mono text-sm">{{ property.key }}</span>
              </div>
            </td>
            
            <td *ngIf="showTypes" class="px-4 py-3 whitespace-nowrap">
              <div class="flex items-center gap-2">
                <base-icon 
                  [name]="getTypeIcon(property.type)"
                  size="sm"
                  [class]="getTypeColor(property.type)"
                ></base-icon>
                <span class="text-xs font-medium" [class]="getTypeColor(property.type)">
                  {{ property.type }}
                </span>
              </div>
            </td>
            
            <td class="px-4 py-3">
              <div class="font-mono text-sm break-all" [class]="getTypeColor(property.type)">
                {{ formatValue(property) }}
              </div>
            </td>
            
            <td *ngIf="showPaths" class="px-4 py-3 whitespace-nowrap text-xs text-gray-500 font-mono">
              {{ property.path }}
            </td>
            
            <td *ngIf="copyable || expandable" class="px-4 py-3 whitespace-nowrap text-right">
              <div class="flex justify-end gap-2">
                <button
                  *ngIf="copyable"
                  type="button"
                  class="p-1 text-gray-400 hover:text-gray-600 rounded"
                  (click)="onCopy(property); $event.stopPropagation()"
                  title="Copy property"
                >
                  <base-icon name="clipboard" size="sm"></base-icon>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- List Display Mode -->
    <div *ngIf="!loading && filteredProperties.length > 0 && displayMode === 'list'" class="space-y-2">
      <div 
        *ngFor="let property of filteredProperties; trackBy: trackByProperty"
        class="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
        (click)="onPropertyClick(property)"
        [style.margin-left]="getIndentation(property.level)"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <button
              *ngIf="property.expandable"
              type="button"
              class="p-1 rounded hover:bg-gray-200"
              (click)="toggleExpansion(property); $event.stopPropagation()"
            >
              <base-icon 
                [name]="property.expanded ? 'chevron-down' : 'chevron-right'"
                size="sm"
              ></base-icon>
            </button>
            
            <div>
              <div class="flex items-center gap-2">
                <span class="font-mono font-medium">{{ property.key }}</span>
                <span *ngIf="showTypes" class="text-xs px-2 py-1 rounded-full bg-gray-100" [class]="getTypeColor(property.type)">
                  {{ property.type }}
                </span>
              </div>
              <div class="text-sm text-gray-600 font-mono mt-1" [class]="getTypeColor(property.type)">
                {{ formatValue(property) }}
              </div>
              <div *ngIf="showPaths" class="text-xs text-gray-400 font-mono mt-1">
                {{ property.path }}
              </div>
            </div>
          </div>
          
          <button
            *ngIf="copyable"
            type="button"
            class="p-2 text-gray-400 hover:text-gray-600 rounded"
            (click)="onCopy(property); $event.stopPropagation()"
            title="Copy property"
          >
            <base-icon name="clipboard" size="sm"></base-icon>
          </button>
        </div>
      </div>
    </div>

    <!-- JSON Display Mode -->
    <div *ngIf="!loading && filteredProperties.length > 0 && displayMode === 'json'" class="relative">
      <pre class="bg-gray-900 text-green-400 p-4 rounded-lg overflow-auto font-mono text-sm">{{ data | json }}</pre>
      <button
        *ngIf="copyable"
        type="button"
        class="absolute top-2 right-2 p-2 bg-gray-800 text-gray-300 hover:text-white rounded"
        (click)="onCopy({key: 'data', value: data, type: 'object', path: 'data', level: 0, expandable: false})"
        title="Copy JSON"
      >
        <base-icon name="clipboard" size="sm"></base-icon>
      </button>
    </div>
  </div>
</div>

import { Component, Input, Output, EventEmitter, OnInit, Inject, Optional, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

export type ObjectDisplayMode = 'table' | 'list' | 'cards' | 'json' | 'tree';
export type ObjectPropertyType = 'string' | 'number' | 'boolean' | 'date' | 'object' | 'array' | 'null' | 'undefined';

export interface ObjectProperty {
  key: string;
  value: any;
  type: ObjectPropertyType;
  path: string;
  level: number;
  expandable: boolean;
  expanded?: boolean;
}

@Component({
  selector: 'base-object',
  templateUrl: './object.component.html',
  styleUrls: ['./object.component.css'],
  standalone: true,
  imports: [CommonModule, IconComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ObjectComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Object-specific inputs
  @Input() data: any = {
    name: 'Sample Object',
    id: 12345,
    active: true,
    created: new Date('2024-01-15'),
    tags: ['frontend', 'angular', 'typescript'],
    metadata: {
      author: 'John Doe',
      version: '1.0.0',
      settings: {
        theme: 'dark',
        notifications: true
      }
    }
  };
  @Input() title: string = 'Object Properties';
  @Input() displayMode: ObjectDisplayMode = 'table';
  @Input() searchable: boolean = false;
  @Input() sortable: boolean = false;
  @Input() expandable: boolean = true;
  @Input() expandAllInitially: boolean = false;
  @Input() maxDepth: number = 10;
  @Input() showTypes: boolean = true;
  @Input() showPaths: boolean = false;
  @Input() emptyMessage: string = 'No data to display';
  @Input() loading: boolean = false;
  @Input() copyable: boolean = false;
  @Input() editable: boolean = false;
  @Input() highlightSearch: boolean = true;

  // Events
  @Output() propertyClick = new EventEmitter<ObjectProperty>();
  @Output() propertyEdit = new EventEmitter<{property: ObjectProperty, newValue: any}>();
  @Output() copy = new EventEmitter<{key: string, value: any}>();
  @Output() expand = new EventEmitter<ObjectProperty>();
  @Output() collapse = new EventEmitter<ObjectProperty>();

  // Internal state
  properties: ObjectProperty[] = [];
  filteredProperties: ObjectProperty[] = [];
  searchTerm: string = '';
  sortDirection: 'asc' | 'desc' = 'asc';
  sortBy: 'key' | 'type' | 'value' = 'key';
  expandedPaths: Set<string> = new Set();

  constructor(@Optional() @Inject('componentProperties') public properties_inject: any) {}

  ngOnInit(): void {
    if (this.properties_inject) {
      // Map properties from injected properties
      this.className = this.properties_inject.className || this.className;
      this.size = this.properties_inject.size || this.size;
      this.variant = this.properties_inject.variant || this.variant;
      this.rounded = this.properties_inject.rounded || this.rounded;
      this.data = this.properties_inject.data || this.data;
      this.title = this.properties_inject.title || this.title;
      this.displayMode = this.properties_inject.displayMode || this.displayMode;
      this.searchable = this.properties_inject.searchable ?? this.searchable;
      this.sortable = this.properties_inject.sortable ?? this.sortable;
      this.expandable = this.properties_inject.expandable ?? this.expandable;
      this.expandAllInitially = this.properties_inject.expandAllInitially ?? this.expandAllInitially;
      this.maxDepth = this.properties_inject.maxDepth || this.maxDepth;
      this.showTypes = this.properties_inject.showTypes ?? this.showTypes;
      this.showPaths = this.properties_inject.showPaths ?? this.showPaths;
      this.copyable = this.properties_inject.copyable ?? this.copyable;
      this.editable = this.properties_inject.editable ?? this.editable;
    }

    this.parseData();
  }

  get computedClasses(): string {
    const baseClasses = 'border transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'border-gray-200 bg-white',
      primary: 'border-blue-200 bg-blue-50',
      secondary: 'border-purple-200 bg-purple-50',
      success: 'border-green-200 bg-green-50',
      warning: 'border-yellow-200 bg-yellow-50',
      danger: 'border-red-200 bg-red-50'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      this.className
    ].filter(Boolean).join(' ');
  }

  get headerClasses(): string {
    return 'px-4 py-3 border-b border-gray-200 bg-gray-50 font-semibold text-gray-900';
  }

  get contentClasses(): string {
    return 'p-4';
  }

  get tableClasses(): string {
    return 'w-full divide-y divide-gray-200';
  }

  parseData(): void {
    this.properties = [];
    this.expandedPaths.clear();
    
    if (this.data === null || this.data === undefined) {
      return;
    }

    this.properties = this.flattenObject(this.data, '', 0);
    
    if (this.expandAllInitially) {
      this.properties.forEach(prop => {
        if (prop.expandable) {
          this.expandedPaths.add(prop.path);
          prop.expanded = true;
        }
      });
    }

    this.filterAndSort();
  }

  flattenObject(obj: any, prefix: string = '', level: number = 0): ObjectProperty[] {
    const result: ObjectProperty[] = [];
    
    if (level > this.maxDepth) {
      return result;
    }

    if (obj === null || obj === undefined) {
      return result;
    }

    for (const [key, value] of Object.entries(obj)) {
      const path = prefix ? `${prefix}.${key}` : key;
      const type = this.getValueType(value);
      const expandable = type === 'object' || type === 'array';
      
      const property: ObjectProperty = {
        key,
        value,
        type,
        path,
        level,
        expandable,
        expanded: this.expandedPaths.has(path)
      };
      
      result.push(property);
      
      // Add nested properties if expanded
      if (expandable && property.expanded && level < this.maxDepth) {
        if (Array.isArray(value)) {
          value.forEach((item, index) => {
            const nestedProps = this.flattenObject({ [index]: item }, path, level + 1);
            result.push(...nestedProps);
          });
        } else if (typeof value === 'object' && value !== null) {
          const nestedProps = this.flattenObject(value, path, level + 1);
          result.push(...nestedProps);
        }
      }
    }

    return result;
  }

  getValueType(value: any): ObjectPropertyType {
    if (value === null) return 'null';
    if (value === undefined) return 'undefined';
    if (Array.isArray(value)) return 'array';
    if (value instanceof Date) return 'date';
    
    const type = typeof value;
    return type as ObjectPropertyType;
  }

  formatValue(property: ObjectProperty): string {
    const { value, type } = property;
    
    switch (type) {
      case 'string':
        return `"${value}"`;
      case 'number':
        return value.toString();
      case 'boolean':
        return value ? 'true' : 'false';
      case 'date':
        return value.toISOString();
      case 'array':
        return `Array(${value.length})`;
      case 'object':
        return `Object(${Object.keys(value).length})`;
      case 'null':
        return 'null';
      case 'undefined':
        return 'undefined';
      default:
        return String(value);
    }
  }

  getTypeColor(type: ObjectPropertyType): string {
    const colors = {
      string: 'text-green-600',
      number: 'text-blue-600',
      boolean: 'text-purple-600',
      date: 'text-orange-600',
      object: 'text-gray-600',
      array: 'text-indigo-600',
      null: 'text-gray-400',
      undefined: 'text-gray-400'
    };
    
    return colors[type] || 'text-gray-600';
  }

  getTypeIcon(type: ObjectPropertyType): string {
    const icons = {
      string: 'text-cursor',
      number: 'hashtag',
      boolean: 'check-circle',
      date: 'calendar',
      object: 'cube',
      array: 'list-bullet',
      null: 'minus-circle',
      undefined: 'question-mark-circle'
    };
    
    return icons[type] || 'cube';
  }

  toggleExpansion(property: ObjectProperty): void {
    if (!property.expandable) return;
    
    property.expanded = !property.expanded;
    
    if (property.expanded) {
      this.expandedPaths.add(property.path);
      this.expand.emit(property);
    } else {
      this.expandedPaths.delete(property.path);
      this.collapse.emit(property);
    }
    
    this.parseData(); // Re-parse to show/hide nested properties
  }

  onPropertyClick(property: ObjectProperty): void {
    this.propertyClick.emit(property);
  }

  onCopy(property: ObjectProperty): void {
    this.copy.emit({ key: property.key, value: property.value });
    
    // Copy to clipboard if available
    if (navigator.clipboard) {
      const copyText = JSON.stringify({ [property.key]: property.value }, null, 2);
      navigator.clipboard.writeText(copyText);
    }
  }

  onSearch(event: Event): void {
    const target = event.target as HTMLInputElement;
    this.searchTerm = target.value.toLowerCase();
    this.filterAndSort();
  }

  onSort(by: 'key' | 'type' | 'value'): void {
    if (this.sortBy === by) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortBy = by;
      this.sortDirection = 'asc';
    }
    this.filterAndSort();
  }

  filterAndSort(): void {
    let filtered = [...this.properties];
    
    // Apply search filter
    if (this.searchTerm) {
      filtered = filtered.filter(prop => 
        prop.key.toLowerCase().includes(this.searchTerm) ||
        prop.path.toLowerCase().includes(this.searchTerm) ||
        String(prop.value).toLowerCase().includes(this.searchTerm)
      );
    }
    
    // Apply sorting
    if (this.sortable) {
      filtered.sort((a, b) => {
        let aVal: any, bVal: any;
        
        switch (this.sortBy) {
          case 'key':
            aVal = a.key;
            bVal = b.key;
            break;
          case 'type':
            aVal = a.type;
            bVal = b.type;
            break;
          case 'value':
            aVal = String(a.value);
            bVal = String(b.value);
            break;
        }
        
        const comparison = aVal < bVal ? -1 : aVal > bVal ? 1 : 0;
        return this.sortDirection === 'asc' ? comparison : -comparison;
      });
    }
    
    this.filteredProperties = filtered;
  }

  expandAll(): void {
    this.properties.forEach(prop => {
      if (prop.expandable) {
        this.expandedPaths.add(prop.path);
      }
    });
    this.parseData();
  }

  collapseAll(): void {
    this.expandedPaths.clear();
    this.parseData();
  }

  getIndentation(level: number): string {
    return `${level * 20}px`;
  }

  trackByProperty(index: number, property: ObjectProperty): string {
    return property.path;
  }
}

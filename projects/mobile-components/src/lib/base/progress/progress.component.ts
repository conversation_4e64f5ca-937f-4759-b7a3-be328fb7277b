import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-progress',
  templateUrl: './progress.component.html',
  styleUrls: ['./progress.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ProgressComponent implements OnInit, OnChanges {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() value: number = 65;
  @Input() max: number = 100;
  @Input() min: number = 0;
  @Input() showLabel: boolean = false;
  @Input() showPercentage: boolean = false;
  @Input() label: string = 'Progress';
  @Input() animated: boolean = true;
  @Input() striped: boolean = false;
  @Input() indeterminate: boolean = false;
  @Input() thickness: 'thin' | 'normal' | 'thick' = 'normal';
  @Input() orientation: 'horizontal' | 'vertical' = 'horizontal';
  @Input() reversed: boolean = false;
  @Input() buffer?: number;
  @Input() disabled: boolean = false;

  // Legacy support
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() contrast?: 'default' | 'contrast';
  @Input() color?: 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'light' | 'dark' | 'black';
  @Input() classes?: {
    wrapper?: string | string[];
    progress?: string | string[];
  } = {};

  // Events
  @Output() progressComplete = new EventEmitter<void>();
  @Output() progressChange = new EventEmitter<number>();

  colors: Record<string, string> = {
    primary: 'nui-progress-primary',
    info: 'nui-progress-info',
    success: 'nui-progress-success',
    warning: 'nui-progress-warning',
    danger: 'nui-progress-danger',
    light: 'nui-progress-light',
    dark: 'nui-progress-dark',
    black: 'nui-progress-black',
  };

  contrasts: Record<string, string> = {
    default: 'nui-progress-default',
    contrast: 'nui-progress-contrast',
  };

  radiuses: Record<string, string> = {
    none: '',
    sm: 'nui-progress-rounded-sm',
    md: 'nui-progress-rounded-md',
    lg: 'nui-progress-rounded-lg',
    full: 'nui-progress-rounded-full',
  };

  sizes: Record<string, string> = {
    xs: 'nui-progress-xs',
    sm: 'nui-progress-sm',
    md: 'nui-progress-md',
    lg: 'nui-progress-lg',
    xl: 'nui-progress-xl',
  };

  computedValue: number = 0;
  computedPercentage: number = 0;
  isIndeterminate: boolean = false;
  
  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'relative overflow-hidden transition-all duration-200';
    
    const sizeClasses = {
      xs: this.orientation === 'horizontal' ? 'h-1' : 'w-1',
      sm: this.orientation === 'horizontal' ? 'h-2' : 'w-2',
      md: this.orientation === 'horizontal' ? 'h-3' : 'w-3',
      lg: this.orientation === 'horizontal' ? 'h-4' : 'w-4',
      xl: this.orientation === 'horizontal' ? 'h-6' : 'w-6'
    };

    const thicknessClasses = {
      thin: this.orientation === 'horizontal' ? 'h-1' : 'w-1',
      normal: this.orientation === 'horizontal' ? 'h-3' : 'w-3',
      thick: this.orientation === 'horizontal' ? 'h-6' : 'w-6'
    };

    const variantClasses = {
      default: 'bg-gray-200 dark:bg-gray-700',
      primary: 'bg-blue-100 dark:bg-blue-900',
      secondary: 'bg-gray-100 dark:bg-gray-800',
      success: 'bg-green-100 dark:bg-green-900',
      warning: 'bg-yellow-100 dark:bg-yellow-900',
      danger: 'bg-red-100 dark:bg-red-900'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const orientationClasses = {
      horizontal: 'w-full',
      vertical: 'h-full'
    };

    const stateClasses = [
      this.disabled ? 'opacity-50 cursor-not-allowed' : '',
      this.orientation === 'vertical' ? 'flex flex-col' : ''
    ];

    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      thicknessClasses[this.thickness] || sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      orientationClasses[this.orientation],
      ...stateClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedBarClasses(): string {
    const baseBarClasses = 'transition-all duration-500 ease-out';
    
    const variantBarClasses = {
      default: 'bg-gray-600 dark:bg-gray-300',
      primary: 'bg-blue-600 dark:bg-blue-400',
      secondary: 'bg-gray-600 dark:bg-gray-400',
      success: 'bg-green-600 dark:bg-green-400',
      warning: 'bg-yellow-600 dark:bg-yellow-400',
      danger: 'bg-red-600 dark:bg-red-400'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const animationClasses = [
      this.animated && this.indeterminate ? 'animate-pulse' : '',
      this.striped ? 'bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:2rem_100%] animate-[progress-stripes_2s_linear_infinite]' : ''
    ];

    const orientationClasses = {
      horizontal: 'h-full',
      vertical: 'w-full'
    };

    return [
      baseBarClasses,
      variantBarClasses[this.variant],
      roundedClasses[this.rounded],
      orientationClasses[this.orientation],
      ...animationClasses
    ].filter(Boolean).join(' ');
  }

  get computedBufferClasses(): string {
    if (!this.buffer) return '';
    
    const baseBufferClasses = 'absolute inset-0 transition-all duration-300';
    
    const variantBufferClasses = {
      default: 'bg-gray-300 dark:bg-gray-600',
      primary: 'bg-blue-200 dark:bg-blue-800',
      secondary: 'bg-gray-200 dark:bg-gray-700',
      success: 'bg-green-200 dark:bg-green-800',
      warning: 'bg-yellow-200 dark:bg-yellow-800',
      danger: 'bg-red-200 dark:bg-red-800'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      baseBufferClasses,
      variantBufferClasses[this.variant],
      roundedClasses[this.rounded]
    ].filter(Boolean).join(' ');
  }

  get computedLabelClasses(): string {
    const baseLabelClasses = 'text-sm font-medium mb-2';
    
    const variantLabelClasses = {
      default: 'text-gray-700 dark:text-gray-300',
      primary: 'text-blue-700 dark:text-blue-300',
      secondary: 'text-gray-600 dark:text-gray-400',
      success: 'text-green-700 dark:text-green-300',
      warning: 'text-yellow-700 dark:text-yellow-300',
      danger: 'text-red-700 dark:text-red-300'
    };

    return [
      baseLabelClasses,
      variantLabelClasses[this.variant]
    ].filter(Boolean).join(' ');
  }

  // Legacy support method
  getLegacyClasses(): string {
    const legacyClasses = [];
    
    if (this.sizes[this.size as keyof typeof this.sizes]) {
      legacyClasses.push(this.sizes[this.size as keyof typeof this.sizes]);
    }
    
    if (this.contrast && this.contrasts[this.contrast as keyof typeof this.contrasts]) {
      legacyClasses.push(this.contrasts[this.contrast as keyof typeof this.contrasts]);
    }
    
    if (this.color && this.colors[this.color as keyof typeof this.colors]) {
      legacyClasses.push(this.colors[this.color as keyof typeof this.colors]);
    }
    
    if (this.rounded && this.radiuses[this.rounded as keyof typeof this.radiuses]) {
      legacyClasses.push(this.radiuses[this.rounded as keyof typeof this.radiuses]);
    }
    
    return legacyClasses.join(' ');
  }

  get progressStyles(): { [key: string]: string } {
    const styles: { [key: string]: string } = {};
    
    if (this.indeterminate) {
      if (this.orientation === 'horizontal') {
        styles['width'] = '100%';
        styles['animation'] = 'progress-indeterminate 2s ease-in-out infinite';
      } else {
        styles['height'] = '100%';
        styles['animation'] = 'progress-indeterminate-vertical 2s ease-in-out infinite';
      }
    } else {
      const percentage = this.computedPercentage;
      if (this.orientation === 'horizontal') {
        styles['width'] = `${this.reversed ? 100 - percentage : percentage}%`;
        styles['transform'] = this.reversed ? 'scaleX(-1)' : '';
      } else {
        styles['height'] = `${this.reversed ? 100 - percentage : percentage}%`;
        styles['transform'] = this.reversed ? 'scaleY(-1)' : '';
      }
    }
    
    return styles;
  }

  get bufferStyles(): { [key: string]: string } {
    if (!this.buffer) return {};
    
    const styles: { [key: string]: string } = {};
    const bufferPercentage = Math.min(Math.max((this.buffer / this.max) * 100, 0), 100);
    
    if (this.orientation === 'horizontal') {
      styles['width'] = `${bufferPercentage}%`;
    } else {
      styles['height'] = `${bufferPercentage}%`;
    }
    
    return styles;
  }

  get displayValue(): string {
    if (this.indeterminate) return 'Loading...';
    return this.showPercentage ? `${Math.round(this.computedPercentage)}%` : `${this.computedValue}/${this.max}`;
  }

  ngOnInit() {
    this.updateComputedValue();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['value'] || changes['max'] || changes['min']) {
      this.updateComputedValue();
    }
  }

  private updateComputedValue() {
    if (this.indeterminate) {
      this.computedValue = 0;
      this.computedPercentage = 0;
      this.isIndeterminate = true;
      return;
    }

    this.computedValue = Math.min(Math.max(this.value, this.min), this.max);
    this.computedPercentage = this.max === this.min ? 0 : ((this.computedValue - this.min) / (this.max - this.min)) * 100;
    this.isIndeterminate = false;

    // Emit events
    this.progressChange.emit(this.computedPercentage);
    
    if (this.computedPercentage >= 100) {
      this.progressComplete.emit();
    }
  }

  increment(amount: number = 1) {
    if (this.disabled || this.indeterminate) return;
    this.value = Math.min(this.value + amount, this.max);
    this.updateComputedValue();
  }

  decrement(amount: number = 1) {
    if (this.disabled || this.indeterminate) return;
    this.value = Math.max(this.value - amount, this.min);
    this.updateComputedValue();
  }

  reset() {
    if (this.disabled) return;
    this.value = this.min;
    this.updateComputedValue();
  }

  complete() {
    if (this.disabled || this.indeterminate) return;
    this.value = this.max;
    this.updateComputedValue();
  }
}

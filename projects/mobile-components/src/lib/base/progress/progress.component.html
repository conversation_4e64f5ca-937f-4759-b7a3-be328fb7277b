<div [attr.id]="item_id" class="w-full">
  <!-- Label -->
  <div *ngIf="showLabel || showPercentage" class="flex justify-between items-center mb-2">
    <span *ngIf="showLabel" [ngClass]="computedLabelClasses">
      {{ label }}
    </span>
    <span *ngIf="showPercentage" class="text-sm text-gray-600 dark:text-gray-400">
      {{ displayValue }}
    </span>
  </div>

  <!-- Progress Container -->
  <div
    [ngClass]="computedClasses"
    role="progressbar"
    [attr.aria-valuenow]="indeterminate ? null : computedValue"
    [attr.aria-valuemin]="min"
    [attr.aria-valuemax]="max"
    [attr.aria-label]="label"
    [attr.aria-valuetext]="indeterminate ? 'Loading' : displayValue"
  >
    <!-- Buffer Bar (if provided) -->
    <div 
      *ngIf="buffer && !indeterminate"
      [ngClass]="computedBufferClasses"
      [ngStyle]="bufferStyles"
    ></div>

    <!-- Progress Bar -->
    <div
      [ngClass]="computedBarClasses"
      [ngStyle]="progressStyles"
    >
      <!-- Striped pattern overlay -->
      <div 
        *ngIf="striped && !indeterminate"
        class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent bg-[length:1rem_100%] animate-[progress-stripes_1s_linear_infinite]"
      ></div>
    </div>

    <!-- Content projection for custom progress content -->
    <ng-content></ng-content>
  </div>

  <!-- Legacy Template (Hidden, for backward compatibility) -->
  <div class="hidden legacy-progress-wrapper">
    <div
      [attr.id]="item_id"
      class="nui-progress"
      [ngClass]="[
        size && sizes[size],
        contrast && contrasts[contrast],
        color && colors[color],
        rounded && radiuses[rounded],
        classes?.wrapper
      ]"
    >
      <div
        class="nui-progress-inner"
        [ngClass]="[classes?.progress]"
        [ngStyle]="{ width: value + '%' }"
        [attr.aria-valuenow]="value"
        [attr.aria-valuemin]="0"
        [attr.aria-valuemax]="max"
        role="progressbar"
      ></div>
    </div>
  </div>
</div>

<div [ngClass]="computedClasses" [attr.id]="item_id">
  <!-- Image Section -->
  <div *ngIf="hasImage" [ngClass]="computedImageClasses">
    <!-- Custom image slot -->
    <div *ngIf="hasImageSlot" class="w-full h-full">
      <ng-content select="[slot='image']"></ng-content>
    </div>
    
    <!-- URL-based image -->
    <img 
      *ngIf="!hasImageSlot && imageUrl"
      [src]="imageUrl"
      [alt]="imageAlt"
      class="w-full h-full object-contain"
    />
    
    <!-- Default placeholder image -->
    <div 
      *ngIf="!hasImageSlot && !imageUrl && showDefaultImage"
      class="w-full h-full flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg"
      [innerHTML]="defaultImageSvg"
    ></div>
  </div>

  <!-- Content Section -->
  <div class="max-w-md mx-auto">
    <!-- Title -->
    <h2 [ngClass]="computedTitleClasses">
      {{ title }}
    </h2>
    
    <!-- Subtitle -->
    <p *ngIf="subtitle" [ngClass]="computedSubtitleClasses">
      {{ subtitle }}
    </p>
    
    <!-- Action Text -->
    <p *ngIf="actionText" class="text-gray-500 dark:text-gray-400 mb-4 text-sm">
      {{ actionText }}
    </p>
    
    <!-- Loading indicator -->
    <div *ngIf="loading" class="flex items-center justify-center mb-4">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900 dark:border-gray-100"></div>
      <span class="ml-2 text-sm text-gray-600 dark:text-gray-400">Loading...</span>
    </div>
    
    <!-- Action Buttons -->
    <div *ngIf="showActionButton && !loading" class="flex flex-col sm:flex-row gap-3 justify-center">
      <button
        type="button"
        [ngClass]="computedButtonClasses"
        [disabled]="disabled"
        (click)="onActionClick($event)"
      >
        {{ actionButtonText }}
      </button>
      
      <button
        *ngIf="variant === 'danger'"
        type="button"
        class="inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700"
        [disabled]="disabled"
        (click)="onRetryClick($event)"
      >
        Try Again
      </button>
    </div>
    
    <!-- Custom content projection -->
    <div class="mt-6">
      <ng-content></ng-content>
    </div>
  </div>

  <!-- Legacy Template (Hidden, for backward compatibility) -->
  <div class="hidden legacy-placeholder-wrapper">
    <div class="nui-placeholder-page" [ngClass]="imageSize && sizes[imageSize]">
      <div class="nui-placeholder-page-inner">
        <div *ngIf="hasImageSlot" class="nui-placeholder-page-img">
          <ng-content select="[slot='image']"></ng-content>
        </div>
        <div class="nui-placeholder-page-content">
          <base-heading
            as="h4"
            weight="medium"
            size="xl"
            class="nui-placeholder-page-title"
          >
            {{ title }}
          </base-heading>
          <p *ngIf="subtitle" class="nui-placeholder-page-subtitle">
            {{ subtitle }}
          </p>
          <ng-content></ng-content>
        </div>
      </div>
    </div>
  </div>
</div>

import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ContentChild,
  ElementRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeadingComponent } from '../heading/heading.component';

@Component({
  selector: 'base-placeholder-page',
  templateUrl: './placeholder-page.component.html',
  styleUrls: ['./placeholder-page.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    HeadingComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PlaceholderPageComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() title: string = 'Page Not Found';
  @Input() subtitle: string = 'The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.';
  @Input() imageUrl?: string;
  @Input() imageAlt: string = 'Placeholder illustration';
  @Input() showDefaultImage: boolean = true;
  @Input() actionText?: string;
  @Input() actionButtonText: string = 'Go Back';
  @Input() showActionButton: boolean = true;
  @Input() centered: boolean = true;
  @Input() fullHeight: boolean = true;
  @Input() loading: boolean = false;
  @Input() disabled: boolean = false;

  // Legacy support
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() imageSize?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';

  // Events
  @Output() actionClick = new EventEmitter<MouseEvent>();
  @Output() retryClick = new EventEmitter<MouseEvent>();

  sizes: Record<string, string> = {
    xs: 'nui-placeholder-xs',
    sm: 'nui-placeholder-sm',
    md: 'nui-placeholder-md',
    lg: 'nui-placeholder-lg',
    xl: 'nui-placeholder-xl',
  };

  @ContentChild('[slot="image"]') imageSlot?: ElementRef;

  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'flex flex-col transition-all duration-200';
    
    const sizeClasses = {
      xs: 'space-y-3 p-4',
      sm: 'space-y-4 p-6',
      md: 'space-y-6 p-8',
      lg: 'space-y-8 p-12',
      xl: 'space-y-10 p-16'
    };

    const variantClasses = {
      default: 'text-gray-600 dark:text-gray-400',
      primary: 'text-blue-600 dark:text-blue-400',
      secondary: 'text-gray-500 dark:text-gray-500',
      success: 'text-green-600 dark:text-green-400',
      warning: 'text-yellow-600 dark:text-yellow-400',
      danger: 'text-red-600 dark:text-red-400'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const layoutClasses = [
      this.centered ? 'items-center text-center' : 'items-start text-left',
      this.fullHeight ? 'min-h-screen justify-center' : '',
      this.disabled ? 'opacity-50 pointer-events-none' : ''
    ];

    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      ...layoutClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedImageClasses(): string {
    const baseImageClasses = 'mx-auto mb-4 transition-all duration-200';
    
    const imageSizeClasses = {
      xs: 'w-16 h-16',
      sm: 'w-24 h-24',
      md: 'w-32 h-32',
      lg: 'w-48 h-48',
      xl: 'w-64 h-64'
    };

    const imageSize = this.imageSize || this.size;

    return [
      baseImageClasses,
      imageSizeClasses[imageSize],
      this.loading ? 'animate-pulse' : ''
    ].filter(Boolean).join(' ');
  }

  get computedTitleClasses(): string {
    const baseTitleClasses = 'font-semibold text-gray-900 dark:text-white mb-2';
    
    const titleSizeClasses = {
      xs: 'text-lg',
      sm: 'text-xl',
      md: 'text-2xl',
      lg: 'text-3xl',
      xl: 'text-4xl'
    };

    return [
      baseTitleClasses,
      titleSizeClasses[this.size]
    ].filter(Boolean).join(' ');
  }

  get computedSubtitleClasses(): string {
    const baseSubtitleClasses = 'text-gray-600 dark:text-gray-400 mb-6';
    
    const subtitleSizeClasses = {
      xs: 'text-sm',
      sm: 'text-base',
      md: 'text-lg',
      lg: 'text-xl',
      xl: 'text-2xl'
    };

    return [
      baseSubtitleClasses,
      subtitleSizeClasses[this.size]
    ].filter(Boolean).join(' ');
  }

  get computedButtonClasses(): string {
    const baseButtonClasses = 'inline-flex items-center justify-center px-4 py-2 font-medium transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    
    const buttonSizeClasses = {
      xs: 'text-xs px-2 py-1',
      sm: 'text-sm px-3 py-1.5',
      md: 'text-base px-4 py-2',
      lg: 'text-lg px-6 py-3',
      xl: 'text-xl px-8 py-4'
    };

    const buttonVariantClasses = {
      default: 'bg-gray-900 text-white hover:bg-gray-800 focus:ring-gray-500 dark:bg-gray-100 dark:text-gray-900 dark:hover:bg-gray-200',
      primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
      secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500',
      success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
      warning: 'bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500',
      danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
    };

    const buttonRoundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      baseButtonClasses,
      buttonSizeClasses[this.size],
      buttonVariantClasses[this.variant],
      buttonRoundedClasses[this.rounded]
    ].filter(Boolean).join(' ');
  }

  // Legacy support method
  getLegacyClasses(): string {
    const legacyClasses = [];
    
    if (this.imageSize && this.sizes[this.imageSize]) {
      legacyClasses.push(this.sizes[this.imageSize]);
    }
    
    return legacyClasses.join(' ');
  }

  get hasImageSlot(): boolean {
    return !!this.imageSlot;
  }

  get hasImage(): boolean {
    return this.hasImageSlot || !!this.imageUrl || this.showDefaultImage;
  }

  get defaultImageSvg(): string {
    return `
      <svg class="w-full h-full text-gray-300 dark:text-gray-600" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
      </svg>
    `;
  }

  onActionClick(event: MouseEvent) {
    if (this.disabled || this.loading) return;
    this.actionClick.emit(event);
  }

  onRetryClick(event: MouseEvent) {
    if (this.disabled || this.loading) return;
    this.retryClick.emit(event);
  }

  ngOnInit() {
    if (!this.imageSize) {
      this.imageSize = this.size; // Use standard size as default
    }
  }
}

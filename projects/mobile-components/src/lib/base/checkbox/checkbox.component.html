<div
  [ngClass]="computedClasses"
  [attr.aria-disabled]="disabled"
>
  <div class="nui-checkbox-outer">
    <input
      #inputRef
      [id]="id"
      [(ngModel)]="modelValue"
      [value]="value"
      [name]="name"
      [ngClass]="computedInputClasses"
      [class]="inputClassString"
      [disabled]="disabled"
      [required]="required"
      [attr.aria-label]="label"
      [attr.aria-describedby]="isErrorString ? id + '-error' : null"
      [attr.aria-invalid]="isErrorString"
      class="nui-checkbox-input"
      type="checkbox"
      (ngModelChange)="onModelChange($event)"
      (change)="onChange_Handler($event)"
    />
    <div class="nui-checkbox-inner"></div>
  </div>
  <div class="nui-checkbox-label-wrapper">
    <label
      *ngIf="label || defaultSlot"
      [for]="id"
      class="nui-checkbox-label-text cursor-pointer select-none"
      [ngClass]="classes?.label"
    >
      <ng-content></ng-content>
      <span *ngIf="label && !defaultSlot">{{ label }}</span>
      <span *ngIf="required" class="text-red-500 ml-1" aria-label="required">*</span>
    </label>
    <div 
      *ngIf="isErrorString" 
      [id]="id + '-error'"
      class="nui-checkbox-error text-red-500 text-sm mt-1"
      role="alert"
      aria-live="polite"
    >
      {{ error }}
    </div>
  </div>
</div>

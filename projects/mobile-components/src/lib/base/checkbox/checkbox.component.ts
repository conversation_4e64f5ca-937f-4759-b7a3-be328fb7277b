import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ViewChild,
  ElementRef,
  ContentChild,
  TemplateRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-checkbox',
  templateUrl: './checkbox.component.html',
  styleUrls: ['./checkbox.component.css'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: CheckboxComponent,
      multi: true,
    },
  ],
  standalone: true,
  imports: [CommonModule, FormsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class CheckboxComponent implements OnInit, ControlValueAccessor {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() label: string = 'Accept terms and conditions';
  @Input() checked: boolean = false;
  @Input() required: boolean = false;
  @Input() disabled: boolean = false;
  @Input() indeterminate: boolean = false;
  @Input() name: string = '';
  @Input() value: string = '';
  @Input() error: string = '';

  // Legacy inputs (preserved for backward compatibility)
  @Input() trueValue: any = true;
  @Input() falseValue: any = false;
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() id?: string;
  @Input() color?:
    | 'default'
    | 'muted'
    | 'light'
    | 'dark'
    | 'black'
    | 'primary'
    | 'info'
    | 'success'
    | 'warning'
    | 'danger' = 'default';
  @Input() classes?: {
    wrapper?: string | string[];
    label?: string | string[];
    input?: string | string[];
  } = {};

  // Events
  @Output() checkedChange = new EventEmitter<boolean>();
  @Output() change = new EventEmitter<Event>();

  @ViewChild('inputRef') inputRef!: ElementRef<HTMLInputElement>;
  @ContentChild(TemplateRef) defaultSlot: TemplateRef<any> | null = null;

  modelValue: boolean = false;
  
  // Legacy support mappings
  radiuses: Record<string, string> = {
    none: '',
    sm: 'nui-checkbox-rounded-sm',
    md: 'nui-checkbox-rounded-md',
    lg: 'nui-checkbox-rounded-lg',
    full: 'nui-checkbox-rounded-full',
  };

  colors: Record<string, string> = {
    default: 'nui-checkbox-default',
    muted: 'nui-checkbox-muted',
    light: 'nui-checkbox-light',
    dark: 'nui-checkbox-dark',
    black: 'nui-checkbox-black',
    primary: 'nui-checkbox-primary',
    info: 'nui-checkbox-info',
    success: 'nui-checkbox-success',
    warning: 'nui-checkbox-warning',
    danger: 'nui-checkbox-danger',
  };

  private onChange: (value: any) => void = () => {};
  private onTouched: () => void = () => {};

  get computedClasses(): string {
    const baseClasses = 'relative inline-flex items-start gap-2 cursor-pointer transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm', 
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: '',
      primary: 'text-blue-600',
      secondary: 'text-gray-600',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      danger: 'text-red-600'
    };

    const roundedClasses = {
      none: '',
      sm: '',
      md: '', 
      lg: '',
      full: ''
    };

    const disabledClasses = this.disabled ? 'opacity-50 cursor-not-allowed' : '';
    
    // Legacy support
    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      disabledClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedInputClasses(): string {
    const baseInputClasses = 'h-4 w-4 rounded border-2 border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200';
    
    const sizeInputClasses = {
      xs: 'h-3 w-3',
      sm: 'h-3.5 w-3.5', 
      md: 'h-4 w-4',
      lg: 'h-5 w-5',
      xl: 'h-6 w-6'
    };

    const variantInputClasses = {
      default: 'text-blue-600',
      primary: 'text-blue-600',
      secondary: 'text-gray-600',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      danger: 'text-red-600'
    };

    const roundedInputClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded', 
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      baseInputClasses,
      sizeInputClasses[this.size],
      variantInputClasses[this.variant],
      roundedInputClasses[this.rounded]
    ].filter(Boolean).join(' ');
  }

  get inputClassString(): string {
    if (!this.classes?.input) return '';
    return Array.isArray(this.classes.input) 
      ? this.classes.input.join(' ') 
      : this.classes.input;
  }

  private getLegacyClasses(): string {
    const legacyWrapper = 'nui-checkbox';
    const legacyRounded = this.rounded ? this.radiuses[this.rounded] : '';
    const legacyColor = this.color ? this.colors[this.color] : '';
    const legacyCustom = Array.isArray(this.classes?.wrapper) 
      ? this.classes.wrapper.join(' ') 
      : this.classes?.wrapper || '';

    return [legacyWrapper, legacyRounded, legacyColor, legacyCustom].filter(Boolean).join(' ');
  }

  ngOnInit() {
    if (!this.id) {
      this.id = `checkbox-${Math.random().toString(36).substring(2, 11)}`;
    }
    // Initialize modelValue from checked input
    this.modelValue = this.checked;
  }

  ngAfterViewInit() {
    this.updateIndeterminate();
  }

  writeValue(value: any): void {
    this.modelValue = value === this.trueValue || value === true;
    this.checked = this.modelValue;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  onModelChange(value: boolean) {
    this.modelValue = value;
    this.checked = value;
    this.onChange(value ? this.trueValue : this.falseValue);
    this.onTouched();
    this.updateIndeterminate();
    
    // Emit events
    this.checkedChange.emit(value);
  }

  onChange_Handler(event: Event) {
    this.change.emit(event);
  }

  private updateIndeterminate() {
    if (this.inputRef && this.inputRef.nativeElement) {
      this.inputRef.nativeElement.indeterminate = this.indeterminate;
    }
  }

  get isErrorString(): boolean {
    return typeof this.error === 'string' && this.error.length > 0;
  }
}

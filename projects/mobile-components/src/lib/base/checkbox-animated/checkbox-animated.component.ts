import {
  Component,
  Input,
  Output,
  EventEmitter,
  ViewChild,
  ElementRef,
  OnInit,
  OnChanges,
  SimpleChanges,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-checkbox-animated',
  templateUrl: './checkbox-animated.component.html',
  styleUrls: ['./checkbox-animated.component.css'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: CheckboxAnimatedComponent,
      multi: true,
    },
  ],
  standalone: true,
  imports: [CommonModule, FormsModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class CheckboxAnimatedComponent
  implements ControlValueAccessor, OnInit, OnChanges
{
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'primary';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'full';

  // Component-specific inputs
  @Input() label: string = 'Enable notifications';
  @Input() checked: boolean = false;
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() name: string = '';
  @Input() animationDuration: number = 600;
  @Input() error: string = '';

  // Legacy inputs (preserved for backward compatibility)
  @Input() value: any;
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() trueValue: any = true;
  @Input() falseValue: any = false;
  @Input() id?: string;
  @Input() color?:
    | 'primary'
    | 'info'
    | 'success'
    | 'warning'
    | 'danger'
    | 'muted'
    | 'light'
    | 'dark'
    | 'black' = 'primary';
  @Input() classes?: {
    wrapper?: string | string[];
    label?: string | string[];
    input?: string | string[];
  } = {
    wrapper: [],
    label: [],
    input: [],
  };

  // Events
  @Output() checkedChange = new EventEmitter<boolean>();
  @Output() changeEvent = new EventEmitter<Event>();
  @Output() modelChange = new EventEmitter<any>();

  @ViewChild('element') element!: ElementRef;
  @ViewChild('inputRef') inputRef!: ElementRef;
  @ViewChild('innerElement') innerElement!: ElementRef;

  internalChecked: boolean = false;
  onChange: any = () => {};
  onTouched: any = () => {};

  colors: Record<string, string> = {
    primary: 'text-primary-500',
    info: 'text-info-500',
    success: 'text-success-500',
    warning: 'text-warning-500',
    danger: 'text-danger-500',
    light: 'text-light-100',
    muted: 'text-muted-400',
    dark: 'text-muted-900 dark:text-muted-100',
    black: 'text-black dark:text-white',
  };

  get computedClasses(): string {
    const baseClasses = 'relative inline-flex items-center gap-3 cursor-pointer transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm', 
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'text-gray-600',
      primary: 'text-blue-600',
      secondary: 'text-gray-600',
      success: 'text-green-600',
      warning: 'text-yellow-600',
      danger: 'text-red-600'
    };

    const roundedClasses = {
      none: '',
      sm: '',
      md: '', 
      lg: '',
      full: ''
    };

    const disabledClasses = this.disabled ? 'opacity-50 cursor-not-allowed' : '';
    
    // Legacy support
    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      disabledClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedCheckboxClasses(): string {
    const baseCheckboxClasses = 'nui-focus block focus-within:outline-current';
    
    const sizeCheckboxClasses = {
      xs: 'h-6 w-6',
      sm: 'h-7 w-7', 
      md: 'h-8 w-8',
      lg: 'h-10 w-10',
      xl: 'h-12 w-12'
    };

    const legacyWrapperClasses = Array.isArray(this.classes?.wrapper) 
      ? this.classes.wrapper.join(' ') 
      : this.classes?.wrapper || '';

    return [
      baseCheckboxClasses,
      sizeCheckboxClasses[this.size],
      legacyWrapperClasses
    ].filter(Boolean).join(' ');
  }

  private getLegacyClasses(): string {
    const legacyColor = this.color ? this.colors[this.color] : '';
    const legacyLabel = Array.isArray(this.classes?.label) 
      ? this.classes.label.join(' ') 
      : this.classes?.label || '';

    return [legacyColor, legacyLabel].filter(Boolean).join(' ');
  }

  ngOnInit() {
    if (!this.id) {
      this.id = `checkbox-animated-${Math.random().toString(36).substring(7)}`;
    }
    // Initialize internalChecked from checked input
    this.internalChecked = this.checked;
    this.updateCheckbox();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['value'] || changes['checked']) {
      this.updateCheckedState();
    }
  }

  writeValue(value: any): void {
    this.value = value;
    this.updateCheckedState();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  updateCheckedState() {
    if (Array.isArray(this.value)) {
      this.internalChecked = this.value.includes(this.trueValue);
    } else if (this.value !== undefined) {
      this.internalChecked = this.value === this.trueValue;
    } else {
      this.internalChecked = this.checked;
    }
    this.updateCheckbox();
  }

  updateCheckbox() {
    if (this.element && this.innerElement) {
      if (this.internalChecked) {
        this.element.nativeElement.classList.add('is-checked');
        this.innerElement.nativeElement.classList.add('is-opaque');
        setTimeout(() => {
          this.element.nativeElement.classList.remove('is-unchecked');
        }, 150);
      } else {
        this.element.nativeElement.classList.add('is-unchecked');
        this.element.nativeElement.classList.remove('is-checked');
        setTimeout(() => {
          this.innerElement.nativeElement.classList.remove('is-opaque');
        }, 150);
      }
    }
  }

  change() {
    if (this.disabled) return;

    if (Array.isArray(this.value)) {
      const values = [...this.value];
      const trueValue = this.trueValue;
      if (trueValue === undefined) {
        return;
      }

      if (this.internalChecked) {
        values.splice(values.indexOf(trueValue), 1);
      } else {
        values.push(trueValue);
      }

      this.value = values;
    } else {
      this.value = this.internalChecked ? this.falseValue : this.trueValue;
    }

    // Update checked state
    this.checked = !this.internalChecked;
    
    this.onChange(this.value);
    this.onTouched();
    this.modelChange.emit(this.value);
    this.checkedChange.emit(this.checked);
    this.updateCheckedState();
  }

  onChangeHandler(event: Event) {
    this.changeEvent.emit(event);
  }

  get isErrorString(): boolean {
    return typeof this.error === 'string' && this.error.length > 0;
  }
}

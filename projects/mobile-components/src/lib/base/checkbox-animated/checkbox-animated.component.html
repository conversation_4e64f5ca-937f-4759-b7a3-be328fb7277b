<div 
  [ngClass]="computedClasses"
  [attr.aria-disabled]="disabled"
>
  <div
    #element
    [ngClass]="computedCheckboxClasses"
  >
    <input
      [id]="id"
      #inputRef
      type="checkbox"
      class="peer cursor-pointer disabled:cursor-not-allowed"
      [attr.true-value]="trueValue"
      [attr.false-value]="falseValue"
      [checked]="internalChecked"
      [value]="value"
      [name]="name"
      [disabled]="disabled"
      [required]="required"
      [attr.aria-label]="label"
      [attr.aria-describedby]="isErrorString ? id + '-error' : null"
      [attr.aria-invalid]="isErrorString"
      [ngClass]="classes?.input"
      (change)="change(); onChangeHandler($event)"
    />
    <label
      class="peer-disabled:opacity-75 cursor-pointer"
      [for]="id"
      [ngClass]="[color && colors[color], classes?.label]"
    >
      <div #innerElement></div>
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
        <circle cx="26" cy="26" r="25" fill="none"/>
        <path fill="none" d="m14.1 27.2 7.1 7.2 16.7-16.8"/>
      </svg>
    </label>
  </div>
  <div class="flex flex-col">
    <span 
      *ngIf="label" 
      class="cursor-pointer select-none"
      [attr.for]="id"
    >
      {{ label }}
      <span *ngIf="required" class="text-red-500 ml-1" aria-label="required">*</span>
    </span>
    <div 
      *ngIf="isErrorString" 
      [id]="id + '-error'"
      class="text-red-500 text-sm mt-1"
      role="alert"
      aria-live="polite"
    >
      {{ error }}
    </div>
  </div>
</div>

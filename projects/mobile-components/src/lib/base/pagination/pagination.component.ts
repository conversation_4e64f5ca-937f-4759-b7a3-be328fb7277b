import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ContentChildren,
  QueryList,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

@Component({
  selector: 'base-pagination',
  templateUrl: './pagination.component.html',
  styleUrls: ['./pagination.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    IconComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class PaginationComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() itemPerPage: number = 10;
  @Input() totalItems: number = 100;
  @Input() currentPage: number = 1;
  @Input() maxLinksDisplayed: number = 5;
  @Input() showFirstLast: boolean = true;
  @Input() showPrevNext: boolean = true;
  @Input() showPageInfo: boolean = false;
  @Input() disabled: boolean = false;
  @Input() noRouter: boolean = false;
  @Input() routerQueryKey: string = 'page';
  @Input() previousIcon: string = 'lucide:chevron-left';
  @Input() nextIcon: string = 'lucide:chevron-right';
  @Input() ellipsis: string = '…';
  @Input() previousLabel: string = 'Previous';
  @Input() nextLabel: string = 'Next';
  @Input() pageInfoText: string = 'Page {current} of {total}';

  // Legacy support
  @Input() itemId?: string = Math.random().toString(36).substring(7);
  @Input() color: 'primary' | 'dark' | 'black' = 'primary';
  @Input() classes: {
    wrapper?: string | string[];
    list?: string | string[];
    link?: string | string[];
    ellipsis?: string | string[];
    buttons?: string | string[];
    button?: string | string[];
  } = {};

  @Output() currentPageChange = new EventEmitter<number>();
  @Output() pageClick = new EventEmitter<{ page: number; event: MouseEvent }>();

  @ContentChildren('previous-icon,next-icon') iconSlots!: QueryList<any>;

  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'flex items-center justify-center space-x-1 transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs gap-0.5',
      sm: 'text-sm gap-1',
      md: 'text-base gap-1',
      lg: 'text-lg gap-2',
      xl: 'text-xl gap-2'
    };

    const variantClasses = {
      default: 'text-gray-700 dark:text-gray-300',
      primary: 'text-blue-700 dark:text-blue-300',
      secondary: 'text-gray-600 dark:text-gray-400',
      success: 'text-green-700 dark:text-green-300',
      warning: 'text-yellow-700 dark:text-yellow-300',
      danger: 'text-red-700 dark:text-red-300'
    };

    const roundedClasses = {
      none: '',
      sm: '',
      md: '',
      lg: '',
      full: ''
    };

    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedLinkClasses(): string {
    const baseLinkClasses = 'flex items-center justify-center px-3 py-2 transition-all duration-200 cursor-pointer';
    
    const sizeClasses = {
      xs: 'min-w-[24px] h-6 text-xs px-1.5 py-1',
      sm: 'min-w-[28px] h-7 text-sm px-2 py-1',
      md: 'min-w-[32px] h-8 text-base px-3 py-2',
      lg: 'min-w-[36px] h-9 text-lg px-4 py-2',
      xl: 'min-w-[40px] h-10 text-xl px-5 py-2'
    };

    const variantClasses = {
      default: 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700',
      primary: 'text-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:hover:bg-blue-900/20',
      secondary: 'text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-800',
      success: 'text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:bg-green-900/20',
      warning: 'text-yellow-600 hover:bg-yellow-50 dark:text-yellow-400 dark:hover:bg-yellow-900/20',
      danger: 'text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      baseLinkClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded]
    ].filter(Boolean).join(' ');
  }

  get computedActiveClasses(): string {
    const activeBase = 'font-semibold';
    
    const variantActiveClasses = {
      default: 'bg-gray-200 text-gray-900 dark:bg-gray-600 dark:text-white',
      primary: 'bg-blue-600 text-white dark:bg-blue-500',
      secondary: 'bg-gray-600 text-white dark:bg-gray-500',
      success: 'bg-green-600 text-white dark:bg-green-500',
      warning: 'bg-yellow-600 text-white dark:bg-yellow-500',
      danger: 'bg-red-600 text-white dark:bg-red-500'
    };

    return [
      activeBase,
      variantActiveClasses[this.variant]
    ].filter(Boolean).join(' ');
  }

  get computedButtonClasses(): string {
    const baseButtonClasses = 'flex items-center justify-center transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed';
    
    const sizeClasses = {
      xs: 'px-2 py-1 text-xs',
      sm: 'px-2.5 py-1.5 text-sm',
      md: 'px-3 py-2 text-base',
      lg: 'px-4 py-2.5 text-lg',
      xl: 'px-5 py-3 text-xl'
    };

    const variantClasses = {
      default: 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700',
      primary: 'text-blue-600 hover:bg-blue-50 dark:text-blue-400 dark:hover:bg-blue-900/20',
      secondary: 'text-gray-600 hover:bg-gray-50 dark:text-gray-400 dark:hover:bg-gray-800',
      success: 'text-green-600 hover:bg-green-50 dark:text-green-400 dark:hover:bg-green-900/20',
      warning: 'text-yellow-600 hover:bg-yellow-50 dark:text-yellow-400 dark:hover:bg-yellow-900/20',
      danger: 'text-red-600 hover:bg-red-50 dark:text-red-400 dark:hover:bg-red-900/20'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    return [
      baseButtonClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded]
    ].filter(Boolean).join(' ');
  }

  // Legacy support method
  getLegacyClasses(): string {
    const legacyClasses = [];
    
    if (this.radiuses[this.rounded as keyof typeof this.radiuses]) {
      legacyClasses.push(this.radiuses[this.rounded as keyof typeof this.radiuses]);
    }
    
    if (this.colors[this.color as keyof typeof this.colors]) {
      legacyClasses.push(this.colors[this.color as keyof typeof this.colors]);
    }
    
    return legacyClasses.join(' ');
  }

  // Convenience getters
  get isFirstPage(): boolean {
    return this.currentPage === 1;
  }

  get isLastPage(): boolean {
    return this.currentPage === this.lastPage;
  }

  get pageInfo(): string {
    return this.pageInfoText
      .replace('{current}', this.currentPage.toString())
      .replace('{total}', this.lastPage.toString());
  }

  radiuses: Record<string, string> = {
    none: '',
    sm: 'nui-pagination-rounded-sm',
    md: 'nui-pagination-rounded-md',
    lg: 'nui-pagination-rounded-lg',
    full: 'nui-pagination-rounded-full',
  };

  colors: Record<string, string> = {
    primary: 'nui-pagination-primary',
    dark: 'nui-pagination-dark',
    black: 'nui-pagination-black',
  };

  lastPage: number = 1;
  totalPageDisplayed: number = 1;
  pages: number[] = [];
  showLastLink: boolean = false;

  constructor(private route: ActivatedRoute, private router: Router) {
    // Initialize with default data if needed
    this.updatePagination();
  }

  ngOnInit() {
    this.updatePagination();
  }

  updatePagination() {
    if (this.totalItems && this.itemPerPage) {
      this.lastPage = Math.ceil(this.totalItems / this.itemPerPage) || 1;
      this.totalPageDisplayed =
        this.lastPage > this.maxLinksDisplayed + 2
          ? this.maxLinksDisplayed + 2
          : this.lastPage;
      this.showLastLink = this.lastPage > 1;
      this.calculatePages();
    }
  }

  calculatePages() {
    this.pages = [];
    let firstButton =
      this.currentPage - Math.floor(this.totalPageDisplayed / 2);
    let lastButton =
      firstButton +
      (this.totalPageDisplayed - Math.ceil(this.totalPageDisplayed % 2));

    if (firstButton < 1) {
      firstButton = 1;
      lastButton = firstButton + (this.totalPageDisplayed - 1);
    }

    if (lastButton > this.lastPage) {
      lastButton = this.lastPage;
      firstButton = lastButton - (this.totalPageDisplayed - 1);
    }

    for (let page = firstButton; page <= lastButton; page += 1) {
      if (page === firstButton || page === lastButton) {
        continue;
      }
      this.pages.push(page);
    }
  }

  paginatedLink(page: number = 1): any {
    if (this.noRouter) {
      return {};
    }

    const _page = Math.max(1, Math.min(page, this.lastPage));
    const query: any = { ...this.route.snapshot.queryParams };

    if (this.routerQueryKey) {
      query[this.routerQueryKey] = _page <= 1 ? undefined : _page;
    }

    return {
      queryParams: query,
    };
  }

  handleLinkClick(event: MouseEvent, page: number = 1) {
    if (this.disabled) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }

    const _page = Math.max(1, Math.min(page, this.lastPage));
    
    // Emit both events for flexibility
    this.currentPageChange.emit(_page);
    this.pageClick.emit({ page: _page, event });

    if (this.noRouter) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }

    if (!this.noRouter) {
      this.router.navigate([], this.paginatedLink(_page));
    }

    return true;
  }

  goToPage(page: number) {
    if (this.disabled) return;
    
    const _page = Math.max(1, Math.min(page, this.lastPage));
    this.currentPage = _page;
    this.currentPageChange.emit(_page);
    this.updatePagination();
  }

  goToPrevious() {
    if (this.disabled || this.isFirstPage) return;
    this.goToPage(this.currentPage - 1);
  }

  goToNext() {
    if (this.disabled || this.isLastPage) return;
    this.goToPage(this.currentPage + 1);
  }

  hasContent(slotName: string): boolean {
    return this.iconSlots.some(
      (slot) => slot.nativeElement.getAttribute('slot') === slotName
    );
  }
}

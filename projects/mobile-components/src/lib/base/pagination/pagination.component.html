<div
  [ngClass]="computedClasses"
  [attr.aria-label]="'Pagination navigation'"
  role="navigation"
>
  <!-- Page Info -->
  <div *ngIf="showPageInfo" class="text-sm text-gray-600 dark:text-gray-400 mr-4">
    {{ pageInfo }}
  </div>

  <!-- Pagination Links -->
  <div class="flex items-center space-x-1">
    <ng-content select="[slot='before-pagination']"></ng-content>
    
    <!-- Previous Button -->
    <button 
      *ngIf="showPrevNext"
      type="button"
      [ngClass]="computedButtonClasses"
      [disabled]="disabled || isFirstPage"
      [attr.aria-label]="previousLabel"
      (click)="handleLinkClick($event, currentPage - 1)"
    >
      <base-icon
        [icon]="previousIcon"
        [ngClass]="size === 'xs' ? 'w-3 h-3' : size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : size === 'xl' ? 'w-7 h-7' : 'w-5 h-5'"
      ></base-icon>
      <span class="sr-only">{{ previousLabel }}</span>
    </button>

    <!-- First Page -->
    <button
      *ngIf="showFirstLast"
      type="button"
      [ngClass]="[computedLinkClasses, currentPage === 1 ? computedActiveClasses : '']"
      [disabled]="disabled"
      [attr.aria-current]="currentPage === 1 ? 'page' : null"
      [attr.aria-label]="'Go to page 1'"
      (click)="handleLinkClick($event, 1)"
    >
      1
    </button>

    <!-- First Ellipsis -->
    <span 
      *ngIf="showFirstLast && showLastLink && pages.length > 0 && pages[0] > 2"
      class="px-2 py-1 text-gray-500 dark:text-gray-400"
      [attr.aria-hidden]="true"
    >
      {{ ellipsis }}
    </span>

    <!-- Middle Pages -->
    <button
      *ngFor="let page of pages"
      type="button"
      [ngClass]="[computedLinkClasses, currentPage === page ? computedActiveClasses : '']"
      [disabled]="disabled"
      [attr.aria-current]="currentPage === page ? 'page' : null"
      [attr.aria-label]="'Go to page ' + page"
      (click)="handleLinkClick($event, page)"
    >
      {{ page }}
    </button>

    <!-- Last Ellipsis -->
    <span 
      *ngIf="showFirstLast && showLastLink && pages[pages.length - 1] < lastPage - 1"
      class="px-2 py-1 text-gray-500 dark:text-gray-400"
      [attr.aria-hidden]="true"
    >
      {{ ellipsis }}
    </span>

    <!-- Last Page -->
    <button
      *ngIf="showFirstLast && showLastLink && lastPage > 1"
      type="button"
      [ngClass]="[computedLinkClasses, currentPage === lastPage ? computedActiveClasses : '']"
      [disabled]="disabled"
      [attr.aria-current]="currentPage === lastPage ? 'page' : null"
      [attr.aria-label]="'Go to page ' + lastPage"
      (click)="handleLinkClick($event, lastPage)"
    >
      {{ lastPage }}
    </button>

    <!-- Next Button -->
    <button 
      *ngIf="showPrevNext"
      type="button"
      [ngClass]="computedButtonClasses"
      [disabled]="disabled || isLastPage"
      [attr.aria-label]="nextLabel"
      (click)="handleLinkClick($event, currentPage + 1)"
    >
      <base-icon
        [icon]="nextIcon"
        [ngClass]="size === 'xs' ? 'w-3 h-3' : size === 'sm' ? 'w-4 h-4' : size === 'lg' ? 'w-6 h-6' : size === 'xl' ? 'w-7 h-7' : 'w-5 h-5'"
      ></base-icon>
      <span class="sr-only">{{ nextLabel }}</span>
    </button>
    
    <ng-content select="[slot='after-pagination']"></ng-content>
  </div>

  <!-- Legacy Template (Hidden, for backward compatibility) -->
  <div class="hidden legacy-pagination-wrapper">
    <div
      class="nui-pagination"
      [ngClass]="[
        rounded && radiuses[rounded],
        color && colors[color],
        classes.wrapper
      ]"
    >
      <ul
        class="nui-pagination-list"
        [ngClass]="[rounded && radiuses[rounded], classes.list]"
      >
        <ng-content select="[slot='before-pagination']"></ng-content>
        <!-- Legacy Link -->
        <li>
          <a
            [routerLink]="paginatedLink(1)"
            tabindex="0"
            class="nui-pagination-link"
            [ngClass]="[
              currentPage === 1 && 'nui-active',
              rounded && radiuses[rounded],
              classes.link
            ]"
            (keydown.space)="$any($event.target).click()"
            (click)="handleLinkClick($event, 1)"
          >
            1
          </a>
        </li>

        <!-- Legacy Ellipsis -->
        <li *ngIf="showLastLink && pages.length > 0 && pages[0] > 2">
          <span
            class="nui-pagination-ellipsis"
            [ngClass]="[rounded && radiuses[rounded], classes.ellipsis]"
          >
            {{ ellipsis }}
          </span>
        </li>

        <!-- Legacy Link -->
        <li *ngFor="let page of pages">
          <a
            [routerLink]="paginatedLink(page)"
            tabindex="0"
            [attr.aria-current]="currentPage === page ? 'page' : null"
            class="nui-pagination-link"
            [ngClass]="[
              currentPage === page && 'nui-active',
              rounded && radiuses[rounded],
              classes.link
            ]"
            (keydown.space)="$any($event.target).click()"
            (click)="handleLinkClick($event, page)"
          >
            {{ page }}
          </a>
        </li>

        <!-- Legacy Ellipsis -->
        <li *ngIf="showLastLink && pages[pages.length - 1] < lastPage - 1">
          <span
            class="nui-pagination-ellipsis"
            [ngClass]="[rounded && radiuses[rounded], classes.ellipsis]"
          >
            {{ ellipsis }}
          </span>
        </li>

        <!-- Legacy Link -->
        <li *ngIf="showLastLink">
          <a
            [routerLink]="paginatedLink(lastPage)"
            tabindex="0"
            class="nui-pagination-link"
            [ngClass]="[
              currentPage === lastPage && 'nui-active',
              rounded && radiuses[rounded],
              classes.link
            ]"
            (keydown.space)="$any($event.target).click()"
            (click)="handleLinkClick($event, lastPage)"
          >
            {{ lastPage }}
          </a>
        </li>
        <ng-content select="[slot='after-pagination']"></ng-content>
      </ul>

      <div
        class="nui-pagination-buttons"
        [ngClass]="[rounded && radiuses[rounded], classes.buttons]"
      >
        <ng-content select="[slot='before-navigation']"></ng-content>

        <!-- Legacy Previous -->
        <a
          [routerLink]="paginatedLink(currentPage - 1)"
          tabindex="0"
          class="nui-pagination-button"
          [ngClass]="classes.button"
          (keydown.space)="$any($event.target).click()"
          (click)="handleLinkClick($event, currentPage - 1)"
        >
          <ng-content select="[slot='previous-icon']"></ng-content>
          <base-icon
            *ngIf="!hasContent('previous-icon')"
            [icon]="previousIcon"
            class="pagination-button-icon"
          ></base-icon>
        </a>

        <!-- Legacy Next -->
        <a
          [routerLink]="paginatedLink(currentPage + 1)"
          tabindex="0"
          class="nui-pagination-button"
          [ngClass]="classes.button"
          (keydown.space)="$any($event.target).click()"
          (click)="handleLinkClick($event, currentPage + 1)"
        >
          <ng-content select="[slot='next-icon']"></ng-content>
          <base-icon
            *ngIf="!hasContent('next-icon')"
            [icon]="nextIcon"
            class="pagination-button-icon"
          ></base-icon>
        </a>
        <ng-content select="[slot='after-navigation']"></ng-content>
      </div>
    </div>
  </div>
</div>

import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { NgClass, NgIf } from '@angular/common';

@Component({
  selector: 'base-button-close',
  templateUrl: './button-close.component.html',
  styleUrls: ['./button-close.component.css'],
  standalone: true,
  imports: [NgClass, NgIf]
})
export class ButtonCloseComponent implements OnInit {
  // Standard inputs
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'ghost' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'full';

  // Component-specific inputs
  @Input() disabled: boolean = false;
  @Input() ariaLabel: string = 'Close';
  @Input() showIcon: boolean = true;
  @Input() iconType: 'x' | 'times' | 'cross' = 'x';

  // Legacy inputs for backward compatibility
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() color?: 'default' | 'default-contrast' | 'muted' | 'muted-contrast' | 'primary' | 'info' | 'success' | 'warning' | 'danger' | 'none';

  // Events
  @Output() closed = new EventEmitter<Event>();

  ngOnInit(): void {
    // Component initialization
  }

  get computedClasses(): string {
    const baseClasses = 'inline-flex items-center justify-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed select-none';
    
    const sizeClasses = {
      xs: 'h-6 w-6 text-xs',
      sm: 'h-8 w-8 text-sm',
      md: 'h-10 w-10 text-base',
      lg: 'h-12 w-12 text-lg',
      xl: 'h-14 w-14 text-xl'
    };

    const variantClasses = {
      default: 'bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-800 focus:ring-gray-500',
      primary: 'bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-800 focus:ring-blue-500',
      secondary: 'bg-gray-100 text-gray-600 hover:bg-gray-200 hover:text-gray-800 focus:ring-gray-500',
      success: 'bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-800 focus:ring-green-500',
      warning: 'bg-yellow-100 text-yellow-600 hover:bg-yellow-200 hover:text-yellow-800 focus:ring-yellow-500',
      danger: 'bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-800 focus:ring-red-500',
      ghost: 'bg-transparent text-gray-500 hover:bg-gray-100 hover:text-gray-700 focus:ring-gray-500'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    // Legacy class support
    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  private getLegacyClasses(): string {
    // Support legacy color input
    const legacyColors = {
      'default': 'text-gray-600 hover:text-gray-800',
      'default-contrast': 'text-gray-800 hover:text-gray-900',
      'muted': 'text-gray-400 hover:text-gray-600',
      'muted-contrast': 'text-gray-600 hover:text-gray-800',
      'primary': 'text-blue-600 hover:text-blue-800',
      'info': 'text-blue-500 hover:text-blue-700',
      'success': 'text-green-600 hover:text-green-800',
      'warning': 'text-yellow-600 hover:text-yellow-800',
      'danger': 'text-red-600 hover:text-red-800',
      'none': ''
    };

    return this.color ? legacyColors[this.color] || '' : '';
  }

  get iconSvg(): string {
    const iconPaths = {
      x: 'M6 6l12 12M6 18L18 6',
      times: 'M6 6l12 12M6 18L18 6',
      cross: 'M6 6l12 12M6 18L18 6'
    };

    return iconPaths[this.iconType];
  }

  onClick(event: Event): void {
    if (!this.disabled) {
      this.closed.emit(event);
    }
  }
}

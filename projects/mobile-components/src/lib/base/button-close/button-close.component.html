<button 
  type="button" 
  [ngClass]="computedClasses"
  [disabled]="disabled"
  (click)="onClick($event)"
  [attr.aria-label]="ariaLabel"
  [attr.aria-disabled]="disabled">
  
  <!-- Close icon SVG -->
  <svg 
    *ngIf="showIcon"
    viewBox="0 0 24 24" 
    fill="none" 
    stroke="currentColor" 
    stroke-width="2" 
    stroke-linecap="round" 
    stroke-linejoin="round"
    class="w-full h-full">
    <path [attr.d]="iconSvg"></path>
  </svg>

  <!-- Fallback content if no icon -->
  <span *ngIf="!showIcon" class="font-medium">×</span>
</button>

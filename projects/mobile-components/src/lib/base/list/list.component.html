<!-- Enhanced List component with dynamic items -->
<div [ngClass]="computedClasses" [attr.aria-label]="'List with ' + items.length + ' items'">
  <!-- Loading state -->
  <div *ngIf="loading" class="flex items-center justify-center py-8">
    <svg class="animate-spin h-8 w-8 text-gray-400" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    <span class="ml-2 text-gray-500">Loading...</span>
  </div>

  <!-- Empty state -->
  <div *ngIf="!loading && items.length === 0" class="flex flex-col items-center justify-center py-12 text-gray-500">
    <base-icon *ngIf="emptyIcon" [icon]="emptyIcon" class="h-12 w-12 mb-4 text-gray-300"></base-icon>
    <p class="text-lg font-medium">{{ emptyText }}</p>
  </div>

  <!-- List content -->
  <ng-container *ngIf="!loading && items.length > 0" [ngSwitch]="ordered">
    <!-- Ordered list -->
    <ol *ngSwitchCase="true" [ngClass]="listClasses" class="list-decimal list-inside">
      <li
        *ngFor="let item of items; let i = index; trackBy: trackByFn"
        class="list-item"
        [ngClass]="getItemClasses(item)"
        [attr.aria-selected]="selectable ? isSelected(item) : null"
        [attr.aria-disabled]="item.disabled"
        (click)="onItemClick(item, $event)"
        (mouseenter)="onItemHover(item)"
      >
        <ng-container *ngTemplateOutlet="itemTemplate; context: { $implicit: item, index: i }"></ng-container>
      </li>
    </ol>

    <!-- Unordered list -->
    <ul *ngSwitchCase="false" [ngClass]="listClasses">
      <li
        *ngFor="let item of items; let i = index; trackBy: trackByFn"
        class="list-item"
        [ngClass]="getItemClasses(item)"
        [attr.aria-selected]="selectable ? isSelected(item) : null"
        [attr.aria-disabled]="item.disabled"
        (click)="onItemClick(item, $event)"
        (mouseenter)="onItemHover(item)"
      >
        <ng-container *ngTemplateOutlet="itemTemplate; context: { $implicit: item, index: i }"></ng-container>
      </li>
    </ul>
  </ng-container>

  <!-- Content projection for custom items -->
  <ng-content></ng-content>
</div>

<!-- Item template -->
<ng-template #itemTemplate let-item let-index="index">
  <div class="flex items-center justify-between w-full" [class.py-3]="!dense" [class.py-1]="dense">
    <!-- Left content -->
    <div class="flex items-center flex-1 min-w-0">
      <!-- Selection checkbox -->
      <input
        *ngIf="selectable && multiSelect"
        type="checkbox"
        [checked]="isSelected(item)"
        [disabled]="item.disabled"
        class="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
        (click)="$event.stopPropagation()"
        (change)="onItemClick(item, $event)"
      >

      <!-- Selection radio -->
      <input
        *ngIf="selectable && !multiSelect"
        type="radio"
        [checked]="isSelected(item)"
        [disabled]="item.disabled"
        [name]="'list-' + item_id"
        class="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
        (click)="$event.stopPropagation()"
        (change)="onItemClick(item, $event)"
      >

      <!-- Avatar -->
      <img
        *ngIf="item.avatar"
        [src]="item.avatar"
        [alt]="item.text"
        class="h-10 w-10 rounded-full mr-3 object-cover"
      >

      <!-- Icon -->
      <base-icon
        *ngIf="item.icon && !item.avatar"
        [icon]="item.icon"
        class="h-5 w-5 mr-3 text-gray-400"
      >
      </base-icon>

      <!-- Text content -->
      <div class="flex-1 min-w-0">
        <p class="font-medium truncate" [class.text-gray-400]="item.disabled">
          {{ item.text }}
        </p>
        <p *ngIf="item.subtitle" class="text-sm text-gray-500 truncate" [class.text-gray-300]="item.disabled">
          {{ item.subtitle }}
        </p>
      </div>
    </div>

    <!-- Right content -->
    <div class="flex items-center space-x-2">
      <!-- Badge -->
      <span
        *ngIf="item.badge"
        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
        [class.bg-gray-50]="item.disabled"
        [class.text-gray-400]="item.disabled"
      >
        {{ item.badge }}
      </span>

      <!-- Navigation arrow for links -->
      <svg
        *ngIf="item.href || item.routerLink"
        class="h-4 w-4 text-gray-400"
        fill="none"
        stroke="currentColor"
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
      </svg>
    </div>
  </div>

  <!-- Divider -->
  <hr *ngIf="divided && !item.divider && index < items.length - 1" class="border-gray-200">
  <hr *ngIf="item.divider" class="border-gray-300 my-2">
</ng-template>

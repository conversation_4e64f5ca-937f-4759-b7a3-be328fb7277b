import { Component, Input, Output, EventEmitter, OnInit, Inject, Optional, ViewEncapsulation, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

export interface ListItem {
  id: string | number;
  text: string;
  subtitle?: string;
  icon?: string;
  avatar?: string;
  badge?: string | number;
  disabled?: boolean;
  divider?: boolean;
  href?: string;
  routerLink?: string | any[];
}

@Component({
  selector: 'base-list',
  templateUrl: './list.component.html',
  styleUrls: ['./list.component.css'],
  encapsulation: ViewEncapsulation.None,
  standalone: true,
  imports: [CommonModule, IconComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class ListComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Legacy inputs (preserved for backward compatibility)
  @Input() ordered: boolean = false;
  @Input() media?: boolean;
  @Input() item_id: string = Math.random().toString(36).substring(7);

  // Enhanced inputs
  @Input() items: ListItem[] = [
    { id: 1, text: 'Sample List Item 1', subtitle: 'This is a sample subtitle', icon: 'star' },
    { id: 2, text: 'Sample List Item 2', subtitle: 'Another example item', icon: 'heart' },
    { id: 3, text: 'Sample List Item 3', subtitle: 'Third sample item', icon: 'bookmark', badge: '3' }
  ];
  @Input() divided: boolean = true;
  @Input() bordered: boolean = false;
  @Input() hoverable: boolean = true;
  @Input() selectable: boolean = false;
  @Input() multiSelect: boolean = false;
  @Input() dense: boolean = false;
  @Input() flush: boolean = false;
  @Input() loading: boolean = false;
  @Input() emptyText: string = 'No items found';
  @Input() emptyIcon?: string = 'inbox';

  // Events
  @Output() itemClick = new EventEmitter<ListItem>();
  @Output() itemSelect = new EventEmitter<ListItem[]>();
  @Output() itemHover = new EventEmitter<ListItem>();

  selectedItems: ListItem[] = [];
  
  constructor(@Optional() @Inject('componentProperties') public properties: any) {}
  
  ngOnInit(): void {
    if (this.properties) {
      // Map properties from injected properties
      this.className = this.properties.className || this.className;
      this.size = this.properties.size || this.size;
      this.variant = this.properties.variant || this.variant;
      this.rounded = this.properties.rounded || this.rounded;
      this.items = this.properties.items || this.items;
      this.ordered = this.properties.ordered || this.ordered;
      this.divided = this.properties.divided || this.divided;
      this.bordered = this.properties.bordered || this.bordered;
      this.hoverable = this.properties.hoverable || this.hoverable;
      this.selectable = this.properties.selectable || this.selectable;
      this.multiSelect = this.properties.multiSelect || this.multiSelect;
      this.dense = this.properties.dense || this.dense;
      this.flush = this.properties.flush || this.flush;
      this.loading = this.properties.loading || this.loading;
      this.emptyText = this.properties.emptyText || this.emptyText;
      this.emptyIcon = this.properties.emptyIcon || this.emptyIcon;
    }
  }

  get computedClasses(): string {
    const baseClasses = 'transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'bg-white text-gray-900',
      primary: 'bg-blue-50 text-blue-900',
      secondary: 'bg-gray-50 text-gray-900',
      success: 'bg-green-50 text-green-900',
      warning: 'bg-yellow-50 text-yellow-900',
      danger: 'bg-red-50 text-red-900'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const stateClasses = [];
    if (this.bordered) stateClasses.push('border border-gray-200');
    if (this.flush) stateClasses.push('border-x-0 rounded-none');
    if (this.dense) stateClasses.push('space-y-0');

    // Legacy support
    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      ...stateClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get listClasses(): string {
    const baseClass = 'nui-list';
    const mediaClass = this.hasMedia ? 'nui-list-media' : '';
    const orderedClass = this.ordered ? 'nui-list-ol' : 'nui-list-ul';
    const nonMediaClass = !this.hasMedia ? `nui-list-base ${orderedClass}` : '';

    return [baseClass, mediaClass, nonMediaClass].filter(Boolean).join(' ');
  }

  private get hasMedia(): boolean {
    return this.media !== undefined ? this.media : false;
  }

  private getLegacyClasses(): string {
    return this.listClasses;
  }

  isSelected(item: ListItem): boolean {
    return this.selectedItems.some(selected => selected.id === item.id);
  }

  onItemClick(item: ListItem, event: Event): void {
    if (item.disabled) return;

    this.itemClick.emit(item);

    if (this.selectable) {
      if (this.multiSelect) {
        const index = this.selectedItems.findIndex(selected => selected.id === item.id);
        if (index > -1) {
          this.selectedItems.splice(index, 1);
        } else {
          this.selectedItems.push(item);
        }
      } else {
        this.selectedItems = this.isSelected(item) ? [] : [item];
      }
      this.itemSelect.emit([...this.selectedItems]);
    }
  }

  onItemHover(item: ListItem): void {
    if (!item.disabled) {
      this.itemHover.emit(item);
    }
  }

  trackByFn(index: number, item: ListItem): any {
    return item.id;
  }

  getItemClasses(item: ListItem): string {
    const baseClasses = 'transition-all duration-200';
    const hoverClasses = this.hoverable && !item.disabled ? 'hover:bg-gray-50 cursor-pointer' : '';
    const disabledClasses = item.disabled ? 'opacity-50 cursor-not-allowed' : '';
    const selectedClasses = this.selectable && this.isSelected(item) ? 'bg-blue-50 border-blue-200' : '';
    const borderedClasses = this.bordered ? 'border-b border-gray-200 last:border-b-0' : '';

    return [
      baseClasses,
      hoverClasses,
      disabledClasses,
      selectedClasses,
      borderedClasses
    ].filter(Boolean).join(' ');
  }
}

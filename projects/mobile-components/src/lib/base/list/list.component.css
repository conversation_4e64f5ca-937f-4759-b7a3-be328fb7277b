/* Enhanced List Component Styles */
:host {
  display: block;
}

/* List item animations */
.list-item {
  position: relative;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  margin-bottom: 0.125rem;
}

.list-item:hover {
  transform: translateX(2px);
}

/* Selection states */
.list-item[aria-selected="true"] {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
  border-left: 4px solid #3b82f6;
}

/* Focus styles for accessibility */
.list-item:focus-visible {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* Hover effects for interactive items */
.list-item.hover\:bg-gray-50:hover {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Disabled item styles */
.list-item[aria-disabled="true"] {
  pointer-events: none;
}

/* Dense list styling */
.dense .list-item {
  padding: 0.25rem 1rem;
}

/* Bordered list styling */
.bordered {
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.bordered .list-item {
  margin-bottom: 0;
  border-radius: 0;
}

/* Flush list styling */
.flush {
  border-left: none;
  border-right: none;
  border-radius: 0;
}

/* Loading animation */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Badge animations */
.list-item:hover .badge {
  transform: scale(1.1);
}

/* Avatar styling */
.list-item img {
  border: 2px solid transparent;
  transition: border-color 0.2s ease-in-out;
}

.list-item:hover img {
  border-color: #e5e7eb;
}

/* Icon styling */
.list-item base-icon {
  transition: color 0.2s ease-in-out;
}

.list-item:hover base-icon {
  color: #3b82f6;
}
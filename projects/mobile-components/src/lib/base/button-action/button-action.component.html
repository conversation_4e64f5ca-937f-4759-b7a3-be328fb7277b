<ng-container [ngSwitch]="is">
  <!-- Button element -->
  <button 
    *ngSwitchCase="'button'"
    [type]="type"
    [disabled]="disabled || loading"
    [ngClass]="computedClasses"
    (click)="onClick($event)"
    [attr.aria-label]="text"
    [attr.aria-disabled]="disabled || loading">
    
    <!-- Loading spinner -->
    <svg 
      *ngIf="loading" 
      class="animate-spin -ml-1 mr-2 h-4 w-4" 
      xmlns="http://www.w3.org/2000/svg" 
      fill="none" 
      viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>

    <!-- Left icon -->
    <ng-container *ngIf="icon && iconPosition === 'left' && !loading">
      <i [class]="icon + ' mr-2'"></i>
    </ng-container>

    <!-- Content or default text -->
    <ng-content></ng-content>
    <span *ngIf="!hasContent">{{ text }}</span>

    <!-- Right icon -->
    <ng-container *ngIf="icon && iconPosition === 'right' && !loading">
      <i [class]="icon + ' ml-2'"></i>
    </ng-container>
  </button>

  <!-- Router link -->
  <a 
    *ngSwitchCase="'router-link'"
    [routerLink]="to"
    [ngClass]="computedClasses"
    [attr.aria-label]="text"
    role="button">
    
    <!-- Left icon -->
    <ng-container *ngIf="icon && iconPosition === 'left'">
      <i [class]="icon + ' mr-2'"></i>
    </ng-container>

    <!-- Content or default text -->
    <ng-content></ng-content>
    <span *ngIf="!hasContent">{{ text }}</span>

    <!-- Right icon -->
    <ng-container *ngIf="icon && iconPosition === 'right'">
      <i [class]="icon + ' ml-2'"></i>
    </ng-container>
  </a>

  <!-- External link -->
  <a 
    *ngSwitchCase="'a'"
    [href]="href"
    [target]="target"
    [rel]="rel"
    [ngClass]="computedClasses"
    [attr.aria-label]="text"
    role="button">
    
    <!-- Left icon -->
    <ng-container *ngIf="icon && iconPosition === 'left'">
      <i [class]="icon + ' mr-2'"></i>
    </ng-container>

    <!-- Content or default text -->
    <ng-content></ng-content>
    <span *ngIf="!hasContent">{{ text }}</span>

    <!-- Right icon -->
    <ng-container *ngIf="icon && iconPosition === 'right'">
      <i [class]="icon + ' ml-2'"></i>
    </ng-container>
  </a>
</ng-container>

import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'base-dropdown-divider',
  templateUrl: './dropdown-divider.component.html',
  styleUrls: ['./dropdown-divider.component.css'],
  standalone: true,
  imports: [CommonModule]
})
export class DropdownDividerComponent {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'none';

  // Component-specific inputs
  @Input() spacing: 'none' | 'xs' | 'sm' | 'md' | 'lg' = 'sm';
  @Input() thickness: 'thin' | 'medium' | 'thick' = 'thin';
  @Input() style: 'solid' | 'dashed' | 'dotted' = 'solid';
  @Input() opacity: number = 1;

  get computedClasses(): string {
    const baseClasses = 'border-t transition-all duration-200';
    
    const sizeClasses = {
      xs: '',
      sm: '',
      md: '',
      lg: '',
      xl: ''
    };

    const variantClasses = {
      default: 'border-gray-200 dark:border-gray-700',
      primary: 'border-blue-200 dark:border-blue-700',
      secondary: 'border-gray-300 dark:border-gray-600',
      success: 'border-green-200 dark:border-green-700',
      warning: 'border-yellow-200 dark:border-yellow-700',
      danger: 'border-red-200 dark:border-red-700'
    };

    const spacingClasses = {
      none: 'my-0',
      xs: 'my-1',
      sm: 'my-2',
      md: 'my-3',
      lg: 'my-4'
    };

    const thicknessClasses = {
      thin: 'border-t',
      medium: 'border-t-2',
      thick: 'border-t-4'
    };

    const styleClasses = {
      solid: 'border-solid',
      dashed: 'border-dashed',
      dotted: 'border-dotted'
    };

    const roundedClasses = {
      none: '',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    // Legacy support
    const legacyClasses = 'nui-dropdown-divider';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      spacingClasses[this.spacing],
      thicknessClasses[this.thickness],
      styleClasses[this.style],
      roundedClasses[this.rounded],
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedStyles(): { [key: string]: any } {
    return {
      opacity: this.opacity
    };
  }
}

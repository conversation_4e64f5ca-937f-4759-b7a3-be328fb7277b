/* Mobile Device Frame Component Styles */

/* Host element */
:host {
  display: inline-block;
}

/* Device frame animations */
.device-frame {
  transition: all 0.3s ease-out;
}

/* Hover effects for interactive devices */
.cursor-pointer:hover {
  filter: brightness(1.05);
}

/* Screen glow effect */
.screen-glow {
  box-shadow: 
    inset 0 0 20px rgba(255, 255, 255, 0.1),
    0 0 20px rgba(0, 0, 0, 0.1);
}

/* Notch styling */
.notch {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
}

/* Home indicator pulse */
.home-indicator {
  animation: homeIndicatorPulse 3s ease-in-out infinite;
}

@keyframes homeIndicatorPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

/* Device type specific styles */
.device-iphone {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
}

.device-android {
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
}

.device-tablet {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
}

.device-laptop {
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
}

.device-desktop {
  background: linear-gradient(135deg, #9ca3af 0%, #d1d5db 100%);
}

/* Screen reflections */
.screen-reflection {
  position: relative;
  overflow: hidden;
}

.screen-reflection::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transform: skewX(-20deg);
  animation: screenReflection 3s ease-in-out infinite;
  pointer-events: none;
  z-index: 1;
}

@keyframes screenReflection {
  0% { left: -100%; }
  50% { left: 100%; }
  100% { left: 100%; }
}

/* Loading shimmer effect */
.loading-shimmer {
  background: linear-gradient(
    90deg,
    #f0f0f0 25%,
    #e0e0e0 50%,
    #f0f0f0 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Orientation transition */
.orientation-transition {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Scale transitions */
.scale-transition {
  transition: transform 0.3s ease-out;
}

/* Focus styles for accessibility */
[role="button"]:focus,
.cursor-pointer:focus {
  outline: none;
  ring: 2px;
  ring-color: rgb(59 130 246 / 0.5);
  ring-offset: 2px;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .bg-gray-50 {
    background-color: rgb(31 41 55);
  }
  
  .bg-white {
    background-color: rgb(17 24 39);
  }
  
  .text-gray-900 {
    color: rgb(243 244 246);
  }
  
  .text-gray-600 {
    color: rgb(156 163 175);
  }
  
  .border-gray-200 {
    border-color: rgb(75 85 99);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .device-frame {
    border: 2px solid currentColor;
  }
  
  .screen-container {
    border: 1px solid currentColor;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  :host {
    break-inside: avoid;
  }
  
  .device-frame {
    border: 2px solid #000;
    background: white !important;
  }
  
  .screen-container {
    border: 1px solid #000;
    background: white !important;
  }
}
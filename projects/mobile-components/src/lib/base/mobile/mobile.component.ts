import { Component, Input, Output, EventEmitter, OnInit, Inject, Optional, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

export type DeviceType = 'iphone' | 'android' | 'tablet' | 'laptop' | 'desktop';
export type DeviceOrientation = 'portrait' | 'landscape';

@Component({
  selector: 'base-mobile',
  templateUrl: './mobile.component.html',
  styleUrls: ['./mobile.component.css'],
  standalone: true,
  imports: [CommonModule, IconComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class MobileComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Mobile device specific inputs
  @Input() deviceType: DeviceType = 'iphone';
  @Input() orientation: DeviceOrientation = 'portrait';
  @Input() showFrame: boolean = true;
  @Input() showNotch: boolean = true;
  @Input() showHomeIndicator: boolean = true;
  @Input() frameColor: string = '#1f2937'; // Default dark gray
  @Input() screenBgColor: string = '#ffffff';
  @Input() title: string = 'Mobile Preview';
  @Input() subtitle: string = '';
  @Input() showHeader: boolean = false;
  @Input() headerColor: string = '#f3f4f6';
  @Input() interactive: boolean = false;
  @Input() loading: boolean = false;
  @Input() scale: number = 1;
  @Input() shadow: boolean = true;
  @Input() borderless: boolean = false;

  // Events
  @Output() deviceClick = new EventEmitter<Event>();
  @Output() screenClick = new EventEmitter<Event>();

  constructor(@Optional() @Inject('componentProperties') public properties: any) {}

  ngOnInit(): void {
    if (this.properties) {
      // Map properties from injected properties
      this.className = this.properties.className || this.className;
      this.size = this.properties.size || this.size;
      this.variant = this.properties.variant || this.variant;
      this.rounded = this.properties.rounded || this.rounded;
      this.deviceType = this.properties.deviceType || this.deviceType;
      this.orientation = this.properties.orientation || this.orientation;
      this.showFrame = this.properties.showFrame ?? this.showFrame;
      this.showNotch = this.properties.showNotch ?? this.showNotch;
      this.showHomeIndicator = this.properties.showHomeIndicator ?? this.showHomeIndicator;
      this.frameColor = this.properties.frameColor || this.frameColor;
      this.screenBgColor = this.properties.screenBgColor || this.screenBgColor;
      this.title = this.properties.title || this.title;
      this.subtitle = this.properties.subtitle || this.subtitle;
      this.showHeader = this.properties.showHeader ?? this.showHeader;
      this.headerColor = this.properties.headerColor || this.headerColor;
      this.interactive = this.properties.interactive ?? this.interactive;
      this.loading = this.properties.loading ?? this.loading;
      this.scale = this.properties.scale || this.scale;
      this.shadow = this.properties.shadow ?? this.shadow;
      this.borderless = this.properties.borderless ?? this.borderless;
    }
  }

  get computedClasses(): string {
    const baseClasses = 'relative inline-flex flex-col transition-all duration-200';
    
    const sizeClasses = {
      xs: this.orientation === 'portrait' ? 'w-40 h-80' : 'w-80 h-40',
      sm: this.orientation === 'portrait' ? 'w-48 h-96' : 'w-96 h-48',
      md: this.orientation === 'portrait' ? 'w-56 h-112' : 'w-112 h-56',
      lg: this.orientation === 'portrait' ? 'w-64 h-128' : 'w-128 h-64',
      xl: this.orientation === 'portrait' ? 'w-72 h-144' : 'w-144 h-72'
    };

    const variantClasses = {
      default: '',
      primary: 'ring-2 ring-blue-500 ring-opacity-50',
      secondary: 'ring-2 ring-gray-500 ring-opacity-50',
      success: 'ring-2 ring-green-500 ring-opacity-50',
      warning: 'ring-2 ring-yellow-500 ring-opacity-50',
      danger: 'ring-2 ring-red-500 ring-opacity-50'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const shadowClasses = this.shadow ? 'shadow-xl' : '';
    const interactiveClasses = this.interactive ? 'cursor-pointer hover:scale-105 transition-transform' : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      roundedClasses[this.rounded],
      shadowClasses,
      interactiveClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get frameClasses(): string {
    if (!this.showFrame || this.borderless) return '';
    
    const baseClasses = 'relative p-2';
    const deviceClasses = {
      iphone: this.orientation === 'portrait' ? 'rounded-3xl' : 'rounded-2xl',
      android: this.orientation === 'portrait' ? 'rounded-2xl' : 'rounded-xl',
      tablet: 'rounded-xl',
      laptop: 'rounded-lg',
      desktop: 'rounded-md'
    };

    return [
      baseClasses,
      deviceClasses[this.deviceType]
    ].filter(Boolean).join(' ');
  }

  get screenClasses(): string {
    const baseClasses = 'relative w-full h-full overflow-hidden transition-colors duration-200';
    
    const deviceClasses = {
      iphone: this.orientation === 'portrait' ? 'rounded-2xl' : 'rounded-xl',
      android: this.orientation === 'portrait' ? 'rounded-xl' : 'rounded-lg',
      tablet: 'rounded-lg',
      laptop: 'rounded-md',
      desktop: 'rounded-sm'
    };

    const loadingClasses = this.loading ? 'animate-pulse bg-gray-200' : '';

    return [
      baseClasses,
      deviceClasses[this.deviceType],
      loadingClasses
    ].filter(Boolean).join(' ');
  }

  get notchClasses(): string {
    if (!this.showNotch || this.deviceType !== 'iphone' || this.orientation === 'landscape') return '';
    
    return 'absolute top-0 left-1/2 transform -translate-x-1/2 w-24 h-6 bg-black rounded-b-2xl z-10';
  }

  get homeIndicatorClasses(): string {
    if (!this.showHomeIndicator || (this.deviceType !== 'iphone' && this.deviceType !== 'android')) return '';
    
    const baseClasses = 'absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-30 z-10';
    const orientationClasses = this.orientation === 'portrait' ? 'w-16 h-1 rounded-full' : 'w-12 h-1 rounded-full';
    
    return [baseClasses, orientationClasses].join(' ');
  }

  get headerClasses(): string {
    if (!this.showHeader) return '';
    
    return 'p-4 border-b border-gray-200 bg-gray-50';
  }

  get deviceIcon(): string {
    const icons = {
      iphone: 'device-mobile',
      android: 'device-mobile',
      tablet: 'device-tablet',
      laptop: 'computer-desktop',
      desktop: 'computer-desktop'
    };
    
    return icons[this.deviceType];
  }

  onDeviceClick(event: Event): void {
    if (!this.interactive) return;
    this.deviceClick.emit(event);
  }

  onScreenClick(event: Event): void {
    if (!this.interactive) return;
    event.stopPropagation();
    this.screenClick.emit(event);
  }
}

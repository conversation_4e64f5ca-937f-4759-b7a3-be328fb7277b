<!-- Mobile Device Frame Component Template -->
<div 
  [class]="computedClasses"
  [style.transform]="'scale(' + scale + ')'"
  [style.transform-origin]="'center'"
  (click)="onDeviceClick($event)"
>
  <!-- Device Frame -->
  <div 
    *ngIf="showFrame && !borderless"
    [class]="frameClasses"
    [style.background-color]="frameColor"
  >
    <!-- Screen Container -->
    <div 
      [class]="screenClasses"
      [style.background-color]="screenBgColor"
      (click)="onScreenClick($event)"
    >
      <!-- Notch (iPhone only) -->
      <div 
        *ngIf="showNotch && deviceType === 'iphone' && orientation === 'portrait'"
        [class]="notchClasses"
      ></div>

      <!-- Header -->
      <div 
        *ngIf="showHeader"
        [class]="headerClasses"
        [style.background-color]="headerColor"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-2">
            <base-icon 
              [name]="deviceIcon"
              size="sm"
              class="text-gray-500"
            ></base-icon>
            <div>
              <div class="font-medium text-sm text-gray-900">{{ title }}</div>
              <div *ngIf="subtitle" class="text-xs text-gray-500">{{ subtitle }}</div>
            </div>
          </div>
          <div class="flex items-center gap-2">
            <div class="text-xs text-gray-500">{{ orientation }}</div>
            <div class="w-2 h-2 rounded-full bg-green-500" 
                 [class.bg-red-500]="loading"
                 [class.animate-pulse]="loading"
            ></div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div 
        *ngIf="loading"
        class="absolute inset-0 flex items-center justify-center bg-gray-50 bg-opacity-90 z-20"
      >
        <div class="flex flex-col items-center gap-3">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <div class="text-sm text-gray-600">Loading...</div>
        </div>
      </div>

      <!-- Content Area -->
      <div class="relative flex-1 overflow-hidden">
        <ng-content>
          <!-- Default content when nothing is projected -->
          <div class="p-6 h-full flex flex-col items-center justify-center text-center bg-gradient-to-br from-blue-50 to-purple-50">
            <base-icon 
              [name]="deviceIcon"
              size="xl"
              class="text-gray-400 mb-4"
            ></base-icon>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ title }}</h3>
            <p class="text-sm text-gray-600 mb-4">
              {{ subtitle || 'Project your content here using content projection.' }}
            </p>
            <div class="flex items-center gap-2 text-xs text-gray-500">
              <span class="inline-flex items-center gap-1">
                <div class="w-2 h-2 rounded-full bg-blue-500"></div>
                {{ deviceType | titlecase }}
              </span>
              <span>•</span>
              <span>{{ orientation | titlecase }}</span>
            </div>
          </div>
        </ng-content>
      </div>

      <!-- Home Indicator -->
      <div 
        *ngIf="showHomeIndicator && (deviceType === 'iphone' || deviceType === 'android')"
        [class]="homeIndicatorClasses"
      ></div>
    </div>
  </div>

  <!-- Borderless Mode (content only) -->
  <div 
    *ngIf="borderless"
    [class]="screenClasses"
    [style.background-color]="screenBgColor"
    (click)="onScreenClick($event)"
  >
    <ng-content>
      <div class="p-6 h-full flex items-center justify-center text-center">
        <div>
          <base-icon 
            [name]="deviceIcon"
            size="xl"
            class="text-gray-400 mb-4"
          ></base-icon>
          <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ title }}</h3>
          <p class="text-sm text-gray-600">Borderless {{ deviceType }} preview</p>
        </div>
      </div>
    </ng-content>
  </div>
</div>

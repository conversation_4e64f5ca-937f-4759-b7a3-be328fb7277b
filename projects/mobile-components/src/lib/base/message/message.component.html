<!-- Message Component Template -->
<div 
  [class]="computedClasses"
  (click)="onClick($event)"
  role="alert"
  [attr.aria-live]="type === 'error' ? 'assertive' : 'polite'"
  [attr.aria-labelledby]="title ? 'message-title-' + size : null"
  [attr.aria-describedby]="'message-content-' + size"
>
  <!-- Icon -->
  <div *ngIf="showIcon" [class]="iconClasses">
    <base-icon 
      [name]="iconName"
      [size]="size === 'xs' ? 'sm' : size === 'sm' ? 'md' : size === 'md' ? 'lg' : 'xl'"
    ></base-icon>
  </div>

  <!-- Content -->
  <div class="flex-1 min-w-0">
    <!-- Title -->
    <div 
      *ngIf="title"
      [class]="titleClasses"
      [id]="'message-title-' + size"
    >
      {{ title }}
    </div>

    <!-- Message Content -->
    <div 
      [class]="messageClasses"
      [id]="'message-content-' + size"
    >
      <ng-content>{{ message }}</ng-content>
    </div>

    <!-- Timestamp -->
    <div 
      *ngIf="showTimestamp"
      [class]="timestampClasses"
    >
      {{ formattedTimestamp }}
    </div>

    <!-- Action Button -->
    <button
      *ngIf="actionLabel"
      type="button"
      [class]="actionButtonClasses"
      (click)="onActionClick($event)"
    >
      <base-icon 
        *ngIf="actionIcon"
        [name]="actionIcon"
        size="sm"
      ></base-icon>
      {{ actionLabel }}
    </button>
  </div>

  <!-- Dismiss Button -->
  <button
    *ngIf="dismissible"
    type="button"
    [class]="dismissButtonClasses"
    (click)="onDismiss()"
    aria-label="Dismiss message"
  >
    <base-icon 
      name="x-mark"
      size="sm"
    ></base-icon>
  </button>
</div>

import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  Inject,
  Optional,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconComponent } from '../icon/icon.component';

export type MessageType = 'info' | 'success' | 'warning' | 'error' | 'announcement';

@Component({
  selector: 'base-message',
  templateUrl: './message.component.html',
  styleUrls: ['./message.component.css'],
  standalone: true,
  imports: [CommonModule, IconComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class MessageComponent implements OnInit {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Message-specific inputs
  @Input() title: string = '';
  @Input() message: string = 'This is a sample message to demonstrate the message component functionality.';
  @Input() type: MessageType = 'info';
  @Input() icon: string = '';
  @Input() dismissible: boolean = false;
  @Input() autoClose: boolean = false;
  @Input() autoCloseDelay: number = 5000; // milliseconds
  @Input() showIcon: boolean = true;
  @Input() showTimestamp: boolean = false;
  @Input() timestamp: Date = new Date();
  @Input() actionLabel: string = '';
  @Input() actionIcon: string = '';
  @Input() persistent: boolean = false;
  @Input() border: 'none' | 'left' | 'top' | 'right' | 'bottom' | 'all' = 'left';
  @Input() shadow: boolean = true;
  @Input() compact: boolean = false;

  // Events
  @Output() messageClick = new EventEmitter<Event>();
  @Output() dismiss = new EventEmitter<void>();
  @Output() actionClick = new EventEmitter<Event>();

  // Internal state
  private autoCloseTimer?: number;
  
  constructor(@Optional() @Inject('componentProperties') public properties: any) {}

  ngOnInit(): void {
    if (this.properties) {
      // Map properties from injected properties
      this.className = this.properties.className || this.className;
      this.size = this.properties.size || this.size;
      this.variant = this.properties.variant || this.variant;
      this.rounded = this.properties.rounded || this.rounded;
      this.title = this.properties.title || this.title;
      this.message = this.properties.message || this.message;
      this.type = this.properties.type || this.type;
      this.icon = this.properties.icon || this.icon;
      this.dismissible = this.properties.dismissible ?? this.dismissible;
      this.autoClose = this.properties.autoClose ?? this.autoClose;
      this.autoCloseDelay = this.properties.autoCloseDelay || this.autoCloseDelay;
      this.showIcon = this.properties.showIcon ?? this.showIcon;
      this.showTimestamp = this.properties.showTimestamp ?? this.showTimestamp;
      this.actionLabel = this.properties.actionLabel || this.actionLabel;
      this.actionIcon = this.properties.actionIcon || this.actionIcon;
      this.persistent = this.properties.persistent ?? this.persistent;
      this.border = this.properties.border || this.border;
      this.shadow = this.properties.shadow ?? this.shadow;
      this.compact = this.properties.compact ?? this.compact;
    }

    // Set auto-close timer if enabled
    if (this.autoClose && !this.persistent) {
      this.autoCloseTimer = window.setTimeout(() => {
        this.onDismiss();
      }, this.autoCloseDelay);
    }
  }

  ngOnDestroy(): void {
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
    }
  }

  get computedClasses(): string {
    const baseClasses = 'relative flex items-start gap-3 p-4 transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs p-2 gap-2',
      sm: 'text-sm p-3 gap-2',
      md: 'text-base p-4 gap-3',
      lg: 'text-lg p-5 gap-4',
      xl: 'text-xl p-6 gap-4'
    };

    const variantClasses = {
      default: 'bg-gray-50 border-gray-200 text-gray-800',
      primary: 'bg-blue-50 border-blue-200 text-blue-800',
      secondary: 'bg-purple-50 border-purple-200 text-purple-800',
      success: 'bg-green-50 border-green-200 text-green-800',
      warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      danger: 'bg-red-50 border-red-200 text-red-800'
    };

    const typeClasses = {
      info: 'bg-blue-50 border-blue-200 text-blue-800',
      success: 'bg-green-50 border-green-200 text-green-800',
      warning: 'bg-yellow-50 border-yellow-200 text-yellow-800',
      error: 'bg-red-50 border-red-200 text-red-800',
      announcement: 'bg-purple-50 border-purple-200 text-purple-800'
    };

    const roundedClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const borderClasses = {
      none: 'border-0',
      left: 'border-l-4 border-t border-r border-b',
      top: 'border-t-4 border-l border-r border-b',
      right: 'border-r-4 border-t border-l border-b',
      bottom: 'border-b-4 border-t border-l border-r',
      all: 'border-2'
    };

    const shadowClasses = this.shadow ? 'shadow-sm' : '';
    const compactClasses = this.compact ? 'py-2' : '';

    // Use type classes if type is set, otherwise use variant
    const colorClasses = this.type && this.type !== 'info' ? 
      typeClasses[this.type] : variantClasses[this.variant];

    return [
      baseClasses,
      sizeClasses[this.size],
      colorClasses,
      roundedClasses[this.rounded],
      borderClasses[this.border],
      shadowClasses,
      compactClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get iconName(): string {
    if (this.icon) return this.icon;
    
    const typeIcons = {
      info: 'information-circle',
      success: 'check-circle',
      warning: 'exclamation-triangle',
      error: 'x-circle',
      announcement: 'megaphone'
    };

    return typeIcons[this.type] || 'information-circle';
  }

  get iconClasses(): string {
    const baseClasses = 'flex-shrink-0 transition-colors duration-200';
    
    const sizeClasses = {
      xs: 'w-4 h-4',
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6',
      xl: 'w-7 h-7'
    };

    const colorClasses = {
      info: 'text-blue-500',
      success: 'text-green-500',
      warning: 'text-yellow-500',
      error: 'text-red-500',
      announcement: 'text-purple-500'
    };

    return [
      baseClasses,
      sizeClasses[this.size],
      colorClasses[this.type] || 'text-gray-500'
    ].filter(Boolean).join(' ');
  }

  get titleClasses(): string {
    const baseClasses = 'font-semibold leading-tight';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    return [
      baseClasses,
      sizeClasses[this.size]
    ].filter(Boolean).join(' ');
  }

  get messageClasses(): string {
    const baseClasses = 'leading-relaxed';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-sm',
      lg: 'text-base',
      xl: 'text-lg'
    };

    const spacingClasses = this.title ? 'mt-1' : '';

    return [
      baseClasses,
      sizeClasses[this.size],
      spacingClasses
    ].filter(Boolean).join(' ');
  }

  get timestampClasses(): string {
    const baseClasses = 'text-xs opacity-60 mt-1';
    return baseClasses;
  }

  get dismissButtonClasses(): string {
    const baseClasses = 'absolute top-2 right-2 flex items-center justify-center w-6 h-6 rounded-full opacity-60 hover:opacity-100 focus:opacity-100 focus:outline-none transition-opacity duration-150';
    return baseClasses;
  }

  get actionButtonClasses(): string {
    const baseClasses = 'inline-flex items-center gap-1 px-3 py-1 text-sm font-medium rounded transition-colors duration-150 mt-2';
    
    const colorClasses = {
      info: 'text-blue-700 bg-blue-100 hover:bg-blue-200',
      success: 'text-green-700 bg-green-100 hover:bg-green-200',
      warning: 'text-yellow-700 bg-yellow-100 hover:bg-yellow-200',
      error: 'text-red-700 bg-red-100 hover:bg-red-200',
      announcement: 'text-purple-700 bg-purple-100 hover:bg-purple-200'
    };

    return [
      baseClasses,
      colorClasses[this.type] || colorClasses.info
    ].filter(Boolean).join(' ');
  }

  get formattedTimestamp(): string {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(this.timestamp);
  }

  onClick(event: Event): void {
    this.messageClick.emit(event);
  }

  onDismiss(): void {
    if (this.autoCloseTimer) {
      clearTimeout(this.autoCloseTimer);
    }
    this.dismiss.emit();
  }

  onActionClick(event: Event): void {
    event.stopPropagation();
    this.actionClick.emit(event);
  }
}

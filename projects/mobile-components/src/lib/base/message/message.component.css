/* Message Component Styles */

/* Smooth entrance animation */
:host {
  display: block;
  animation: messageEnter 0.3s ease-out;
}

@keyframes messageEnter {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Smooth exit animation */
:host.message-exit {
  animation: messageExit 0.2s ease-in forwards;
}

@keyframes messageExit {
  from {
    opacity: 1;
    transform: translateY(0);
    max-height: 200px;
  }
  to {
    opacity: 0;
    transform: translateY(-8px);
    max-height: 0;
    margin: 0;
    padding: 0;
  }
}

/* Enhanced focus styles for dismiss button */
button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* Hover effects for action button */
button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Auto-close progress indicator */
.auto-close-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: linear-gradient(90deg, currentColor 0%, transparent 100%);
  animation: autoCloseProgress linear;
}

@keyframes autoCloseProgress {
  from { width: 100%; }
  to { width: 0%; }
}

/* Interactive states */
[role="alert"]:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Icon pulse animation for important messages */
.icon-pulse {
  animation: iconPulse 2s infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  [class*="bg-gray-50"] {
    background-color: rgb(31 41 55);
    border-color: rgb(75 85 99);
    color: rgb(229 231 235);
  }
  
  [class*="bg-blue-50"] {
    background-color: rgb(30 58 138);
    border-color: rgb(59 130 246);
    color: rgb(219 234 254);
  }
  
  [class*="bg-green-50"] {
    background-color: rgb(20 83 45);
    border-color: rgb(34 197 94);
    color: rgb(220 252 231);
  }
  
  [class*="bg-yellow-50"] {
    background-color: rgb(133 77 14);
    border-color: rgb(245 158 11);
    color: rgb(254 249 195);
  }
  
  [class*="bg-red-50"] {
    background-color: rgb(127 29 29);
    border-color: rgb(239 68 68);
    color: rgb(254 226 226);
  }
  
  [class*="bg-purple-50"] {
    background-color: rgb(88 28 135);
    border-color: rgb(168 85 247);
    color: rgb(243 232 255);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  [role="alert"] {
    border-width: 2px;
    border-style: solid;
  }
  
  button:focus {
    outline: 3px solid currentColor;
    outline-offset: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  :host {
    animation: none;
  }
}

/* Print styles */
@media print {
  :host {
    break-inside: avoid;
    border: 1px solid #000;
    background: white !important;
    color: black !important;
  }
  
  button {
    display: none;
  }
}
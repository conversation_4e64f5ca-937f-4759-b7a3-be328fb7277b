<ng-container [ngSwitch]="effectiveLevel">
  <!-- Heading levels h1-h6 -->
  <h1 *ngSwitchCase="'h1'" [ngClass]="computedClasses" [attr.id]="item_id">
    {{ effectiveText }}
    <ng-content></ng-content>
  </h1>
  <h2 *ngSwitchCase="'h2'" [ngClass]="computedClasses" [attr.id]="item_id">
    {{ effectiveText }}
    <ng-content></ng-content>
  </h2>
  <h3 *ngSwitchCase="'h3'" [ngClass]="computedClasses" [attr.id]="item_id">
    {{ effectiveText }}
    <ng-content></ng-content>
  </h3>
  <h4 *ngSwitchCase="'h4'" [ngClass]="computedClasses" [attr.id]="item_id">
    {{ effectiveText }}
    <ng-content></ng-content>
  </h4>
  <h5 *ngSwitchCase="'h5'" [ngClass]="computedClasses" [attr.id]="item_id">
    {{ effectiveText }}
    <ng-content></ng-content>
  </h5>
  <h6 *ngSwitchCase="'h6'" [ngClass]="computedClasses" [attr.id]="item_id">
    {{ effectiveText }}
    <ng-content></ng-content>
  </h6>
  
  <!-- Non-semantic elements -->
  <span *ngSwitchCase="'span'" [ngClass]="computedClasses" [attr.id]="item_id">
    {{ effectiveText }}
    <ng-content></ng-content>
  </span>
  
  <!-- Default paragraph case -->
  <p *ngSwitchDefault [ngClass]="computedClasses" [attr.id]="item_id">
    {{ effectiveText }}
    <ng-content></ng-content>
  </p>
</ng-container>
  
  
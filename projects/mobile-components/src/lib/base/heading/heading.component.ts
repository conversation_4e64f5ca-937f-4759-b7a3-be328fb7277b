import { Component, Input, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';

export type HeadingLevel = 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'span' | 'p';
export type HeadingSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl' | '6xl' | '7xl' | '8xl' | '9xl';
export type HeadingWeight = 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold';
export type HeadingLead = 'none' | 'tight' | 'snug' | 'normal' | 'relaxed' | 'loose';

@Component({
  selector: 'base-heading',
  templateUrl: './heading.component.html',
  styleUrls: ['./heading.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class HeadingComponent implements OnInit {
  // Standard inputs for all components
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'none';

  // Typography-specific inputs
  @Input() text: string = 'Heading Text';
  @Input() level: HeadingLevel = 'h2';
  @Input() headingSize: HeadingSize = 'xl';
  @Input() weight: HeadingWeight = 'semibold';
  @Input() lineHeight: HeadingLead = 'normal';
  @Input() color: string = '';
  @Input() align: 'left' | 'center' | 'right' | 'justify' = 'left';
  @Input() transform: 'none' | 'uppercase' | 'lowercase' | 'capitalize' = 'none';
  @Input() decoration: 'none' | 'underline' | 'overline' | 'line-through' = 'none';
  @Input() italic: boolean = false;
  @Input() truncate: boolean = false;

  // Legacy compatibility
  @Input() as: HeadingLevel = 'p'; // Legacy prop name
  @Input() lead: HeadingLead = 'normal'; // Legacy prop name
  @Input() innerText: string = ''; // Legacy prop name
  @Input() item_id: string = Math.random().toString(36).substring(7);

  // Legacy class mappings for backward compatibility
  private sizes = {
    xs: 'nui-heading-xs',
    sm: 'nui-heading-sm',
    md: 'nui-heading-md',
    lg: 'nui-heading-lg',
    xl: 'nui-heading-xl',
    '2xl': 'nui-heading-2xl',
    '3xl': 'nui-heading-3xl',
    '4xl': 'nui-heading-4xl',
    '5xl': 'nui-heading-5xl',
    '6xl': 'nui-heading-6xl',
    '7xl': 'nui-heading-7xl',
    '8xl': 'nui-heading-8xl',
    '9xl': 'nui-heading-9xl',
  };

  private weights = {
    light: 'nui-weight-light',
    normal: 'nui-weight-normal',
    medium: 'nui-weight-medium',
    semibold: 'nui-weight-semibold',
    bold: 'nui-weight-bold',
    extrabold: 'nui-weight-extrabold',
  };

  private leads = {
    none: 'nui-lead-none',
    tight: 'nui-lead-tight',
    snug: 'nui-lead-snug',
    normal: 'nui-lead-normal',
    relaxed: 'nui-lead-relaxed',
    loose: 'nui-lead-loose',
  };

  get effectiveLevel(): HeadingLevel {
    return this.level || this.as;
  }

  get effectiveText(): string {
    return this.text || this.innerText || 'Heading Text';
  }

  get effectiveHeadingSize(): HeadingSize {
    return this.headingSize;
  }

  get effectiveWeight(): HeadingWeight {
    return this.weight;
  }

  get effectiveLineHeight(): HeadingLead {
    return this.lineHeight || this.lead;
  }

  get computedClasses(): string {
    const classes: string[] = [];

    // Base component class
    classes.push('nui-heading');

    // Legacy class support for backward compatibility
    if (this.effectiveHeadingSize && this.sizes[this.effectiveHeadingSize]) {
      classes.push(this.sizes[this.effectiveHeadingSize]);
    }
    if (this.effectiveWeight && this.weights[this.effectiveWeight]) {
      classes.push(this.weights[this.effectiveWeight]);
    }
    if (this.effectiveLineHeight && this.leads[this.effectiveLineHeight]) {
      classes.push(this.leads[this.effectiveLineHeight]);
    }

    // Standard size classes (Tailwind)
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };
    if (sizeClasses[this.size]) {
      classes.push(sizeClasses[this.size]);
    }

    // Variant color classes
    const variantClasses = {
      default: 'text-gray-900 dark:text-white',
      primary: 'text-blue-600 dark:text-blue-400',
      secondary: 'text-gray-600 dark:text-gray-300',
      success: 'text-green-600 dark:text-green-400',
      warning: 'text-yellow-600 dark:text-yellow-400',
      danger: 'text-red-600 dark:text-red-400'
    };
    if (variantClasses[this.variant]) {
      classes.push(variantClasses[this.variant]);
    }

    // Font weight classes
    const weightClasses = {
      light: 'font-light',
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
      extrabold: 'font-extrabold'
    };
    if (weightClasses[this.effectiveWeight]) {
      classes.push(weightClasses[this.effectiveWeight]);
    }

    // Line height classes
    const lineHeightClasses = {
      none: 'leading-none',
      tight: 'leading-tight',
      snug: 'leading-snug',
      normal: 'leading-normal',
      relaxed: 'leading-relaxed',
      loose: 'leading-loose'
    };
    if (lineHeightClasses[this.effectiveLineHeight]) {
      classes.push(lineHeightClasses[this.effectiveLineHeight]);
    }

    // Text alignment
    const alignClasses = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify'
    };
    if (alignClasses[this.align]) {
      classes.push(alignClasses[this.align]);
    }

    // Text transform
    const transformClasses = {
      none: '',
      uppercase: 'uppercase',
      lowercase: 'lowercase',
      capitalize: 'capitalize'
    };
    if (transformClasses[this.transform]) {
      classes.push(transformClasses[this.transform]);
    }

    // Text decoration
    const decorationClasses = {
      none: 'no-underline',
      underline: 'underline',
      overline: 'overline',
      'line-through': 'line-through'
    };
    if (decorationClasses[this.decoration]) {
      classes.push(decorationClasses[this.decoration]);
    }

    // Italic style
    if (this.italic) {
      classes.push('italic');
    }

    // Truncate text
    if (this.truncate) {
      classes.push('truncate');
    }

    // Custom color override
    if (this.color) {
      classes.push(this.color);
    }

    // Custom className
    if (this.className) {
      classes.push(this.className);
    }

    return classes.filter(Boolean).join(' ');
  }

  // Legacy classes getter for backward compatibility
  get classes(): string[] {
    return [
      'nui-heading',
      this.effectiveHeadingSize && this.sizes[this.effectiveHeadingSize],
      this.effectiveWeight && this.weights[this.effectiveWeight],
      this.effectiveLineHeight && this.leads[this.effectiveLineHeight],
    ].filter(Boolean);
  }

  constructor() {}

  ngOnInit(): void {
  }
}

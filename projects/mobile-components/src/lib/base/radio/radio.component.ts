import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  ViewChild,
  ElementRef,
  forwardRef,
  Injectable,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';

@Injectable({
  providedIn: 'root',
})
export class NuiDefaultPropertyService {
  private defaults: Record<string, Record<string, any>> = {
    BaseRadio: {
      rounded: 'md',
      color: 'default',
    },
    // Add more component defaults here as needed
  };

  getDefaultProperty<T>(
    componentName: string,
    propertyName: string,
    currentValue: T | undefined
  ): T {
    if (currentValue !== undefined) {
      return currentValue;
    }
    return this.defaults[componentName]?.[propertyName] ?? undefined;
  }
}

@Component({
  selector: 'base-radio',
  templateUrl: './radio.component.html',
  styleUrls: ['./radio.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RadioComponent),
      multi: true,
    },
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class RadioComponent<T = any> implements OnInit, ControlValueAccessor {
  // Standard inputs (MANDATORY for every component)
  @Input() className: string = '';
  @Input() size: 'xs' | 'sm' | 'md' | 'lg' | 'xl' = 'md';
  @Input() variant: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default';
  @Input() rounded: 'none' | 'sm' | 'md' | 'lg' | 'full' = 'md';

  // Component-specific inputs
  @Input() value?: T; // Value when this radio is selected
  @Input() name?: string; // Radio group name
  @Input() label: string = 'Sample Radio Option'; // Radio label text
  @Input() description?: string; // Optional description text
  @Input() disabled: boolean = false; // Disabled state
  @Input() required: boolean = false; // Required for form validation
  @Input() checked: boolean = false; // Initial checked state
  @Input() inline: boolean = false; // Inline layout
  @Input() labelPosition: 'left' | 'right' = 'right'; // Label position relative to radio
  @Input() helpText?: string; // Additional help text
  @Input() errorText?: string; // Error message text
  @Input() radioSize: 'sm' | 'md' | 'lg' = 'md'; // Radio button size
  @Input() color: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' = 'default'; // Radio color theme

  // Legacy support
  @Input() id?: string;
  @Input() item_id: string = Math.random().toString(36).substring(7);
  @Input() error?: string | boolean;
  @Input() classes?: {
    wrapper?: string | string[];
    label?: string | string[];
    inputDot?: string | string[];
    inputBg?: string | string[];
  } = {};

  // Events
  @Output() checkedChange = new EventEmitter<boolean>();
  @Output() radioSelect = new EventEmitter<{ value: T; checked: boolean }>();
  @Output() radioFocus = new EventEmitter<FocusEvent>();
  @Output() radioBlur = new EventEmitter<FocusEvent>();

  @ViewChild('inputRef') inputRef!: ElementRef<HTMLInputElement>;

  modelValue: T | undefined;
  onChange: any = () => {};
  onTouched: any = () => {};

  // Legacy color mappings
  colors: Record<string, string> = {
    default: 'nui-radio-default',
    muted: 'nui-radio-muted',
    light: 'nui-radio-light',
    dark: 'nui-radio-dark',
    black: 'nui-radio-black',
    primary: 'nui-radio-primary',
    info: 'nui-radio-info',
    success: 'nui-radio-success',
    warning: 'nui-radio-warning',
    danger: 'nui-radio-danger',
  };

  radiuses: Record<string, string> = {
    none: 'nui-radio-rounded-none',
    sm: 'nui-radio-rounded-sm',
    md: 'nui-radio-rounded-md',
    lg: 'nui-radio-rounded-lg',
    full: 'nui-radio-rounded-full',
  };

  // Computed classes for modern Tailwind integration
  get computedClasses(): string {
    const baseClasses = 'relative flex items-start transition-all duration-200';
    
    const sizeClasses = {
      xs: 'text-xs',
      sm: 'text-sm',
      md: 'text-base',
      lg: 'text-lg',
      xl: 'text-xl'
    };

    const variantClasses = {
      default: 'text-gray-900 dark:text-gray-100',
      primary: 'text-blue-900 dark:text-blue-100',
      secondary: 'text-gray-600 dark:text-gray-300',
      success: 'text-green-900 dark:text-green-100',
      warning: 'text-yellow-900 dark:text-yellow-100',
      danger: 'text-red-900 dark:text-red-100'
    };

    const inlineClasses = this.inline ? 'inline-flex mr-4' : 'flex';
    const disabledClasses = this.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer';

    const legacyClasses = this.getLegacyClasses();

    return [
      baseClasses,
      sizeClasses[this.size],
      variantClasses[this.variant],
      inlineClasses,
      disabledClasses,
      legacyClasses,
      this.className
    ].filter(Boolean).join(' ');
  }

  get computedRadioClasses(): string {
    const baseRadioClasses = 'relative border-2 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    const radioSizeClasses = {
      sm: 'w-4 h-4',
      md: 'w-5 h-5',
      lg: 'w-6 h-6'
    };

    const variantRadioClasses = {
      default: 'border-gray-300 text-gray-600 focus:ring-gray-500',
      primary: 'border-blue-300 text-blue-600 focus:ring-blue-500',
      secondary: 'border-gray-400 text-gray-700 focus:ring-gray-500',
      success: 'border-green-300 text-green-600 focus:ring-green-500',
      warning: 'border-yellow-300 text-yellow-600 focus:ring-yellow-500',
      danger: 'border-red-300 text-red-600 focus:ring-red-500'
    };

    const roundedRadioClasses = {
      none: 'rounded-none',
      sm: 'rounded-sm',
      md: 'rounded-md',
      lg: 'rounded-lg',
      full: 'rounded-full'
    };

    const checkedClasses = this.isChecked ? 'border-current bg-current' : 'bg-white dark:bg-gray-800';
    const disabledRadioClasses = this.disabled ? 'cursor-not-allowed' : '';

    return [
      baseRadioClasses,
      radioSizeClasses[this.radioSize],
      variantRadioClasses[this.variant],
      roundedRadioClasses[this.rounded],
      checkedClasses,
      disabledRadioClasses
    ].filter(Boolean).join(' ');
  }

  get computedLabelClasses(): string {
    const baseLabelClasses = 'block font-medium select-none';
    
    const labelOrderClasses = this.labelPosition === 'left' ? 'order-1 mr-3' : 'order-2 ml-3';
    const disabledLabelClasses = this.disabled ? 'cursor-not-allowed' : 'cursor-pointer';

    return [
      baseLabelClasses,
      labelOrderClasses,
      disabledLabelClasses
    ].filter(Boolean).join(' ');
  }

  get computedWrapperClasses(): string {
    const flexOrderClasses = this.labelPosition === 'left' ? 'flex-row-reverse' : 'flex-row';
    return `flex items-center ${flexOrderClasses}`;
  }

  constructor(private nuiDefaultPropertyService: NuiDefaultPropertyService) {}

  ngOnInit() {
    this.color = this.nuiDefaultPropertyService.getDefaultProperty(
      'BaseRadio',
      'color',
      this.color
    );
    this.rounded = this.nuiDefaultPropertyService.getDefaultProperty(
      'BaseRadio',
      'rounded',
      this.rounded
    ) as 'none' | 'sm' | 'md' | 'lg' | 'full';
    
    if (!this.id) {
      this.id = `radio-${this.item_id}`;
    }
    if (!this.name) {
      this.name = `radio-group-${this.item_id}`;
    }
  }

  // Legacy support method
  getLegacyClasses(): string {
    const legacyClasses = [];
    
    if (this.color && this.colors[this.color as keyof typeof this.colors]) {
      legacyClasses.push(this.colors[this.color as keyof typeof this.colors]);
    }
    
    if (this.rounded && this.radiuses[this.rounded as keyof typeof this.radiuses]) {
      legacyClasses.push(this.radiuses[this.rounded as keyof typeof this.radiuses]);
    }
    
    return legacyClasses.join(' ');
  }

  get isChecked(): boolean {
    return this.checked || this.modelValue === this.value;
  }

  get displayLabel(): string {
    return this.label || 'Radio Option';
  }

  get hasError(): boolean {
    return !!(this.error || this.errorText);
  }

  get errorMessage(): string {
    if (typeof this.error === 'string') return this.error;
    return this.errorText || '';
  }

  // Form control methods
  writeValue(value: T): void {
    this.modelValue = value;
    this.checked = value === this.value;
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    if (this.inputRef?.nativeElement) {
      this.inputRef.nativeElement.disabled = isDisabled;
    }
  }

  // Event handlers
  onRadioChange(event: Event): void {
    if (this.disabled) return;
    
    const target = event.target as HTMLInputElement;
    const isChecked = target.checked;
    
    this.checked = isChecked;
    this.modelValue = isChecked ? this.value : undefined;
    
    this.onChange(this.modelValue);
    this.checkedChange.emit(isChecked);
    this.radioSelect.emit({ value: this.value!, checked: isChecked });
  }

  onRadioFocus(event: FocusEvent): void {
    this.onTouched();
    this.radioFocus.emit(event);
  }

  onRadioBlur(event: FocusEvent): void {
    this.radioBlur.emit(event);
  }

  // Utility methods
  toggle(): void {
    if (this.disabled) return;
    this.checked = !this.checked;
    this.modelValue = this.checked ? this.value : undefined;
    this.onChange(this.modelValue);
    this.checkedChange.emit(this.checked);
  }

  select(): void {
    if (this.disabled) return;
    this.checked = true;
    this.modelValue = this.value;
    this.onChange(this.modelValue);
    this.checkedChange.emit(true);
  }

  get el(): HTMLInputElement {
    return this.inputRef?.nativeElement;
  }

  get isErrorString(): boolean {
    return typeof this.error === 'string';
  }
}

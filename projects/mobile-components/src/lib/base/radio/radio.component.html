<div 
  [class]="computedClasses"
  [attr.id]="item_id + '-wrapper'"
>
  <!-- Radio Input Container -->
  <div [class]="computedWrapperClasses">
    <!-- Radio Input -->
    <div class="relative flex items-center justify-center order-1">
      <input
        #inputRef
        [id]="id"
        [attr.name]="name"
        [value]="value"
        [(ngModel)]="modelValue"
        [checked]="isChecked"
        [disabled]="disabled"
        [required]="required"
        [class]="computedRadioClasses"
        type="radio"
        [attr.aria-describedby]="description ? id + '-description' : null"
        [attr.aria-invalid]="hasError"
        (change)="onRadioChange($event)"
        (focus)="onRadioFocus($event)"
        (blur)="onRadioBlur($event)"
      />
      
      <!-- Radio Dot Indicator -->
      <div 
        *ngIf="isChecked"
        class="absolute inset-0 flex items-center justify-center pointer-events-none"
      >
        <div class="w-2 h-2 bg-white rounded-full" 
             [ngClass]="{ 'w-1.5 h-1.5': radioSize === 'sm', 'w-2.5 h-2.5': radioSize === 'lg' }">
        </div>
      </div>
    </div>

    <!-- Label and Content -->
    <div [class]="computedLabelClasses" *ngIf="label || description || helpText">
      <!-- Main Label -->
      <label 
        [for]="id"
        class="block font-medium cursor-pointer"
        [ngClass]="{ 'cursor-not-allowed': disabled }"
      >
        {{ displayLabel }}
        <span *ngIf="required" class="text-red-500 ml-1">*</span>
      </label>
      
      <!-- Description -->
      <p 
        *ngIf="description"
        [id]="id + '-description'"
        class="mt-1 text-sm text-gray-600 dark:text-gray-400"
      >
        {{ description }}
      </p>
      
      <!-- Help Text -->
      <p 
        *ngIf="helpText && !hasError"
        class="mt-1 text-sm text-gray-500 dark:text-gray-400"
      >
        {{ helpText }}
      </p>
      
      <!-- Error Message -->
      <p 
        *ngIf="hasError"
        class="mt-1 text-sm text-red-600 dark:text-red-400"
        role="alert"
      >
        {{ errorMessage }}
      </p>
    </div>
  </div>

  <!-- Slotted Content -->
  <ng-content></ng-content>

  <!-- Legacy Support Template -->
  <div class="nui-radio" [ngClass]="[color && colors[color], classes?.wrapper]" style="display: none;">
    <div class="nui-radio-outer">
      <input
        type="radio"
        [value]="value"
        class="nui-radio-input"
      />
      <div [ngClass]="classes?.inputBg" class="nui-radio-inner"></div>
      <div [ngClass]="classes?.inputDot" class="nui-radio-dot"></div>
    </div>
    <div class="nui-radio-label-wrapper">
      <label
        [ngClass]="classes?.label"
        class="nui-radio-label-text"
      >
        {{ label }}
      </label>
      <div *ngIf="error && isErrorString" class="nui-radio-error">
        {{ error }}
      </div>
    </div>
  </div>
</div>

<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="profile?.givenNames + ' ' + profile?.surname"
      [membership]="profile?.newMembershipNumber"
      type="notification"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages?.landing?.loggedinIcon"
    />
  </div>
  
  <!-- Notification Settings Content -->
  <div class="notification-settings-content ion-padding" style="background-color: #f4f5f8;">

    <div *ngIf="loading" class="loading-container">
      <ion-spinner name="crescent"></ion-spinner>
      <p>Loading your preferences...</p>
    </div>

    <div *ngIf="!loading && preferences">
      <!-- Notification Preferences Section -->
      <div class="section-card">
        <h3 class="section-title">
          <ion-icon name="notifications-outline"></ion-icon>
          Notification Preferences
        </h3>
          <ion-item>
            <ion-toggle [(ngModel)]="preferences.pushNotifications" 
                       (ionChange)="onPreferencesChanged()">
            </ion-toggle>
            <ion-label>
              <h2>Push Notifications</h2>
              <p>Receive push notifications from the app</p>
            </ion-label>
          </ion-item>
          <ion-item>
            <ion-toggle [(ngModel)]="preferences.emailNotifications" 
                       (ionChange)="onPreferencesChanged()">
            </ion-toggle>
            <ion-label>
              <h2>Email Notifications</h2>
              <p>Receive notifications via email</p>
            </ion-label>
          </ion-item>
      </div>

      <!-- Communication Preferences Section -->
      <div class="section-card">
        <h3 class="section-title">
          <ion-icon name="mail-outline"></ion-icon>
          Communication Preferences
        </h3>
        
        <form [formGroup]="communicationForm">
          <div class="form-grid">
            <!-- Receive Notification of Account Changes via -->
            <div class="form-field">
              <ion-item class="modern-input">
                <ion-icon slot="start" name="notifications-outline" class="field-icon"></ion-icon>
                <ion-select
                  label="Receive Notification of Account Changes via"
                  labelPlacement="floating"
                  formControlName="accountChangeNotification"
                >
                  <ion-select-option value="both">Both</ion-select-option>
                  <ion-select-option value="email">Email Only</ion-select-option>
                  <ion-select-option value="sms">SMS Only</ion-select-option>
                  <ion-select-option value="none">No Thanks</ion-select-option>
                </ion-select>
              </ion-item>
            </div>

            <!-- Receive Mica Newsletter via -->
            <div class="form-field">
              <ion-item class="modern-input">
                <ion-icon slot="start" name="newspaper-outline" class="field-icon"></ion-icon>
                <ion-select
                  label="Receive Mica Newsletter via"
                  labelPlacement="floating"
                  formControlName="newsletterNotification"
                >
                  <ion-select-option value="both">Both</ion-select-option>
                  <ion-select-option value="email">Email Only</ion-select-option>
                  <ion-select-option value="sms">SMS Only</ion-select-option>
                  <ion-select-option value="none">No Thanks</ion-select-option>
                </ion-select>
              </ion-item>
            </div>

            <!-- Receive Promotions from Mica via -->
            <div class="form-field">
              <ion-item class="modern-input">
                <ion-icon slot="start" name="pricetag-outline" class="field-icon"></ion-icon>
                <ion-select
                  label="Receive Promotions from Mica via"
                  labelPlacement="floating"
                  formControlName="promotionsNotification"
                >
                  <ion-select-option value="both">Both</ion-select-option>
                  <ion-select-option value="email">Email Only</ion-select-option>
                  <ion-select-option value="sms">SMS Only</ion-select-option>
                  <ion-select-option value="none">No Thanks</ion-select-option>
                </ion-select>
              </ion-item>
            </div>

            <!-- Receive Promotions from Mica Partners via -->
            <div class="form-field">
              <ion-item class="modern-input">
                <ion-icon slot="start" name="people-outline" class="field-icon"></ion-icon>
                <ion-select
                  label="Receive Promotions from Mica Partners via"
                  labelPlacement="floating"
                  formControlName="partnerPromotionsNotification"
                >
                  <ion-select-option value="both">Both</ion-select-option>
                  <ion-select-option value="email">Email Only</ion-select-option>
                  <ion-select-option value="sms">SMS Only</ion-select-option>
                  <ion-select-option value="none">No Thanks</ion-select-option>
                </ion-select>
              </ion-item>
            </div>

            <!-- Receive Statements via Email -->
            <div class="form-field full-width">
              <ion-item class="modern-input">
                <ion-icon slot="start" name="document-text-outline" class="field-icon"></ion-icon>
                <ion-select
                  label="Receive Statements via Email"
                  labelPlacement="floating"
                  formControlName="statementsViaEmail"
                >
                  <ion-select-option value="yes">Yes</ion-select-option>
                  <ion-select-option value="no">No</ion-select-option>
                </ion-select>
              </ion-item>
            </div>

            <!-- Security Question -->
            <div class="form-field full-width">
              <ion-item class="modern-input">
                <ion-icon slot="start" name="shield-checkmark-outline" class="field-icon"></ion-icon>
                <ion-select
                  label="Security Question *"
                  labelPlacement="floating"
                  formControlName="securityQuestion"
                >
                  <ion-select-option value="city">In what city did you meet your spouse/significant other?</ion-select-option>
                  <ion-select-option value="pet">What was the name of your first pet?</ion-select-option>
                  <ion-select-option value="school">What was the name of your elementary school?</ion-select-option>
                  <ion-select-option value="mother">What is your mother's maiden name?</ion-select-option>
                  <ion-select-option value="car">What was the make of your first car?</ion-select-option>
                </ion-select>
              </ion-item>
            </div>

            <!-- Answer -->
            <div class="form-field full-width">
              <ion-item class="modern-input">
                <ion-icon slot="start" name="key-outline" class="field-icon"></ion-icon>
                <ion-input
                  label="Answer *"
                  labelPlacement="floating"
                  type="text"
                  formControlName="securityAnswer"
                  placeholder="Enter your answer"
                ></ion-input>
              </ion-item>
              <div class="error-message" *ngIf="communicationForm.get('securityAnswer')?.invalid && communicationForm.get('securityAnswer')?.touched">
                <ion-icon name="alert-circle-outline"></ion-icon>
                <span>Security answer is required</span>
              </div>
            </div>
          </div>

          <!-- Action Buttons -->
          <div class="action-section">
            <div class="button-group">
              <ion-button 
                expand="block" 
                class="save-button" 
                (click)="saveCommunicationPreferences()"
                [disabled]="!communicationForm.valid || loading"
              >
                <ion-icon name="save-outline" slot="start"></ion-icon>
                Update
              </ion-button>
              
              <ion-button 
                expand="block" 
                fill="outline"
                class="cancel-button" 
                (click)="cancelCommunicationPreferences()"
                [disabled]="loading"
              >
                Cancel
              </ion-button>
            </div>
          </div>
        </form>
      </div>

      <!-- Topic Subscriptions Section -->
      <div class="topic-subscriptions-section" *ngIf="preferences?.pushNotifications && fcmToken" style="display: none;">
        <ion-card>
          <ion-card-header>
            <ion-card-title>
              <ion-icon name="notifications-outline"></ion-icon>
              Topic Subscriptions
            </ion-card-title>
            <ion-card-subtitle>
              Subscribe to specific notification topics to receive targeted updates
            </ion-card-subtitle>
          </ion-card-header>
          <ion-card-content>
            <div *ngIf="topicsLoading" class="topics-loading">
              <ion-spinner name="crescent" size="small"></ion-spinner>
              <span>Updating subscriptions...</span>
            </div>
            
            <div class="topics-list">
              <div *ngFor="let topic of availableTopics" class="topic-item">
                <ion-item>
                  <ion-checkbox 
                    slot="start" 
                    [checked]="topic.subscribed"
                    [disabled]="topicsLoading"
                    (ionChange)="onTopicToggle(topic, $event)">
                  </ion-checkbox>
                  <ion-label>
                    <h3>{{ topic.name }}</h3>
                    <p>{{ topic.description }}</p>
                  </ion-label>
                </ion-item>
              </div>
            </div>

            <div class="custom-topic-section" *ngIf="preferences?.pushNotifications && fcmToken">
              <ion-card>
                <ion-card-header>
                  <ion-card-title>
                    <ion-icon name="add-circle-outline"></ion-icon>
                    Custom Topic Subscription
                  </ion-card-title>
                  <ion-card-subtitle>
                    Subscribe to a custom topic by entering its name
                  </ion-card-subtitle>
                </ion-card-header>
                <ion-card-content>
                  <div class="custom-topic-input">
                    <ion-item>
                      <ion-input 
                        [(ngModel)]="customTopicName" 
                        placeholder="Enter topic name (e.g., news, updates, alerts)"
                        [disabled]="topicsLoading">
                      </ion-input>
                      <ion-button 
                        slot="end" 
                        fill="solid" 
                        size="small"
                        [disabled]="!customTopicName || topicsLoading"
                        (click)="subscribeToCustomTopic()">
                        <ion-icon name="add-outline" slot="start"></ion-icon>
                        Subscribe
                      </ion-button>
                    </ion-item>
                  </div>
                  
                  <div class="custom-topic-help">
                    <p><small>Enter any topic name to subscribe to custom notifications. Topic names should be lowercase and contain only letters, numbers, and underscores.</small></p>
                  </div>
                </ion-card-content>
              </ion-card>
            </div>

            <div class="subscribed-topics-summary" *ngIf="subscribedTopics.length > 0">
              <p><strong>Currently subscribed to {{ subscribedTopics.length }} topic(s):</strong></p>
              <ion-chip *ngFor="let topicId of subscribedTopics" color="primary">
                <ion-label>{{ getTopicName(topicId) }}</ion-label>
              </ion-chip>
            </div>

            <div class="no-subscriptions" *ngIf="subscribedTopics.length === 0">
              <p>You are not subscribed to any topics. Select topics above to receive targeted notifications.</p>
            </div>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- Topic Subscriptions Disabled Message -->
      <div class="topic-subscriptions-disabled" *ngIf="preferences?.pushNotifications && !fcmToken">
        <ion-card>
          <ion-card-header>
            <ion-card-title>
              <ion-icon name="notifications-off-outline"></ion-icon>
              Topic Subscriptions
            </ion-card-title>
          </ion-card-header>
          <ion-card-content>
            <p>Topic subscriptions require an FCM token. Please ensure notifications are properly enabled and try refreshing the token.</p>
            <ion-button 
              fill="outline" 
              size="small" 
              (click)="getFCMToken()">
              <ion-icon name="refresh-outline" slot="start"></ion-icon>
              Refresh Token
            </ion-button>
          </ion-card-content>
        </ion-card>
      </div>

      <!-- FCM Token Section (for testing) -->
      <div class="section-card" *ngIf="preferences?.pushNotifications">
        <h3 class="section-title">
          <ion-icon name="key-outline"></ion-icon>
          FCM Registration Token (For Testing)
        </h3>
        <div class="fcm-content">
            <div *ngIf="fcmToken; else noToken">
              <p class="token-description">Use this token to send test notifications from Firebase Console:</p>
              
              <div class="token-display">
                <ion-button 
                  fill="outline" 
                  size="small" 
                  (click)="toggleTokenDisplay()">
                  <ion-icon [name]="showToken ? 'eye-off-outline' : 'eye-outline'" slot="start"></ion-icon>
                  {{ showToken ? 'Hide Token' : 'Show Token' }}
                </ion-button>
                
                <div *ngIf="showToken" class="token-content">
                  <div class="token-text">{{ fcmToken }}</div>
                  <ion-button 
                    fill="solid" 
                    color="primary" 
                    size="small" 
                    (click)="copyToken()">
                    <ion-icon name="copy-outline" slot="start"></ion-icon>
                    Copy Token
                  </ion-button>
                  
                  <ion-button 
                    fill="solid" 
                    color="success" 
                    size="small" 
                    (click)="saveToken()">
                    <ion-icon name="save-outline" slot="start"></ion-icon>
                    Save Token
                  </ion-button>
                </div>
              </div>
              
              <div class="firebase-links">
                <p><strong>Firebase Console:</strong></p>
                <a href="https://console.firebase.google.com/project/loyalty-test-project-58bcb/messaging" 
                   target="_blank" 
                   class="firebase-link">
                  <ion-icon name="open-outline"></ion-icon>
                  Open Firebase Messaging Console
                </a>
              </div>
            </div>
            
            <ng-template #noToken>
              <p>No FCM token available. Enable notifications to generate a token.</p>
              <ion-button 
                fill="outline" 
                size="small" 
                (click)="getFCMToken()">
                <ion-icon name="refresh-outline" slot="start"></ion-icon>
                Get Token
              </ion-button>
            </ng-template>
        </div>
      </div>

      <!-- Device Management Section -->
      <div class="section-card" *ngIf="preferences?.pushNotifications">
        <h3 class="section-title">
          <ion-icon name="phone-portrait-outline"></ion-icon>
          Device Management
          <ion-button 
            fill="clear" 
            size="small" 
            (click)="toggleDeviceManagement()"
            class="section-toggle">
            <ion-icon [name]="showDeviceManagement ? 'chevron-up-outline' : 'chevron-down-outline'"></ion-icon>
          </ion-button>
        </h3>
        <p class="section-subtitle">Manage devices registered for push notifications</p>
        
        <div class="device-content" *ngIf="showDeviceManagement">
            <div class="device-actions">
              <ion-button 
                fill="outline" 
                size="small" 
                (click)="refreshDeviceTokens()"
                [disabled]="deviceTokensLoading">
                <ion-icon name="refresh-outline" slot="start"></ion-icon>
                Refresh List
              </ion-button>
            </div>

            <div *ngIf="deviceTokensLoading" class="device-loading">
              <ion-spinner name="crescent" size="small"></ion-spinner>
              <span>Loading registered devices...</span>
            </div>

            <div *ngIf="!deviceTokensLoading && deviceTokensError" class="device-error">
              <ion-icon name="warning-outline" color="danger"></ion-icon>
              <div class="error-content">
                <h4>Error Loading Devices</h4>
                <p>{{ deviceTokensError }}</p>
                <ion-button 
                  fill="outline" 
                  size="small" 
                  color="danger"
                  (click)="refreshDeviceTokens()">
                  <ion-icon name="refresh-outline" slot="start"></ion-icon>
                  Try Again
                </ion-button>
              </div>
            </div>

            <div *ngIf="!deviceTokensLoading && !deviceTokensError && deviceTokens.length > 0" class="device-list">
              <div *ngFor="let device of deviceTokens; let i = index" class="device-item">
                <ion-item>
                  <ion-avatar slot="start">
                    <div class="device-icon">
                      <ion-icon 
                        [name]="device.device.includes('android') ? 'phone-portrait-outline' : 
                               device.device.includes('ios') ? 'phone-portrait-outline' : 
                               'desktop-outline'">
                      </ion-icon>
                    </div>
                  </ion-avatar>
                  
                  <ion-label>
                    <h3>
                      {{ getDeviceName(device.device) }}
                      <ion-chip *ngIf="device.isCurrentDevice" color="success" size="small">
                        <ion-label>Current Device</ion-label>
                      </ion-chip>
                    </h3>
                    <p>Device ID: {{ device.device }}</p>
                    <p *ngIf="device.timestamp">Registered: {{ getDeviceTime(device.timestamp) }}</p>
                    <p>Token: {{ device.token.substring(0, 20) }}...</p>
                  </ion-label>
                  
                  <ion-button 
                    slot="end" 
                    fill="clear" 
                    color="medium"
                    (click)="copyDeviceToken(device.token)">
                    <ion-icon name="copy-outline"></ion-icon>
                  </ion-button>
                  
                  <ion-button 
                    slot="end" 
                    fill="clear" 
                    color="danger"
                    (click)="deleteDeviceToken(device)"
                    [disabled]="deviceTokensLoading">
                    <ion-icon name="trash-outline"></ion-icon>
                  </ion-button>
                </ion-item>
              </div>
            </div>

            <div *ngIf="!deviceTokensLoading && !deviceTokensError && deviceTokens.length === 0" class="no-devices">
              <div class="empty-state">
                <ion-icon name="phone-portrait-outline" size="large"></ion-icon>
                <h3>No Registered Devices</h3>
                <p>No devices are currently registered for push notifications.</p>
                <ion-button 
                  fill="outline" 
                  size="small" 
                  (click)="refreshDeviceTokens()">
                  <ion-icon name="refresh-outline" slot="start"></ion-icon>
                  Refresh List
                </ion-button>
              </div>
            </div>

            <div class="device-info">
              <ion-note>
                <p><strong>Note:</strong> You can remove devices that no longer need to receive notifications. 
                The current device will be marked accordingly.</p>
              </ion-note>
            </div>
        </div>
      </div>

      <!-- About Notifications Section -->
      <div class="section-card">
        <h3 class="section-title">
          <ion-icon name="information-circle-outline"></ion-icon>
          About Notifications
        </h3>
        <p>Notifications help you stay informed about important updates, special offers, and account activity.</p>
        <p>You can change your notification preferences at any time from this screen.</p>
        <p>Topic subscriptions allow you to receive notifications about specific categories that interest you.</p>
        <p>If you're not receiving notifications, please check your device settings to ensure notifications are enabled for this app.</p>
      </div>
    </div>
  </div>
</lib-page-wrapper>
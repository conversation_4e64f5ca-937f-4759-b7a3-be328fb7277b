import { Component, OnInit, Input, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { FormsModule, ReactiveFormsModule, FormBuilder, FormGroup, Validators, AbstractControl } from '@angular/forms';
import { Router } from '@angular/router';
// Note: Services will need to be injected from the consuming application
// import { PushNotificationService } from '../../services/push-notification.service';
// import { MemberService, FirebaseMemberService, LssConfig } from 'lp-client-api';
import { take } from 'rxjs';

// Define interfaces locally since mobile-components import is not available
interface NotificationPreferences {
  emailNotifications: boolean;
  pushNotifications: boolean;
  smsNotifications: boolean;
  enabled?: boolean;
  groups?: any;
  sounds?: boolean;
  vibration?: boolean;
  quietHours?: any;
}

interface NotificationGroup {
  id: string;
  name: string;
  description: string;
  defaultEnabled: boolean;
  icon: string;
}

export interface FirebaseTopic {
  id: string;
  name: string;
  description: string;
  subscribed: boolean;
}

export interface DeviceToken {
  token: string;
  device: string;
  platform?: string;
  timestamp?: string;
  isCurrentDevice?: boolean;
}

@Component({
  selector: 'lib-notification-settings-page',
  standalone: true,
  imports: [CommonModule, IonicModule, FormsModule, ReactiveFormsModule],
  templateUrl: './notification-settings-page.component.html',
  styleUrls: ['./notification-settings-page.component.scss'],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class NotificationSettingsPageComponent implements OnInit {
  // Inputs for services and data from consuming application
  @Input() profile: any = null;
  @Input() lssConfig: any = null;
  @Input() pushNotificationService: any = null;
  @Input() memberService: any = null;
  @Input() firebaseMemberService: any = null;

  loading = true;
  preferences: NotificationPreferences | null = null;
  notificationGroups: NotificationGroup[] = [];
  fcmToken: string | null = null;
  showToken = false;
  communicationForm: FormGroup;
  
  // Topic subscription properties
  topicsLoading = false;
  availableTopics: FirebaseTopic[] = [
    { id: 'general', name: 'General Updates', description: 'General app updates and announcements', subscribed: false },
    { id: 'promotions', name: 'Promotions & Offers', description: 'Special deals and promotional offers', subscribed: false },
    { id: 'loyalty_rewards', name: 'Loyalty Rewards', description: 'Points updates and reward notifications', subscribed: false },
    { id: 'events', name: 'Events', description: 'Upcoming events and activities', subscribed: false },
    { id: 'news', name: 'News & Updates', description: 'Latest news and product updates', subscribed: false }
  ];
  subscribedTopics: string[] = [];

  // Add this property
  customTopicName: string = '';

  // Device management properties
  deviceTokens: DeviceToken[] = [];
  deviceTokensLoading = false;
  showDeviceManagement = true; // Show expanded by default
  deviceTokensError: string | null = null;

  constructor(
    private router: Router,
    private formBuilder: FormBuilder
  ) {
    // Initialize communication form
    this.communicationForm = this.formBuilder.group({
      accountChangeNotification: ['both'],
      newsletterNotification: ['both'],
      promotionsNotification: ['no'],
      partnerPromotionsNotification: ['no'],
      statementsViaEmail: ['yes'],
      securityQuestion: ['city', Validators.required],
      securityAnswer: ['', Validators.required]
    });
  }

  /**
   * Show success alert to user
   */
  private showSuccessAlert(message: string): void {
    alert('✅ ' + message);
  }

  /**
   * Show error alert to user
   */
  private showErrorAlert(message: string): void {
    alert('❌ ' + message);
  }

  ngOnInit() {
    // Initialize mock preferences if services are not provided
    if (!this.pushNotificationService) {
      this.initializeMockData();
      this.loading = false;
      return;
    }
    
    this.loadPreferences();
    this.loadTopicSubscriptions();
    this.loadDeviceTokens();
  }

  /**
   * Initialize mock data when services are not available
   */
  initializeMockData() {
    this.preferences = {
      emailNotifications: true,
      pushNotifications: true,
      smsNotifications: false,
      enabled: true
    };
    
    this.availableTopics.forEach(topic => {
      topic.subscribed = false;
    });
  }


  /**
   * Save communication preferences
   */
  async saveCommunicationPreferences() {
    if (this.communicationForm.valid) {
      try {
        this.loading = true;
        
        // Here you would typically call a service to save the preferences
        // For now, we'll just show a success message
        const formData = this.communicationForm.value;
        console.log('Saving communication preferences:', formData);
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        this.showSuccessAlert('Communication preferences updated successfully');
        
        this.loading = false;
      } catch (error) {
        console.error('Error saving communication preferences:', error);
        this.showErrorAlert('Failed to update communication preferences');
        this.loading = false;
      }
    } else {
      this.showErrorAlert('Please fill in all required fields correctly');
    }
  }

  /**
   * Cancel communication preferences changes
   */
  cancelCommunicationPreferences() {
    // Reset form to original values
    if (this.profile?.emailAddress) {
      this.communicationForm.patchValue({
        email: this.profile.emailAddress,
        reEnterEmail: this.profile.emailAddress
      });
    }
    
    // Reset other fields to defaults
    this.communicationForm.patchValue({
      accountChangeNotification: 'both',
      newsletterNotification: 'both',
      promotionsNotification: 'no',
      partnerPromotionsNotification: 'no',
      statementsViaEmail: 'yes'
    });
  }

  /**
   * Load user notification preferences
   */
  async loadPreferences() {
    if (!this.pushNotificationService) {
      this.initializeMockData();
      return;
    }

    try {
      this.loading = true;

      // Get notification groups
      if (this.pushNotificationService.notificationGroups) {
        this.notificationGroups = this.pushNotificationService.notificationGroups;
      }

      // Get user preferences
      this.preferences = await this.pushNotificationService.getPreferences();

      // Try to get FCM token if notifications are enabled
      if (this.preferences?.enabled) {
        this.fcmToken = await this.pushNotificationService.getCurrentFCMToken();
        // Only attempt to save if we already have a resolvable member ID
        const memberId = this.resolveMemberId();
        if (this.fcmToken && memberId) {
          await this.saveToken(memberId);
        } else if (this.fcmToken && this.memberService?.profileLoadComplete) {
          // Defer the save until profile finishes loading (one-shot)
          this.memberService.profileLoadComplete.pipe(take(1)).subscribe((loaded: boolean) => {
            if (loaded) {
              const mid = this.resolveMemberId();
              if (mid) {
                this.saveToken(mid).catch(err => console.error('Deferred token save failed:', err));
              }
            }
          });
        }
      }

      this.loading = false;
    } catch (error) {
      console.error('Error loading notification preferences', error);
      this.loading = false;
    }
  }

  /**
   * Load topic subscriptions from localStorage
   */
  loadTopicSubscriptions() {
    try {
      const stored = localStorage.getItem('firebase_topic_subscriptions');
      if (stored) {
        this.subscribedTopics = JSON.parse(stored);
        // Update available topics with subscription status
        this.availableTopics.forEach(topic => {
          topic.subscribed = this.subscribedTopics.includes(topic.id);
        });
      }
    } catch (error) {
      console.error('Error loading topic subscriptions:', error);
    }
  }

  /**
   * Subscribe to a Firebase topic
   */
  async subscribeToTopic(topicId: string): Promise<boolean> {
    if (!this.firebaseMemberService) {
      this.showErrorAlert('Firebase service not available');
      return false;
    }

    try {
      this.topicsLoading = true;
      
      if (!this.fcmToken) {
        console.error('No FCM token available for topic subscription');
        this.showErrorAlert('No FCM token available. Please refresh the token and try again.');
        return false;
      }

      // Use Firebase member service to subscribe to topic
      const response = await this.firebaseMemberService.subscribeToTopic(topicId).toPromise();
      const success = response?.success || false;
      
      if (success) {
        // Update local state
        if (!this.subscribedTopics.includes(topicId)) {
          this.subscribedTopics.push(topicId);
          this.saveTopicSubscriptions();
        }
        
        // Update topic status if it exists in available topics
        const topic = this.availableTopics.find(t => t.id === topicId);
        if (topic) {
          topic.subscribed = true;
        }
        
        console.log(`Successfully subscribed to topic: ${topicId}`);
        this.showSuccessAlert(`Successfully subscribed to topic: ${topicId}`);
        return true;
      } else {
        this.showErrorAlert(`Failed to subscribe to topic: ${topicId}. Please ensure the notification backend is running.`);
        return false;
      }
    } catch (error) {
      console.error(`Error subscribing to topic ${topicId}:`, error);
      this.showErrorAlert(`Error subscribing to topic: ${topicId}. Please check the backend connection.`);
      return false;
    } finally {
      this.topicsLoading = false;
    }
  }

  /**
   * Unsubscribe from a Firebase topic
   */
  async unsubscribeFromTopic(topicId: string): Promise<boolean> {
    if (!this.firebaseMemberService) {
      this.showErrorAlert('Firebase service not available');
      return false;
    }

    try {
      this.topicsLoading = true;
      
      if (!this.fcmToken) {
        console.error('No FCM token available for topic unsubscription');
        this.showErrorAlert('No FCM token available. Please refresh the token and try again.');
        return false;
      }

      // Use Firebase member service to unsubscribe from topic
      const response = await this.firebaseMemberService.unsubscribeFromTopic(topicId).toPromise();
      const success = response?.success || false;
      
      if (success) {
        // Update local state
        this.subscribedTopics = this.subscribedTopics.filter(id => id !== topicId);
        this.saveTopicSubscriptions();
        
        // Update topic status
        const topic = this.availableTopics.find(t => t.id === topicId);
        if (topic) {
          topic.subscribed = false;
        }
        
        console.log(`Successfully unsubscribed from topic: ${topicId}`);
        this.showSuccessAlert(`Successfully unsubscribed from topic: ${topicId}`);
        return true;
      } else {
        this.showErrorAlert(`Failed to unsubscribe from topic: ${topicId}. Please ensure the notification backend is running.`);
        return false;
      }
    } catch (error) {
      console.error(`Error unsubscribing from topic ${topicId}:`, error);
      this.showErrorAlert(`Error unsubscribing from topic: ${topicId}. Please check the backend connection.`);
      return false;
    } finally {
      this.topicsLoading = false;
    }
  }

  /**
   * Handle topic subscription toggle
   */
  async onTopicToggle(topic: FirebaseTopic, event: any) {
    const isChecked = event.detail.checked;
    
    if (isChecked) {
      const success = await this.subscribeToTopic(topic.id);
      if (!success) {
        // Revert the toggle if subscription failed
        event.target.checked = false;
        topic.subscribed = false;
      }
    } else {
      const success = await this.unsubscribeFromTopic(topic.id);
      if (!success) {
        // Revert the toggle if unsubscription failed
        event.target.checked = true;
        topic.subscribed = true;
      }
    }
  }

  /**
   * Save topic subscriptions to localStorage
   */
  private saveTopicSubscriptions() {
    try {
      localStorage.setItem('firebase_topic_subscriptions', JSON.stringify(this.subscribedTopics));
    } catch (error) {
      console.error('Error saving topic subscriptions:', error);
    }
  }

  /**
   * Handle preferences changed event
   */
  async onPreferencesChanged() {
    if (!this.preferences || !this.pushNotificationService) return;
    
    try {
      // Save the updated preferences
      await this.pushNotificationService.savePreferences(this.preferences);

      // If notifications were enabled, request permission
      if (this.preferences.pushNotifications) {
        const permissionStatus = await this.pushNotificationService.requestPermission();
        if (!permissionStatus) {
          // If permission was denied, update preferences to reflect this
          this.preferences.pushNotifications = false;
          await this.pushNotificationService.savePreferences(this.preferences);
        } else {
          // Permission granted, try to get FCM token
          this.fcmToken = await this.pushNotificationService.getCurrentFCMToken();
          
          // Automatically save the token to backend and reload topic subscriptions if we now have a token
          if (this.fcmToken) {
            await this.saveToken();
            this.loadTopicSubscriptions();
          }
        }
      } else {
        // Notifications disabled, clear token and unsubscribe from all topics
        this.fcmToken = null;
        await this.unsubscribeFromAllTopics();
      }
    } catch (error) {
      console.error('Error saving notification preferences', error);
    }
  }

  /**
   * Unsubscribe from all topics when notifications are disabled
   */
  private async unsubscribeFromAllTopics() {
    for (const topicId of this.subscribedTopics) {
      await this.unsubscribeFromTopic(topicId);
    }
  }

  /**
   * Toggle showing the FCM token
   */
  toggleTokenDisplay() {
    this.showToken = !this.showToken;
  }

  /**
   * Copy FCM token to clipboard
   */
  async copyToken() {
    if (this.fcmToken) {
      try {
        await navigator.clipboard.writeText(this.fcmToken);
        console.log('📋 FCM Token copied to clipboard:', this.fcmToken);
        
        // Show success message (you could add a toast here)
        alert('FCM Token copied to clipboard!');
      } catch (error) {
        console.error('Failed to copy token:', error);
        
        // Fallback: log to console
        console.log('📋 FCM Token (copy manually):', this.fcmToken);
        alert('Copy failed. Check console for token.');
      }
    }
  }

  /**
   * Save FCM token to backend
   * Optionally provide a resolved memberId to avoid backend errors when profile isn't ready
   */
  async saveToken(memberId?: string) {
    console.log('🔴 saveToken() method called');
    
    if (!this.firebaseMemberService || !this.memberService) {
      console.warn('Firebase or member service not available');
      return;
    }
    
    if (this.fcmToken) {
      try {
        console.log('📤 Saving Firebase token to backend:', this.fcmToken.substring(0, 20) + '...');
        const resolvedMemberId = memberId || this.resolveMemberId();
        if (!resolvedMemberId) {
          console.warn('⏳ Profile not ready yet — deferring token link until profile loads');
          if (this.memberService?.profileLoadComplete) {
            this.memberService.profileLoadComplete.pipe(take(1)).subscribe((loaded: boolean) => {
              if (loaded) {
                const mid = this.resolveMemberId();
                if (mid) {
                  this.saveToken(mid).catch(err => console.error('Deferred token save failed:', err));
                }
              }
            });
          }
          return;
        }

        console.log('🔍 Using memberId for token link:', resolvedMemberId);

        this.firebaseMemberService.linkFirebaseToken(this.fcmToken, resolvedMemberId).subscribe({
          next: (response: any) => {
            console.log('🔍 Observable next() called with response:', response);
            if (response.success) {
              console.log('🔥 Firebase token saved successfully:', response);
              // Use a non-blocking confirmation for UX; avoid intrusive alerts
              // alert('Firebase token saved to backend successfully!');
            } else {
              console.warn('⚠️ Firebase token save failed:', response.error);
              // Avoid blocking alert popups; log instead
              // alert('Failed to save token: ' + (response.error || 'Unknown error'));
            }
          },
          error: (error: any) => {
            console.error('🔍 Observable error() called with error:', error);
            console.error('❌ Error saving Firebase token:', error);
            // Avoid intrusive alerts for 5xx errors to prevent repeated popups
            // alert('Error saving token: ' + error.message);
          },
          complete: () => {
            console.log('🔍 Observable complete() called');
          }
        });
        
        console.log('🔍 Subscribe() method called, waiting for response...');
      } catch (error) {
        console.error('🔍 Exception in saveToken():', error);
        console.error('Failed to save token:', error);
        // Avoid intrusive alerts here as well
        // alert('Failed to save token. Check console for details.');
      }
    } else {
      console.log('🔍 No FCM token available');
      // alert('No FCM token available to save');
    }
  }

  /**
   * Get FCM token manually
   */
  async getFCMToken() {
    if (!this.pushNotificationService) {
      this.showErrorAlert('Push notification service not available');
      return;
    }

    try {
      this.fcmToken = await this.pushNotificationService.getCurrentFCMToken();
      if (this.fcmToken) {
        console.log('📋 FCM Token retrieved:', this.fcmToken);
        // Automatically save the token to backend once profile/memberId is known
        const memberId = this.resolveMemberId();
        await this.saveToken(memberId || undefined);
        // Reload topic subscriptions now that we have a token
        this.loadTopicSubscriptions();
      }
    } catch (error) {
      console.error('Error getting FCM token:', error);
    }
  }

  /**
   * Navigate back to previous page
   */
  goBack() {
    this.router.navigate(['/secure/notification-settings']);
  }

  /**
   * Helper method to get topic name from topic ID
   */
  getTopicName(topicId: string): string {
    const topic = this.availableTopics.find(t => t.id === topicId);
    return topic?.name || topicId;
  }

  // Add method for custom topic subscription
  async subscribeToCustomTopic(): Promise<void> {
    if (!this.customTopicName || !this.customTopicName.trim()) {
      this.showErrorAlert('Please enter a topic name');
      return;
    }

    const topicName = this.customTopicName.trim();
    
    // Validate topic name format (allow mixed case, letters, numbers, underscores, hyphens)
    if (!/^[a-zA-Z0-9_-]+$/.test(topicName)) {
      this.showErrorAlert('Topic name should only contain letters, numbers, underscores, and hyphens');
      return;
    }

    const success = await this.subscribeToTopic(topicName);
    
    if (success) {
      // Add to available topics list if not already there
      if (!this.availableTopics.find(t => t.id === topicName)) {
        this.availableTopics.push({
          id: topicName,
          name: topicName, // Keep original case
          description: 'Custom topic subscription',
          subscribed: true
        });
      }
      
      // Clear input
      this.customTopicName = '';
    }
    // Note: Success/error messages are now handled in subscribeToTopic method
  }

  /**
   * Load all device tokens for the current member
   */
  async loadDeviceTokens(): Promise<void> {
    if (!this.firebaseMemberService || !this.memberService) {
      this.deviceTokensError = 'Required services not available';
      return;
    }

    try {
      this.deviceTokensLoading = true;
      this.deviceTokensError = null;
      
      console.log('📱 Loading device tokens (member determined from auth context)');
      
      const response = await this.firebaseMemberService.getDeviceTokens().toPromise();
      console.log('📱 Raw API response:', response);
      
      if (response?.success && response.data) {
        // Transform response data to DeviceToken interface
        // API now returns fields in lowercase (id, mpacc, device, token, auditUser, auditDate)
        this.deviceTokens = response.data.map((device: any) => ({
          token: device.token || device.TOKEN,
          device: device.device || device.DEVICE || device.deviceId,
          platform: this.extractPlatformFromDevice(device.device || device.DEVICE),
          timestamp: device.auditDate || device.AUDITDATE || device.timestamp || device.createdAt,
          isCurrentDevice: (device.token || device.TOKEN) === this.fcmToken
        }));
        
        console.log('📱 Loaded device tokens:', this.deviceTokens);
        console.log('📱 Current FCM token for comparison:', this.fcmToken);
        console.log('📱 Device tokens mapped successfully - count:', this.deviceTokens.length);
        this.deviceTokensError = null;
      } else {
        console.warn('⚠️ Failed to load device tokens:', response?.error);
        this.deviceTokens = [];
        this.deviceTokensError = response?.error || 'Failed to load device tokens from server';
      }
      
    } catch (error: any) {
      console.error('❌ Error loading device tokens:', error);
      console.error('❌ Error details:', {
        message: error.message,
        status: error.status,
        statusText: error.statusText,
        url: error.url
      });
      
      this.deviceTokens = [];
      
      if (error.status === 403) {
        this.deviceTokensError = 'Access denied. You may not have permission to view device tokens.';
      } else if (error.status === 404) {
        this.deviceTokensError = 'Device token endpoint not found. Please check API configuration.';
      } else if (error.status === 0) {
        this.deviceTokensError = 'Unable to connect to server. Please check your internet connection.';
      } else {
        this.deviceTokensError = `Failed to load device tokens: ${error.message || 'Unknown error'}`;
      }
      
      console.error('🔍 Device tokens error set to:', this.deviceTokensError);
    } finally {
      this.deviceTokensLoading = false;
    }
  }

  /**
   * Delete a specific device token
   */
  async deleteDeviceToken(deviceToken: DeviceToken): Promise<void> {
    if (!this.firebaseMemberService || !this.memberService) {
      this.showErrorAlert('Required services not available');
      return;
    }

    try {
      // Confirm deletion
      const confirmDelete = confirm(`Are you sure you want to remove this device?\n\nDevice: ${this.getDeviceName(deviceToken.device)}\nToken: ${deviceToken.token.substring(0, 20)}...`);
      
      if (!confirmDelete) {
        return;
      }

      this.deviceTokensLoading = true;
      
      const memberProfile = this.memberService.profileSubject?.value || this.profile;
      const memberId = memberProfile?.newMembershipNumber || memberProfile?.mpacc || memberProfile?.uniqueId;
      
      if (!memberId) {
        this.showErrorAlert('No member ID available for deleting device token');
        return;
      }

      console.log('🗑️ Deleting device token:', deviceToken.token.substring(0, 20) + '...');
      
      const response = await this.firebaseMemberService.deleteDeviceToken(memberId, deviceToken.token).toPromise();
      
      if (response?.success) {
        this.showSuccessAlert('Device token deleted successfully');
        
        // Remove from local array
        this.deviceTokens = this.deviceTokens.filter(dt => dt.token !== deviceToken.token);
        
        // If we deleted the current device token, clear the local reference
        if (deviceToken.isCurrentDevice) {
          this.fcmToken = null;
        }
        
        console.log('✅ Device token deleted successfully');
      } else {
        this.showErrorAlert('Failed to delete device token: ' + (response?.error || 'Unknown error'));
      }
      
    } catch (error) {
      console.error('❌ Error deleting device token:', error);
      this.showErrorAlert('Error deleting device token: ' + (error as Error).message);
    } finally {
      this.deviceTokensLoading = false;
    }
  }

  /**
   * Refresh the device tokens list
   */
  async refreshDeviceTokens(): Promise<void> {
    await this.loadDeviceTokens();
  }

  /**
   * Toggle device management section visibility
   */
  toggleDeviceManagement(): void {
    this.showDeviceManagement = !this.showDeviceManagement;
    
    // Load device tokens when opening the section
    if (this.showDeviceManagement && this.deviceTokens.length === 0) {
      this.loadDeviceTokens();
    }
  }

  /**
   * Get a user-friendly device name from device ID
   */
  getDeviceName(deviceId: string): string {
    if (!deviceId) return 'Unknown Device';
    
    // Extract platform and identifier from device ID
    if (deviceId.includes('android_')) {
      return '📱 Android Device';
    } else if (deviceId.includes('ios_')) {
      return '📱 iOS Device';
    } else if (deviceId.includes('web_')) {
      return '💻 Web Browser';
    } else if (deviceId === 'android') {
      return '📱 Android Device (Legacy)';
    } else if (deviceId === 'ios') {
      return '📱 iOS Device (Legacy)';
    } else if (deviceId === 'web') {
      return '💻 Web Browser (Legacy)';
    }
    
    return `📱 ${deviceId}`;
  }

  /**
   * Get device registration time as readable string
   */
  getDeviceTime(timestamp?: string): string {
    if (!timestamp) return 'Unknown';
    
    try {
      const date = new Date(timestamp);
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    } catch (error) {
      return 'Invalid date';
    }
  }

  /**
   * Copy device token to clipboard
   */
  async copyDeviceToken(token: string): Promise<void> {
    try {
      await navigator.clipboard.writeText(token);
      this.showSuccessAlert('Device token copied to clipboard!');
    } catch (error) {
      console.error('Failed to copy device token:', error);
      console.log('📋 Device Token (copy manually):', token);
      this.showErrorAlert('Copy failed. Check console for token.');
    }
  }

  /**
   * Resolve the current memberId from profile or injected services
   */
  private resolveMemberId(): string | null {
    // Prefer injected memberService profile when available
    const profileSvc = this.memberService?.profileSubject?.value;
    const candidate = profileSvc?.newMembershipNumber || profileSvc?.mpacc || profileSvc?.uniqueId ||
                      this.profile?.newMembershipNumber || this.profile?.mpacc || this.profile?.uniqueId;
    if (candidate && String(candidate).trim() !== '') return String(candidate);
    return null;
  }

  /**
   * Extract platform from device ID string
   */
  private extractPlatformFromDevice(deviceId: string): string {
    if (!deviceId) return 'unknown';
    
    const deviceLower = deviceId.toLowerCase();
    if (deviceLower.includes('android')) {
      return 'android';
    } else if (deviceLower.includes('ios')) {
      return 'ios';
    } else if (deviceLower.includes('web')) {
      return 'web';
    }
    
    return 'unknown';
  }
}
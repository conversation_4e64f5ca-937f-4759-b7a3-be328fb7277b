// Minimal SCSS for component-level styles that can't be handled by Tailwind
// Most layout has been moved to Tailwind classes in the template

/* Global CSS classes that child components can use */
:host ::ng-deep {
  .section-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.25rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    
    @media (min-width: 768px) {
      padding: 1.75rem;
      margin-bottom: 1.5rem;
      
      &:hover {
        transform: translateY(-0.125rem);
        box-shadow: 0 25px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      }
    }
    
    @media (min-width: 1200px) {
      padding: 2rem;
      margin-bottom: 1.75rem;
    }
  }
  
  .hero-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    margin-top: 0;
    position: relative;
    z-index: 20;
    
    @media (min-width: 768px) {
      padding: 1.75rem;
    }
    
    @media (min-width: 1200px) {
      padding: 2rem;
    }
  }
}

/* iOS smooth scrolling for scrollable mode */
.touch-pan-y {
  -webkit-overflow-scrolling: touch;
}

:host {
  display: block;
  min-height: 100%;
  width: 100%;
}

.page-wrapper-root {
  min-height: 100%;
  flex: 1 1 auto;
}

.page-wrapper-content {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
}

.page-wrapper-container {
  flex: 1 1 auto;
  display: flex;
  flex-direction: column;
}

import { Component, Type, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SnakeComponent } from '../../../features/games/games/snake/snake.component';
import { HangmanComponent } from '../../../features/games/games/hangman/hangman.component';
import { TicTacToeComponent } from '../../../features/games/games/tic-tac-toe/tic-tac-toe.component';
import { MemoryComponent } from '../../../features/games/games/memory/memory.component';
import { PuzzleComponent } from '../../../features/games/games/puzzle/puzzle.component';
import { QuizComponent } from '../../../features/games/games/quiz/quiz.component';
import { RacingComponent } from '../../../features/games/games/racing/racing.component';
import { TriviaComponent } from '../../../features/games/games/trivia/trivia.component';
import { WordleComponent } from '../../../features/games/games/wordle/wordle.component';
import { FlappyBirdComponent } from '../../../features/games/games/flappy-bird/flappy-bird.component';
import { BlackjackComponent } from '../../../features/games/games/blackjack/blackjack.component';
import { SudokuComponent } from '../../../features/games/games/sudoku/sudoku.component';
import { MinesweeperComponent } from '../../../features/games/games/minesweeper/minesweeper.component';
import { Twenty48Component } from '../../../features/games/games/2048/2048.component';
import { ChessComponent } from '../../../features/games/games/chess/chess.component';
import { CrosswordComponent } from '../../../features/games/games/crossword/crossword.component';
import { PokerComponent } from '../../../features/games/games/poker/poker.component';
import { TetrisComponent } from '../../../features/games/games/tetris/tetris.component';
import { TowerDefenseComponent } from '../../../features/games/games/tower-defense/tower-defense.component';
import { PlatformerComponent } from '../../../features/games/games/platformer/platformer.component';
import { RockPaperScissorsComponent } from '../../../features/games/games/rock-paper-scissors/rock-paper-scissors.component';
import { SimonSaysComponent } from '../../../features/games/games/simon-says/simon-says.component';
import { PongComponent } from '../../../features/games/games/pong/pong.component';
import { BreakoutComponent } from '../../../features/games/games/breakout/breakout.component';
import { PacManComponent } from '../../../features/games/games/pac-man/pac-man.component';
@Component({
  selector: 'lib-games',
  templateUrl: './games.component.html',
  styleUrls: ['./games.component.css'],
  standalone: true,
  imports: [CommonModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class GamesComponent {
  games: string[] = [
    '2048',
    'blackjack',
    'breakout',
    'chess',
    'crossword',
    'flappy-bird',
    'hangman',
    'memory',
    'minesweeper',
    'pac-man',
    'platformer',
    'poker',
    'pong',
    'puzzle',
    'quiz',
    'racing',
    'rock-paper-scissors',
    'simon-says',
    'snake',
    'sudoku',
    'tetris',
    'tic-tac-toe',
    'tower-defense',
    'trivia',
    'wordle',
  ];
  selectedGames: string[] = [];
  selectedGame: string | null = 'blackjack';

  toggleGameSelection(game: string): void {
    const index = this.selectedGames.indexOf(game);
    if (index === -1) {
      this.selectedGames.push(game);
    } else {
      this.selectedGames.splice(index, 1);
    }
  }

  playSelectedGames(): void {
    console.log('Playing selected games:', this.selectedGames);
    // Implement game launching logic here
  }
  selectGame(game: string) {
    this.selectedGame = game;
  }

  playSelectedGame() {
    if (this.selectedGame) {
      // Implement the logic to start the selected game
      console.log(`Starting game: ${this.selectedGame}`);
    }
  }

  getGameComponent(): Type<any> {
    switch (this.selectedGame) {
      case '2048':
        return Twenty48Component;
      case 'blackjack':
        return BlackjackComponent;
      case 'breakout':
        return BreakoutComponent;
      case 'chess':
        return ChessComponent;
      case 'crossword':
        return CrosswordComponent;
      case 'flappy-bird':
        return FlappyBirdComponent;
      case 'hangman':
        return HangmanComponent;
      case 'memory':
        return MemoryComponent;
      case 'minesweeper':
        return MinesweeperComponent;
      case 'pac-man':
        return PacManComponent;
      case 'platformer':
        return PlatformerComponent;
      case 'pong':
        return PongComponent;
      case 'poker':
        return PokerComponent;
      case 'puzzle':
        return PuzzleComponent;
      case 'quiz':
        return QuizComponent;
      case 'racing':
        return RacingComponent;
      case 'rock-paper-scissors':
        return RockPaperScissorsComponent;
      case 'simon-says':
        return SimonSaysComponent;
      case 'snake':
        return SnakeComponent;
      case 'sudoku':
        return SudokuComponent;
      case 'tetris':
        return TetrisComponent;
      case 'tic-tac-toe':
        return TicTacToeComponent;
      case 'tower-defense':
        return TowerDefenseComponent;
      case 'trivia':
        return TriviaComponent;
      case 'wordle':
        return WordleComponent;
      // ... add cases for other games
      default:
        return WordleComponent;
    }
  }
}

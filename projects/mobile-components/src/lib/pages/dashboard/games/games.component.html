<div class="games-dashboard">
  <h2>Game Selection Dashboard</h2>
  <div class="game-list">
    <div *ngFor="let game of games" class="game-item">
      <label [ngClass]="{ selected: selectedGame === game }">
        <input
          type="radio"
          [checked]="selectedGame === game"
          (change)="selectGame(game)"
        />
        {{ game }}
      </label>
    </div>
  </div>
  <button (click)="playSelectedGame()" [disabled]="!selectedGame">
    Play Selected Game
  </button>
</div>
<div>
  <ng-container *ngIf="selectedGame">
    <ng-container *ngComponentOutlet="getGameComponent()"></ng-container>
  </ng-container>
</div>

import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';

// Import standalone components
import { GamesDashboardComponent } from './games-dashboard/games-dashboard.component';

// Import modules
import { GamesComponentsModule } from '../../games/components/games-components.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    IonicModule,
    // Import standalone components
    GamesDashboardComponent,
    GamesComponentsModule
  ],
  exports: [
    GamesDashboardComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // Add schema for web components like ion-icon
})
export class GamesPagesModule { }

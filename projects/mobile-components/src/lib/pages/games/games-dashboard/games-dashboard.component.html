<div class="">
  <div class="absolute inset-0 z-0 h-screen bg-blue-400 animated-bg"></div>
  <div class="overflow-y-auto absolute inset-0 z-10 pb-24 space-y-2">
    <games-header bgColor="blue" />

    <div class="relative px-4 animate-fade-in-down">
      <!-- Header Section -->
      <div class="mb-2 text-center">
        <h1 class="text-3xl font-bold text-red-100">Welcome Back!</h1>
        <p class="text-red-100">Ready to play and earn?</p>
      </div>

      <!-- Points Section -->

    </div>
    <div
    class="p-4 mx-2 bg-blue-600 rounded-xl shadow-lg animate-fade-in-right"
  >
    <div class="flex justify-between items-center">
      <h2 class="text-xl font-bold text-red-100">Your Points</h2>
      <span class="text-3xl font-bold text-red-200 hover:text-green-400">{{
        totalPoints
      }}</span>
    </div>
    <div class="mt-2 text-sm text-red-200">
      <p>Level: {{ userLevel }}</p>
      <p>Next reward at: {{ nextRewardThreshold }} points</p>
    </div>
  </div>
    <!-- Favorite Games Section -->
    <div class="p-4 mx-2 bg-red-600 rounded-xl shadow-lg animate-fade-in-left">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-2xl font-bold text-orange-100">Favorite Games</h2>
        <a routerLink="/public/games/all" class="text-green-400">View All</a>
      </div>
      <div class="grid grid-cols-3 gap-4">
        <ng-container *ngFor="let game of favoriteGames; let i = index">
          <div
            [ngClass]="{ 'text-red-200': i < 3, 'text-gray-400': i >= 3 }"
            class="text-center transition-all duration-300 transform cursor-pointer group hover:scale-105"
            (click)="playGame(game)"
          >
            <div class="relative mb-2">
              <img
                [src]="game.image"
                [alt]="game.name"
                class="object-cover mx-auto w-24 h-24 rounded-lg shadow-md"
              />
              <div
                class="absolute inset-0 bg-black bg-opacity-0 rounded-lg transition-all duration-300 group-hover:bg-opacity-20"
              ></div>
            </div>
            <p class="text-sm font-medium">{{ game.name }}</p>
            <p class="mt-1 text-xs">{{ game.genre }}</p>
          </div>
        </ng-container>
      </div>
    </div>

    <div class="mx-2">
      <!-- Nearest Stores Section -->
      <div class="p-6 bg-red-600 rounded-xl shadow-lg animate-fade-in-right">
        <h2 class="mb-4 text-2xl font-bold text-red-100">Nearest Stores</h2>
        <div class="space-y-4">
          <div
            *ngFor="let store of nearestStores"
            [ngClass]="{
              'border-red-300': store.hasActivePromotion,
              'border-red-200': !store.hasActivePromotion
            }"
            class="flex items-center p-3 bg-red-500 rounded-lg border-2 transition-all duration-300 transform cursor-pointer hover:scale-102"
            (click)="viewStorePromotions(store)"
          >
            <img
              [src]="store.image"
              [alt]="store.name"
              class="object-cover mr-4 w-16 h-16 rounded-full"
            />
            <div class="flex-grow">
              <h3 class="font-semibold text-red-100">{{ store.name }}</h3>
              <p class="text-sm text-red-200">{{ store.distance }} km away</p>
            </div>
            <div
              [ngClass]="{
                'text-red-200': store.hasActivePromotion,
                'text-red-400': !store.hasActivePromotion
              }"
              class="text-right"
            >
              <p class="font-medium">
                {{
                  store.hasActivePromotion
                    ? "Active Promo!"
                    : "No active promos"
                }}
              </p>
              <p class="text-sm">{{ store.promoCount }} offers</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { GamesDashboardComponent } from './games-dashboard.component';
import { GamesHeaderComponent } from '../../../games/components/games-header/games-header.component';

@NgModule({
  declarations: [
    GamesDashboardComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule,
    GamesHeaderComponent
  ],
  exports: [
    GamesDashboardComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class GamesDashboardModule { }

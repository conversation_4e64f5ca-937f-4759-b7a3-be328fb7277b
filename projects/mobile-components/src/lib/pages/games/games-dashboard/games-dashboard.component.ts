import { Component, Input, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { Router } from '@angular/router';
import { LssConfig, GameService } from 'lp-client-api';
import { CommonModule } from '@angular/common';
import { GamesHeaderComponent } from '../../../games/components/games-header/games-header.component';

interface Game {
  name: string;
  description: string;
  image: string;
  genre: string;
}

interface Store {
  id: number;
  name: string;
  distance: number;
  hasActivePromotion: boolean;
  image: string;
  promoCount: number;
}

@Component({
  selector: 'lib-games-dashboard',
  templateUrl: './games-dashboard.component.html',
  styleUrls: ['./games-dashboard.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    GamesHeaderComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class GamesDashboardComponent {
  @Input() public profile: any;

  public totalPoints: number = 1250;
  public favoriteGames: Game[] = [];
  public nearestStores: Store[] = [];
  public userLevel: number = 1;
  public nextRewardThreshold: number = 1000;

  constructor(
    protected readonly router: Router,
    public lssConfig: LssConfig,
    private gameService: GameService
  ) {}

  ngOnInit() {
    this.initializeDummyData();
  }

  private initializeDummyData() {
    this.favoriteGames = [
      {
        name: 'tetris',
        description: 'Tetris game',
        image: 'assets/images/games/tetris.jpeg',
        genre: 'Arcade Game',
      },
      {
        name: 'sudoku',
        description: 'Sudoku game',
        image: 'assets/images/games/sudoku.jpeg',
        genre: 'Puzzle Game',
      },
      {
        name: 'pac-man',
        description: 'Pac-Man game',
        image: 'assets/images/games/pacman.jpeg',
        genre: 'Arcade Game',
      },
    ];

    this.nearestStores = [
      {
        id: 1,
        image: 'assets/images/stores/mica.png',
        name: 'Mica Centurion',
        distance: 1.2,
        hasActivePromotion: true,
        promoCount: 2,
      },
      {
        id: 2,
        image: 'assets/images/stores/sanparks.jpg',
        name: 'Sanparks',
        distance: 2.5,
        hasActivePromotion: false,
        promoCount: 0,
      },
    ];
  }

  public async playGame(game: Game) {
    console.log(`Playing ${game.name}`);

    // Get the active game from service
    const activeGame = this.gameService.getActiveGame();
    if (!activeGame) {
      console.error('No active game found');
      return;
    }

    // Get the config ID from active game
    const configId = activeGame.gameConfig?.[0]?.id;
    if (!configId) {
      console.error('No game config found');
      return;
    }

    try {
      // Get the account ID from the first game participation
      const accountId = activeGame.gameParticipation?.[0]?.id || 0;
      console.log('Using account ID:', accountId);

      // Get game configs for the current account
      const configs = await this.gameService
        .getAccountGameConfigs(accountId)
        .toPromise();

      if (!configs) {
        throw new Error('Failed to load game configs');
      }

      // Find existing instance or create new one
      const config = configs.find(c => c.gameConfig === configId);
      let instanceId = config?.gameInstance || null;

      if (!instanceId) {
        // Create new instance if none exists
        const instance = await this.gameService.createGameInstance(configId).toPromise();
        if (!instance) {
          throw new Error('Failed to create game instance');
        }
        instanceId = instance.id;
      }

      // Navigate to game component
      this.router.navigate(['/public/games/single'], {
        queryParams: {
          game: game.name,
          instance: instanceId
        }
      });
    } catch (error) {
      console.error('Error preparing game:', error);
    }
  }

  public viewStorePromotions(store: Store) {
    console.log(`Viewing promotions for ${store.name}`);
    // Implement navigation to store promotions page
    // this.router.navigate(['/stores', store.id, 'promotions']);
  }
}

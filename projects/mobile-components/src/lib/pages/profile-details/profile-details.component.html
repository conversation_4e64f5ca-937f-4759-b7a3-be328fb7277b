<div class="app-background">
<lib-head-logo
[names]="profile?.givenNames + ' ' + profile?.surname"
[membership]="profile?.newMembershipNumber"
type="membership"
[balance]="profile?.currentBalance"
[src]="lssConfig.pages?.landing?.loggedinIcon || 'assets/images/default-logo.png'"
/>

   

  
    <ion-row>
      <ion-col>
        <ion-card class="card" [routerLink]="['/public/profile-details']">
          <ion-card-content class="quick-action">
            <ion-row>
              <div class="center">
                <ion-icon name="person-add-outline"></ion-icon>
              </div>
              <div class="center">
                <ion-card-subtitle>Profile</ion-card-subtitle>
              </div>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </ion-col>
      
      <ion-col>
        <ion-card class="card" [routerLink]="['/secure/security']">
          <ion-card-content class="quick-action">
            <ion-row>
              <div class="center">
                <ion-icon icon="shield-checkmark-outline"></ion-icon>
              </div>
              <div class="center">
                <ion-card-subtitle>Security</ion-card-subtitle>
              </div>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-row>
      <ion-col>
        <ion-card class="card" [routerLink]="['/secure/pools']">
          <ion-card-content class="quick-action">
            <ion-row>
              <div class="center">
                <ion-icon icon="card-outline"></ion-icon>
              </div>
              <div class="center">
                <ion-card-subtitle>Pools</ion-card-subtitle>
              </div>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </ion-col>
      <ion-col>
        <ion-card class="card" [routerLink]="['/secure/points']">
          <ion-card-content class="quick-action">
            <ion-row>
              <div class="center">
                <ion-icon icon="card-outline"></ion-icon>
              </div>
              <div class="center">
                <ion-card-subtitle>Points</ion-card-subtitle>
              </div>
            </ion-row>
          </ion-card-content>
        </ion-card>
      </ion-col>
    </ion-row>
    <ion-card>
      <!-- <ion-card class="card">
        <ion-card-content class="quick-action"> -->
      <ion-item>
        <ion-row class="ion-justify-content-between w-full">
          <ion-card-subtitle>Rand Value</ion-card-subtitle>
  
          <ion-text *ngIf="profile?.availRands"
            >R {{ profile?.availRands?.toFixed(2) }}</ion-text
          >
          <ion-text *ngIf="!profile?.availRands">R 0.00</ion-text>
        </ion-row>
      </ion-item>
  
      <!-- </ion-card-content>
      </ion-card> -->
      <!-- <ion-card class="card">
        <ion-card-content class="quick-action"> -->
      <ion-item>
        <ion-row class="ion-justify-content-between w-full">
          <ion-card-subtitle>Current available Balance</ion-card-subtitle>
          <ion-text>{{ profile?.availUnits }}</ion-text>
        </ion-row>
      </ion-item>
      <!-- </ion-card-content>
      </ion-card>
      <ion-card class="card">
        <ion-card-content class="quick-action"> -->
      <ion-item [routerLink]="['/secure/points']" class="points-item">
        <ion-row class="ion-justify-content-between w-full">
          <ion-card-subtitle>{{lssConfig.pointsTitle}}</ion-card-subtitle>
          <ion-text>{{ profile?.currentBalance! }}</ion-text>
        </ion-row>
      </ion-item>
      <!-- </ion-card-content>
      </ion-card> -->
      <!-- <ion-card class="card">
        <ion-card-content class="quick-action"> -->
      <ion-item [routerLink]="['/secure/points']" class="points-item">
        <ion-row class="ion-justify-content-between w-full">
          <ion-card-subtitle>Earned</ion-card-subtitle>
          <ion-text class="pcu-earned">{{profile?.baseMiles! + profile?.bonusMiles!}}</ion-text>
        </ion-row>
      </ion-item>
      <!-- </ion-card-content>
      </ion-card> -->
  
      <!-- <ion-card class="card">
        <ion-card-content class="quick-action"> -->
      <ion-item  class="points-item">
        <ion-row class="ion-justify-content-between w-full">
          <ion-card-subtitle> Used</ion-card-subtitle>
          <ion-text class="pcu-spent">{{profile?.expiredMiles! + profile?.usedMiles!}}</ion-text>
        </ion-row>
      </ion-item>
      <!-- </ion-card-content> 
      </ion-card> -->
    </ion-card>
    
    <ion-col>
      <ion-card class="card" [routerLink]="['/secure/profileremove']">
        <ion-card-content class="quick-action">
          <ion-row>
            <div class="center">
              <ion-icon name="trash-outline"></ion-icon>
            </div>
            <div class="center">
              <ion-card-subtitle>Remove Profile</ion-card-subtitle>
            </div>
          </ion-row>
        </ion-card-content>
      </ion-card>
    </ion-col>
</div>
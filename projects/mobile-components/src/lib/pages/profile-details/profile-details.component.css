.app-background {
    --background: var(--ion-color-base);
  }

   .card {
     --background: var(--ion-color-primary-shade);
    color: var(--ion-color-primary-contrast)
   }
  .center {
    height: 100%;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .account-name {
    font-size: 26px
  }

  .name-text {
    margin-left: 20px;
    font-size: 26px

  }

  .w-full {
    width: 100%;
  }
  .card-background {
    background-color: #fff;
    width: 100vw;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}
.transaction-summary {
    ion-row {
        h3 {
            margin-bottom: 1rem;
            margin-left: 16px;
            font-size: 1.4rem;
            font-weight: 400;
        }
    }
    
    ion-col {
        ion-row {
            h3 {
                font-size: 0.8rem;
                font-weight: 400;
                width: 100%;
                margin-right: 16px;

                span {
                    font-size: 1rem;
                    padding-bottom: 0;
                }
            }
        }
    }
    
    h3 {
        margin-bottom: 1rem;
        margin-left: 16px;

        span {
            font-size: 1rem;
            display: block;
            padding-bottom: 12px;
        }
    }
}

.pcu-earned {
    color: var(--ion-color-success) !important;
}

.pcu-spent {
    color: var(--ion-color-danger) !important;
}

.points-item {
    cursor: pointer;
    position: relative;
    
    &::after {
        content: '';
        position: absolute;
        right: 16px;
        top: 50%;
        transform: translateY(-50%);
        width: 8px;
        height: 8px;
        border-top: 2px solid var(--ion-color-medium);
        border-right: 2px solid var(--ion-color-medium);
        transform: translateY(-50%) rotate(45deg);
    }
    
    &:hover {
        background-color: rgba(var(--ion-color-primary-rgb), 0.1);
    }
}
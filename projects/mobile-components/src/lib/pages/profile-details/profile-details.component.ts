import { Component, Injector, OnInit, ChangeDetectorRef } from '@angular/core';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
} from 'lp-client-api';
import { AbstractComponent } from '../../shared/abstract.component';
import { IonicModule } from '@ionic/angular';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router'; 
import { HeadLogoComponent } from '../../head-logo/head-logo.component';


@Component({
  selector: 'lib-profile-details',
  templateUrl: './profile-details.component.html',
  styleUrl: './profile-details.component.css',
  standalone: true,
  imports: [IonicModule, CommonModule, RouterModule, HeadLogoComponent],
})
export class ProfileDetailsComponent extends AbstractComponent {
  profile?: MemberProfile;


  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    private cd: ChangeDetectorRef,
    public lssConfig: LssConfig,
    protected readonly router: Router,
  ) {
    super(injector);
  }

  ngOnInit() {
    console.log('[ProfileDetailsComponent] ngOnInit!?!?!');
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data: MemberProfile | null) => {
        console.log('[ProfileDetailsComponent] Received data from profileSubject:', data);
        this.profile = data ?? undefined; 
        if (this.profile) {
          console.log('[ProfileDetailsComponent] Profile data received, calling getBalance().');
          this.getBalance();
        }
        this.detectChanges();
      })
    );
  }

  private getBalance() {
    if (!this.profile?.membershipNumber) {
      console.warn('[ProfileDetailsComponent] No membershipNumber available for balance fetch');
      return;
    }
    
    this.memberService
      .memberBalance(this.profile.membershipNumber)
      .subscribe({
        next: (balanceData: any) => {
          console.log('[ProfileDetailsComponent] Received balance data:', balanceData);
          this.profile = { ...this.profile, ...balanceData };
          console.log('[ProfileDetailsComponent] Profile after merging balance:', this.profile);
          // Explicitly trigger change detection after balance update if needed
          // this.detectChanges(); 
        },
        error: (err: any) => {
          console.error('[ProfileDetailsComponent] Error fetching balance:', err);
        }
      });
  }
}

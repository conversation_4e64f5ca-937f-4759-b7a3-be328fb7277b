import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router'; // Pages likely involve routing

// Import Page components
// Standalone (3)
import { PagesCustomizerComponent } from './customizer/pages-customizer.component';
import { HomeComponent } from './home/<USER>';
import { ProfileDetailsComponent } from './profile-details/profile-details.component';
import { DynamicDashboardComponent } from './dynamic-dashboard/dynamic-dashboard.component';
import { PagesLoginTheme1Component } from './login/themes/theme1/pages-login-theme1.component';
import { PagesLandingTheme1Component } from './landing/themes/theme1/pages-landing-theme1.component';
@NgModule({
  declarations: [
    // Non-Standalone Components (1)
  ],
  imports: [
    CommonModule,
    IonicModule,
    RouterModule, // Import RouterModule if pages use routerLink etc.
    // Standalone Components (3)
    PagesCustomizerComponent,
    HomeComponent,
    ProfileDetailsComponent,
    DynamicDashboardComponent,
    PagesLoginTheme1Component,
    PagesLandingTheme1Component,


  ],
  exports: [
    // Non-Standalone Components (1)
    DynamicDashboardComponent,
    // Standalone Components (1)
    PagesCustomizerComponent,
    PagesLoginTheme1Component,
    PagesLandingTheme1Component,
  ]
})
export class PagesModule { }

.animated-bg {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1) 20px,
      transparent 20px,
      transparent 40px
    );
    animation: move 20s linear infinite;
    z-index: 0;
  }
}

@keyframes move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 282px 0;
  }
}

.animated-bg {
  background: linear-gradient(
    45deg,
    rgba(59, 130, 246, 0.8) 0%,
    rgba(37, 99, 235, 0.8) 100%
  );
  background-size: 400% 400%;
  animation: gradient 15s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Card hover effects */
.bg-gray-200 {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.bg-gray-200:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

/* Stats animation */
.text-2xl {
  transition: transform 0.2s ease;
}

.text-2xl:hover {
  transform: scale(1.1);
}

/* Button animations */
button {
  transition: all 0.3s ease;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

button:active {
  transform: translateY(1px);
}

/* Trophy icon animation */
ion-icon[name="trophy-outline"] {
  animation: bounce 1s ease infinite;
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-2px);
  }
}

/* Loading animation */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Fade in animation for content */
.flex-grow {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Logo animation */
.logo-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 1000;
}

.game-logo {
  width: 200px;
  height: 200px;
  border-radius: 16px;
  object-fit: contain;
  transform-origin: center;
}

@keyframes flyInSpinOut {
  0% {
    transform: translateX(-100vw) rotate(0deg);
    opacity: 0;
  }

  20% {
    transform: translateX(0) rotate(0deg);
    opacity: 1;
  }

  40% {
    transform: translateX(0) rotate(720deg);
    opacity: 1;
  }

  60% {
    transform: translateX(0) rotate(720deg);
    opacity: 1;
  }

  100% {
    transform: translateX(100vw) rotate(1080deg);
    opacity: 0;
  }
}

.logo-animation {
  animation: flyInSpinOut 3s ease-in-out forwards;
}

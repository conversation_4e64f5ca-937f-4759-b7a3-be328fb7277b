<div class="flex overflow-hidden overflow-y-auto flex-col min-h-screen bg-blue-200">
  <div class="absolute inset-0 z-0 h-screen bg-blue-400 animated-bg"></div>
  <div class="overflow-y-auto absolute inset-0 z-10">
    <games-header bgColor="blue"></games-header>

    <!-- Loading State -->
    <div *ngIf="loading" class="flex justify-center items-center min-h-[200px]">
      <div class="flex items-center text-lg text-white">
        <ion-icon name="hourglass-outline" class="mr-2 animate-spin"></ion-icon>
        Loading game...
      </div>
    </div>

    <!-- Error State -->
    <div *ngIf="error" class="p-4 text-center text-red-500">
      {{ error }}
    </div>

    <!-- Game Dashboard -->
    <div *ngIf="!loading && !error" class="flex flex-col flex-grow p-4 mx-auto max-w-4xl relative">
      <!-- Animated Logo -->
      <div *ngIf="showLogoAnimation" class="logo-container">
        <img src="assets/images/games/mascot.png"
             class="game-logo logo-animation"
             alt="Game mascot">
      </div>
      
      <!-- Game Header -->
      <div class="flex relative justify-between items-center p-4 mb-6 rounded-lg backdrop-blur-sm bg-white/20">
        <!-- Game background image -->
        <img *ngIf="activeGame?.backgroundImage"
             [src]="activeGame?.backgroundImage"
             class="object-cover absolute inset-0 z-0 w-full h-full opacity-20"
             alt="Game background">
        <div class="flex items-center">
          <img *ngIf="activeGame?.backgroundImage"
               [src]="activeGame?.backgroundImage"
               class="object-cover mr-4 w-16 h-16 rounded-lg"
               alt="Game logo">
          <div>
            <h1 class="text-2xl font-bold text-white">{{ gameTitle }}</h1>
            <p class="text-blue-100">{{ activeGame?.gameType?.typeDescription?.description }}</p>
          </div>
        </div>
        <div class="flex gap-2">
          <button 
          class="px-4 py-2 text-white bg-green-500 rounded-lg transition-colors hover:bg-green-400"
          (click)="startGame()">
    <ion-icon name="play-outline" class="mr-1"></ion-icon>
    Start Game
  </button>
          <!-- <button *ngIf="!gameInstance"
                  class="px-4 py-2 text-white bg-green-500 rounded-lg transition-colors hover:bg-green-400"
                  (click)="startGame()">
            <ion-icon name="play-outline" class="mr-1"></ion-icon>
            Start Game
          </button>
          <button *ngIf="gameInstance"
                  class="px-4 py-2 text-white bg-blue-500 rounded-lg transition-colors hover:bg-blue-400"
                  (click)="navigateToGame()">
            <ion-icon name="game-controller-outline" class="mr-1"></ion-icon>
            Continue Game
          </button> -->
        </div>
      </div>

      <!-- Game Stats and Info Grid -->
      <div class="grid grid-cols-1 gap-6 md:grid-cols-2">
        <!-- Stats Card -->
        <div class="p-6 rounded-lg backdrop-blur-sm bg-white/20">
          <h2 class="mb-4 text-xl font-bold text-white">Your Stats</h2>
          <div class="grid grid-cols-2 gap-4">
            <div class="p-3 text-center rounded-lg bg-white/10">
              <p class="text-2xl font-bold text-yellow-400">{{ bestScore }}</p>
              <p class="text-blue-100">Best Score</p>
            </div>
            <div class="p-3 text-center rounded-lg bg-white/10">
              <p class="text-2xl font-bold text-yellow-400">{{ gamesPlayed }}</p>
              <p class="text-blue-100">Games Played</p>
            </div>
            <div class="p-3 text-center rounded-lg bg-white/10">
              <p class="text-2xl font-bold text-yellow-400">{{ currentStreak }}</p>
              <p class="text-blue-100">Current Streak</p>
            </div>
            <div class="p-3 text-center rounded-lg bg-white/10">
              <p class="text-2xl font-bold text-yellow-400">{{ gameInstance?.gameEvents?.length || 0 }}</p>
              <p class="text-blue-100">Recent Games</p>
            </div>
          </div>
        </div>

        <!-- Game Rules -->
        <div class="p-6 rounded-lg backdrop-blur-sm bg-white/20">
          <h2 class="mb-4 text-xl font-bold text-white">How to Play</h2>
          <div class="space-y-2 text-blue-100" [ngSwitch]="selectedGame">
            <div *ngSwitchCase="'wordle'" class="space-y-2">
              <p>🎯 Guess the word in 6 tries</p>
              <p>🟩 Green tiles: Letter is correct and in right spot</p>
              <p>🟨 Yellow tiles: Letter is in word but wrong spot</p>
              <p>⬜️ Gray tiles: Letter is not in word</p>
              <p>⌨️ Use keyboard or type to enter letters</p>
            </div>
            <div *ngSwitchCase="'memory'" class="space-y-2">
              <p>🎴 Find matching pairs of cards</p>
              <p>🕒 Complete levels within time limit</p>
              <p>🎯 Remember card positions</p>
              <p>⭐️ Score bonus points for speed</p>
              <p>🔄 Progress through harder levels</p>
            </div>
            <div *ngSwitchCase="'2048'" class="space-y-2">
              <p>🔢 Combine matching numbers</p>
              <p>⬆️ Swipe in any direction to move tiles</p>
              <p>🎯 Reach the 2048 tile to win</p>
              <p>📊 Plan your moves carefully</p>
              <p>💫 Get bonus points for large combinations</p>
            </div>
            <div *ngSwitchCase="'snake'" class="space-y-2">
              <p>🐍 Control the snake using arrow keys</p>
              <p>🍎 Collect food to grow longer</p>
              <p>⚡️ Speed increases as you grow</p>
              <p>❌ Avoid hitting walls and yourself</p>
              <p>🏆 Aim for the highest score</p>
            </div>
            <div *ngSwitchCase="'minesweeper'" class="space-y-2">
              <p>💣 Find all mines without triggering them</p>
              <p>🔢 Numbers show adjacent mines</p>
              <p>🚩 Right-click to flag potential mines</p>
              <p>🤔 Use logic to deduce mine locations</p>
              <p>⏱️ Complete faster for better scores</p>
            </div>
            <div *ngSwitchCase="'tetris'" class="space-y-2">
              <p>🟦 Arrange falling blocks to create lines</p>
              <p>🔄 Rotate pieces to fit perfectly</p>
              <p>⚡️ Speed increases with level</p>
              <p>🌟 Clear multiple lines for bonus points</p>
              <p>📊 Plan ahead using next piece preview</p>
            </div>
            <div *ngSwitchCase="'sudoku'" class="space-y-2">
              <p>🔢 Fill grid with numbers 1-9</p>
              <p>✨ Each row, column, and box must contain 1-9</p>
              <p>💡 Use hints when stuck</p>
              <p>⏱️ Solve faster for better scores</p>
              <p>🎯 Multiple difficulty levels available</p>
            </div>
            <div *ngSwitchDefault class="space-y-2">
              <p>Select a game to see its rules and instructions</p>
            </div>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="p-6 rounded-lg backdrop-blur-sm bg-white/20">
          <h2 class="mb-4 text-xl font-bold text-white">Recent Activity</h2>
          <div class="space-y-3">
            <div *ngFor="let event of gameInstance?.gameEvents?.slice(-3)"
                 class="flex justify-between items-center p-3 rounded-lg bg-white/10">
              <div class="flex items-center">
                <ion-icon name="trophy-outline" class="mr-2 text-yellow-400"
                         *ngIf="event.score > 0"></ion-icon>
                <div>
                  <p class="text-white">Level {{ event.level }}</p>
                  <p class="text-sm text-blue-100">Score: {{ event.score }}</p>
                </div>
              </div>
              <p class="text-sm text-blue-100">{{ getDuration(event.duration) }}</p>
            </div>
            <p *ngIf="!gameInstance?.gameEvents?.length" class="text-center text-blue-100">
              No recent games played
            </p>
          </div>
        </div>

        <!-- Achievements -->
        <div class="p-6 rounded-lg backdrop-blur-sm bg-white/20">
          <h2 class="mb-4 text-xl font-bold text-white">Achievements</h2>
          <div class="grid grid-cols-2 gap-4">
            <div *ngFor="let prize of prizes"
                 class="flex items-center p-3 rounded-lg bg-white/10">
              <ion-icon name="trophy-outline" class="mr-3 text-2xl text-yellow-400"></ion-icon>
              <div>
                <p class="text-white">{{ prize.name }}</p>
                <p class="text-sm text-blue-100">{{ prize.points }} points</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Game Stats -->
        <div class="p-6 rounded-lg backdrop-blur-sm bg-white/20">
          <h2 class="mb-4 text-xl font-bold text-white">Game Stats</h2>
          <div class="space-y-4" [ngSwitch]="selectedGame">
            <div *ngSwitchCase="'wordle'" class="grid grid-cols-2 gap-4">
              <div class="p-3 rounded-lg bg-white/10">
                <p class="text-2xl font-bold text-yellow-400">
                  {{ (gameProgress?.gameSpecificProgress || {})['totalWins'] || '0' }}
                </p>
                <p class="text-blue-100">Total Wins</p>
              </div>
              <div class="p-3 rounded-lg bg-white/10">
                <p class="text-2xl font-bold text-yellow-400">
                  {{ (gameProgress?.gameSpecificProgress || {})['averageAttempts'] || '0' }}
                </p>
                <p class="text-blue-100">Avg Attempts</p>
              </div>
              <div class="p-3 rounded-lg bg-white/10">
                <p class="text-2xl font-bold text-yellow-400">{{ currentStreak }}</p>
                <p class="text-blue-100">Win Streak</p>
              </div>
              <div class="p-3 rounded-lg bg-white/10">
                <p class="text-2xl font-bold text-yellow-400">
                  {{ (gameProgress?.gameSpecificProgress || {})['perfectGames'] || '0' }}
                </p>
                <p class="text-blue-100">Perfect Games</p>
              </div>
            </div>
            <div *ngSwitchCase="'memory'" class="grid grid-cols-2 gap-4">
              <div class="p-3 rounded-lg bg-white/10">
                <p class="text-2xl font-bold text-yellow-400">
                  {{ (gameProgress?.gameSpecificProgress || {})['totalMatches'] || '0' }}
                </p>
                <p class="text-blue-100">Total Matches</p>
              </div>
              <div class="p-3 rounded-lg bg-white/10">
                <p class="text-2xl font-bold text-yellow-400">
                  {{ (gameProgress?.gameSpecificProgress || {})['bestTime'] || '0' }}s
                </p>
                <p class="text-blue-100">Best Time</p>
              </div>
              <div class="p-3 rounded-lg bg-white/10">
                <p class="text-2xl font-bold text-yellow-400">
                  {{ ((gameProgress?.gameSpecificProgress || {})['completedLevels'] || []).length || '0' }}
                </p>
                <p class="text-blue-100">Levels Complete</p>
              </div>
              <div class="p-3 rounded-lg bg-white/10">
                <p class="text-2xl font-bold text-yellow-400">{{ currentStreak }}</p>
                <p class="text-blue-100">Win Streak</p>
              </div>
            </div>
            <!-- Add similar stat blocks for other games -->
            <div *ngSwitchDefault class="text-center text-blue-100">
              <p>Select a game to view detailed statistics</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

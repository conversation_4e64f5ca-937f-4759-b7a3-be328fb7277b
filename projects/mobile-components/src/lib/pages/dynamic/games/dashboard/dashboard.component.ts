import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { GameService } from 'lp-client-api';
import { Game, GameConfig, GameInstance } from 'lp-client-api';
import { CommonModule } from '@angular/common';

interface Prize {
  name: string;
  points: number;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css'],
  standalone: true,
  imports: [
    CommonModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class DashboardComponent implements OnInit {
  loading = true;
  error: string | null = null;
  gameTitle: string = '';
  selectedGame: string | null = null;
  gameInstance: GameInstance | null = null;
  activeGame: Game | null = null;
  gameInstances: GameInstance[] = [];

  // Stats (to be implemented with real data)
  bestScore: number = 0;
  gamesPlayed: number = 0;
  currentStreak: number = 0;

  // Sample prizes (to be replaced with real data)
  prizes: Prize[] = [
    { name: 'Bronze Trophy', points: 100 },
    { name: 'Silver Trophy', points: 250 },
    { name: 'Gold Trophy', points: 500 },
  ];

  // Add missing properties
  gameProgress: any = null;

  // Add animation state
  showLogoAnimation = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private gameService: GameService
  ) {}

  ngOnInit() {
    // Get active game from service
    this.activeGame = this.gameService.getActiveGame();
    console.log('Active game from service:', this.activeGame);

    if (this.activeGame) {
      this.selectedGame =
        this.activeGame.gameType.typeDescription.code.toLowerCase();
      this.gameTitle = this.activeGame.name.replace('%20', ' ');

      // Load all instances for this game
      this.loadGameInstances();

      // Trigger logo animation after a short delay
      setTimeout(() => {
        this.showLogoAnimation = true;
        // Hide animation after it completes
        setTimeout(() => {
          this.showLogoAnimation = false;
        }, 3000);
      }, 500);
    } else {
      console.error('No active game found');
      this.error = 'No active game found';
      this.loading = false;
    }
  }

  private loadGameInstances() {
    if (!this.activeGame?.id) {
      this.error = 'No game config found';
      this.loading = false;
      return;
    }

    this.loading = true;
    this.gameService.getAllGameInstances(this.activeGame.id).subscribe({
      next: (instances: GameInstance[]) => {
        console.log('Loaded game instances:', instances);
        this.gameInstances = instances;

        // If no instances exist, create one
        if (instances.length === 0) {
          this.createNewInstance();
        } else {
          // Get the most recent instance
          const mostRecent = instances[instances.length - 1];
          this.selectInstance(mostRecent);
        }

        this.loading = false;
      },
      error: (error: Error) => {
        console.error('Error loading game instances:', error);
        this.error = 'Failed to load game instances';
        this.loading = false;
      },
    });
  }

  private createNewInstance() {
    if (!this.activeGame?.id) return;

    this.gameService.createGameInstance(this.activeGame.id).subscribe({
      next: (instance: GameInstance) => {
        console.log('Created new game instance:', instance);
        this.gameInstances.push(instance);
        this.selectInstance(instance);
      },
      error: (error: Error) => {
        console.error('Error creating game instance:', error);
        this.error = 'Failed to create game instance';
      },
    });
  }

  selectInstance(instance: GameInstance) {
    console.log('Selecting instance:', instance);
    this.gameInstance = instance;
    localStorage.setItem('currentGameInstanceId', instance.id.toString());

    // Update stats based on instance
    if (instance.gameEvents?.length) {
      const lastEvent = instance.gameEvents[instance.gameEvents.length - 1];
      this.bestScore = Math.max(this.bestScore, lastEvent.score || 0);
      this.gamesPlayed = instance.gameEvents.length;
    }
  }

  playGame() {
    if (!this.activeGame || !this.gameInstance) return;

    const typeCode =
      this.activeGame.gameType.typeDescription.code.toLowerCase();
    this.router.navigate(['/games/play'], {
      queryParams: {
        game: typeCode,
        instance: this.gameInstance.id,
      },
    });
  }

  public navigateBack() {
    this.router.navigate(['/games']);
  }

  public async navigateToGame() {
    if (!this.activeGame) {
      this.activeGame = this.gameService.getActiveGame();
    }

    if (!this.activeGame) {
      this.error = 'No active game found';
      return;
    }

    try {
      const instance: any = await this.gameService
        .createGameInstance(this.activeGame.id)
        .toPromise();
      console.log('Created new game instance:', instance, this.activeGame.id);

      if (!instance?.id) {
        throw new Error('Created instance is invalid');
      }

      this.gameInstance = instance;
      localStorage.setItem('currentGameInstanceId', instance.id.toString());
    } catch (error) {
      console.error('Failed to create game instance:', error);
      throw new Error('Failed to create game instance');
    }
    if (this.gameInstance) {
      this.router.navigate(['/public/games/single'], {
        queryParams: {
          game: this.selectedGame,
          instance: this.gameInstance.id,
        },
      });
    }
  }

  public async startGame() {
    try {
      this.loading = true;
      this.error = null;

      // Get active game and validate
      if (!this.activeGame) {
        this.activeGame = this.gameService.getActiveGame();
      }

      // It's crucial this.activeGame and its relevant properties are available.
      // The original check for gameParticipation might be too strict if the API is down.
      // We'll rely on specific error messages in the catch block for the workaround.
      if (!this.activeGame?.id) { // Simpler check, more specific checks in catch for workaround
        console.error('Initial check: Active game or game ID is missing.', this.activeGame);
        throw new Error('No active game found or game ID is missing');
      }
      
      // Attempt to ensure selectedGame is set early if possible
      if (!this.selectedGame && this.activeGame?.gameType?.typeDescription?.code) {
        this.selectedGame = this.activeGame.gameType.typeDescription.code.toLowerCase();
      }

      if (!this.gameInstance) {
        console.log('Creating new game instance for game:', this.activeGame.id);

        try {
          const instance = await this.gameService
            .createGameInstance(this.activeGame.id)
            .toPromise();
          console.log('Created new game instance:', instance);

          if (!instance?.id) {
            throw new Error('Created instance is invalid');
          }

          this.gameInstance = instance;
          localStorage.setItem('currentGameInstanceId', instance.id.toString());
        } catch (error) {
          // This specific catch is for instance creation failure
          console.error('Failed to create game instance during initial attempt:', error);
          // We will let the main catch block handle this for the workaround logic
          throw error; // Re-throw to be caught by the main catch block
        }
      }

      // Navigate to game component
      this.navigateToGame();

    } catch (error) {
      console.error('Error starting game:', error); // Keep this original log

      const gameCode = this.activeGame?.gameType?.typeDescription?.code?.toLowerCase();
      const WORKAROUND_GAMES = ['wordle', 'memory', '2048', 'snake'];
      const isWorkaroundGame = gameCode && WORKAROUND_GAMES.includes(gameCode);
      let errorMessage = error instanceof Error ? error.message : 'Unknown error starting game';

      const workaroundErrorMessages = [
        'No active game configuration found',
        'No active game found or game ID is missing', // From our initial check
        'Failed to create game instance',
        'Created instance is invalid'
      ];

      if (isWorkaroundGame && 
          (workaroundErrorMessages.includes(errorMessage) || errorMessage.includes('Http failure response'))
         ) {
        console.warn(`Workaround Activated: Navigating for ${gameCode} despite error: "${errorMessage}"`);
        
        if (!this.activeGame?.id) {
          console.error('Workaround cannot proceed: activeGame.id is missing. Cannot create dummy instance or navigate.');
          this.error = 'Workaround failed: Essential game data (activeGame.id) is missing.';
        } else {
          if (!this.gameInstance) {
            this.gameInstance = {
              id: Date.now(), // Unique placeholder ID
              gameId: this.activeGame.id,
              status: 'WORKAROUND_ACTIVE',
              // Ensure GameInstance interface basic requirements are met, even if empty arrays/default values
              createdOn: new Date().toISOString(),
              createdBy: 'workaround_user',
              version: 0,
              account: 0, // Or a sensible default/placeholder
              gameEvents: [],
              startDate: new Date().toISOString(), // Added to satisfy GameInstance type
              endDate: new Date().toISOString(),   // Added to satisfy GameInstance type (adjust if a future date is more appropriate)
            } as GameInstance; // Type assertion
            localStorage.setItem('currentGameInstanceId', this.gameInstance.id.toString());
            console.log('Created dummy game instance for workaround:', this.gameInstance);
          }
    
          // Ensure this.selectedGame is set, as navigateToGame relies on it for queryParams
          if (!this.selectedGame && this.activeGame?.gameType?.typeDescription?.code) {
            this.selectedGame = this.activeGame.gameType.typeDescription.code.toLowerCase();
            console.log('Workaround: Ensured this.selectedGame is set to:', this.selectedGame);
          }
    
          if (this.gameInstance && this.selectedGame) {
            console.log(`Workaround: Attempting to navigate to game: ${this.selectedGame} with instance: ${this.gameInstance.id}`);
            this.navigateToGame(); // Attempt navigation
          } else {
            console.error('Workaround navigation aborted: gameInstance or selectedGame could not be set/confirmed.');
            this.error = 'Workaround navigation failed: Could not prepare necessary data (gameInstance or selectedGame).';
            if (!this.gameInstance) console.error('Reason: gameInstance is null/undefined.');
            if (!this.selectedGame) console.error('Reason: selectedGame is null/undefined.');
          }
        }
      } else { // Default error handling for non-workaround cases or unhandled errors
        this.error = errorMessage;
      }
    } finally {
      this.loading = false;
    }
  }

  // Add missing methods
  getDuration(duration: number): string {
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    }
    return `${seconds}s`;
  }

  formatTime(seconds: number): string {
    if (!seconds) return '0';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    if (minutes > 0) {
      return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }
    return remainingSeconds.toString();
  }
}

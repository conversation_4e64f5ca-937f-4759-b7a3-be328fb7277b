import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

import { GameService } from 'lp-client-api';
import { GamesHeaderComponent } from '../../../../features/games/games/components/games-header/games-header.component';
import { Game, GameConfigs } from 'lp-client-api';
import { forkJoin } from 'rxjs';
import { map } from 'rxjs/operators';

interface GameViewModel {
  game: Game;
  display: GameDisplay;
  playable?: boolean;
  activeInstance?: number;
}

interface GameDisplay {
  description: string;
  image: string;
  genre: string;
  difficulty: string;
  frequency: string;
  attempts: number;
}

@Component({
  selector: 'games-all',
  templateUrl: './all.component.html',
  styleUrls: ['./all.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    IonicModule,
    GamesHeaderComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class AllGamesComponent implements OnInit {
  private readonly MEMKEY = 517193;
  public games: GameViewModel[] = [];
  public filteredGames: GameViewModel[] = [];
  public loading = true;
  public error: string | null = null;
  public searchTerm = '';

  constructor(
    private router: Router,
    private gameService: GameService
  ) {}

  ngOnInit() {
    this.loadGames();
  }

  private loadGames() {
    // Get both all games and account-specific games
    forkJoin({
      allGames: this.gameService.getAllGameConfigs(),
      accountGames: this.gameService.getAccountGameConfigs(this.MEMKEY)
    }).subscribe({
      next: ({ allGames, accountGames }) => {
        console.log('All Games:', allGames);
        console.log('Account Games:', accountGames);

        // Map games with playability info
        this.games = allGames.map(game => {
          // Find matching account config
          const accountConfig = accountGames.find(
            acc => acc.gameConfig === game.id
          );

          return {
            game,
            display: this.getGameDisplay(game),
            playable: !!accountConfig, // This should now be true for all games with our modified GameService
            activeInstance: accountConfig?.gameInstance || undefined
          };
        });

        // Initialize filtered games
        this.filterGames();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading games:', error);
        this.error = 'Failed to load games';
        this.loading = false;
      }
    });
  }

  private getGameDisplay(game: Game): GameDisplay {
    const typeCode = game.gameType.typeDescription.code;
    const config = game.gameConfig[0];

    return {
      description: game.gameType.typeDescription.description,
      image: game.backgroundImage || `assets/images/games/${typeCode.toLowerCase()}.jpg`,
      genre: game.gameType.categoryDescription,
      difficulty: config?.difficulty || 'MEDIUM',
      frequency: config?.frequency || 'DAILY',
      attempts: config?.frequencyAttempts || 3
    };
  }

  public filterGames(event?: Event) {
    if (event) {
      this.searchTerm = (event.target as HTMLInputElement).value.toLowerCase();
    }

    this.filteredGames = this.games.filter(gameViewModel => {
      const game = gameViewModel.game;
      const display = gameViewModel.display;
      return (
        game.name.toLowerCase().includes(this.searchTerm) ||
        display.description.toLowerCase().includes(this.searchTerm) ||
        display.genre.toLowerCase().includes(this.searchTerm)
      );
    });
  }

  public playGame(gameViewModel: GameViewModel) {
    if (!gameViewModel.playable) {
      console.warn('Game is not playable for this account');
      return;
    }

    console.log('Playing game:', gameViewModel);
    localStorage.setItem('activeGame', JSON.stringify(gameViewModel.game));

    // Get the game type code for routing
    const typeCode = gameViewModel.game.gameType.typeDescription.code.toLowerCase();
    console.log('Game type code:', typeCode);

    this.router.navigate(['/public/games/dashboard'], {
      queryParams: {
        game: typeCode,
        instance: gameViewModel.activeInstance
      }
    });
  }
}

<div class="flex overflow-y-auto flex-col h-screen bg-green-800">
  <div class="absolute inset-0 z-0 h-screen bg-green-800 animated-bg"></div>
  <div class="overflow-y-auto absolute inset-0 z-10 pb-24 space-y-4 h-screen">
    <games-header bgColor="green" class="z-50"></games-header>
    <h1 class="mb-4 text-3xl font-bold text-center text-green-100">
      All Games
    </h1>
    <div class="games-container">
      <div class="search-container">
        <input
          type="text"
          placeholder="Search games..."
          (input)="filterGames($event)"
          [value]="searchTerm"
          class="search-input"
        >
      </div>

      <div *ngIf="loading" class="loading">
        Loading games...
      </div>

      <div *ngIf="error" class="error">
        {{ error }}
      </div>

      <div *ngIf="!loading && !error" class="games-grid">
        <div *ngFor="let gameViewModel of filteredGames"
             class="game-card"
             [class.playable]="gameViewModel.playable"
             (click)="playGame(gameViewModel)">

          <div class="game-image">
            <img [src]="gameViewModel.display.image" [alt]="gameViewModel.game.name">
          </div>

          <div class="game-info">
            <h3>{{ gameViewModel.game.name }}</h3>
            <p class="description">{{ gameViewModel.display.description }}</p>

            <div class="game-details">
              <span class="genre">{{ gameViewModel.display.genre }}</span>
              <span class="difficulty">{{ gameViewModel.display.difficulty }}</span>
              <span class="frequency">{{ gameViewModel.display.frequency }}</span>
              <span class="attempts">{{ gameViewModel.display.attempts }} attempts</span>
            </div>

            <div class="game-status">
              <span *ngIf="gameViewModel.playable" class="status playable">
                Playable {{ gameViewModel.activeInstance ? '(Active)' : '' }}
              </span>
              <span *ngIf="!gameViewModel.playable" class="status not-playable">
                Not Playable
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

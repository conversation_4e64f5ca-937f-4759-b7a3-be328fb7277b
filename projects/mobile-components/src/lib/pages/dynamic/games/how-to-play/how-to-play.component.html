<div
    class="flex overflow-hidden flex-col min-h-screen bg-orange-200 bp-100"
>
<div class="absolute inset-0 z-0 h-screen bg-orange-400 animated-bg"></div>
<div class="overflow-hidden absolute inset-0 z-10 h-screen">
    <games-header bgColor="orange" />

    <games-name />

  <div class="flex z-50 justify-center items-center px-4 mt-8">
    <div
  class="p-4 text-center bg-orange-500 rounded-lg shadow-md transition transform cursor-pointer hover:bg-orange-200 hover:shadow-lg hover:scale-105"
>
      <p class="px-4 text-xl text-yellow-500">{{ getGameRules() }}</p>
    </div>
    </div>
  </div>
</div>

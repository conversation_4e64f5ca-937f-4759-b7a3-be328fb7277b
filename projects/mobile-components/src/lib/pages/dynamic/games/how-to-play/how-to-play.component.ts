import { Component, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { GamesHeaderComponent } from '../../../../features/games/games/components/games-header/games-header.component';
import { GamesNameComponent } from '../../../../features/games/games/components/games-name/games-name.component';

@Component({
  selector: 'games-how-to-play',
  templateUrl: './how-to-play.component.html',
  styleUrls: ['./how-to-play.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    GamesHeaderComponent,
    GamesNameComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class HowToPlayComponent implements OnInit {
  games: string[] = [
    '2048',
    'blackjack',
    'breakout',
    'chess',
    'crossword',
    'flappy-bird',
    'hangman',
    'memory',
    'minesweeper',
    'pac-man',
    'platformer',
    'poker',
    'pong',
    'puzzle',
    'quiz',
    'racing',
    'rock-paper-scissors',
    'simon-says',
    'snake',
    'sudoku',
    'tetris',
    'tic-tac-toe',
    'tower-defense',
    'trivia',
    'wordle',
    'wheel-spin',
    'candy-crush',
    'solitaire',
  ];
  selectedGame: string | null = null;
  constructor(private route: ActivatedRoute, private router: Router) {}

  ngOnInit() {
    this.route.queryParams.subscribe((params) => {
      console.log('Query params:', params);
      const game = params['game'];
      console.log('Game:', game);
      if (game) {
        const lowercaseGame = game.toLowerCase();
        if (this.games.includes(lowercaseGame)) {
          this.selectedGame = lowercaseGame;
        } else {
          this.selectedGame = 'blackjack'; // Default game if not specified or invalid
        }
      } else {
        this.selectedGame = 'blackjack'; // Default game if not specified
      }
    });
  }
  getGameRules(): string {
    switch (this.selectedGame) {
      case '2048':
        return '2048 is a single-player puzzle game where the player must slide numbered tiles on a grid to combine them to create a tile with the number 2048. The game is won when the player successfully creates a tile with the number 2048. If the player is unable to make any further moves, the game is lost.';
      case 'blackjack':
        return "Blackjack is a card game where the player must beat the dealer. The player wins if the sum of their cards is greater than the dealer's cards, but less than 21. If the player's cards sum to 21, the player wins automatically. If the player's cards sum to more than 21, the player loses.";
      case 'breakout':
        return 'Breakout is a single-player game where the player must destroy all the bricks on the screen by hitting them with a ball. The player wins if they destroy all the bricks. If the ball hits the bottom of the screen, the player loses.';
      case 'chess':
        return "Chess is a two-player strategy game where the player must defeat the opponent's king. The player wins if they capture the opponent's king. If the player's king is captured, the player loses.";
      case 'crossword':
        return 'Crossword is a word puzzle game where the player must fill in the blanks in a crossword puzzle. The player wins if they fill in all the blanks correctly. If the player is unable to fill in all the blanks, the player loses.';
      case 'flappy-bird':
        return 'Flappy Bird is a single-player game where the player must navigate a bird through a series of pipes without hitting them. The player wins if they successfully navigate through all the pipes. If the bird hits a pipe, the player loses.';
      case 'hangman':
        return 'Hangman is a word puzzle game where the player must guess the letters in a word by selecting them from a list of letters. The player wins if they guess all the letters in the word. If the player is unable to guess all the letters in the word, the player loses.';
      case 'memory':
        return 'Memory is a single-player game where the player must match pairs of cards by flipping them over. The player wins if they match all the pairs correctly. If the player is unable to match all the pairs, the player loses.';
      case 'minesweeper':
        return 'Minesweeper is a single-player game where the player must navigate a minefield without detonating any mines. The player wins if they successfully navigate through the minefield. If the player detonates a mine, the player loses.';
      case 'pac-man':
        return 'Pac-Man is a single-player game where the player must navigate a maze while eating pellets and avoiding ghosts. The player wins if they successfully navigate through the maze without being caught by the ghosts. If the player is caught by a ghost, the player loses.';
      case 'platformer':
        return 'Platformer is a single-player game where the player must navigate a character through a series of levels, avoiding obstacles and enemies. The player wins if they successfully navigate through all the levels. If the player is caught by an enemy, the player loses.';
      case 'pong':
        return "Pong is a two-player game where the player must hit a ball past the opponent's paddle. The player wins if they successfully hit the ball past the opponent's paddle. If the player is unable to hit the ball past the opponent's paddle, the player loses.";
      case 'poker':
        return "Poker is a card game where the player must beat the dealer. The player wins if the sum of their cards is greater than the dealer's cards, but less than 21. If the player's cards sum to 21, the player wins automatically. If the player's cards sum to more than 21, the player loses.";
      case 'puzzle':
        return 'Puzzle is a single-player game where the player must solve a puzzle by arranging pieces in a specific pattern. The player wins if they successfully solve the puzzle. If the player is unable to solve the puzzle, the player loses.';
      case 'quiz':
        return 'Quiz is a single-player game where the player must answer a series of questions. The player wins if they successfully answer all the questions. If the player is unable to answer all the questions, the player loses.';
      case 'racing':
        return 'Racing is a single-player game where the player must navigate a car through a series of tracks. The player wins if they successfully navigate through all the tracks. If the player is unable to navigate through all the tracks, the player loses.';
      case 'rock-paper-scissors':
        return 'Rock-Paper-Scissors is a two-player game where the player must beat the opponent by choosing the correct option. The player wins if they choose the correct option. If the player is unable to choose the correct option, the player loses.';
      case 'simon-says':
        return 'Simon Says is a single-player game where the player must follow the instructions given by the game. The player wins if they successfully follow all the instructions. If the player is unable to follow all the instructions, the player loses.';
      case 'snake':
        return 'Snake is a single-player game where the player must navigate a snake through a series of levels, avoiding obstacles and enemies. The player wins if they successfully navigate through all the levels. If the player is caught by an enemy, the player loses.';
      case 'sudoku':
        return 'Sudoku is a single-player game where the player must fill in a grid of numbers so that each row, column, and 3x3 box contains the numbers 1-9. The player wins if they successfully fill in the grid correctly. If the player is unable to fill in the grid correctly, the player loses.';
      case 'tetris':
        return 'Tetris is a single-player game where the player must navigate a falling block through a series of levels, avoiding obstacles and enemies. The player wins if they successfully navigate through all the levels. If the player is unable to navigate through all the levels, the player loses.';
      case 'tic-tac-toe':
        return 'Tic-Tac-Toe is a two-player game where the player must beat the opponent by choosing the correct option. The player wins if they choose the correct option. If the player is unable to choose the correct option, the player loses.';
      case 'tower-defense':
        return 'Tower Defense is a single-player game where the player must defend a tower from waves of enemies. The player wins if they successfully defend the tower from all the waves of enemies. If the player is unable to defend the tower from all the waves of enemies, the player loses.';
      case 'trivia':
        return 'Trivia is a single-player game where the player must answer a series of questions. The player wins if they successfully answer all the questions. If the player is unable to answer all the questions, the player loses.';
      case 'wordle':
        return 'Wordle is a single-player game where the player must guess a word by selecting letters from a list of letters. The player wins if they successfully guess the word. If the player is unable to guess the word, the player loses.';
      case 'wheel-spin':
        return 'Wheel Spin is a single-player game where the player must spin a wheel and land on a specific color. The player wins if they successfully land on the specific color. If the player is unable to land on the specific color, the player loses.';
      case 'candy-crush':
        return 'Candy Crush is a single-player game where the player must match pairs of candies by clicking on them. The player wins if they successfully match all the pairs of candies. If the player is unable to match all the pairs of candies, the player loses.';
      case 'solitaire':
        return 'Solitaire is a single-player game where the player must navigate a deck of cards through a series of levels, avoiding obstacles and enemies. The player wins if they successfully navigate through all the levels. If the player is unable to navigate through all the levels, the player loses.';
      // ... add cases for other games
      default:
        return 'No rules available for this game.';
    }
  }
}

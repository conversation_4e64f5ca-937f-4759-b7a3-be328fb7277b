import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { HowToPlayComponent } from './how-to-play.component';
import { GamesHeaderComponent } from '../../../../games/components/games-header/games-header.component';
import { GamesNameComponent } from '../../../../games/components/games-name/games-name.component';

@NgModule({
  declarations: [
    HowToPlayComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    RouterModule,
    GamesHeaderComponent,
    GamesNameComponent
  ],
  exports: [
    HowToPlayComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class HowToPlayModule { }

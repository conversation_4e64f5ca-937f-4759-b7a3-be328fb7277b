import {
  Component,
  <PERSON>,
  OnInit,
  AfterViewInit,
  Query<PERSON>ist,
  ViewChildren,
  ElementRef,
  CUSTOM_ELEMENTS_SCHEMA,
  NO_ERRORS_SCHEMA
} from '@angular/core';
import { ActivatedRoute, Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { BaseModule } from '../../../../base/base.module';
import { GamesHeaderComponent } from '../../../../features/games/games/components/games-header/games-header.component';
import { GameService } from 'lp-client-api';

@Component({
  selector: 'app-games-categories',
  templateUrl: './categories.component.html',
  styleUrls: ['./categories.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    FormsModule,
    BaseModule,
    GamesHeaderComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA],
  styles: [
    `
      @keyframes fadeIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      :host ::ng-deep .animate-fade-in {
        animation: fadeIn 0.5s ease-out forwards;
      }

      :host ::ng-deep .base-card {
        opacity: 0;
      }
    `,
  ],
})
export class GamesCategoriesComponent implements OnInit, AfterViewInit {
  [x: string]: any;
  categoryName: string = 'All';
  gameCategories: {
    [key: string]: { name: string; description: string; image: string }[];
  } = {
    puzzle: [
      {
        name: '2048',
        description: 'Sliding number puzzle',
        image: 'assets/images/games/2048.jpeg',
      },
      {
        name: 'crossword',
        description: 'Crossword puzzle',
        image: 'assets/images/games/crossword.jpeg',
      },
      {
        name: 'memory',
        description: 'Memory game',
        image: 'assets/images/games/memory.jpeg',
      },
      {
        name: 'minesweeper',
        description: 'Minesweeper game',
        image: 'assets/images/games/minesweeper.jpeg',
      },
      {
        name: 'puzzle',
        description: 'Puzzle game',
        image: 'assets/images/games/puzzle.jpeg',
      },
      {
        name: 'sudoku',
        description: 'Sudoku game',
        image: 'assets/images/games/sudoku.jpeg',
      },
      {
        name: 'tetris',
        description: 'Tetris game',
        image: 'assets/images/games/tetris.jpeg',
      },
      {
        name: 'wordle',
        description: 'Wordle game',
        image: 'assets/images/games/wordle.jpeg',
      },
    ],
    card: [
      {
        name: 'blackjack',
        description: 'Blackjack game',
        image: 'assets/images/games/black-jack.jpeg',
      },
      {
        name: 'poker',
        description: 'Poker game',
        image: 'assets/images/games/poker.jpeg',
      },
    ],
    arcade: [
      {
        name: 'breakout',
        description: 'Breakout game',
        image: 'assets/images/games/breakout.jpeg',
      },
      {
        name: 'flappy-bird',
        description: 'Flappy Bird game',
        image: 'assets/images/games/flappy-bird.jpeg',
      },
      {
        name: 'pac-man',
        description: 'Pac-Man game',
        image: 'assets/images/games/pacman.jpeg',
      },
      {
        name: 'pong',
        description: 'Pong game',
        image: 'assets/images/games/pong.jpeg',
      },
      {
        name: 'snake',
        description: 'Snake game',
        image: 'assets/images/games/snake.jpg',
      },
    ],
    strategy: [
      {
        name: 'chess',
        description: 'Chess game',
        image: 'assets/images/games/chess.jpeg',
      },
    ],
    action: [
      {
        name: 'platformer',
        description: 'Platformer game',
        image: 'assets/images/games/platformer.jpeg',
      },
      {
        name: 'racing',
        description: 'Racing game',
        image: 'assets/images/games/racing.jpeg',
      },
    ],
    trivia: [
      {
        name: 'hangman',
        description: 'Hangman game',
        image: 'assets/images/games/hangman.jpeg',
      },
    ],
    sports: [
      {
        name: 'rock-paper-scissors',
        description: 'Rock-Paper-Scissors game',
        image: 'assets/images/games/rock-paper-scissors.jpeg',
      },
    ],
  };

  filteredGames: any[] = [];
  games: any[] = [];
  searchTerm: string = '';
  searchResults: any[] = [];

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private gameService: GameService
  ) {}

  // Add property to store all games from API
  private allGames: any[] = [];

  ngOnInit() {
    // Load all games first
    this.gameService.getAllGameConfigs().subscribe({
      next: (games) => {
        console.log('Games loaded successfully from API:', games);
        this.allGames = games;

        // Process the category from query params
        this.processQueryParams();
      },
      error: (error) => {
        console.error('Error loading games from API:', error);
        // Fall back to hardcoded data
        this.processQueryParams();
      }
    });
  }

  private processQueryParams() {
    this.route.queryParams.subscribe((queryParams) => {
      console.log('Query params:', queryParams);
      this.categoryName = queryParams['category'] || 'All';
      this.processCategory();
    });
  }

  private processCategory() {
    const normalizedCategory = this.categoryName.toLowerCase();
    console.log('Category from URL:', this.categoryName);
    console.log('Normalized category:', normalizedCategory);

    if (this.allGames.length > 0) {
      // Log all game categories for debugging
      console.log('All game categories:', this.allGames.map(game => ({
        name: game.name,
        category: game.gameType?.categoryDescription
      })));

      // Use real game data if available
      console.log('Using real game data from API');

      if (normalizedCategory === 'all' || !normalizedCategory) {
        // Show all games
        this.filteredGames = this.allGames.map(game => this.mapGameToDisplay(game));
      } else {
        // Try different matching strategies
        // 1. Exact match (case-insensitive)
        let filteredGames = this.allGames.filter(game => {
          const gameCategory = game.gameType?.categoryDescription?.toLowerCase() || '';
          return gameCategory === normalizedCategory;
        });

        // 2. If no exact matches, try partial match
        if (filteredGames.length === 0) {
          filteredGames = this.allGames.filter(game => {
            const gameCategory = game.gameType?.categoryDescription?.toLowerCase() || '';
            return gameCategory.includes(normalizedCategory) || normalizedCategory.includes(gameCategory);
          });
        }

        // 3. If still no matches, try matching individual words
        if (filteredGames.length === 0) {
          filteredGames = this.allGames.filter(game => {
            const gameCategory = game.gameType?.categoryDescription?.toLowerCase() || '';
            const categoryWords = gameCategory.split(/\s+/);
            return categoryWords.some((word: string) => word === normalizedCategory);
          });
        }

        // 4. Try matching by game type (some games might be categorized by type rather than category)
        if (filteredGames.length === 0) {
          filteredGames = this.allGames.filter(game => {
            const gameType = game.gameType?.typeDescription?.code?.toLowerCase() || '';
            return gameType.includes(normalizedCategory) || normalizedCategory.includes(gameType);
          });
        }

        // 5. Special cases for common categories
        if (filteredGames.length === 0) {
          // Define game types for common categories
          const categoryGameTypes: { [key: string]: string[] } = {
            'arcade': ['snake', 'pacman', 'tetris', 'breakout', 'flappy-bird', 'pong'],
            'puzzle': ['2048', 'memory', 'minesweeper', 'sudoku', 'wordle', 'crossword'],
            'card': ['blackjack', 'poker', 'solitaire'],
            'strategy': ['chess', 'tower-defense'],
            'word': ['hangman', 'wordle', 'crossword'],
            'sports': ['rock-paper-scissors']
          };

          // Check if we have a special case for this category
          const gameTypes = categoryGameTypes[normalizedCategory];
          if (gameTypes) {
            console.log(`Using special case for category '${normalizedCategory}'`);
            filteredGames = this.allGames.filter(game => {
              const gameType = game.gameType?.typeDescription?.code?.toLowerCase() || '';
              return gameTypes.includes(gameType);
            });
          }
        }

        // Map the filtered games to display format
        this.filteredGames = filteredGames.map(game => this.mapGameToDisplay(game));

        console.log('Filtered games by category:', this.filteredGames);

        // If no games found, show all games with a warning
        if (this.filteredGames.length === 0) {
          console.warn(`No games found for category '${this.categoryName}' in API data. Showing all games.`);
          this.filteredGames = this.allGames.map(game => this.mapGameToDisplay(game));
        }
      }
    } else {
      // Fall back to hardcoded data
      console.log('Falling back to hardcoded game data');

      if (this.gameCategories[normalizedCategory]) {
        this.filteredGames = this.gameCategories[normalizedCategory];
      } else {
        // Try to find a matching category in the hardcoded data
        const matchingCategory = Object.keys(this.gameCategories).find(key =>
          key.includes(normalizedCategory) || normalizedCategory.includes(key)
        );

        if (matchingCategory) {
          console.log(`Found matching category '${matchingCategory}' for '${normalizedCategory}'`);
          this.filteredGames = this.gameCategories[matchingCategory];
        } else {
          console.warn(`Category '${this.categoryName}' not found in hardcoded data. Showing all games.`);
          this.filteredGames = this.getAllGames();
        }
      }
    }

    this.filterGames();
  }

  private mapGameToDisplay(game: any) {
    const typeCode = game.gameType?.typeDescription?.code || 'default';

    return {
      name: game.name,
      description: game.gameType?.typeDescription?.description || '',
      image: game.backgroundImage || `assets/images/games/${typeCode.toLowerCase()}.jpg`,
      id: typeCode.toLowerCase()
    };
  }

  navigateToGame(gameId: string) {
    // Load all games to find the matching one
    this.gameService.getAllGameConfigs().subscribe({
      next: (games) => {
        // Find the game with matching type code
        const game = games.find(g =>
          g.gameType?.typeDescription?.code?.toLowerCase() === gameId.toLowerCase()
        );

        if (game) {
          console.log(`Found game with ID ${gameId}:`, game);

          // Store the game in localStorage and set as active
          localStorage.setItem('activeGame', JSON.stringify(game));
          this.gameService.setActiveGame(game);

          // Navigate to dashboard with the correct game type
          this.router.navigate(['/public/games/dashboard'], {
            queryParams: { game: gameId },
          });
        } else {
          console.error(`Game with ID ${gameId} not found`);
        }
      },
      error: (error) => {
        console.error('Error loading games:', error);

        // Fall back to direct navigation if game loading fails
        this.router.navigate(['/public/games/dashboard'], {
          queryParams: { game: gameId },
        });
      }
    });
  }

  filterGames() {
    const searchTerm = this.searchTerm.toLowerCase();
    console.log('Search term:', searchTerm);
    this.searchResults = this.filteredGames.filter((game) =>
      game.name.toLowerCase().includes(searchTerm)
    );
    console.log('Search results:', this.searchResults);
  }

  private getAllGames(): {
    name: string;
    description: string;
    image: string;
  }[] {
    return ([] as any[]).concat(...Object.values(this.gameCategories));
  }

  @ViewChildren('gameCard') gameCards!: QueryList<ElementRef>;

  ngAfterViewInit() {
    console.log('ngAfterViewInit called');
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          console.log('Entry intersecting:', entry.isIntersecting);
          if (entry.isIntersecting) {
            entry.target.classList.add('animate-fade-in');
            observer.unobserve(entry.target);
          }
        });
      },
      { threshold: 0.1 }
    );

    console.log('Number of game cards:', this.gameCards.length);
    this.gameCards.forEach((el) => {
      if (el.nativeElement instanceof Element) {
        observer.observe(el.nativeElement);
      } else {
        console.warn('Invalid element:', el.nativeElement);
      }
    });
  }
}

<div class="flex overflow-y-auto flex-col h-screen bg-navy-800">
  <div class="absolute inset-0 z-0 h-screen bg-navy-800 animated-bg"></div>
  <div class="overflow-y-auto absolute inset-0 z-10 pb-12 space-y-4 h-screen">
    <games-header bgColor="navy" class='z-50'/>
   
    <!-- New search input -->
    <div class="flex justify-center px-4">
      <input
        type="text"
        [(ngModel)]="searchTerm"
        (input)="filterGames()"
        placeholder="Search games..."
        class="px-4 py-2 w-full max-w-md rounded-lg text-navy-800 bg-navy-100 focus:outline-none focus:ring-2 focus:ring-primary-500"
      />
    </div>
    <div
      class="grid overflow-y-auto flex-grow grid-cols-1 gap-4 p-4 category-list md:grid-cols-2 lg:grid-cols-3"
    >
      <app-card
        *ngFor="let game of searchResults"
        class="mb-4 category-item group-hover:bg-navy-200 animate-fade-in-right"
        #gameCard
      >
        <div
          class="overflow-hidden bg-white rounded-lg border shadow-lg border-navy-700 group-hover:bg-primary-500"
        >
          <img [src]="game.image" [alt]="game.name" class="mx-auto h-48" />
          <hr class="border-t border-gray-500" />
          <div class="p-4 bg-navy-200 group-hover:bg-navy-300">
            <h3 class="mb-2 text-2xl font-semibold text-navy-800">
              {{ game.name }}
            </h3>
            <p class="mb-4 text-navy-700 group-hover:text-navy-100">
              {{ game.description }}
            </p>
            <button
              class="px-4 py-2 text-white rounded shadow-md transition bg-navy-500 hover:bg-navy-600"
              (click)="navigateToGame(game.id || game.name)"
            >
              Play Now
            </button>
          </div>
        </div>
      </app-card>
    </div>
  </div>
</div>

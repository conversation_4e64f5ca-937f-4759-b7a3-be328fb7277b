import { NgModule, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';

// Import standalone components
import { DashboardComponent } from './dashboard/dashboard.component';
import { HowToPlayComponent } from './how-to-play/how-to-play.component';

// Import modules
import { GamesComponentsModule } from '../../../games/components/games-components.module';
import { GamesCategoriesModule } from './categories/categories.module';
import { GamesSingleModule } from './single/single.module';
import { AllGamesModule } from './all/all.module';

@NgModule({
  declarations: [],
  imports: [
    CommonModule,
    FormsModule,
    RouterModule,
    IonicModule,
    // Import standalone components
    DashboardComponent,
    HowToPlayComponent,
    GamesComponentsModule,
    GamesCategoriesModule,
    GamesSingleModule,
    AllGamesModule
  ],
  exports: [
    DashboardComponent,
    HowToPlayComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA] // Add schema for web components like ion-icon
})
export class DynamicGamesModule { }

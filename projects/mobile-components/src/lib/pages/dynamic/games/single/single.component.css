.animated-bg {
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.1),
      rgba(255, 255, 255, 0.1) 20px,
      transparent 20px,
      transparent 40px
    );
    animation: move 20s linear infinite;
    z-index: 0;
  }
}

@keyframes move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 282px 0;
  }
}

.game-container {
  min-height: 100vh;
  background-size: cover;
  background-position: center;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.5rem;
}

.game-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
}

.game-selector {
  padding: 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #ccc;
  background: white;
}

.game-content {
  flex: 1;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 0.5rem;
  padding: 1rem;
  display: flex;
  flex-direction: column;
}

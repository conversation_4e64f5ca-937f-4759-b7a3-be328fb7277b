import { Component, Type, OnInit, Injector, Inject, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA } from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { GameService, LssConfig } from 'lp-client-api';
import { GameConfigService } from '../../../../shared/services/game-config.service';
import { GamesHeaderComponent } from '../../../../features/games/games/components/games-header/games-header.component';
// import { ConfigService } from 'projects/lp-client/src/app/services/config.service';
// Import the GamesConfig from lss-config.ts
import { SnakeComponent } from '../../../../features/games/games/snake/snake.component';
import { HangmanComponent } from '../../../../features/games/games/hangman/hangman.component';
import { TicTacToeComponent } from '../../../../features/games/games/tic-tac-toe/tic-tac-toe.component';
import { MemoryComponent } from '../../../../features/games/games/memory/memory.component';
import { PuzzleComponent } from '../../../../features/games/games/puzzle/puzzle.component';
import { QuizComponent } from '../../../../features/games/games/quiz/quiz.component';
import { RacingComponent } from '../../../../features/games/games/racing/racing.component';
import { TriviaComponent } from '../../../../features/games/games/trivia/trivia.component';
import { WordleComponent } from '../../../../features/games/games/wordle/wordle.component';
import { FlappyBirdComponent } from '../../../../features/games/games/flappy-bird/flappy-bird.component';
import { BlackjackComponent } from '../../../../features/games/games/blackjack/blackjack.component';
import { SudokuComponent } from '../../../../features/games/games/sudoku/sudoku.component';
import { MinesweeperComponent } from '../../../../features/games/games/minesweeper/minesweeper.component';
import { Twenty48Component } from '../../../../features/games/games/2048/2048.component';
import { ChessComponent } from '../../../../features/games/games/chess/chess.component';
import { CrosswordComponent } from '../../../../features/games/games/crossword/crossword.component';
import { PokerComponent } from '../../../../features/games/games/poker/poker.component';
import { TetrisComponent } from '../../../../features/games/games/tetris/tetris.component';
import { TowerDefenseComponent } from '../../../../features/games/games/tower-defense/tower-defense.component';
import { PlatformerComponent } from '../../../../features/games/games/platformer/platformer.component';
import { RockPaperScissorsComponent } from '../../../../features/games/games/rock-paper-scissors/rock-paper-scissors.component';
import { SimonSaysComponent } from '../../../../features/games/games/simon-says/simon-says.component';
import { PongComponent } from '../../../../features/games/games/pong/pong.component';
import { BreakoutComponent } from '../../../../features/games/games/breakout/breakout.component';
import { PacManComponent } from '../../../../features/games/games/pac-man/pac-man.component';
import { WheelSpinComponent } from '../../../../features/games/games/wheel-spin/wheel-spin.component';
import { CandyCrushComponent } from '../../../../features/games/games/candy-crush/candy-crush.component';
import { SolitaireComponent } from '../../../../features/games/games/solitaire/solitaire.component';
import { HotOrColdComponent } from '../../../../features/games/games/hot-or-cold/hot-or-cold.component';
import { SubmitSelfieComponent } from '../../../../features/games/games/submit-selfie/submit-selfie.component';
import { TreasureHuntComponent } from '../../../../features/games/games/treasure-hunt/treasure-hunt.component';

@Component({
  selector: 'app-games-single',
  templateUrl: './single.component.html',
  styleUrls: ['./single.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    GamesHeaderComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class GamesSingleComponent implements OnInit {
  selectedGame: string | null = null;
  gameInstance: any = null;
  gameConfig: any = null;
  activeGame: any = null;
  gameComponent: Type<any> | null = null;

  private readonly gameComponentMap: { [key: string]: Type<any> } = {
    'game2048': Twenty48Component,
    blackjack: BlackjackComponent,
    breakout: BreakoutComponent,
    chess: ChessComponent,
    crossword: CrosswordComponent,
    'flappy-bird': FlappyBirdComponent,
    hangman: HangmanComponent,
    memory: MemoryComponent,
    minesweeper: MinesweeperComponent,
    'pac-man': PacManComponent,
    platformer: PlatformerComponent,
    pong: PongComponent,
    poker: PokerComponent,
    puzzle: PuzzleComponent,
    quiz: QuizComponent,
    racing: RacingComponent,
    'rock-paper-scissors': RockPaperScissorsComponent,
    'simon-says': SimonSaysComponent,
    snake: SnakeComponent,
    sudoku: SudokuComponent,
    tetris: TetrisComponent,
    'tic-tac-toe': TicTacToeComponent,
    'tower-defense': TowerDefenseComponent,
    trivia: TriviaComponent,
    wordle: WordleComponent,
    spinthewheel: WheelSpinComponent,
    'candy-crush': CandyCrushComponent,
    candycrush: CandyCrushComponent,
    solitaire: SolitaireComponent,
    'hot-or-cold': HotOrColdComponent,
    'location-based-photo': SubmitSelfieComponent,
    'treasure-hunt': TreasureHuntComponent,
    treasurehunt: TreasureHuntComponent,
  };

  constructor(
    private router: Router,
    private gameService: GameService,
    private gameConfigService: GameConfigService,
    public injector: Injector,
    // private configService: ConfigService
  ) {}

  ngOnInit() {
    // console.log('CONFIG SERVICE', this.configService.sysConfig);

    // // Initialize GameConfigService with games configuration from ConfigService
    // if (this.configService.sysConfig.games) {
    //   console.log('Setting games configuration from ConfigService to GameConfigService');
    //   const success = this.gameConfigService.setGamesConfig(this.configService.sysConfig.games);
    //   if (success) {
    //     console.log('Games configuration set successfully');
    //   } else {
    //     console.error('Failed to set games configuration');
    //   }
    // } else {
    //   console.error('No games configuration found in ConfigService');
    // }

    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const rawGameCode = urlParams.get('game')?.toLowerCase() || '';
    // Normalize game code by removing spaces and hyphens
    const gameCode = rawGameCode.replace(/[\s-]/g, '');
    console.log('GAME CODE', gameCode)
    const instanceId = parseInt(urlParams.get('instance') || '0');

    if (!gameCode) {
      console.error('No game code provided in URL');
      this.router.navigate(['/games']);
      return;
    }

    // Check if the game component exists directly
    let component = this.gameComponentMap[gameCode];

    // If not found, try to find a matching key by normalizing the keys
    if (!component) {
      const matchingKey = Object.keys(this.gameComponentMap).find(
        key => this.normalizeGameCode(key) === gameCode
      );
      if (matchingKey) {
        component = this.gameComponentMap[matchingKey];
      }
    }

    // Check if we found a component
    if (!component) {
      console.error(`Invalid game code: ${gameCode}`);
      this.router.navigate(['/games']);
      return;
    }

    // Get active game from service
    this.activeGame = this.gameService.getActiveGame();
    console.log('Active game from service:', this.activeGame);
    console.log('Active game from service:', instanceId);

    if (!this.activeGame && instanceId) {
      // Try to load the game instance if we have an instance ID
      this.gameService.getAllGameConfigs().subscribe({
        next: (games) => {
          const game = games.find(
            (g) => g.gameType.typeDescription.code.toLowerCase() === gameCode
          );
          if (game) {
            this.activeGame = game;
            this.gameService.setActiveGame(game);
            this.loadGameWithInstance(gameCode, instanceId);
          } else {
            console.error('Game not found in configs');
            this.router.navigate(['/games']);
          }
        },
        error: (error) => {
          console.error('Error loading game configs:', error);
          this.router.navigate(['/games']);
        },
      });
    } else if (this.activeGame) {
      this.loadGameWithInstance(gameCode, instanceId);
    } else {
      console.error(
        'No active game or instance ID found',
        instanceId,
        this.activeGame
      );
      console.log('------');
      this.router.navigate(['/games']);
    }
  }

  private loadGameWithInstance(gameCode: string, instanceId: number) {
    console.log('Loading game instance:', instanceId);
    console.log('loading game with instance', this.activeGame);
    if (!this.activeGame?.id) {
      console.error('No game config found in active game');
      this.router.navigate(['/games']);
      return;
    }

    const configId = this.activeGame.id;

    // Get game styles from GameConfigService
    const gameStyles = this.getGameStyles(gameCode);
    console.log('Game styles from GameConfigService:', gameStyles);

    if (instanceId) {
      // Load existing instance
      this.gameService.getGameInstance(configId, instanceId).subscribe({
        next: (instance) => {
          this.gameInstance = instance;
          this.gameConfig = {
            ...this.activeGame,
            ...(gameStyles?.config || {}), // Merge game styles
            gameStyles: gameStyles // Keep original styles separately if needed
          };
          this.loadGameComponent(gameCode);
        },
        error: (error) => {
          console.error('Error loading game instance:', error);
          this.router.navigate(['/games']);
        },
      });
    } else {
      // Create new instance
      this.gameService.createGameInstance(configId).subscribe({
        next: (instance) => {
          this.gameInstance = instance;
          this.gameConfig = {
            ...this.activeGame,
            ...(gameStyles?.config || {}), // Merge game styles
            gameStyles: gameStyles // Keep original styles separately if needed
          };
          this.loadGameComponent(gameCode);
        },
        error: (error) => {
          console.error('Error creating game instance:', error);
          this.router.navigate(['/games']);
        },
      });
    }
  }

  private getGameStyles(gameCode: string): any {
    console.log('GAME CODE', gameCode);

    // Make sure GameConfigService is configured
    // if (!this.gameConfigService.isReady() && this.configService.sysConfig.games) {
    //   console.log('GameConfigService not ready, setting games configuration again');
    //   this.gameConfigService.setGamesConfig(this.configService.sysConfig.games);
    // }

    // First try to get the game config using the GameConfigService
    try {
      const gameConfig = this.gameConfigService.getGameById(gameCode);
      console.log('GAME CONFIG from service -----', gameConfig);
      if (gameConfig) {
        return gameConfig.config;
      }
    } catch (error) {
      console.error('Error getting game config from GameConfigService:', error);
    }

    return null;
  }

  private normalizeGameCode(code: string): string {
    return code.toLowerCase().replace(/[\s-]/g, '');
  }

  private loadGameComponent(gameId: string) {
    // Check if the game component exists directly
    let component = this.gameComponentMap[gameId];

    // If not found, try to find a matching key by normalizing the keys
    if (!component) {
      const matchingKey = Object.keys(this.gameComponentMap).find(
        key => this.normalizeGameCode(key) === gameId
      );
      if (matchingKey) {
        component = this.gameComponentMap[matchingKey];
      }
    }

    if (component) {
      // Store the component for ngComponentOutlet
      this.gameComponent = component;
      console.log('Game component loaded:', gameId);
    } else {
      console.error(`No component found for game: ${gameId}`);
      this.router.navigate(['/games']);
    }
  }

  getGameComponent(): { component: Type<any>; injector: Injector } | null {
    if (!this.gameComponent || !this.gameConfig) {
      return null;
    }

    // Create inputs for the dynamic component
    const injector = Injector.create({
      providers: [
        {
          provide: 'config',
          useValue: this.gameConfig,
        },
        {
          provide: 'instance',
          useValue: this.gameInstance || {},
        },
      ],
      parent: this.injector,
    });

    return {
      component: this.gameComponent,
      injector: injector,
    };
  }

  onGameChange(event: Event) {
    const select = event.target as HTMLSelectElement;
    const gameCode = select.value;
    if (gameCode) {
      this.router.navigate(['/public/games/single'], {
        queryParams: {
          game: gameCode,
          instance: this.gameInstance?.id,
        },
      });
    }
  }

  availableGames() {
    return Object.keys(this.gameComponentMap);
  }
}

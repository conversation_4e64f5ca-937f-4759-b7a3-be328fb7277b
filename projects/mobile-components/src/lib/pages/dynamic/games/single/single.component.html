

<div
    class="flex overflow-hidden flex-col min-h-screen bg-blue-500 bp-100"
>
<div class="absolute inset-0 z-0 h-screen bg-blue-500 animated-bg"></div>
<div class="overflow-hidden absolute inset-0 z-10 h-screen">
    <games-header bgColor="blue" />


    <div class="z-10  mx-auto max-w-screen-sm">
        <ng-container *ngIf="activeGame && gameComponent">
            <ng-container *ngComponentOutlet="gameComponent;
                injector: injector;
                inputs: {
                    gameId: activeGame.id,
                    config: gameConfig,
                    gameInstance: gameInstance?.gameInstance,
                    game: activeGame
                }">
            </ng-container>
        </ng-container>
    </div>
  </div>
</div>

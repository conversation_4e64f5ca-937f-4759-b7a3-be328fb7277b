import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-balance-card',
  template: `
    <div class="balance-card" [ngClass]="class">
      <div class="balance-label">{{ text }}</div>
      <div class="balance-amount">{{ amount | number }}</div>
      <div class="balance-points">{{ points }} Points</div>
    </div>
  `,
  styles: [`
    .balance-card {
      padding: 1rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .balance-label {
      font-size: 0.875rem;
      color: #666;
      margin-bottom: 0.5rem;
    }
    .balance-amount {
      font-size: 2rem;
      font-weight: bold;
      margin-bottom: 0.25rem;
    }
    .balance-points {
      font-size: 0.875rem;
      color: #666;
    }
  `],
  standalone: true,
  imports: [CommonModule]
})
export class BalanceCardComponent {
  @Input() text: string = 'Balance';
  @Input() amount: number = 0;
  @Input() points: number = 0;
  @Input() class: string = '';
}

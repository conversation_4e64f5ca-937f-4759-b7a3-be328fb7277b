import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-dynamic-header',
  template: `
    <div class="header-container" [ngClass]="class">
      <img *ngIf="icon" [src]="icon" [alt]="title" class="header-icon">
      <h1 class="header-title">{{ title }}</h1>
      <h2 class="header-subtitle">{{ subtitle }}</h2>
    </div>
  `,
  styles: [`
    .header-container {
      text-align: center;
      padding: 1rem;
    }
    .header-icon {
      max-width: 120px;
      margin: 0 auto 1rem;
    }
    .header-title {
      font-size: 1.5rem;
      font-weight: bold;
      margin-bottom: 0.5rem;
    }
    .header-subtitle {
      font-size: 1rem;
      color: #666;
    }
  `],
  standalone: true,
  imports: [CommonModule]
})
export class DynamicHeaderComponent {
  @Input() title: string = '';
  @Input() subtitle: string = '';
  @Input() icon: string = '';
  @Input() class: string = '';
}

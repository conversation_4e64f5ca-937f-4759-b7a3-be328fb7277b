import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { IonicModule } from '@ionic/angular';

interface Action {
  text: string;
  icon: string;
  link: string;
  class: string;
}

@Component({
  selector: 'app-action-grid',
  template: `
    <div class="action-grid" [ngClass]="class">
      <a *ngFor="let action of actions"
         [routerLink]="action.link"
         [ngClass]="action.class"
         class="action-item">
        <ion-icon [name]="action.icon" class="action-icon"></ion-icon>
        <span class="action-text">{{ action.text }}</span>
      </a>
    </div>
  `,
  styles: [`
    .action-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
      gap: 1rem;
      padding: 1rem;
    }
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 1rem;
      text-decoration: none;
      color: inherit;
      transition: transform 0.2s;
    }
    .action-item:hover {
      transform: translateY(-2px);
    }
    .action-icon {
      font-size: 2rem;
      margin-bottom: 0.5rem;
    }
    .action-text {
      font-size: 0.875rem;
      text-align: center;
    }
  `],
  standalone: true,
  imports: [CommonModule, RouterModule, IonicModule]
})
export class ActionGridComponent {
  @Input() actions: Action[] = [];
  @Input() class: string = '';
}

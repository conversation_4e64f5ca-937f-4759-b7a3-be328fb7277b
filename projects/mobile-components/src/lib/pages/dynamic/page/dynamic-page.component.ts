import { Component, OnInit, Injector, ComponentFactoryResolver, ViewChild, ViewContainerRef, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { ActivatedRoute } from '@angular/router';
import { LssConfig } from 'lp-client-api';
import { DynamicHeaderComponent } from '../components/header/header.component';
import { BalanceCardComponent } from '../components/balance-card/balance-card.component';
import { ActionGridComponent } from '../components/action-grid/action-grid.component';

@Component({
  selector: 'app-dynamic-page',
  template: `
    <div class="dynamic-page" [ngClass]="pageConfig?.class">
      <ng-container #componentContainer></ng-container>
    </div>
  `,
  styles: [
    `
      .dynamic-page {
        width: 100%;
        height: 100%;
      }
    `
  ],
  standalone: true,
  imports: [CommonModule, RouterModule, DynamicHeaderComponent, Ba<PERSON><PERSON>ardComponent, ActionGridComponent]
})
export class DynamicPageComponent implements OnInit {
  @ViewChild('componentContainer', { read: ViewContainerRef, static: true })
  componentContainer!: ViewContainerRef;

  @Input() pageKey?: string;
  pageConfig: any;

  private componentMap: { [key: string]: any } = {
    'header': DynamicHeaderComponent,
    'balance-card': BalanceCardComponent,
    'action-grid': ActionGridComponent
  };

  constructor(
    private route: ActivatedRoute,
    private injector: Injector,
    private componentFactoryResolver: ComponentFactoryResolver,
    private lssConfig: LssConfig
  ) {}

  ngOnInit() {
    // If pageKey not provided via input, get it from route
    if (!this.pageKey) {
      this.route.data.subscribe(data => {
        this.pageKey = data['pageKey'];
        this.loadPage();
      });
    } else {
      this.loadPage();
    }
  }

  private loadPage() {
    if (!this.pageKey || !this.lssConfig.pages[this.pageKey]) {
      console.error(`Page configuration not found for key: ${this.pageKey}`);
      return;
    }

    this.pageConfig = this.lssConfig.pages[this.pageKey];
    this.renderComponents();
  }

  private renderComponents() {
    if (!this.pageConfig?.components) {
      return;
    }

    this.componentContainer.clear();
    this.pageConfig.components.forEach((componentConfig: any) => {
      try {
        // Get component class from componentMap
        const componentClass = this.componentMap[componentConfig.type];
        if (!componentClass) {
          console.error(`Component type not found: ${componentConfig.type}`);
          return;
        }

        // Create component
        const componentFactory = this.componentFactoryResolver.resolveComponentFactory(componentClass);
        const componentRef = this.componentContainer.createComponent(componentFactory);

        // Set inputs
        Object.keys(componentConfig.inputs || {}).forEach(key => {
          (componentRef.instance as any)[key] = componentConfig.inputs[key];
        });

        // Apply styles
        if (componentConfig.styles) {
          Object.assign(componentRef.location.nativeElement.style, componentConfig.styles);
        }
      } catch (error) {
        console.error('Error rendering component:', error);
      }
    });
  }
}

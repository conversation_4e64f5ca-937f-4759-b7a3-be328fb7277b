import { Component, Injector, ChangeDetectorRef, CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { RouterModule } from '@angular/router';
import { AbstractComponent } from '../../shared/abstract.component';
import { PagesLoginTheme1Component } from '../login/themes/theme1/pages-login-theme1.component';
import { PagesLandingTheme1Component } from '../landing/themes/theme1/pages-landing-theme1.component';
import { WidgetsModule } from '../../widgets/widgets.module';

/* Commenting out lp-client-api temporarily
*/

import {
  KeyCloakService,
  MemberProfile,
  MemberService,
  LssConfig,
} from 'lp-client-api';
import { Router } from '@angular/router';
// import { MemberProfile } from 'lp-client-api';

@Component({
  selector: 'app-home',
  templateUrl: 'home.component.html',
  styleUrls: ['home.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    RouterModule,
    PagesLoginTheme1Component,
    PagesLandingTheme1Component,
    WidgetsModule
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA, NO_ERRORS_SCHEMA]
})
export class HomeComponent extends AbstractComponent {
  @Input() profile?: MemberProfile;
  constructor(
    injector: Injector,
    public kc: KeyCloakService,
    // private memberService: MemberService,
    protected readonly router: Router,
    private cd: ChangeDetectorRef,
    // private profile: MemberProfile,

    // public lssConfig: LssConfig
  ) {
    super(injector);
  }
  ngOnInit() {
    // Ensure lssConfig is initialized with default values if needed
    /* Commenting out lp-client-api usage
    if (!this.lssConfig.pages) {
      // Provide default configuration or handle initialization
      console.warn('LssConfig pages not found, using defaults.');
    }

    this.addGlobalSubscription(
      this.kc.isAuthenticated().subscribe((auth) => {
        if (auth) {
          this.memberService.getProfile().subscribe((profile) => {
            this.profile = profile;
            this.cd.detectChanges();
          });
        }
      })
    );
    */
  }

  // getBalance() {
  //   this.memberService
  //     .memberBalance(this.kc.lpUniueReference)
  //     .subscribe((data: any) => {
  //       this.profile = { ...this.profile, ...data };
  //       console.log(this.profile);
  //       this.cd.detectChanges();
  //     });
  // }

  navigateToDashboard() {
    // Example: Navigate to a dashboard route
    /* Commenting out lp-client-api usage
    if (this.lssConfig?.pages?.dashboard?.route) {
      this.router.navigate([this.lssConfig.pages.dashboard.route]);
    } else {
      this.router.navigate(['/dashboard']); // Default route
      this.cd.detectChanges();
    }
    */
  }
}

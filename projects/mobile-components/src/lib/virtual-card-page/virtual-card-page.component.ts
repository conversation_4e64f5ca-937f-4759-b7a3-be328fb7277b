import { Component, Input, Output, EventEmitter, OnInit, OnDestroy, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { ComponentsModule } from '../components.module';

export interface VirtualCardProfile {
  givenNames?: string;
  surname?: string;
  newMembershipNumber?: string;
  membershipNumber?: string;
  currentBalance?: number;
  virtualCard?: string;
}

@Component({
  selector: 'lib-virtual-card-page',
  templateUrl: './virtual-card-page.component.html',
  styleUrls: ['./virtual-card-page.component.scss'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    CommonModule,
    IonicModule,
    ComponentsModule
  ]
})
export class VirtualCardPageComponent implements OnInit, OnDestroy {
  @Input() profile?: VirtualCardProfile;
  @Input() memberService?: any;
  @Input() keyCloakService?: any;
  @Input() lssConfig?: any;
  @Input() multiTenantContext?: any;
  @Input() apiBaseUrl?: string;
  @Input() productId?: string;

  @Output() cardLoaded = new EventEmitter<boolean>();
  @Output() cardError = new EventEmitter<string>();
  @Output() profileLoaded = new EventEmitter<VirtualCardProfile>();

  // Card state
  cardLoadedState = false;
  cardErrorState = false;
  loading = false;

  constructor() {}

  ngOnInit() {
    console.log('VirtualCardPageComponent initialized');
    this.loadVirtualCard();
  }

  ngOnDestroy() {
    // Clean up subscriptions if any
  }

  /**
   * Load virtual card using unified API approach
   * Uses productId from environment (single-tenant) or program context (multi-tenant)
   */
  private loadVirtualCard(): void {
    if (!this.keyCloakService || !this.memberService) {
      this.loading = false;
      this.cardErrorState = true;
      return;
    }

    const userId = this.keyCloakService.getUserIdForApi ? this.keyCloakService.getUserIdForApi() : null;
    const { productId, mpacc } = this.getProductContext();

    console.log('Loading virtual card with unified API:');
    console.log('- User ID:', userId);
    console.log('- Product ID:', productId);
    console.log('- MPACC:', mpacc);

    if (!userId || !productId || !mpacc) {
      console.error('Missing required parameters for virtual card API call');
      this.loading = false;
      this.cardErrorState = true;
      this.cardError.emit('Missing required parameters for virtual card API call');
      return;
    }

    // Subscribe to profile changes
    if (this.memberService.profileSubject) {
      this.memberService.profileSubject.subscribe((data: any) => {
        this.profile = data;
        this.loading = false;
        this.profileLoaded.emit(data);
      });
    }

    // Load program member profile using unified API
    console.log('Loading member profile for virtual card...');
    if (this.memberService.loadProgramMemberProfile) {
      this.memberService.loadProgramMemberProfile(userId, productId, mpacc).subscribe({
        next: (profile: any) => {
          console.log('Member profile loaded for virtual card:', profile);
          // Profile is automatically set in the service
        },
        error: (error: any) => {
          console.error('Error loading member profile for virtual card:', error);
          this.cardErrorState = true;
          this.loading = false;
          this.cardError.emit(error.message || 'Error loading member profile');
        }
      });
    }
  }

  /**
   * Get product context based on tenant type
   * Single-tenant: Uses environment configuration
   * Multi-tenant: Uses selected program context
   */
  private getProductContext(): { productId: string | null; mpacc: string | null } {
    if (this.isMultiTenant) {
      // Multi-tenant: get from selected program
      const programContext = this.multiTenantContext.currentProgramContext;
      if (programContext) {
        return {
          productId: programContext.programId,
          mpacc: programContext.mpacc
        };
      }
      return { productId: null, mpacc: null };
    } else {
      // Single-tenant: get from runtime configuration
      const productId = String(this.lssConfig?.productId || this.productId || 'rmic');
      const mpacc = this.profile?.membershipNumber || this.profile?.newMembershipNumber;
      
      return {
        productId: productId || null,
        mpacc: mpacc || null
      };
    }
  }

  private get isMultiTenant(): boolean {
    return this.multiTenantContext?.isMultiTenant || false;
  }

  // Card image handlers
  onCardImageLoad() {
    setTimeout(() => {
      this.cardLoadedState = true;
      this.cardErrorState = false;
      this.cardLoaded.emit(true);
    }, 300); // Small delay for smooth animation
  }

  onCardImageError() {
    this.cardErrorState = true;
    this.cardLoadedState = false;
    this.cardError.emit('Failed to load card image');
  }

  reloadCard() {
    this.cardErrorState = false;
    this.cardLoadedState = false;
    
    // Force reload by updating the image src
    if (this.profile?.virtualCard) {
      const img = new Image();
      img.src = this.profile.virtualCard;
      img.onload = () => this.onCardImageLoad();
      img.onerror = () => this.onCardImageError();
    }
  }

  // Format number with thousand separators
  formatNumber(value: number | undefined | null): string {
    if (!value && value !== 0) return '0';
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  // Helper methods for template
  get cardholderName(): string {
    return `${this.profile?.givenNames || ''} ${this.profile?.surname || ''}`.trim();
  }

  get memberNumber(): string {
    return this.profile?.newMembershipNumber || this.profile?.membershipNumber || '';
  }

  get availablePoints(): string {
    return this.formatNumber(this.profile?.currentBalance);
  }

  get cardImageSrc(): string {
    return this.profile?.virtualCard || '';
  }
}
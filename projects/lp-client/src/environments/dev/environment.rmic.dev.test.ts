// Test environment file to verify autoRedirectOnLogin: false behavior
// Copy of environment.rmic.dev.ts with autoRedirectOnLogin set to false

export const environment = {
  production: false,
  env: 'QA',
  client: 'rmic',
  lssConfig: {
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '812275411',
    apiIdKeyStart: 'ITMANQA_038958288_START',
    apiIdKeyEnd: 'ITMANQA_039785672_END',
    appCode: 'start',
    appName: 'Make It With Mica',
    appVersion: '0.0.1',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.49975999318031,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'http://alpine',
    apiBaseUrl: 'https://rmicdev.loyaltyplus.aero/',
    logEndpoint: 'https://rmicdev.loyaltyplus.aero/extsecure/tools/logservice',
    configAPIUrl: 'http://{hostname}/config/api/v1/',
    termsConditions:
      'https://rmicdev.loyaltyplus.aero/rmic/rmic/terms/Mica_Terms_Conditions.pdf',
    pointsExpire: false,
    pointsTitle: 'Points',
    memberPhone: {
      dialCode: '+27',
      nationalNumber: '',
    },
    memberCard: '',

    telephone: {
      selectFirstCountry: true,
      preferredCountries: ['za'],
      onlyCountries: ['za', 'ls', 'bw', 'na', 'sz', 'mz'],
    },
    theme: {
      layout: 'sidebar',
      backgroundImage: '',
      colours: {
        primary: '#f5821f', // MICA orange
        primaryContrast: '#ffffff',
        primaryShade: '#dc6a0a',
        primaryTint: '#ff9435',

        secondary: '#ffd400',
        secondaryContrast: '#ffffff',
        secondaryShade: '#4854e0',
        secondaryTint: '#6370ff',

        tertiary: '#ffd400',
        tertiaryContrast: '#ffffff',
        tertiaryShade: '#4854e0',
        tertiaryTint: '#6370ff',

        success: '#2dd36f',
        successContrast: '#000000',
        successShade: '#28ba62',
        successTint: '#42d77d',

        warning: '#ffc409',
        warningContrast: '#000000',
        warningShade: '#e0ac08',
        warningTint: '#ffca22',

        danger: '#eb445a',
        dangerContrast: '#ffffff',
        dangerShade: '#cf3c4f',
        dangerTint: '#ed576b',

        medium: '#92949c',
        mediumContrast: '#000000',
        mediumShade: '#808289',
        mediumTint: '#9d9fa6',

        base: '#0b69b4',
        baseContrast: '#ffffff',
        baseShade: '#16315d',
        baseTint: '#4c8dff',

        light: '#f4f5f8',
        lightContrast: '#000000',
        lightShade: '#d7d8da',
        lightTint: '#f5f6f9',

        //--ion-background-color: '#f6f6f6',
        //--ion-background-color: transparent',

        // --ion-text-color: '#737373',
        // --ion-text-color: '#fff',

        // --ion-item-color: '#737373',

        step50: '#f8f8f8',
        step100: '#f1f1f1',
        step150: '#eaeaea',
        step200: '#e3e3e3',
        step250: '#dcdcdc',
        step300: '#d5d5d5',
        step350: '#cecece',
        step400: '#c7c7c7',
        step450: '#c0c0c0',
        step500: '#b9b9b9',
        step550: '#b2b2b2',
        step600: '#ababab',
        step650: '#a4a4a4',
        step700: '#9d9d9d',
        step750: '#969696',
        step800: '#8f8f8f',
        step850: '#888888',
        step900: '#818181',
        step950: '#7a7a7a',
      },
    },
    forms: {
      login: {
        options: {
          email: true,
          google: true,
        },
      },
      contactus: {
        categories: ['General', 'Complaint'],
      },
    },
    pages: {
      landing: {
        themes: ['theme-1', 'theme-2', 'theme-3', 'theme-4'],
        theme: 'theme-1',
        class: 'bg-base h-full',
        title: {
          text: 'Welcome',
          class: 'text-primary',
        },
        subtitle: {
          text: 'Welcome',
          class: 'text-primary',
        },
        balance: {
          text: 'Welcome',
          class: 'text-center w-90 primary rounded-lg mx-auto p-4 shadow mt-8',
        },
        action_card: {
          class: 'mt-8 px-2',
          profile: {
            text: 'Profile',
            class:
              'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex',
            class_icon_outer: 'action-icon text-baseShade',
            class_icon: 'w-4 h-4 -mt-4',
            class_text: 'text-xl -mt-5',

            icon: 'person-circle-outline',
          },
          card: {
            text: 'Card',
            class:
              'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex',
            class_icon_outer: 'action-icon text-baseShade',
            class_icon: 'w-4 h-4 -mt-4',
            class_text: 'text-xl -mt-5',

            icon: 'card-outline',
          },
          history: {
            text: 'Transactions',
            class:
              'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex',
            class_icon_outer: 'action-icon text-baseShade',
            class_icon: 'w-4 h-4 -mt-4',
            class_text: 'text-xl -mt-5',

            icon: 'cart-outline',
          },
          stores: {
            text: 'Stores',
            class:
              'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex',
            class_icon_outer: 'action-icon text-baseShade',
            class_icon: 'w-4 h-4 -mt-4',
            class_text: 'text-xl -mt-5',

            icon: 'location-outline',
          },
          contact: {
            text: 'Contact Us',
            class:
              'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex',
            class_icon_outer: 'action-icon text-baseShade',
            class_icon: 'w-4 h-4 -mt-4',
            class_text: 'text-xl -mt-5',

            icon: 'call-outline',
          },
        },
        icon: 'assets/images/make-it.png',
        loggedinIcon: 'assets/images/make-it.png',
        autoRedirectOnLogin: false, // TEST: Stay on landing page after login
      },
      login: {
        themes: ['theme-1', 'theme-2', 'theme-3', 'theme-4'],
        class: 'bg-base h-screen',
        theme: 'theme-1',
        title: {
          text: 'Welcome',
          class: 'text-primaryContrast text-4xl text-center',
        },

        subtitle: {
          text: 'to Make It With Mica',
          class: 'text-primaryContrast text-center w-full',
        },
        icon: {
          src: 'assets/images/make-it.png',
          class: 'rounded-large text-center w-80 mx-auto',
        },
        auth_buttons: {
          class: ' px-4 mt-6',
          login: {
            text: 'Sign in',
            class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
          },
          signup: {
            text: 'Sign up',
            class:
              'primary text-sm text-center w-full p-5 rounded-lg shadow my-3',
          },
          password: {
            text: 'Forgot Password',
            class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
          },
        },
        social_buttons: {
          class: 'flex w-full text-center mt-6',
          facebook: {
            icon: 'logo-facebook',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
            size: 'large',
          },
          twitter: {
            icon: 'logo-x',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          linkedin: {
            icon: 'logo-linkedin',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          youtube: {
            icon: 'logo-youtube',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          pinterest: {
            icon: 'logo-pinterest',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          instagram: {
            icon: 'logo-instagram',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
        },
        loggedinIcon: 'assets/images/make-it.png',
      },
      contact: {
        categoryCode: 'CNCT',
        icon: 'assets/images/make-it.png',
        title: 'Welcome',
        subtitle: 'Let us demo!',
      },
      profile: {
        preferencesCode: 'PART',
      },
    },
    contact: {
      callCenter: '012 141 3596',
    },
    socials: {
      facebook: 'https://www.facebook.com/micahardware',
      twitter: 'https://twitter.com/micahardware',
      linkedin:
        'https://www.linkedin.com/company/mica-hardware?originalSubdomain=za',
      youtube: 'https://www.youtube.com/channel/UCrCGdzTbu8xWKpubdHz9VjA',
      pinterest: 'https://za.pinterest.com/micahardware/',
      instagram: 'https://www.instagram.com/micahardware/?hl=en',
    },
    terminology: {
      pool: {
        singular: 'Community',
        plural: 'Communities',
        management: 'Community Management',
        member: 'Community Member',
        notInMessage: 'Member is currently not in a Community',
        createNew: 'Create New Community',
        joinExisting: 'Join Existing Community',
        invitation: 'Community Invitation',
        exitConfirmTitle: 'Exit Community',
        exitConfirmMessage: 'Are you sure you want to exit the community',
        exitWarning:
          'Warning: This action cannot be undone. You will need to be re-invited to rejoin the community.',
        createdSuccess: 'Community created successfully!',
        joinRequestSuccess: 'Join request sent successfully!',
        invitationSentSuccess: 'Invitation sent successfully!',
        exitSuccess: 'You have successfully exited the community.',
        removeMemberSuccess: 'Member removed successfully!',
        approveJoinSuccess:
          'Join request approved! Member has been added to the community.',
        rejectJoinSuccess: 'Join request rejected and member removed.',
        acceptInviteSuccess: 'Successfully joined the community!',
        inviteAcceptedToast: 'Community invitation accepted successfully!',
        inviteDeclinedToast: 'Community invitation declined',
        errorOccurred: 'An error occurred with the community',
        exitToast: 'You have successfully exited the community',
        inviteToast:
          'Successfully invited member {membershipNumber} to the community!',
        loadingInfo: 'Loading community information...',
        noInfoAvailable: 'No community information available.',
        alreadyInAnother:
          'This member is already in another community and cannot join this one.',
        failedToCreate: 'Failed to create community. Please try again.',
        failedToJoin: 'Failed to request community join. Please try again.',
        failedToExit: 'Failed to exit community. Please try again.',
        failedToLoad: 'Failed to load community information',
        acceptInfoText:
          'By accepting this invitation, you will become a member of this community and can participate in communities activities.',
        totalUnits: 'Total Points',
      },
    },
    navigation: {
      sidebarTitle: 'Mica',
      sidebarIcon: 'assets/images/make-it.png',
      type: 'sidebar',
      routes: [
        {
          id: 'Login',
          text: 'Login',
          link: 'login',
          icon: '',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
        },
        {
          id: 'Register',
          text: 'Register',
          link: 'register',
          icon: '',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
        },
        {
          id: 'Logout',
          text: 'Logout',
          link: 'logout',
          icon: '',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
        },
        {
          id: 'Home',
          text: 'Home',
          link: '/',
          icon: 'home',
          active: true,
          sidebar: true,
          admin: false,

          sort: 0,
        },
        {
          id: 'Profile',
          text: 'Profile',
          link: '/app/account',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,

          sort: 0,
        },
        {
          id: 'Card',
          text: 'Card',
          link: '/app/virtualcard',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
        },
        {
          id: 'Pooling',
          text: 'Communities',
          link: '/secure/pools',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
        },
        {
          id: 'Transactions',
          text: 'Transactions',
          link: '/app/transactions',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
        },
        {
          id: 'Stores',
          text: 'Stores',
          link: '/app/stores',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
        },
        {
          id: 'Contact',
          text: 'Contact Us',
          link: '/secure/contactus',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
        },
        {
          id: 'Programs',
          text: 'My Programs',
          link: '/secure/program-management',
          icon: 'layers',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
        },
      ],
    },
    authConfig: {
      issuer: 'https://authdev.loyaltyplus.aero/auth/realms/Mica',
      clientId: 'mobile-app',
      logoutUrl: '/',
      url: 'https://authdev.loyaltyplus.aero/auth',
      realm: 'Mica',
      initOptions: {
        adapter: 'default',
        responseType: 'code',
        scope: 'openid profile email offline_access',
        onLoad: 'check-sso',
        redirectUri: window.location.origin.replace('.aero', '.aero/lp-mobile'),
        silentCheckSsoRedirectUri:
          window.location.origin.replace('.aero', '.aero/lp-mobile') +
          '/assets/silent-check-sso.html',
      },
    },
    // Workflow Configuration
    workflow: {
      // Set to 'simple' for direct login → home
      // Set to 'program-selection' for login → program selection → onboarding → home
      type: 'simple', // Options: 'simple' | 'program-selection'
      
      // Product/Program identifier for single-tenant apps
      productId: 'rmic', // As per Emil's conversation
      
      // Multi-tenant configuration (not used in single-tenant mode)
      multiTenant: false,
      
      // Workflow 2: Program Selection Configuration
      programSelection: {
        enabled: true,
        requiredPrograms: ['loyalty-base'], // Programs that must be selected
        skipOptional: true, // Allow skipping optional programs
        categories: ['rewards', 'benefits', 'partnerships'],
        onboardingSteps: ['programs', 'profile', 'preferences', 'welcome'],
        mockData: true, // Use mock data for development
      },
      
      // Feature flags for gradual rollout
      features: {
        programSelection: true,
        onboardingFlow: true,
        welcomeDashboard: true,
        skipOnboarding: false, // Set to true to bypass for existing users
        pools: {
          extsecure: {
            readEnabled: true,
            mutateEnabled: true
          }
        }
      },
      
      // Navigation configuration (empty for single-tenant)
      navigation: {
        programDependentRoutes: [] as string[],
        alwaysVisibleRoutes: [] as string[]
      },
    },
  },
};

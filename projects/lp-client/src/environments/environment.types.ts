export interface LssConfig {
  googleApiKey?: string;
  apiId?: string;
  apiIdKeyStart?: string;
  apiIdKeyEnd?: string;
  appCode: string;
  appName: string;
  appVersion?: string;
  useAuth: boolean;
  useION: boolean;
  useISO: boolean;
  defaultNotAuthURL: string;
  autoLogout: boolean;
  autoLogoutTimeout: number;
  autoLogoutWarning: number;
  defaultLat?: number;
  defaultLng?: number;
  loadIdentity: boolean;
  identityBaseUrl: string;
  appBaseUrl: string;
  apiBaseUrl: string;
  logEndpoint?: string;
  configAPIUrl?: string;
  termsConditions?: string;
  icon?: string;
  mascot?: string;
  memberPhone?: {
    dialCode: string;
    nationalNumber: string;
  };
  memberCard?: string;
  telephone?: {
    selectFirstCountry: boolean;
    preferredCountries: string[];
    onlyCountries: string[];
  };
  theme?: {
    layout: string;
    backgroundImage: string;
    colours: {
      primary: string;
      primaryContrast: string;
      primaryShade: string;
      primaryTint: string;
      secondary: string;
      secondaryContrast: string;
      secondaryShade: string;
      secondaryTint: string;
    };
  };
  games?: {
    [key: string]: any;
  };
  pages?: {
    [key: string]: any;
    landing?: {
      [key: string]: any;
      autoRedirectOnLogin?: boolean;
    };
  };
  contact?: {
    [key: string]: any;
  };
  socials?: {
    [key: string]: any;
  };
  navigation?: {
    sidebarTitle?: string;
    sidebarIcon?: string;
    type?: string;
    routes: Array<{
      id?: string;
      path?: string;
      icon: string;
      label: string;
      main?: boolean;
      sidebar?: boolean;
      more?: boolean;
      exact?: boolean;
      link?: string;
      active?: boolean;
      admin?: boolean;
      sort?: number;
    }>;
  };
  authConfig?: {
    [key: string]: any;
  };
  workflow?: {
    type?: string;
    multiTenant?: boolean;
    productId?: string; // Program/product identifier for single-tenant apps
    programSelection?: any;
    features?: any;
    navigation?: {
      programDependentRoutes?: string[];
      alwaysVisibleRoutes?: string[];
    };
  };
  useDemoProfile?: boolean;
}

export interface Environment {
  production: boolean;
  env: string;
  client?: string;
  lssConfig: LssConfig;
  firebase?: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
    measurementId: string;
    vapidKey: string;
  };
  notificationApi?: {
    baseUrl: string;
  };
}

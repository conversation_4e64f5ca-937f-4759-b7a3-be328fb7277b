// Type augmentation for Google Maps marker types
// This fixes compatibility issues between @angular/google-maps and @types/google.maps

declare namespace google.maps.marker {
  // Add missing PinElement type
  export class PinElement extends HTMLElement {
    constructor(options?: PinElementOptions);
    element: HTMLElement;
    glyph?: string | Element | URL | null;
    glyphColor?: string;
    background?: string;
    borderColor?: string | null;
    scale?: number | null;
  }

  // Add missing PinElementOptions interface
  export interface PinElementOptions {
    glyph?: string | Element | URL | null;
    glyphColor?: string;
    background?: string;
    borderColor?: string | null;
    scale?: number | null;
  }

  // Add missing AdvancedMarkerElement type
  export class AdvancedMarkerElement {
    constructor(options?: AdvancedMarkerElementOptions);
    position: google.maps.LatLng | google.maps.LatLngLiteral | null;
    title?: string | null;
    map: google.maps.Map | null;
    content?: Node | PinElement | null;
    gmpDraggable?: boolean | null;
    collisionBehavior?: CollisionBehavior | null;
    zIndex?: number | null;
    
    addListener(eventName: string, handler: Function): google.maps.MapsEventListener;
  }

  // Add missing AdvancedMarkerElementOptions interface
  export interface AdvancedMarkerElementOptions {
    position?: google.maps.LatLng | google.maps.LatLngLiteral | null;
    map?: google.maps.Map | null;
    content?: Node | PinElement | null;
    title?: string | null;
    collisionBehavior?: CollisionBehavior | null;
    gmpDraggable?: boolean | null;
    zIndex?: number | null;
  }

  // Add missing CollisionBehavior enum
  export enum CollisionBehavior {
    OPTIONAL_AND_HIDES_LOWER_PRIORITY = 'OPTIONAL_AND_HIDES_LOWER_PRIORITY',
    REQUIRED = 'REQUIRED',
    REQUIRED_AND_HIDES_OPTIONAL = 'REQUIRED_AND_HIDES_OPTIONAL'
  }
}

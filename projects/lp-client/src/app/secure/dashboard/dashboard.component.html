<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="profile?.givenNames + ' ' + profile?.surname"
      [membership]="profile?.newMembershipNumber"
      type="profile"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>

  <div class="dashboard-contents">
    <!-- Dashboard Action Cards Grid -->
    <lib-dashboard-grid>
      <ion-col size="12" size-sm="6" size-lg="4" *ngFor="let action of dashboardActions">
        <lib-dashboard-action-card
          [icon]="action.icon"
          [title]="action.title"
          [route]="action.route"
          color="primary">
        </lib-dashboard-action-card>
      </ion-col>
      
      <!-- Remove Profile Action -->

    </lib-dashboard-grid>

    <!-- Balance Summary Card -->
    <lib-balance-summary
      title="Balance Summary"
      icon="wallet-outline"
      [items]="[
        {label: 'Rand Value', value: profile?.availRands || 0, format: 'currency'},
        {label: 'Current Available Balance', value: profile?.availUnits || 0, format: 'number'},
        {label: lssConfig.pointsTitle || 'Points', value: profile?.currentBalance || 0, format: 'number', route: ['/secure/points'], valueClass: 'primary'},
        {label: 'Earned', value: (profile?.baseMiles || 0) + (profile?.bonusMiles || 0), format: 'number', route: ['/secure/points'], valueClass: 'success'},
        {label: 'Used', value: (profile?.expiredMiles || 0) + (profile?.usedMiles || 0), format: 'number', valueClass: 'danger'}
      ]">
    </lib-balance-summary>

      <lib-dashboard-action-card
          icon="trash-outline"
          title="Remove Profile"
          route="/secure/profileremove"
          color="danger">
        </lib-dashboard-action-card>
  </div>
</lib-page-wrapper>
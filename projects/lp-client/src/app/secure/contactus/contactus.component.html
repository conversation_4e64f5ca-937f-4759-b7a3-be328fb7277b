<ion-content [scrollY]="true" [scrollX]="false">
  <lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
    <div header>
      <lib-head-logo
        type="contact"
        [names]="profile?.givenNames + ' ' + profile?.surname"
        [src]="lssConfig.pages.landing.loggedinIcon"
      />
    </div>

    <!-- Contact Section -->
    <div class="contact-section">
      <!-- Quick Contact Card -->
      <lib-contact-card
        icon="call-outline"
        title="Need Quick Help?"
        subtitle="Call our support center"
        [phoneNumber]="lssConfig.contact.callCenter">
      </lib-contact-card>

      <!-- Contact Form -->
      <lib-contact-form
        [categories]="categories"
        [phoneConfig]="phoneConfig"
        (formSubmit)="onFormSubmit($any($event))">
      </lib-contact-form>
    </div>
  </lib-page-wrapper>
</ion-content>
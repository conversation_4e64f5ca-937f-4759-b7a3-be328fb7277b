import { Component, Injector, OnInit, CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { AbstractControlOptions, Validators } from '@angular/forms';
import {
  CustomValidators,
  KeyCloakService,
  LPMemberEntityTools,
  ContactForm,
  MemberService,
  MemberProfile,
  LPCode,
  LssConfig,
} from 'lp-client-api';
import { Router } from '@angular/router';
import { Observable } from 'rxjs';
import {
  AbstractFormComponent,
  ContactFormData,
  PageWrapperComponent,
  HeadLogoComponent,
  ContactCardComponent,
  ContactFormComponent
} from 'mobile-components';
import { environment } from '../../../environments/environment';
import moment from 'moment';

@Component({
  selector: 'app-contactus',
  templateUrl: 'contactus.component.html',
  styleUrls: ['contactus.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    IonicModule,
    PageWrapperComponent,
    HeadLogoComponent,
    ContactCardComponent,
    ContactFormComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA]
})
export class ContactusComponent
  extends AbstractFormComponent<ContactForm>
  implements OnInit
{
  error?: string;
  contactForm?: ContactForm;
  profile?: MemberProfile;
  categories: { value: string; label: string }[] = [];
  phoneConfig: any;

  constructor(
    injector: Injector,
    protected readonly keyCloakService: KeyCloakService,
    private memberService: MemberService,
    protected readonly router: Router,
    public lssConfig: LssConfig
  ) {
    super(injector);
    this.generateForm();
    this.phoneConfig = {
      selectFirstCountry: lssConfig.telephone.selectFirstCountry,
      preferredCountries: lssConfig.telephone.preferredCountries
    };
  }
  environment = environment;

  ionViewWillEnter() {
    // Load categories
    this.getCodeList('CNCT').subscribe((codes: LPCode[]) => {
      this.categories = codes.map(code => ({
        value: code.codeId || '',
        label: code.description || ''
      }));
    });

    this.loading = true;
    this.memberService
      .getProfile(this.keyCloakService.lpUniueReference, true)
      .subscribe((data) => {
        this.profile = data;
        if (data) {
          let tel = null;
          if (data.personTelephone) {
            for (let i = 0; i < data.personTelephone.length; i++) {
              if (data.personTelephone[i].telephoneType == 'CELL')
                tel = data.personTelephone[i];
            }
            if (tel) {
              let pl = {
                internationalNumber:
                  tel.countryCode + ' ' + tel.telephoneNumber?.substring(1),
                nationalNumber: tel.telephoneNumber,
                isoCode: 'za',
                dialCode: tel.countryCode,
              };
              this._form.controls['phone'].patchValue(pl);
            }
          }
          this._form.controls['givenNames'].patchValue(data.givenNames);
          this._form.controls['surname'].patchValue(data.surname);
          this._form.controls['email'].patchValue(data.emailAddress);
        }
      });
  }

  ngOnInit(): void {
    this.showLoadingModal('Loading..').then(() => {
      this.keyCloakService.authStatus.subscribe((value) => {
        if (value != null && value.eventName !== 'init') {
          if (value.eventName !== null && value.eventName === 'login') {
            if (this.keyCloakService.userProfile?.mustRegister) {
              this.contactForm = {} as ContactForm;
              this.contactForm.externalId =
                this.keyCloakService.userProfile.sub!;
              this.contactForm.emailAddress =
                this.keyCloakService.userProfile.email!;
              this.contactForm.givenNames =
                this.keyCloakService.userProfile.given_name!;
              this.contactForm.surname =
                this.keyCloakService.userProfile.family_name!;
              this._form.patchValue(this.contactForm);
            }
          }
          this.dismissLoadingModal();
        }
      });
    });
  }

  generateForm(): void {
    this._form = this._formBuilder.group({
      givenNames: ['', [Validators.required]],
      surname: ['', [Validators.required]],
      category: ['', [Validators.required]],
      phone: [
        LPMemberEntityTools.getIONTelephoneFromLPTel(
          LPMemberEntityTools.getEmptyPhone('CELL')
        ),
        [Validators.required],
      ],
      email: [
        '',
        Validators.compose([
          Validators.required,
          Validators.pattern('^[a-z0-9._%+-]+@[a-z0-9.-]+.[a-z]{2,4}$'),
        ]),
      ],
      message: ['', [Validators.required]],
    } as AbstractControlOptions);
  }

  showTerms(): void {
    this._formState = this._formStateType.terms;
  }

  canSignup(): boolean {
    return this.isFormValid() && this.form.terms.value === true;
  }

  onFormSubmit(formData: ContactFormData): void {
    // Map the form data to the expected format
    const payload: any = {
      ...formData,
      personTelephone: [
        {
          telephoneType: 'CELL',
          countryCode: formData.phone,
          telephoneCode: '',
          telephoneNumber: formData.phone,
          telephoneExtension: '',
          phoneable: '',
          smsable: '',
        },
      ],
      submitDate: moment().format('YYYY-MM-DD HH:mm:ss')
    };

    this.showLoadingModal('Please wait while we send your data!').then(() => {
      this.memberService
        .contactUs(this.keyCloakService.lpUniueReference, payload)
        .subscribe({
          error: (error: any) => {
            console.log('err contact us', error);
            this.dismissLoadingModal();
            this.presentToast({
              message: 'Oops, something went wrong!',
              color: 'danger',
              position: 'bottom',
            }).then();

            this._formState = this._formStateType.fail;
            this.error = error.error.detail;
          },
          next: (body: any) => {
            console.log('body contact us', body);
            this.dismissLoadingModal();
            this.presentToast({
              message: 'Your message has been sent',
              position: 'bottom',
            }).then();

            this._formState = this._formStateType.pass;
            this.formData = body;
            this.router.navigate(['/app/home']);
          },
        });
    });
  }

  submit(): void {
    this.formData = this.getFormValues();
    console.log('form', this.formData);

    let payload: any = this.formData;
    payload.personTelephone = [
      {
        telephoneType: 'CELL',
        countryCode: payload.phone.dialCode,
        telephoneCode: '',
        telephoneNumber: payload.phone.nationalNumber,
        telephoneExtension: '',
        phoneable: '',
        smsable: '',
      },
    ];

    console.log('payload', payload);
    payload.submitDate = moment().format('YYYY-MM-DD HH:mm:ss');

    if (this.formData) {
      this.showLoadingModal('Please wait while we send your data!').then(() => {
        this.memberService
          .contactUs(this.keyCloakService.lpUniueReference, this.formData)
          .subscribe({
            error: (error: any) => {
              console.log('err contact us', error);
              this.dismissLoadingModal();
              this.presentToast({
                message: 'Oops, something went wrong!',
                color: 'danger',
                position: 'bottom',
              }).then();

              this._formState = this._formStateType.fail;
              this.error = error.error.detail;
            },
            next: (body: any) => {
              console.log('body contact us', body);
              this.dismissLoadingModal();
              this.presentToast({
                message: 'Your message has been sent',
                position: 'bottom',
              }).then();

              this._formState = this._formStateType.pass;
              this.formData = body;
              this.router.navigate(['/app/home']);
            },
          });
      });
    }
  }
}
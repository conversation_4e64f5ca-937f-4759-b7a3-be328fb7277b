import { Component, Injector, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import {
  MemberProfile,
  MemberService,
  KeyCloakService,
  LssConfig,
} from 'lp-client-api';
import { AbstractComponent, ComponentsModule } from 'mobile-components';
import { environment } from '../../../environments/environment';
import { MultiTenantContextService } from '../../services/multi-tenant-context.service';
import { resolveSingleTenantMpacc, resolveSingleTenantProductId } from '../../services/tenant-context.helpers';

@Component({
  selector: 'app-virtualcard',
  templateUrl: 'virtualcard.component.html',
  styleUrls: ['virtualcard.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    IonicModule,
    ComponentsModule
  ]
})
export class VirtualCardComponent extends AbstractComponent {
  profile?: MemberProfile;
  environment = environment;
  
  // Card state
  cardLoaded = false;
  cardError = false;

  constructor(
    injector: Injector,
    private memberService: MemberService,
    private kc: KeyCloakService,
    public lssConfig: LssConfig,
    private multiTenantContext: MultiTenantContextService
  ) {
    super(injector);
  }

  ngOnInit() {
    this.loadVirtualCard();
  }

  /**
   * Load virtual card using unified API approach
   * Uses productId from environment (single-tenant) or program context (multi-tenant)
   */
  private loadVirtualCard(): void {
    const userId = this.kc.getUserIdForApi();
    const { productId, mpacc } = this.getProductContext();

    console.log('Loading virtual card with unified API:');
    console.log('- User ID:', userId);
    console.log('- Product ID:', productId);
    console.log('- MPACC:', mpacc);

    if (!userId || !productId || !mpacc) {
      console.error('Missing required parameters for virtual card API call');
      this.loading = false;
      this.cardError = true;
      this.detectChanges();
      return;
    }

    // Subscribe to profile changes
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        this.loading = false;
        this.detectChanges();
      })
    );

    // Load program member profile using unified API
    console.log('Loading member profile for virtual card...');
    this.addGlobalSubscription(
      this.memberService.loadProgramMemberProfile(userId, productId, mpacc).subscribe({
        next: (profile) => {
          console.log('Member profile loaded for virtual card:', profile);
          // Profile is automatically set in the service
        },
        error: (error) => {
          console.error('Error loading member profile for virtual card:', error);
          this.cardError = true;
          this.loading = false;
          this.detectChanges();
        }
      })
    );
  }

  /**
   * Get product context based on tenant type
   * Single-tenant: Uses environment configuration
   * Multi-tenant: Uses selected program context
   */
  private getProductContext(): { productId: string | null; mpacc: string | null } {
    if (this.multiTenantContext.isMultiTenant) {
      const programContext = this.multiTenantContext.currentProgramContext;
      if (programContext) {
        return {
          productId: programContext.programId,
          mpacc: programContext.mpacc
        };
      }
      return { productId: null, mpacc: null };
    }

    const productId = resolveSingleTenantProductId(this.lssConfig, this.environment);
    const mpacc = resolveSingleTenantMpacc(this.memberService.profileSubject.value, this.kc);

    return {
      productId,
      mpacc
    };
  }

  // Card image handlers
  onCardImageLoad() {
    setTimeout(() => {
      this.cardLoaded = true;
      this.cardError = false;
      this.detectChanges();
    }, 300); // Small delay for smooth animation
  }

  onCardImageError() {
    this.cardError = true;
    this.cardLoaded = false;
    this.detectChanges();
  }

  reloadCard() {
    this.cardError = false;
    this.cardLoaded = false;
    this.detectChanges();
    
    // Force reload by updating the image src
    if (this.profile?.virtualCard) {
      const img = new Image();
      img.src = this.profile.virtualCard;
      img.onload = () => this.onCardImageLoad();
      img.onerror = () => this.onCardImageError();
    }
  }

  // Format number with thousand separators
  formatNumber(value: number | undefined | null): string {
    if (!value && value !== 0) return '0';
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
}
import { Component, Injector, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  MemberService,
  Statement,
  KeyCloakService,
  MemberProfile,
  LssConfig,
  ProgramService,
  ProgramTransaction,
} from 'lp-client-api';
import { AbstractFormComponent, ComponentsModule } from 'mobile-components';
import { environment } from '../../../environments/environment';
import { MultiTenantContextService } from '../../services/multi-tenant-context.service';
import { resolveSingleTenantMpacc, resolveSingleTenantProductId } from '../../services/tenant-context.helpers';
@Component({
  selector: 'app-transactions',
  templateUrl: './transactions.component.html',
  styleUrls: ['./transactions.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    ComponentsModule
  ]
})
export class TransactionsComponent extends AbstractFormComponent<Statement> {
  //loading = false;
  searchRender = false;
  statements: Statement[] = [];
  filteredStatements: Statement[] = [];
  selectedTransactionType: string = 'All';
  beginDate?: any;
  endDate?: Date;
  profile?: MemberProfile;

  constructor(
    injector: Injector,
    private memberService: MemberService,
    private programService: ProgramService,
    private mtContext: MultiTenantContextService,
    private kc: KeyCloakService,
    public lssConfig: LssConfig
  ) {
    super(injector);
    this.addGlobalSubscription(
      this.memberService.profileSubject.subscribe((data) => {
        this.profile = data;
        if (this.profile) {
          this._formState = this._formStateType.read;
        }
      })
    );
  }
  environment = environment;

  private get isMultiTenant(): boolean {
    return this.mtContext.isMultiTenant;
  }

  /**
   * Converts ProgramTransaction (multi-tenant API) to Statement (expected by UI)
   * @param programTx ProgramTransaction from the multi-tenant API
   * @returns Statement object compatible with existing UI logic
   */
  private programTxToStatement(programTx: ProgramTransaction): Statement {
    return {
      // Map transaction ID - use transactionId or fall back to a generated one
      invoiceNumber: programTx.transactionId || `${programTx.productId}-${programTx.mpacc}-${Date.now()}`,
      
      // Map transaction type (should be compatible)
      transactionType: programTx.transactionType,
      
      // Map transaction points (main points field)
      transactionPoints: programTx.points,
      
      // Map transaction value/amount to actValue
      actValue: programTx.amount,
      
      // Map transaction date - convert to Date object if it's a string
      transactionDate: new Date(programTx.transactionDate),
      
      // Map load date to the same as transaction date (common pattern in existing code)
      loadDate: programTx.transactionDate,
      
      // Use description as label (some UI components expect this)
      label: new Date(programTx.transactionDate),
      
      // Set quantity to 1 as default (not provided by ProgramTransaction)
      quantity: 1,
      
      // Additional fields that might be useful
      // expDate not provided by ProgramTransaction, set to undefined
      expDate: undefined,
      
      // tierPoints not provided, set to undefined  
      tierPoints: undefined
    };
  }

  override ionViewDidEnter(): void {
    super.ionViewDidEnter();
    this.loading = true;
    this.search();
  }

  search(retry?: boolean): void {
    if (!this.profile && !retry) {
      setTimeout(() => this.search(true), 500);
      return;
    }
    let limit = 10; // Standard pagination limit

    this.showLoadingModal('Fetching your latest transactions').then(() => {
      this.loadTransactionsUnified(limit);
    });
  }

  /**
   * Load transactions using unified API approach
   * Uses productId from environment (single-tenant) or program context (multi-tenant)
   */
  private loadTransactionsUnified(limit: number): void {
    console.log('Loading transactions with unified API approach');
    
    const userId = this.kc.getUserIdForApi();
    const { productId, mpacc } = this.getProductContext();
    
    if (!userId || !productId || !mpacc) {
      console.error('Missing required parameters for unified transaction API call:', { userId, productId, mpacc });
      this.dismissLoadingModal();
      this.presentToast({
        message: 'Unable to load transactions. Missing required information.',
        color: 'warning',
        position: 'middle',
      }).then();
      return;
    }
    
    console.log('Unified transaction request:', { userId, productId, mpacc, limit });
    
    // Prepare query options
    const queryOptions = {
      pageSize: limit,
      offset: 0,
      beginDate: this.beginDate ? this.formatDateForAPI(this.beginDate) : '2020-01-01',
      endDate: this.endDate ? this.formatDateForAPI(this.endDate) : '2025-12-31',
      filter: 'award'
    };
    
    this.programService
      .getUserProgramTransactions(userId, productId, mpacc, queryOptions)
      .subscribe({
        error: (error) => {
          console.error('Unified transaction API error:', error);
          this.dismissLoadingModal();
          this.presentToast({
            message: 'Oops, something went wrong loading transactions!',
            color: 'danger',
            position: 'middle',
          }).then();
        },
        next: (programTransactions) => {
          console.log('Unified transactions received:', programTransactions);
          if (programTransactions && programTransactions.length > 0) {
            // Convert ProgramTransaction[] to Statement[] using our mapper
            this.statements = programTransactions.map(tx => this.programTxToStatement(tx));
            
            // Log all unique transaction types
            const uniqueTypes = [...new Set(this.statements.map(s => s.transactionType))];
            console.log('Available transaction types:', uniqueTypes);
            
            this.filterTransactions();
          } else {
            this.statements = [];
            this.filteredStatements = [];
            console.log('No transactions found');
          }
          this.dismissLoadingModal();
        },
      });
  }

  /**
   * Get product context based on tenant type
   * Single-tenant: Uses environment configuration
   * Multi-tenant: Uses selected program context
   */
  private getProductContext(): { productId: string | null; mpacc: string | null } {
    if (this.isMultiTenant) {
      // Multi-tenant: get from selected program
      const programContext = this.mtContext.currentProgramContext;
      if (programContext) {
        return {
          productId: programContext.programId,
          mpacc: programContext.mpacc
        };
      }
      return { productId: null, mpacc: null };
    }

    const productId = resolveSingleTenantProductId(this.lssConfig, this.environment);
    const profileSource = this.profile ?? this.memberService.profileSubject.value;
    const mpacc = resolveSingleTenantMpacc(profileSource, this.kc);

    return {
      productId,
      mpacc
    };
  }

  /**
   * Load transactions for multi-tenant applications using program-specific API
   */
  private loadMultiTenantTransactions(limit: number): void {
    console.log('Loading multi-tenant transactions');
    
    // Get multi-tenant context information
    const programContext = this.mtContext.currentProgramContext;
    const userId = this.kc.getUserIdForApi();
    
    if (!programContext || !programContext.programId || !programContext.mpacc) {
      console.error('Multi-tenant context missing required data:', programContext);
      this.dismissLoadingModal();
      this.presentToast({
        message: 'Program context is missing. Please select a program first.',
        color: 'warning',
        position: 'middle',
      }).then();
      return;
    }
    
    if (!userId) {
      console.error('User ID is missing for multi-tenant transaction call');
      this.dismissLoadingModal();
      this.presentToast({
        message: 'Unable to get user information. Please try logging in again.',
        color: 'warning',
        position: 'middle',
      }).then();
      return;
    }
    
    console.log('Multi-tenant transaction request:', {
      userId,
      programId: programContext.programId,
      mpacc: programContext.mpacc,
      limit
    });
    
    // Prepare query options
    const queryOptions = {
      pageSize: limit,
      offset: 0,
      beginDate: this.beginDate ? this.formatDateForAPI(this.beginDate) : '2020-01-01',
      endDate: this.endDate ? this.formatDateForAPI(this.endDate) : '2025-12-31',
      filter: 'award' // Optional filter as mentioned in the user's requirements
    };
    
    this.programService
      .getUserProgramTransactions(
        userId,
        programContext.programId,
        programContext.mpacc,
        queryOptions
      )
      .subscribe({
        error: (error) => {
          console.error('Multi-tenant transaction error:', error);
          this.dismissLoadingModal();
          this.presentToast({
            message: 'Oops, something went wrong loading transactions!',
            color: 'danger',
            position: 'middle',
          }).then();
        },
        next: (programTransactions) => {
          console.log('Multi-tenant transactions received:', programTransactions);
          if (programTransactions && programTransactions.length > 0) {
            // Convert ProgramTransaction[] to Statement[] using our mapper
            this.statements = programTransactions.map(tx => this.programTxToStatement(tx));
            
            // Log all unique transaction types
            const uniqueTypes = [...new Set(this.statements.map(s => s.transactionType))];
            console.log('Available transaction types (multi-tenant):', uniqueTypes);
            
            this.filterTransactions();
          } else {
            this.statements = [];
            this.filteredStatements = [];
            console.log('No multi-tenant transactions found');
          }
          this.dismissLoadingModal();
        },
      });
  }

  /**
   * Load transactions for single-tenant applications using member-specific API
   */
  private loadSingleTenantTransactions(limit: number): void {
    console.log('Loading single-tenant transactions');
    
    this.memberService
      .getTransactionHistory(
        this.kc.lpUniueReference,
        this.beginDate,
        this.endDate,
        0,
        limit
      )
      .subscribe({
        error: (error) => {
          console.error('Single-tenant transaction error:', error);
          this.dismissLoadingModal();
          this.presentToast({
            message: 'Oops, something went wrong!',
            color: 'danger',
            position: 'middle',
          }).then();
        },
        next: (body) => {
          console.log('Single-tenant transactions received:', body);
          if (body !== undefined) {
            this.statements = body;
            // Log all unique transaction types to understand what reversal types exist
            const uniqueTypes = [...new Set(body.map(s => s.transactionType))];
            console.log('Available transaction types (single-tenant):', uniqueTypes);
            this.filterTransactions();
          }
          this.dismissLoadingModal();
        },
      });
  }

  /**
   * Format a date for API consumption (YYYY-MM-DD)
   */
  private formatDateForAPI(date: Date): string {
    return date.toISOString().split('T')[0];
  }

  selectTransactionType(type: string): void {
    this.selectedTransactionType = type;
    this.filterTransactions();
  }

  filterTransactions(): void {
    if (this.selectedTransactionType === 'All') {
      this.filteredStatements = [...this.statements];
    } else if (this.selectedTransactionType === 'Reversals') {
      // Include all reversal-related transaction types
      this.filteredStatements = this.statements.filter(statement => 
        this.isReversalTransaction(statement)
      );
    } else {
      this.filteredStatements = this.statements.filter(
        statement => statement.transactionType === this.selectedTransactionType
      );
    }
  }

  isReversalTransaction(statement: Statement): boolean {
    // Check for explicit reversal/refund transaction types
    const reversalTypes = ['Refund', 'RefundRedemption', 'Reversal', 'Void', 'Cancel'];
    
    if (reversalTypes.includes(statement.transactionType || '')) {
      return true;
    }
    
    // Check for transactions with negative points (potential reversals)
    const hasNegativePoints = (statement.transactionPoints || 0) < 0;
    
    // Check for transactions with negative value (potential reversals)
    const hasNegativeValue = (statement.actValue || 0) < 0;
    
    return hasNegativePoints || hasNegativeValue;
  }

  getTransactionIcon(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return 'add-circle-outline';
      case 'Redemption':
        return 'remove-circle-outline';
      case 'Refund':
      case 'RefundRedemption':
        return 'refresh-circle-outline';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return 'arrow-undo-circle-outline';
      case 'Token':
      case 'TokenRedemption':
        return 'gift-outline';
      default:
        return 'pricetags-outline';
    }
  }

  getTransactionColor(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return 'success';
      case 'Redemption':
        return 'danger';
      case 'Refund':
      case 'RefundRedemption':
        return 'warning';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return 'dark';
      case 'Token':
      case 'TokenRedemption':
        return 'tertiary';
      default:
        return 'medium';
    }
  }

  getTransactionIconColor(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return '#28a745';
      case 'Redemption':
        return '#dc3545';
      case 'Refund':
      case 'RefundRedemption':
        return '#ffc107';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return '#343a40';
      case 'Token':
      case 'TokenRedemption':
        return '#6f42c1';
      default:
        return '#6c757d';
    }
  }

  getTransactionPointsClass(transactionType?: string): string {
    switch (transactionType) {
      case 'Accrual':
        return 'pcu-earned';
      case 'Redemption':
        return 'pcu-spent';
      case 'Refund':
      case 'RefundRedemption':
        return 'pcu-refund';
      case 'Reversal':
      case 'Void':
      case 'Cancel':
        return 'pcu-reversal';
      default:
        return 'transaction-points';
    }
  }

  getTransactionPointsDisplay(statement: Statement): string {
    const points = statement.transactionPoints || 0;
    const transactionType = statement.transactionType;
    
    if (transactionType === 'Accrual' || transactionType === 'Refund') {
      return `+${Math.abs(points)}`;
    } else if (transactionType === 'Redemption' || transactionType === 'RefundRedemption') {
      return `-${Math.abs(points)}`;
    } else if (transactionType === 'Reversal' || transactionType === 'Void' || transactionType === 'Cancel') {
      // For reversals, show the actual sign of the points (could be + or -)
      return points >= 0 ? `+${points}` : `${points}`;
    }
    
    // For other transactions, show the actual value with appropriate sign
    return points >= 0 ? `+${points}` : `${points}`;
  }

  formatDate(dateString: string): string {
    if (!dateString) return '';
    
    // Handle UTC timezone format like "2024-11-27T13:07:01Z[UTC]"
    let cleanDateString = dateString;
    if (dateString.includes('[UTC]')) {
      cleanDateString = dateString.replace(/\[UTC\]$/, '');
    }
    
    const date = new Date(cleanDateString);
    if (isNaN(date.getTime())) return '';
    const options: Intl.DateTimeFormatOptions = { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric'
    };
    return date.toLocaleDateString('en-US', options);
  }

  formatCurrency(value: number): string {
    if (!value && value !== 0) return '0.00';
    return value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }

  getBaseMiles(statement: Statement): number {
    // If statement has specific baseMiles property, use it
    // Otherwise, for Accrual transactions, use transactionPoints
    if (statement.transactionType === 'Accrual') {
      return statement.transactionPoints || 0;
    }
    return 0;
  }

  getBonusMiles(statement: Statement): number {
    // Return bonus miles if available, otherwise 0
    return 0;
  }

  getUnitsRedeemed(statement: Statement): number {
    // For redemption transactions, use the transactionPoints value
    if (statement.transactionType === 'Redemption' || 
        statement.transactionType === 'RefundRedemption' || 
        statement.transactionType === 'Reversals') {
      return Math.abs(statement.transactionPoints || 0);
    }
    return 0;
  }
}

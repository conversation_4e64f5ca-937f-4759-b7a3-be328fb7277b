/* Onboarding Flow Layout Fix */
:host {
  display: block;
  height: 100%;
  overflow-y: auto;
}

/* Ensure lib-page-wrapper allows scrolling */
::ng-deep lib-page-wrapper {
  height: 100%;
  overflow-y: auto;
}

/* Notification Settings Content Container - make scrollable */
.notification-settings-content {
  position: relative;
  z-index: 2;
  min-height: 100vh;
  overflow-y: auto;
  padding-bottom: 40px;
  
  /* Override page-wrapper padding to make header and content flush with edges */
  margin: -16px -16px -24px !important;
  
  @media (min-width: 768px) {
    margin: -24px -32px -40px !important;
  }
  
  @media (min-width: 1200px) {
    margin: -28px -40px -48px !important;
  }
}

.onboarding-flow-content {
  --background: var(--ion-color-light);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  padding: 16px;
  
  /* Override page-wrapper padding to make header centered properly */
  margin: -16px -16px -24px !important;
  
  @media (min-width: 768px) {
    margin: -24px -32px -40px !important;
  }
  
  @media (min-width: 1200px) {
    margin: -28px -40px -48px !important;
    max-width: 1000px;
    margin-left: auto !important;
    margin-right: auto !important;
    margin-top: -28px !important;
    margin-bottom: -48px !important;
  }
  
  // Header section - properly centered
  .header-section {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
    
    .header-card {
      width: 100%;
      max-width: 600px;
      margin: 0;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      background: white;
      
      ion-card-content {
        padding: 20px;
      }
    }
  }
  
  // Content area
  .content-scroll-area {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    padding-bottom: 120px; // Space for fixed navigation

    .main-content {
      max-width: 800px;
      margin: 0 auto;
    }
  }
  
// Fixed navigation section - positioned outside scroll container
.navigation-section {
  position: fixed;
  bottom: 20px;
  left: 16px;
  right: 16px;
  z-index: 1000;

  @media (min-width: 768px) {
    left: 32px;
    right: 32px;
  }

  @media (min-width: 1200px) {
    max-width: 1000px;
    left: 50%;
    right: auto;
    transform: translateX(-50%);
    padding: 0 40px;
  }

  .navigation-card {
    margin: 0;
    border-radius: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
    background: white;

    ion-card-content {
      padding: 16px;
    }

    .navigation-controls {
      display: flex;
      align-items: center;
      gap: 12px;

      .spacer {
        flex: 1;
      }

      ion-button {
        margin: 0;
        --border-radius: 12px;
        font-weight: 500;
      }
    }
  }
}

  // Personal Information Form
  .personal-info-form {
    margin-top: 20px;

    ion-item {
      --border-color: var(--ion-color-light-shade);
      margin-bottom: 16px;
      border-radius: 8px;
      
      &.ion-invalid.ion-touched {
        --border-color: var(--ion-color-danger);
      }
    }

    .error-message {
      color: var(--ion-color-danger);
      font-size: 12px;
      margin-top: -12px;
      margin-bottom: 16px;
      margin-left: 16px;
    }
  }
  
  // Header Card Styles (consolidated)
  .header-section .header-card {
    .page-title {
      margin-bottom: 20px;
      
      h1 {
        margin: 0 0 8px 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--ion-color-primary);
      }
      
      p {
        margin: 0;
        font-size: 15px;
        line-height: 1.5;
        color: var(--ion-color-medium);
      }
    }
    
    .progress-section {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 12px;
      
      .progress-bar {
        flex: 1;
        height: 6px;
        background: #f0f0f0;
        border-radius: 3px;
        overflow: hidden;
        
        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, var(--ion-color-primary) 0%, var(--ion-color-primary-shade) 100%);
          border-radius: 3px;
          transition: width 0.3s ease;
        }
      }
      
      .progress-text {
        font-size: 13px;
        color: var(--ion-color-medium);
        white-space: nowrap;
        font-weight: 500;
      }
    }
    
    .time-estimate {
      display: flex;
      align-items: center;
      gap: 6px;
      justify-content: center;
      font-size: 13px;
      color: var(--ion-color-medium);
      
      ion-icon {
        font-size: 16px;
      }
    }
  }
  
  // Loading State
  .loading-container {
    padding: 0 16px;
    
    .loading-card {
      margin: 0;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      background: white;
      
      .loading-content {
        text-align: center;
        padding: 32px 20px;
        
        ion-spinner {
          margin-bottom: 16px;
          --color: var(--ion-color-primary);
        }
        
        p {
          margin: 0;
          color: var(--ion-color-medium);
          font-size: 14px;
        }
      }
    }
  }
  
  // Error State
  .error-container {
    padding: 0 16px;
    
    .error-card {
      margin: 0;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      background: white;
      
      .error-content {
        text-align: center;
        padding: 32px 20px;
        
        ion-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }
        
        h3 {
          margin: 0 0 12px 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--ion-color-danger);
        }
        
        p {
          margin: 0 0 20px 0;
          color: var(--ion-color-medium);
          font-size: 14px;
        }
        
        ion-button {
          --border-radius: 12px;
        }
      }
    }
  }
  
  // Main Content (within scrollable area)
  .content-scroll-area .main-content {
    // No additional padding since parent has padding
    
    // Steps Card
    .steps-card {
      margin: 0 0 20px;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      background: white;
      
      ion-card-header {
        padding: 16px 16px 0;
        background: white;
        
        h3 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: var(--ion-color-dark);
          padding-bottom: 16px;
          border-bottom: 1px solid #f0f0f0;
        }
      }
      
      ion-card-content {
        padding: 16px;
      }
      
      .steps-indicator {
        .step-item {
          display: flex;
          align-items: center;
          gap: 12px;
          padding: 12px;
          cursor: pointer;
          transition: all 0.2s ease;
          border-radius: 12px;
          background: #f8f9fa;
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          &:hover:not(.disabled):not(.current) {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            background: white;
          }
          
          &.current {
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f2ff 100%);
            border: 2px solid rgba(var(--ion-color-primary-rgb), 0.3);
            
            .step-circle {
              background: var(--ion-color-primary);
              color: white;
            }
            
            .step-title {
              color: var(--ion-color-primary);
              font-weight: 600;
            }
          }
          
          &.completed {
            .step-circle {
              background: var(--ion-color-success);
              color: white;
            }
            
            .step-title {
              color: var(--ion-color-success);
            }
            
            .step-description {
              color: var(--ion-color-success);
            }
          }
          
          &.disabled {
            opacity: 0.5;
            cursor: not-allowed;
          }
          
          .step-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--ion-color-medium);
            transition: all 0.2s ease;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
            
            .step-icon {
              font-size: 20px;
              
              &.completed {
                font-size: 18px;
              }
            }
          }
          
          .step-details {
            flex: 1;
            
            .step-title {
              margin: 0 0 4px 0;
              font-size: 15px;
              font-weight: 500;
              color: var(--ion-color-dark);
            }
            
            .step-description {
              margin: 0;
              font-size: 13px;
              color: var(--ion-color-medium);
              line-height: 1.3;
            }
          }
        }
      }
    }
    
    // Step Content Card
    .step-content-card {
      margin: 0 0 20px;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
      background: white;
      
      ion-card-header {
        padding: 20px;
        background: white;
        
        .step-header {
          display: flex;
          align-items: flex-start;
          gap: 16px;
          
          .step-icon-wrapper {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            background: linear-gradient(135deg, #f0f8ff 0%, #e6f2ff 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
            
            .step-icon-large {
              font-size: 28px;
              color: var(--ion-color-primary);
            }
          }
          
          .step-info {
            flex: 1;
            
            h2 {
              margin: 0 0 6px 0;
              font-size: 20px;
              font-weight: 600;
              color: var(--ion-color-dark);
            }
            
            p {
              margin: 0;
              font-size: 14px;
              color: var(--ion-color-medium);
              line-height: 1.4;
            }
          }
          
          .required-badge {
            margin-top: 4px;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 10px;
            border-radius: 6px;
          }
        }
      }
      
      ion-card-content {
        padding: 0 20px 20px;
      }
    }
    
    // Navigation Card Styles (moved to navigation-section)
    // See .navigation-section .navigation-card below
  }
}

// Step Content Styles
.step-content {
  .continue-button {
    margin-top: 24px;
    --border-radius: 12px;
    font-weight: 600;
  }
  
  h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--ion-color-dark);
  }
  
  > p {
    margin: 0 0 20px 0;
    color: var(--ion-color-medium);
    line-height: 1.5;
    font-size: 14px;
  }
}

// Welcome Content
.welcome-content {
  .welcome-features {
    margin-bottom: 24px;
    
    .feature-item {
      display: flex;
      align-items: center;
      gap: 16px;
      margin-bottom: 20px;
      padding: 16px;
      background: #f8f9fa;
      border-radius: 12px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .icon-wrapper {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: white;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-shrink: 0;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        
        ion-icon {
          font-size: 24px;
        }
      }
      
      .feature-text {
        h4 {
          margin: 0 0 4px 0;
          font-size: 15px;
          font-weight: 600;
          color: var(--ion-color-dark);
        }
        
        p {
          margin: 0;
          font-size: 13px;
          color: var(--ion-color-medium);
        }
      }
    }
  }
}

// Personal Info Content
.personal-info-content {
  .form-note {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px;
    background: #fff9e6;
    border-radius: 8px;
    margin-bottom: 24px;
    font-size: 13px;
    color: var(--ion-color-warning-shade);
    border: 1px solid rgba(var(--ion-color-warning-rgb), 0.3);
    
    ion-icon {
      flex-shrink: 0;
      font-size: 18px;
    }
  }
  
  .mock-form {
    margin-bottom: 8px;
    
    ion-item {
      margin-bottom: 16px;
      --border-radius: 12px;
      --background: #f8f9fa;
      --padding-start: 16px;
      --padding-end: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// Program Selection Content
.program-selection-content {
  text-align: center;
  
  .selection-button {
    margin: 24px 0 12px;
    --border-radius: 12px;
  }
  
  .skip-note {
    padding: 16px;
    background: #f8f9fa;
    border-radius: 12px;
    margin-top: 16px;
    
    p {
      margin: 0;
      font-size: 13px;
      color: var(--ion-color-medium);
      font-style: italic;
    }
  }
  
  .programs-selected {
    .success-message {
      margin: 24px 0;
      padding: 20px;
      background: linear-gradient(135deg, #f0fff4 0%, #e6ffed 100%);
      border-radius: 12px;
      border: 2px solid rgba(var(--ion-color-success-rgb), 0.3);
      
      ion-icon {
        font-size: 48px;
        margin-bottom: 12px;
      }
      
      .selected-count {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: var(--ion-color-success-shade);
      }
    }
    
    .continue-button {
      margin-top: 12px;
    }
  }
}

// Preferences Content
.preferences-content {
  .preferences-options {
    margin: 24px 0;
    
    ion-item {
      margin-bottom: 12px;
      --border-radius: 12px;
      --background: #f8f9fa;
      --padding-start: 16px;
      --padding-end: 16px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      ion-label {
        h3 {
          margin: 0 0 4px 0;
          font-size: 15px;
          font-weight: 500;
          color: var(--ion-color-dark);
        }
        
        p {
          margin: 0;
          font-size: 13px;
          color: var(--ion-color-medium);
        }
      }
    }
  }
}

// Verification Content
.verification-content {
  text-align: center;
  
  .verification-status {
    margin: 24px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    
    .verification-item {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 12px;
      margin-bottom: 12px;
      font-size: 15px;
      color: var(--ion-color-dark);
      
      &:last-child {
        margin-bottom: 0;
      }
      
      ion-icon {
        font-size: 20px;
      }
    }
  }
}

// Completion Content
.completion-content {
  text-align: center;
  
  .success-animation {
    margin-bottom: 20px;
    
    ion-icon {
      font-size: 80px;
      animation: bounceIn 0.6s ease;
    }
  }
  
  h3 {
    margin: 0 0 16px 0;
    color: var(--ion-color-success);
    font-size: 24px;
    font-weight: 600;
  }
  
  .next-steps {
    text-align: left;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 12px;
    margin-bottom: 24px;
    
    h4 {
      margin: 0 0 12px 0;
      color: var(--ion-color-primary);
      font-size: 15px;
      font-weight: 600;
    }
    
    ul {
      margin: 0;
      padding-left: 20px;
      color: var(--ion-color-medium);
      
      li {
        margin-bottom: 6px;
        line-height: 1.4;
        font-size: 13px;
        
        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

// Animations
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Responsive Design
@media (min-width: 768px) {
  .onboarding-flow-content {
    .header-card,
    .main-content {
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }
  }
}

@media (min-width: 1024px) {
  .onboarding-flow-content {
    .header-card,
    .main-content {
      max-width: 1000px;
    }
  }
}

// Mobile Responsive
@media (max-width: 768px) {
  .onboarding-flow-content {
    .header-card {
      margin: 0 12px 16px;
    }
    
    .main-content {
      padding: 0 12px;
      
      .steps-card,
      .step-content-card,
      .navigation-card {
        margin-left: 0;
        margin-right: 0;
      }
    }
  }
}
<lib-page-wrapper [containerSize]="'lg'" [hasBackground]="true">
  <div header>
    <lib-head-logo
      [names]="profile ? (profile.givenNames || '') + ' ' + (profile.surname || '') : ''"
      [membership]="profile?.newMembershipNumber"
      type="profile"
      [balance]="profile?.currentBalance"
      [src]="lssConfig.pages.landing.loggedinIcon"
    />
  </div>

  <div class="onboarding-flow-content">
    <!-- Header Card - Fixed at top -->
    <div class="header-section">
      <ion-card class="header-card">
        <ion-card-content>
          <div class="page-title">
            <h1>Account Setup</h1>
            <p *ngIf="currentStep()">{{ currentStep()?.title }}</p>
          </div>
          
          <!-- Progress indicator -->
          <div class="progress-section">
            <div class="progress-bar">
              <div class="progress-fill" [style.width.%]="progress()"></div>
            </div>
            <span class="progress-text">{{ progress() }}% Complete</span>
          </div>
          
          <!-- Time Estimate -->
          <div class="time-estimate" *ngIf="currentStep()?.estimatedTimeMinutes">
            <ion-icon name="time-outline"></ion-icon>
            <span>{{ formatTimeRemaining() }} remaining</span>
          </div>
        </ion-card-content>
      </ion-card>
    </div>

    <!-- Scrollable Content Area -->
    <div class="content-scroll-area">
      <!-- Loading State -->
      <div *ngIf="isLoading && !hasError" class="loading-container">
      <ion-card class="loading-card">
        <ion-card-content>
          <div class="loading-content">
            <ion-spinner></ion-spinner>
            <p>Loading onboarding...</p>
          </div>
        </ion-card-content>
      </ion-card>
    </div>

      <!-- Error State -->
      <div *ngIf="hasError" class="error-container">
      <ion-card class="error-card">
        <ion-card-content>
          <div class="error-content">
            <ion-icon name="alert-circle" color="danger" size="large"></ion-icon>
            <h3>Something went wrong</h3>
            <p>{{ errorMessage }}</p>
            <ion-button (click)="retry()" color="primary" fill="solid">
              <ion-icon name="refresh" slot="start"></ion-icon>
              Try Again
            </ion-button>
          </div>
        </ion-card-content>
      </ion-card>
    </div>

      <!-- Main Content -->
      <div *ngIf="!isLoading && !hasError && onboardingState() && currentStep()" class="main-content">
        <ion-card class="steps-card">
        <ion-card-header>
          <h3>Progress Steps</h3>
        </ion-card-header>
        <ion-card-content>
          <div class="steps-indicator">
            <div 
              *ngFor="let step of allSteps(); let i = index"
              class="step-item"
              [class.completed]="isStepCompleted(step.id)"
              [class.current]="isStepCurrent(i)"
              [class.disabled]="i > (onboardingState()?.currentStepIndex || 0)"
              (click)="!isStepCurrent(i) && i <= (onboardingState()?.currentStepIndex || 0) && goToStep(step.id)">
              
              <div class="step-circle">
                <ion-icon 
                  *ngIf="isStepCompleted(step.id)" 
                  name="checkmark" 
                  class="step-icon completed">
                </ion-icon>
                <ion-icon 
                  *ngIf="!isStepCompleted(step.id)" 
                  [name]="getStepIcon(step.id)" 
                  class="step-icon">
                </ion-icon>
              </div>
              
              <div class="step-details">
                <div class="step-title">{{ step.title }}</div>
                <div class="step-description">{{ getStepDescription(step) }}</div>
              </div>
            </div>
          </div>
        </ion-card-content>
      </ion-card>

        <!-- Current Step Content Card -->
        <ion-card class="step-content-card">
        <ion-card-header>
          <div class="step-header">
            <div class="step-icon-wrapper">
              <ion-icon [name]="getStepIcon(currentStep()!.id)" class="step-icon-large"></ion-icon>
            </div>
            <div class="step-info">
              <h2>{{ currentStep()?.title }}</h2>
              <p>{{ currentStep()?.description }}</p>
            </div>
            <ion-badge 
              *ngIf="currentStep()?.isRequired" 
              color="warning" 
              class="required-badge">
              Required
            </ion-badge>
          </div>
        </ion-card-header>

        <ion-card-content>
          <!-- Step-specific content based on current step -->
          <div [ngSwitch]="currentStep()?.id">
            
            <!-- Welcome Step -->
            <div *ngSwitchCase="'welcome'" class="step-content welcome-content">
              <div class="welcome-message">
                <h3>Welcome to our Loyalty Program!</h3>
                <p>We're excited to have you join us. This quick setup will help us personalize your experience and ensure you get the most out of your membership.</p>
                
                <div class="welcome-features">
                  <div class="feature-item">
                    <div class="icon-wrapper">
                      <ion-icon name="gift" color="primary"></ion-icon>
                    </div>
                    <div class="feature-text">
                      <h4>Earn Rewards</h4>
                      <p>Get points on every purchase</p>
                    </div>
                  </div>
                  <div class="feature-item">
                    <div class="icon-wrapper">
                      <ion-icon name="star" color="primary"></ion-icon>
                    </div>
                    <div class="feature-text">
                      <h4>Exclusive Benefits</h4>
                      <p>Access member-only offers</p>
                    </div>
                  </div>
                  <div class="feature-item">
                    <div class="icon-wrapper">
                      <ion-icon name="people" color="primary"></ion-icon>
                    </div>
                    <div class="feature-text">
                      <h4>VIP Treatment</h4>
                      <p>Priority customer service</p>
                    </div>
                  </div>
                </div>

                <ion-button 
                  (click)="completeCurrentStep()" 
                  expand="block" 
                  size="large" 
                  color="primary"
                  class="continue-button">
                  Let's Get Started
                  <ion-icon name="arrow-forward" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>

            <!-- Personal Info Step -->
            <div *ngSwitchCase="'personal-info'" class="step-content personal-info-content">
              <div class="info-message">
                <h3>Personal Information</h3>
                <p>Help us get to know you better so we can provide a personalized experience.</p>
                
                <div class="form-note">
                  <ion-icon name="lock-closed" color="medium"></ion-icon>
                  <span>Your information is secure and will only be used to enhance your experience.</span>
                </div>

                <!-- Personal Information Form -->
                <form [formGroup]="personalInfoForm" class="personal-info-form">
                  <ion-item>
                    <ion-label position="floating">First Name *</ion-label>
                    <ion-input 
                      formControlName="givenNames"
                      placeholder="Enter your first name">
                    </ion-input>
                  </ion-item>
                  <div *ngIf="personalInfoForm.get('givenNames')?.invalid && personalInfoForm.get('givenNames')?.touched" 
                       class="error-message">
                    First name is required
                  </div>

                  <ion-item>
                    <ion-label position="floating">Last Name *</ion-label>
                    <ion-input 
                      formControlName="surname"
                      placeholder="Enter your last name">
                    </ion-input>
                  </ion-item>
                  <div *ngIf="personalInfoForm.get('surname')?.invalid && personalInfoForm.get('surname')?.touched" 
                       class="error-message">
                    Last name is required
                  </div>

                  <ion-item>
                    <ion-label position="floating">Email Address *</ion-label>
                    <ion-input 
                      formControlName="emailAddress"
                      type="email"
                      placeholder="Enter your email">
                    </ion-input>
                  </ion-item>
                  <div *ngIf="personalInfoForm.get('emailAddress')?.invalid && personalInfoForm.get('emailAddress')?.touched" 
                       class="error-message">
                    <span *ngIf="personalInfoForm.get('emailAddress')?.errors?.['required']">Email is required</span>
                    <span *ngIf="personalInfoForm.get('emailAddress')?.errors?.['email']">Please enter a valid email</span>
                  </div>

                  <ion-item>
                    <ion-label position="floating">Country Code *</ion-label>
                    <ion-select 
                      formControlName="countryCode"
                      placeholder="Select country code">
                      <ion-select-option value="+27">+27 (South Africa)</ion-select-option>
                      <ion-select-option value="+1">+1 (US/Canada)</ion-select-option>
                      <ion-select-option value="+44">+44 (UK)</ion-select-option>
                      <ion-select-option value="+61">+61 (Australia)</ion-select-option>
                    </ion-select>
                  </ion-item>

                  <ion-item>
                    <ion-label position="floating">Phone Number *</ion-label>
                    <ion-input 
                      formControlName="phoneNumber"
                      type="tel"
                      placeholder="Enter your phone number">
                    </ion-input>
                  </ion-item>
                  <div *ngIf="personalInfoForm.get('phoneNumber')?.invalid && personalInfoForm.get('phoneNumber')?.touched" 
                       class="error-message">
                    Phone number is required
                  </div>
                </form>

                <ion-button 
                  (click)="completeCurrentStep()" 
                  expand="block" 
                  size="large" 
                  color="primary"
                  class="continue-button">
                  Continue
                  <ion-icon name="arrow-forward" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>

            <!-- Program Selection Step -->
            <div *ngSwitchCase="'program-selection'" class="step-content program-selection-content">
              <div class="selection-message">
                <h3>Choose Your Programs</h3>
                <p>Select the loyalty programs that match your interests and lifestyle.</p>
                
                <!-- Show different content based on whether programs have been selected -->
                <div *ngIf="!programsSelected()" class="no-programs-selected">
                  <ion-button 
                    (click)="navigateToProgramSelection()"
                    expand="block" 
                    size="large" 
                    color="primary"
                    fill="outline"
                    class="selection-button">
                    <ion-icon name="apps" slot="start"></ion-icon>
                    Select Programs
                  </ion-button>

                  <div class="skip-note">
                    <p>You can also skip this step and select programs later from your dashboard.</p>
                  </div>
                </div>
                
                <div *ngIf="programsSelected()" class="programs-selected">
                  <div class="success-message">
                    <ion-icon name="checkmark-circle" color="success" size="large"></ion-icon>
                    <p class="selected-count">You have selected {{ selectedProgramsCount() }} program<span *ngIf="selectedProgramsCount() > 1">s</span></p>
                  </div>
                  
                  <ion-button 
                    (click)="navigateToProgramSelection()"
                    expand="block" 
                    size="large" 
                    color="primary"
                    fill="outline"
                    class="selection-button">
                    <ion-icon name="apps" slot="start"></ion-icon>
                    Review/Change Programs
                  </ion-button>
                  
                  <ion-button 
                    (click)="completeProgramSelectionStep()"
                    expand="block" 
                    size="large" 
                    color="primary"
                    class="continue-button">
                    Continue with Selected Programs
                    <ion-icon name="arrow-forward" slot="end"></ion-icon>
                  </ion-button>
                </div>
              </div>
            </div>

            <!-- Preferences Step -->
            <div *ngSwitchCase="'preferences'" class="step-content preferences-content">
              <div class="preferences-message">
                <h3>Communication Preferences</h3>
                <p>Choose how you'd like to hear from us about offers, updates, and important information.</p>
                
                <!-- Mock preferences -->
                <div class="preferences-options">
                  <ion-item>
                    <ion-checkbox slot="start"></ion-checkbox>
                    <ion-label>
                      <h3>Email Notifications</h3>
                      <p>Receive offers and updates via email</p>
                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-checkbox slot="start"></ion-checkbox>
                    <ion-label>
                      <h3>SMS Notifications</h3>
                      <p>Get text messages for time-sensitive offers</p>
                    </ion-label>
                  </ion-item>
                  <ion-item>
                    <ion-checkbox slot="start"></ion-checkbox>
                    <ion-label>
                      <h3>Push Notifications</h3>
                      <p>Receive app notifications for new rewards</p>
                    </ion-label>
                  </ion-item>
                </div>

                <ion-button 
                  (click)="completeCurrentStep()" 
                  expand="block" 
                  size="large" 
                  color="primary"
                  class="continue-button">
                  Save Preferences
                  <ion-icon name="arrow-forward" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>

            <!-- Verification Step -->
            <div *ngSwitchCase="'verification'" class="step-content verification-content">
              <div class="verification-message">
                <h3>Account Verification</h3>
                <p>We need to verify your account to ensure security and enable all features.</p>
                
                <div class="verification-status">
                  <div class="verification-item">
                    <ion-icon name="checkmark-circle" color="success"></ion-icon>
                    <span>Email verified</span>
                  </div>
                  <div class="verification-item">
                    <ion-icon name="checkmark-circle" color="success"></ion-icon>
                    <span>Phone number verified</span>
                  </div>
                  <div class="verification-item">
                    <ion-icon name="checkmark-circle" color="success"></ion-icon>
                    <span>Identity confirmed</span>
                  </div>
                </div>

                <ion-button 
                  (click)="completeCurrentStep()" 
                  expand="block" 
                  size="large" 
                  color="primary"
                  class="continue-button">
                  Complete Verification
                  <ion-icon name="shield-checkmark" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>

            <!-- Completion Step -->
            <div *ngSwitchCase="'completion'" class="step-content completion-content">
              <div class="completion-message">
                <div class="success-animation">
                  <ion-icon name="checkmark-circle" color="success" size="large"></ion-icon>
                </div>
                <h3>You're All Set!</h3>
                <p>Your account has been successfully configured. Welcome to our loyalty program!</p>
                
                <div class="next-steps">
                  <h4>What's Next?</h4>
                  <ul>
                    <li>Explore the home page</li>
                    <li>Start earning points</li>
                    <li>Check out available rewards</li>
                    <li>Manage your programs</li>
                  </ul>
                </div>

                <ion-button 
                  (click)="completeCurrentStep()" 
                  expand="block" 
                  size="large" 
                  color="primary"
                  class="continue-button">
                  Start Using App
                  <ion-icon name="home" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>

            <!-- Default case -->
            <div *ngSwitchDefault class="step-content default-content">
              <div class="default-message">
                <h3>{{ currentStep()?.title }}</h3>
                <p>{{ currentStep()?.description }}</p>
                
                <ion-button 
                  (click)="completeCurrentStep()" 
                  expand="block" 
                  size="large" 
                  color="primary"
                  class="continue-button">
                  Continue
                  <ion-icon name="arrow-forward" slot="end"></ion-icon>
                </ion-button>
              </div>
            </div>
          </div>
        </ion-card-content>
      </ion-card>

      </div>
    </div>
  </div>

  <!-- Fixed Navigation at Bottom - Moved outside main content -->
  <div class="navigation-section">
      <ion-card 
        *ngIf="!isLoading && !hasError && onboardingState() && currentStep()"
        class="navigation-card" 
        #navigationCard>
        <ion-card-content>
        <div class="navigation-controls">
          <ion-button 
            *ngIf="canGoBack()"
            (click)="previousStep()" 
            fill="clear" 
            color="medium"
            [disabled]="isLoading">
            <ion-icon name="arrow-back" slot="start"></ion-icon>
            Back
          </ion-button>
          
          <div class="spacer"></div>

          <ion-button 
            *ngIf="currentStep() && !currentStep()?.isRequired" 
            (click)="skipCurrentStep()" 
            fill="clear"
            color="medium">
            Skip
          </ion-button>
          
          <ion-button 
            *ngIf="canProceed() && !isLastStep()"
            (click)="nextStep()" 
            color="primary"
            [disabled]="isLoading">
            Next
            <ion-icon name="arrow-forward" slot="end"></ion-icon>
          </ion-button>

          <ion-button 
            (click)="exitOnboarding()" 
            fill="clear"
            color="danger">
            <ion-icon name="close" slot="start"></ion-icon>
            Exit
          </ion-button>
        </div>
      </ion-card-content>
    </ion-card>
  </div>
</lib-page-wrapper>
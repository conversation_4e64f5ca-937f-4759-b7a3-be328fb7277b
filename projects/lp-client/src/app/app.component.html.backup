<ion-app id="app" class="ion-page app-background bg-app">
  <!-- Desktop Sidebar Menu (Permanent Left) -->
  <div class="desktop-sidebar" *ngIf="loggedin">
    <div class="sidebar-header">
      <div class="sidebar-logo" *ngIf="sidebarLogoUrl">
        <img [src]="sidebarLogoUrl" [alt]="sidebarLogoAlt" />
      </div>
      <div class="sidebar-profile" *ngIf="profile">
        <div class="profile-avatar">
          <ion-icon name="person-circle"></ion-icon>
        </div>
        <h3>{{ profile.givenNames }} {{ profile.surname }}</h3>
        <p>{{ profile.newMembershipNumber }}</p>
      </div>
    </div>
    
    <div class="sidebar-menu">
      <div class="menu-items">
        <a 
          *ngFor="let item of menuList" 
          class="sidebar-menu-item" 
          [class.active]="item.link == '/' + pageTitle"
          [routerLink]="[item.link]">
          <ion-icon [name]="getMenuIcon(item.text)"></ion-icon>
          <span>{{ item.text }}</span>
        </a>
        
        <div class="sidebar-divider"></div>
        
        <a class="sidebar-menu-item logout" (click)="logout()">
          <ion-icon name="log-out-outline"></ion-icon>
          <span>Sign Out</span>
        </a>
      </div>
    </div>
    
    <div class="sidebar-footer">
      <div class="app-version">
        <p>Version 1.0.0</p>
      </div>
    </div>
  </div>

  <!-- Main Layout Container -->
  <div class="main-layout" [class.with-sidebar]="loggedin">
    <!-- Modern Header -->
    <ion-header class="modern-header" *ngIf="loggedin">
      <ion-toolbar class="modern-toolbar">
        <ion-buttons slot="start">
          <ion-button class="back-button" (click)="back()" *ngIf="showMenuBack">
            <ion-icon name="chevron-back-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
        
        <ion-title class="modern-title">{{ pageText }}</ion-title>
        
        <ion-buttons slot="end">
          <ion-button class="notification-button" [routerLink]="'/public/notifications'" *ngIf="loggedin">
            <div class="notification-wrapper">
              <ion-icon name="notifications-outline"></ion-icon>
              <span class="notification-badge" *ngIf="count > 0">{{ count > 99 ? '99+' : count }}</span>
            </div>
          </ion-button>
          <ion-button class="menu-toggle-button mobile-only" (click)="toggleMenu()">
            <ion-icon name="menu-outline"></ion-icon>
          </ion-button>
        </ion-buttons>
      </ion-toolbar>
    </ion-header>

    <!-- Main Content -->
    <ion-content id="app-content"
                 class="app-background bg-app"
                 [class.no-content-padding]="router.url.startsWith('/secure/notification-settings')"
                 style="--background: transparent;"
                 scrollY="true" scrollX="false">
      <ion-router-outlet></ion-router-outlet>
    </ion-content>
  </div>

  <!-- Notification Toast Component -->
  <app-notification-toast></app-notification-toast>

  <!-- Custom Mobile Menu Overlay (No Interference) -->
  <div class="mobile-menu-overlay" [class.menu-open]="mobileMenuOpen" (click)="closeMobileMenu()">
    <div class="mobile-menu-panel" (click)="$event.stopPropagation()">
      <div class="menu-header">
        <h2>Menu</h2>
        <ion-button fill="clear" (click)="closeMobileMenu()">
          <ion-icon name="close-outline"></ion-icon>
        </ion-button>
      </div>
      
      <div class="menu-content">
        <!-- Profile Section (when logged in) -->
        <div class="profile-section" *ngIf="loggedin && profile">
          <div class="profile-avatar">
            <ion-icon name="person-circle"></ion-icon>
          </div>
          <h3>{{ profile.givenNames }} {{ profile.surname }}</h3>
          <p>{{ profile.newMembershipNumber }}</p>
        </div>

        <!-- Logo Section -->
        <div class="logo-section" *ngIf="lssConfig.navigation.sidebarIcon && !loggedin">
          <img [src]="lssConfig.navigation.sidebarIcon" alt="Logo" />
        </div>

        <!-- Menu Items -->
        <div class="menu-list">
          <!-- Logged In Menu -->
          <ng-container *ngIf="loggedin">
            <div *ngFor="let item of menuList" 
                 class="menu-item" 
                 [class.active]="item.link == '/' + pageTitle"
                 (click)="navigateAndClose(item.link)">
              <ion-icon [name]="getMenuIcon(item.text)"></ion-icon>
              <span>{{ item.text }}</span>
            </div>
            
            <div class="menu-divider"></div>
            
            <div class="menu-item logout" (click)="logoutAndClose()">
              <ion-icon name="log-out-outline"></ion-icon>
              <span>Sign Out</span>
            </div>
          </ng-container>

          <!-- Not Logged In Menu -->
          <ng-container *ngIf="!loggedin">
            <div class="menu-item" (click)="loginAndClose()">
              <ion-icon name="log-in-outline"></ion-icon>
              <span>Sign In</span>
            </div>
            
            <div class="menu-item" (click)="navigateAndClose('/public/validate')">
              <ion-icon name="person-add-outline"></ion-icon>
              <span>Sign Up</span>
            </div>
            
            <div class="menu-item" (click)="navigateAndClose('/public/password')">
              <ion-icon name="key-outline"></ion-icon>
              <span>Forgot Password</span>
            </div>
          </ng-container>
        </div>
      </div>
      
      <div class="menu-footer">
        <div class="app-version">
          <p>Version 1.0.0</p>
        </div>
      </div>
    </div>
  </div>

</ion-app>
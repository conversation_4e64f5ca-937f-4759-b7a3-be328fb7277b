import { APP_INITIALIZER, ApplicationConfig, importProvidersFrom } from '@angular/core';
import { RouteReuseStrategy, provideRouter } from '@angular/router';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import {
  HttpClientModule,
  HTTP_INTERCEPTORS,
} from '@angular/common/http';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import {
  InterceptorService,
  KeyCloakService,
  LogPublishersService,
  LogService,
  LssConfig,
  AuthGuardService,
} from 'lp-client-api';
import { environment } from '../environments/environment';
import { ConfigService } from './services/config.service';
import { JsonDateAdapter } from './utils/json-date-adapter';
import { Routes } from '@angular/router';

const routes: Routes = [
  {
    path: '',
    loadChildren: () => import('./public/app-tabs/app-tabs.module').then(m => m.AppTabsPageModule)
  },
  {
    path: 'public',
    loadChildren: () => import('./public/public.module').then(m => m.PublicModule)
  },
  {
    path: 'secure',
    loadChildren: () => import('./secure/secure.module').then(m => m.SecureModule),
    canActivate: [AuthGuardService]
  }
];

function initializeKeycloak(config: ConfigService) {
  return () => config.loadConfig();
}

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    importProvidersFrom(
      BrowserModule,
      BrowserAnimationsModule,
      FontAwesomeModule,
      HttpClientModule,
      IonicModule.forRoot()
    ),
    { provide: 'environment', useValue: environment },
    {
      provide: APP_INITIALIZER,
      useFactory: initializeKeycloak,
      multi: true,
      deps: [ConfigService],
    },
    {
      provide: LssConfig,
      useFactory: (config: ConfigService) => {
        return config.sysConfig;
      },
      deps: [ConfigService],
    },
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: InterceptorService,
      multi: true,
      deps: [KeyCloakService],
    },
    LogService,
    LogPublishersService,
    JsonDateAdapter,
    ConfigService,
    KeyCloakService,
    AuthGuardService,
  ],
};

/* Ensure the host element is scrollable */
:host {
  display: block;
  height: 100%;
  overflow-y: auto;
}

/* Ensure lib-page-wrapper allows scrolling */
::ng-deep lib-page-wrapper {
  min-height: 100%;
}

/* Base Background */
.app-background {
  --background: var(--ion-color-base, #124d75);
}

.bg-app {
  background: var(--ion-color-base, #215d84) !important;
}

/* Modern Header */
.modern-header {
  background: transparent;
  flex-shrink: 0;
  position: relative;
  z-index: 10; // Above content but below sidebar
  
  .modern-toolbar {
    --background: var(--ion-color-base-shade, #00569a);
    --border-width: 0;
    --padding-top: 0;
    --padding-bottom: 0;
    // Remove default horizontal padding so header is flush with edges
    --padding-start: 0px;
    --padding-end: 0px;
    --min-height: 56px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    
    .modern-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
      text-align: center;
      padding: 0;
    }
    
    ion-button {
      --color: white;
      --padding-start: 8px;
      --padding-end: 8px;
      --padding-top: 8px;
      --padding-bottom: 8px;
      margin: 0;
      
      ion-icon {
        font-size: 24px;
      }
    }
    
    .back-button {
      ion-icon {
        font-size: 28px;
      }
    }
    
    .notification-button {
      position: relative;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        transform: scale(1.1);
        
        ion-icon {
          color: rgba(255, 255, 255, 0.9) !important;
          filter: drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3));
        }
      }
      
      &:active {
        transform: scale(0.95);
        transition: all 0.1s ease;
      }
      
      .notification-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        
        ion-icon {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .notification-badge {
          position: absolute;
          top: -4px;
          right: -4px;
          background: #FF6B35;
          color: white;
          font-size: 10px;
          font-weight: 600;
          padding: 2px 4px;
          border-radius: 10px;
          min-width: 16px;
          text-align: center;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          animation: pulse 2s infinite;
          
          @keyframes pulse {
            0% {
              transform: scale(1);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
            50% {
              transform: scale(1.1);
              box-shadow: 0 2px 8px rgba(255, 107, 53, 0.4);
            }
            100% {
              transform: scale(1);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            }
          }
          
          &:hover {
            transform: scale(1.2);
            box-shadow: 0 4px 12px rgba(255, 107, 53, 0.5);
            animation: none;
          }
        }
      }
    }
  }
}

/* Modern Side Menu */
.modern-menu {
  --width: 280px;
  
  .menu-header {
    background: white;
    
    ion-toolbar {
      --background: white;
      --border-width: 0;
      border-bottom: 1px solid #e0e0e0;
      
      ion-title {
        font-size: 20px;
        font-weight: 600;
        color: #212121;
      }
      
      .close-menu-button {
        --color: #666;
        
        ion-icon {
          font-size: 24px;
        }
      }
    }
  }
  
  .menu-content {
    --background: #f8f9fa;
    
    .profile-section {
      background: white;
      padding: 24px;
      text-align: center;
      border-bottom: 1px solid #e0e0e0;
      
      .profile-avatar {
        width: 80px;
        height: 80px;
        margin: 0 auto 16px;
        
        ion-icon {
          font-size: 80px;
          color: #9e9e9e;
        }
      }
      
      h3 {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: #212121;
      }
      
      p {
        margin: 0;
        font-size: 14px;
        color: #666;
      }
    }
    
    .logo-section {
      padding: 24px;
      text-align: center;
      background: white;
      border-bottom: 1px solid #e0e0e0;
      
      img {
        max-width: 120px;
        height: auto;
      }
    }
    
    .menu-list {
      background: transparent;
      padding: 8px 0;
      
      .menu-item {
        --background: transparent;
        --padding-start: 24px;
        --padding-end: 24px;
        --padding-top: 12px;
        --padding-bottom: 12px;
        --min-height: 48px;
        --border-color: transparent;
        margin: 4px 8px;
        border-radius: 8px;
        transition: all 0.3s ease;
        
        ion-icon {
          color: #666;
          font-size: 22px;
          margin-right: 16px;
        }
        
        ion-label {
          font-size: 15px;
          font-weight: 500;
          color: #424242;
        }
        
        &:hover {
          --background: rgba(0, 0, 0, 0.04);
        }
        
        &.active {
          --background: rgba(255, 107, 53, 0.1);
          
          ion-icon {
            color: var(--ion-color-primary, #FF6B35);
          }
          
          ion-label {
            color: var(--ion-color-primary, #FF6B35);
            font-weight: 600;
          }
        }
        
        &.logout {
          ion-icon {
            color: #F44336;
          }
          
          &:hover {
            --background: rgba(244, 67, 54, 0.08);
          }
        }
      }
      
      .menu-divider {
        height: 1px;
        background: #e0e0e0;
        margin: 8px 24px;
      }
    }
    
  }
  
  .menu-footer {
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    
    .app-version {
      padding: 16px;
      text-align: center;
      
      p {
        margin: 0;
        font-size: 12px;
        color: #999;
      }
    }
  }
}

/* Modern Tab Bar */
.modern-tabs {
  .modern-tab-bar {
    --background: white;
    --border: 0;
    height: 65px;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    padding: 0 10px;
    
    .tab-button {
      --color: #999;
      --color-selected: var(--ion-color-primary, #FF6B35);
      --padding-top: 8px;
      --padding-bottom: 8px;
      position: relative;
      
      .tab-icon-wrapper {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12px;
        transition: all 0.3s ease;
        margin: 0 auto 4px;
        
        ion-icon {
          font-size: 24px;
          transition: all 0.3s ease;
        }
        
        &.selected {
          background: rgba(255, 107, 53, 0.1);
          
          ion-icon {
            color: var(--ion-color-primary, #FF6B35);
            transform: scale(1.1);
          }
        }
      }
      
      ion-label {
        font-size: 11px;
        font-weight: 500;
        text-transform: none;
        margin-top: 2px;
      }
      
      &.tab-selected {
        ion-label {
          color: var(--ion-color-primary, #FF6B35);
          font-weight: 600;
        }
      }
      
      // Active indicator
      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%) scaleX(0);
        width: 30px;
        height: 3px;
        background: var(--ion-color-primary, #FF6B35);
        border-radius: 0 0 3px 3px;
        transition: transform 0.3s ease;
      }
      
      &.tab-selected::before {
        transform: translateX(-50%) scaleX(1);
      }
    }
  }
}

/* Menu backdrop blur effect */
ion-menu {
  .blur {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    z-index: -1;
  }
}

/* Desktop Sidebar (Permanent Left) */
.desktop-sidebar {
  display: none !important; // Force hidden on mobile
  position: fixed;
  left: -280px; // Move off-screen by default
  top: 0;
  width: 280px;
  height: 100vh;
  background: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  z-index: 10;
  overflow-y: auto;
  overflow-x: hidden;
  pointer-events: none !important; // Force no interactions by default
  transition: left 0.3s ease; // Smooth transition
  
  // Only show and enable interactions on desktop
  max-width: 280px;
  box-sizing: border-box;

  @media (min-width: 768px) {
    display: block !important;
    left: 0; // Move back on screen
    pointer-events: auto !important; // Enable interactions on desktop
  }
  
  .sidebar-header {
    background: white;
    border-bottom: 1px solid #e0e0e0;
    padding: 24px;
    
    .sidebar-logo {
      text-align: center;
      margin-bottom: 16px;
      
      img {
        max-width: 120px;
        height: auto;
      }
    }
    
    .sidebar-profile {
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
      border-radius: 8px;
      padding: 8px;
      margin: -8px;
      
      &:hover {
        background: rgba(0, 0, 0, 0.02);
        transform: translateY(-1px);
      }
      
      .profile-avatar {
        width: 60px;
        height: 60px;
        margin: 0 auto 12px;
        position: relative;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        
        &::before {
          content: '';
          position: absolute;
          inset: -3px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--ion-color-primary, #FF6B35), transparent);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        ion-icon {
          font-size: 60px;
          color: #9e9e9e;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          z-index: 1;
        }
      }
      
      h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #212121;
        transition: all 0.3s ease;
      }
      
      p {
        margin: 0;
        font-size: 13px;
        color: #666;
        transition: all 0.3s ease;
      }
      
      &:hover {
        .profile-avatar {
          transform: scale(1.05);
          
          &::before {
            opacity: 0.1;
          }
          
          ion-icon {
            color: var(--ion-color-primary, #FF6B35);
            filter: drop-shadow(0 2px 8px rgba(255, 107, 53, 0.3));
          }
        }
        
        h3 {
          color: var(--ion-color-primary, #FF6B35);
        }
        
        p {
          color: #424242;
        }
      }
    }
  }
  
  .sidebar-menu {
    padding: 12px 0;
    
    .menu-items {
      .sidebar-menu-item {
        display: flex;
        align-items: center;
        padding: 12px 24px;
        margin: 2px 8px;
        border-radius: 8px;
        text-decoration: none;
        color: #424242;
        font-size: 15px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        
        // Subtle shimmer effect on hover
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
          transition: left 0.5s ease;
        }
        
        ion-icon {
          font-size: 20px;
          margin-right: 16px;
          color: #666;
          min-width: 20px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateZ(0); // Hardware acceleration
        }
        
        span {
          flex: 1;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        &:hover {
          background: rgba(0, 0, 0, 0.04);
          transform: translateX(4px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          
          &::before {
            left: 100%;
          }
          
          ion-icon {
            transform: scale(1.1) translateZ(0);
            color: var(--ion-color-primary, #FF6B35);
          }
          
          span {
            font-weight: 600;
          }
        }
        
        &:active {
          transform: translateX(2px) scale(0.98);
          transition: all 0.1s ease;
        }
        
        &.active {
          background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));
          color: var(--ion-color-primary, #FF6B35);
          font-weight: 600;
          box-shadow: 0 2px 8px rgba(255, 107, 53, 0.2);
          
          ion-icon {
            color: var(--ion-color-primary, #FF6B35);
            transform: scale(1.1) translateZ(0);
            filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.3));
          }
          
          &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 60%;
            background: var(--ion-color-primary, #FF6B35);
            border-radius: 0 2px 2px 0;
            box-shadow: 0 0 8px rgba(255, 107, 53, 0.4);
          }
        }
        
        &.logout {
          color: #F44336;
          
          ion-icon {
            color: #F44336;
          }
          
          &:hover {
            background: rgba(244, 67, 54, 0.08);
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.15);
            
            ion-icon {
              color: #F44336;
              transform: scale(1.1) translateZ(0);
              filter: drop-shadow(0 2px 4px rgba(244, 67, 54, 0.3));
            }
          }
        }
      }
      
      .sidebar-divider {
        height: 1px;
        background: #e0e0e0;
        margin: 8px 24px;
      }
    }
  }
  
  .sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    padding: 16px;
    text-align: center;
    
    .app-version {
      p {
        margin: 0;
        font-size: 12px;
        color: #999;
      }
    }
  }
}

/* Main Layout Container */
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100%;
  transition: margin-left 0.3s ease;
  
  &.with-sidebar {
    // Force no margin by default (mobile behavior)
    margin-left: 0 !important;
  }
  
  ion-content {
    flex: 1;
    --background: transparent;
    --overflow: auto;
    /* Enable natural scrolling */
    --padding-top: 0;
    --padding-bottom: 0;
    --padding-start: 0;
    --padding-end: 0;

    /* Keep Ionic scroll containers flexible */
    .scroll-content,
    .inner-scroll {
      min-height: auto !important;
      height: auto !important;
      /* Remove flex constraints that prevent expansion */
      flex: none !important;
      contain: none !important;
    }

    /* Newer Ionic builds use shadow parts for scroll handling */
    &::part(scroll) {
      height: auto;
      min-height: auto;
      overflow-y: auto;
    }

    /* Ensure ion-router-outlet content can expand */
    ion-router-outlet {
      height: auto !important;
      min-height: auto !important;
      display: block !important;
    }
  }
}

/* Utility class to remove all ion-content padding (used for full-bleed pages) */
.no-content-padding {
  --padding-top: 0 !important;
  --padding-bottom: 0 !important;
  --padding-start: 0 !important;
  --padding-end: 0 !important;
}

/* Mobile-only elements */
.mobile-only {
  display: block;
}

/* Tablet and Desktop Responsive Styles */
@media (min-width: 768px) {
  // Only apply margin when logged in (when sidebar should show)
  .main-layout.with-sidebar {
    /* Use padding-left to reserve space for the fixed sidebar */
    padding-left: 280px !important;
    margin-left: 0 !important;
    transition: padding-left 0.3s ease; // Smooth transition
    width: 100% !important;
    max-width: 100% !important;
    
    ion-content {
      width: 100% !important;
      max-width: none !important;
      --padding-start: 24px; // More padding on desktop
      --padding-end: 24px;
      --padding-top: 24px;
      --padding-bottom: 24px;
    }
  }
  
  .mobile-only {
    display: none; // Hide hamburger menu
  }
  
  // Hide mobile menu completely on desktop
  .modern-menu {
    display: none !important;
  }
  
  // Hide custom mobile menu overlay on desktop
  .mobile-menu-overlay {
    display: none !important;
  }
  
  // Adjust toolbar for desktop
  .modern-header {
    z-index: 11; // Ensure header stays above content and not under sidebar
    .modern-toolbar {
      --padding-start: 0px;
      --padding-end: 0px;
    }
  }
}

/* Large Desktop Optimizations */
@media (min-width: 1200px) {
  .desktop-sidebar {
    width: 320px; // Slightly wider on large screens
  }
  
  .main-layout.with-sidebar {
    padding-left: 320px;
    margin-left: 0 !important;
    
    ion-content {
      --padding-start: 32px; // Even more padding on large screens
      --padding-end: 32px;
      --padding-top: 32px;
      --padding-bottom: 32px;
    }
  }
}

/* Preserve Mobile Behavior */
@media (max-width: 767px) {
  .desktop-sidebar {
    display: none !important; // Always hidden on mobile
  }
  
  .main-layout {
    margin-left: 0 !important; // Force no margin on mobile
    height: 100%;
    
    // Ensure mobile touch interactions work
    ion-content {
      touch-action: auto !important;
      -webkit-overflow-scrolling: touch;
      pointer-events: auto !important;
    }
    
    // Fix any potential mobile blocking elements
    * {
      touch-action: auto !important;
    }
  }
  
  .mobile-only {
    display: block !important; // Ensure hamburger shows
  }
  
  .modern-menu {
    display: block !important; // Ensure mobile menu works
  }
  
  // Global mobile interaction fix
  ion-router-outlet,
  ion-router-outlet *,
  ion-content,
  ion-content * {
    pointer-events: auto !important;
    touch-action: auto !important;
  }
  
  // CRITICAL FIX: Menu functional but doesn't block content interactions
  
  // Ensure content is always interactive, even with menu system active
  #app-content {
    pointer-events: auto !important;
    touch-action: auto !important;
    position: relative !important;
    z-index: 1 !important;
  }
  
  // Override Ionic menu transforms that block content when menu closed
  .menu-content-open #app-content {
    pointer-events: auto !important;
    touch-action: auto !important;
  }
  
  // Ensure menu backdrop works but doesn't block content when dismissed
  ion-backdrop {
    pointer-events: auto !important;
  }
  
  // Force menu to work properly on mobile
  .modern-menu {
    display: block !important;
    pointer-events: auto !important;
    
    // Menu should be interactive when open
    &.show-menu {
      pointer-events: auto !important;
      z-index: 1000 !important;
    }
  }
  
  // Critical: Ensure content remains clickable after menu interactions
  body:not(.menu-open) #app-content {
    pointer-events: auto !important;
    touch-action: auto !important;
    transform: none !important;
  }
  
  // Custom Mobile Menu Styles (No Ion-Menu Interference) - MOBILE ONLY
  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    
    &.menu-open {
      opacity: 1;
      visibility: visible;
    }
    
    .mobile-menu-panel {
      position: absolute;
      right: 0;
      top: 0;
      width: 280px;
      height: 100vh;
      background: white;
      transform: translateX(100%);
      transition: transform 0.3s ease;
      display: flex;
      flex-direction: column;
      box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
      
      .menu-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px;
        border-bottom: 1px solid #e0e0e0;
        background: white;
        
        h2 {
          margin: 0;
          font-size: 18px;
          font-weight: 600;
          color: #333;
        }
        
        ion-button {
          --color: #666;
        }
      }
      
      .menu-content {
        flex: 1;
        overflow-y: auto;
        padding: 20px;
        
        .profile-section {
          text-align: center;
          padding: 20px 0;
          border-bottom: 1px solid #e0e0e0;
          margin-bottom: 20px;
          
          .profile-avatar {
            margin-bottom: 12px;
            
            ion-icon {
              font-size: 48px;
              color: var(--ion-color-primary, #0072bc);
            }
          }
          
          h3 {
            margin: 8px 0 4px;
            font-size: 16px;
            font-weight: 600;
            color: #333;
          }
          
          p {
            margin: 0;
            font-size: 14px;
            color: #666;
          }
        }
        
        .logo-section {
          text-align: center;
          padding: 20px 0;
          border-bottom: 1px solid #e0e0e0;
          margin-bottom: 20px;
          
          img {
            max-width: 120px;
            height: auto;
          }
        }
        
        .menu-list {
          .menu-item {
            display: flex;
            align-items: center;
            padding: 16px 0;
            cursor: pointer;
            border-bottom: 1px solid #f5f5f5;
            transition: all 0.2s ease;
            color: #333 !important; // Force text color
            
            &:hover {
              background: rgba(0, 114, 188, 0.05);
              padding-left: 8px;
            }
            
            &.active {
              background: rgba(0, 114, 188, 0.1);
              color: var(--ion-color-primary, #0072bc) !important;
              font-weight: 600;
            }
            
            &.logout {
              color: #e53e3e !important;
              border-top: 1px solid #e0e0e0;
              margin-top: 12px;
              padding-top: 16px;
            }
            
            ion-icon {
              font-size: 20px;
              margin-right: 16px;
              width: 20px;
              color: inherit !important;
            }
            
            span {
              font-size: 14px;
              font-weight: 500;
              color: inherit !important;
            }
          }
          
          .menu-divider {
            height: 1px;
            background: #e0e0e0;
            margin: 12px 0;
          }
        }
      }
      
      .menu-footer {
        padding: 16px 20px;
        border-top: 1px solid #e0e0e0;
        background: #f8f9fa;
        
        .app-version {
          text-align: center;
          
          p {
            margin: 0;
            font-size: 12px;
            color: #666;
          }
        }
      }
    }
    
    &.menu-open .mobile-menu-panel {
      transform: translateX(0);
    }
  }
}

/* Small Mobile Adjustments */
@media (max-width: 320px) {
  .modern-header {
    .modern-toolbar {
      .modern-title {
        font-size: 16px;
      }
    }
  }
  
  .modern-menu {
    --width: 260px;
  }
}
/* Override any default Ionic padding and ensure flush layout */
ion-app {
  margin: 0 !important;
  padding: 0 !important;
}

.main-layout {
  margin: 0 !important;
  padding: 0 !important;
}

.modern-header {
  margin: 0 !important;
  padding: 0 !important;
}

.modern-toolbar {
  --padding-start: 0px !important;
  --padding-end: 0px !important;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
  --min-height: 56px;
  margin: 0 !important;
  padding: 0 !important;
}

#app-content {
  --padding-start: 0px !important;
  --padding-end: 0px !important;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure ion-router-outlet has no padding */
ion-router-outlet {
  margin: 0 !important;
  padding: 0 !important;
}

/* Remove any default body/html margins that might cause white space */
html, body {
  margin: 0 !important;
  padding: 0 !important;
}

/* Override any default Ionic content padding */
ion-content {
  --padding-start: 0px !important;
  --padding-end: 0px !important;
  --padding-top: 0px !important;
  --padding-bottom: 0px !important;
}

/* Ensure all child elements respect the flush layout */
.main-layout ion-content::part(background) {
  margin: 0 !important;
  padding: 0 !important;
}

.main-layout ion-content::part(scroll) {
  margin: 0 !important;
  padding: 0 !important;
}

/* CRITICAL FIX: Override default padding to eliminate white space around content */
.main-layout {
  ion-content {
    /* Remove ALL default padding to make content flush to edges */
    --padding-top: 0 !important;
    --padding-bottom: 0 !important;
    --padding-start: 0 !important;
    --padding-end: 0 !important;
  }
}

/* Desktop responsive - also remove padding */
@media (min-width: 768px) {
  .main-layout.with-sidebar {
    ion-content {
      /* Remove ALL default padding on desktop too */
      --padding-start: 0 !important;
      --padding-end: 0 !important;
      --padding-top: 0 !important;
      --padding-bottom: 0 !important;
    }
  }
}

/* Large Desktop - also remove padding */
@media (min-width: 1200px) {
  .main-layout.with-sidebar {
    ion-content {
      /* Remove ALL default padding on large screens too */
      --padding-start: 0 !important;
      --padding-end: 0 !important;
      --padding-top: 0 !important;
      --padding-bottom: 0 !important;
    }
  }
}

/* Additional override to ensure no spacing */
#app-content {
  --padding-start: 0 !important;
  --padding-end: 0 !important;
  --padding-top: 0 !important;
  --padding-bottom: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Override any router outlet padding */
ion-router-outlet {
  margin: 0 !important;
  padding: 0 !important;
}

/* Additional specific rule for notification settings page if needed */
ion-content.no-content-padding {
  --padding-start: 0 !important;
  --padding-end: 0 !important;
  --padding-top: 0 !important;
  --padding-bottom: 0 !important;
}

/*
  GLOBAL SCROLLING FIX:
  This ensures that any component rendered inside the main ion-content
  can expand vertically, allowing ion-content to handle scrolling.
*/
ion-router-outlet > *:not(ion-router-outlet) {
  display: block;
  height: auto;
  min-height: 100%;
}

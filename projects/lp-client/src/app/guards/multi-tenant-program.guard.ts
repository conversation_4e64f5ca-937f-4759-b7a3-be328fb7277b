import { Injectable } from '@angular/core';
import { CanActivate, Router, UrlTree } from '@angular/router';
import { Observable } from 'rxjs';
import { MultiTenantContextService } from '../services/multi-tenant-context.service';

@Injectable({
  providedIn: 'root'
})
export class MultiTenantProgramGuard implements CanActivate {

  constructor(
    private multiTenantContext: MultiTenantContextService,
    private router: Router
  ) {}

  canActivate(): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
    // If not multi-tenant, always allow access
    if (!this.multiTenantContext.isMultiTenant) {
      return true;
    }

    // If multi-tenant but no program selected, redirect to program selection
    if (!this.multiTenantContext.hasProgramSelected) {
      console.log('MultiTenantProgramGuard: No program selected, redirecting to program selection');
      return this.router.createUrlTree([this.multiTenantContext.getProgramSelectionRoute()]);
    }

    // Program is selected, allow access
    return true;
  }
}
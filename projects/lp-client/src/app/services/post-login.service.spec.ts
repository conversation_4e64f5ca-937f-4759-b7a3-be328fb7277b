import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { BehaviorSubject } from 'rxjs';
import { PostLoginService, PostLoginRoute } from './post-login.service';
import { KeyCloakService, LssConfig } from 'lp-client-api';
import { MultiTenantContextService } from './multi-tenant-context.service';

describe('PostLoginService', () => {
  let service: PostLoginService;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockKeycloak: jasmine.SpyObj<KeyCloakService>;
  let mockLssConfig: jasmine.SpyObj<LssConfig>;
  let mockMultiTenantContext: jasmine.SpyObj<MultiTenantContextService>;
  let authStatusSubject: BehaviorSubject<any>;

  beforeEach(() => {
    // Create spies
    mockRouter = jasmine.createSpyObj('Router', ['navigate', 'isActive']);
    mockKeycloak = jasmine.createSpyObj('KeyCloakService', ['getUserIdForApi']);
    mockLssConfig = jasmine.createSpyObj('LssConfig', [], {
      pages: {
        landing: {
          autoRedirectOnLogin: true
        }
      },
      workflow: {
        type: 'simple',
        multiTenant: false
      }
    });
    mockMultiTenantContext = jasmine.createSpyObj('MultiTenantContextService', [], {
      hasProgramSelected: false,
      isMultiTenant: false
    });

    // Setup auth status subject
    authStatusSubject = new BehaviorSubject(null);
    Object.defineProperty(mockKeycloak, 'authStatus', {
      get: () => authStatusSubject,
      configurable: true
    });

    Object.defineProperty(mockKeycloak, 'authSuccess', {
      get: () => true,
      configurable: true
    });

    TestBed.configureTestingModule({
      providers: [
        PostLoginService,
        { provide: Router, useValue: mockRouter },
        { provide: KeyCloakService, useValue: mockKeycloak },
        { provide: LssConfig, useValue: mockLssConfig },
        { provide: MultiTenantContextService, useValue: mockMultiTenantContext }
      ]
    });

    service = TestBed.inject(PostLoginService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Configuration Initialization', () => {
    it('should default to auto-redirect enabled when flag is not specified', () => {
      // Reset config to not have the flag
      mockLssConfig.pages = { landing: {} };
      service = TestBed.inject(PostLoginService);
      
      expect(service.autoRedirectEnabled).toBe(true);
    });

    it('should respect autoRedirectOnLogin flag when set to false', () => {
      mockLssConfig.pages = { landing: { autoRedirectOnLogin: false } };
      service = TestBed.inject(PostLoginService);
      
      expect(service.autoRedirectEnabled).toBe(false);
    });

    it('should respect autoRedirectOnLogin flag when set to true', () => {
      mockLssConfig.pages = { landing: { autoRedirectOnLogin: true } };
      service = TestBed.inject(PostLoginService);
      
      expect(service.autoRedirectEnabled).toBe(true);
    });
  });

  describe('getPostLoginRoute', () => {
    it('should return stay-on-landing when auto-redirect is disabled', () => {
      mockLssConfig.pages = { landing: { autoRedirectOnLogin: false } };
      service = TestBed.inject(PostLoginService);

      const route: PostLoginRoute = service.getPostLoginRoute();

      expect(route).toEqual({
        path: '/public/landing',
        reason: 'stay-on-landing'
      });
    });

    it('should return single-tenant-home for simple workflow', () => {
      mockLssConfig.workflow = { type: 'simple', multiTenant: false };
      
      const route: PostLoginRoute = service.getPostLoginRoute();

      expect(route).toEqual({
        path: '/app/home',
        reason: 'single-tenant-home'
      });
    });

    it('should return multi-tenant-program-selection when multi-tenant and no program selected', () => {
      mockLssConfig.workflow = { type: 'program-selection', multiTenant: true };
      Object.defineProperty(mockMultiTenantContext, 'hasProgramSelected', {
        get: () => false,
        configurable: true
      });

      const route: PostLoginRoute = service.getPostLoginRoute();

      expect(route).toEqual({
        path: '/secure/program-selection',
        reason: 'multi-tenant-program-selection'
      });
    });

    it('should return multi-tenant-home when multi-tenant and program selected', () => {
      mockLssConfig.workflow = { type: 'program-selection', multiTenant: true };
      Object.defineProperty(mockMultiTenantContext, 'hasProgramSelected', {
        get: () => true,
        configurable: true
      });

      const route: PostLoginRoute = service.getPostLoginRoute();

      expect(route).toEqual({
        path: '/app/home',
        reason: 'multi-tenant-home'
      });
    });
  });

  describe('handlePostLogin', () => {
    beforeEach(() => {
      spyOn(localStorage, 'removeItem');
      spyOn(localStorage, 'getItem').and.returnValue(null);
    });

    it('should clear authurllocked and not navigate when auto-redirect disabled', (done) => {
      mockLssConfig.pages = { landing: { autoRedirectOnLogin: false } };
      service = TestBed.inject(PostLoginService);

      service.handlePostLogin();

      setTimeout(() => {
        expect(localStorage.removeItem).toHaveBeenCalledWith('authurllocked');
        expect(mockRouter.navigate).not.toHaveBeenCalled();
        done();
      }, 10);
    });

    it('should navigate to home when auto-redirect enabled and on landing page', (done) => {
      mockRouter.isActive.and.returnValue(true);
      mockLssConfig.workflow = { type: 'simple', multiTenant: false };

      service.handlePostLogin();

      setTimeout(() => {
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/app/home']);
        done();
      }, 10);
    });

    it('should not navigate when not on landing page', (done) => {
      mockRouter.isActive.and.returnValue(false);

      service.handlePostLogin();

      setTimeout(() => {
        expect(mockRouter.navigate).not.toHaveBeenCalled();
        done();
      }, 10);
    });
  });

  describe('handleAuthenticatedPageLoad', () => {
    beforeEach(() => {
      spyOn(localStorage, 'getItem').and.returnValue(null);
      Object.defineProperty(mockKeycloak, 'authSuccess', {
        get: () => true,
        configurable: true
      });
    });

    it('should not navigate when auto-redirect disabled', () => {
      mockLssConfig.pages = { landing: { autoRedirectOnLogin: false } };
      service = TestBed.inject(PostLoginService);

      service.handleAuthenticatedPageLoad();

      expect(mockRouter.navigate).not.toHaveBeenCalled();
    });

    it('should navigate when on landing page and no locked URL', (done) => {
      mockRouter.isActive.and.returnValue(true);
      mockLssConfig.workflow = { type: 'simple', multiTenant: false };

      service.handleAuthenticatedPageLoad();

      setTimeout(() => {
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/app/home']);
        done();
      }, 150);
    });

    it('should not navigate when there is a locked URL', () => {
      mockRouter.isActive.and.returnValue(true);
      (localStorage.getItem as jasmine.Spy).and.returnValue('/some/locked/url');

      service.handleAuthenticatedPageLoad();

      expect(mockRouter.navigate).not.toHaveBeenCalled();
    });
  });

  describe('subscribeToAuthChanges', () => {
    it('should handle login events and trigger post-login logic', (done) => {
      mockRouter.isActive.and.returnValue(true);
      spyOn(service, 'handlePostLogin');

      const subscription = service.subscribeToAuthChanges().subscribe();

      // Simulate login event
      authStatusSubject.next({ eventName: 'login', eventData: {} });

      setTimeout(() => {
        expect(service.handlePostLogin).toHaveBeenCalled();
        subscription.unsubscribe();
        done();
      }, 10);
    });

    it('should ignore non-login events', (done) => {
      spyOn(service, 'handlePostLogin');

      const subscription = service.subscribeToAuthChanges().subscribe();

      // Simulate non-login event
      authStatusSubject.next({ eventName: 'refresh', eventData: {} });

      setTimeout(() => {
        expect(service.handlePostLogin).not.toHaveBeenCalled();
        subscription.unsubscribe();
        done();
      }, 10);
    });
  });

  describe('Integration Scenarios', () => {
    beforeEach(() => {
      spyOn(localStorage, 'removeItem');
      spyOn(localStorage, 'getItem').and.returnValue(null);
      mockRouter.isActive.and.returnValue(true);
    });

    it('should handle single-tenant login flow', (done) => {
      // Configure for single-tenant
      mockLssConfig.pages = { landing: { autoRedirectOnLogin: true } };
      mockLssConfig.workflow = { type: 'simple', multiTenant: false };
      service = TestBed.inject(PostLoginService);

      // Simulate login
      const subscription = service.subscribeToAuthChanges().subscribe();
      authStatusSubject.next({ eventName: 'login', eventData: {} });

      setTimeout(() => {
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/app/home']);
        subscription.unsubscribe();
        done();
      }, 10);
    });

    it('should handle multi-tenant login flow without program selection', (done) => {
      // Configure for multi-tenant
      mockLssConfig.pages = { landing: { autoRedirectOnLogin: true } };
      mockLssConfig.workflow = { type: 'program-selection', multiTenant: true };
      Object.defineProperty(mockMultiTenantContext, 'hasProgramSelected', {
        get: () => false,
        configurable: true
      });
      service = TestBed.inject(PostLoginService);

      // Simulate login
      const subscription = service.subscribeToAuthChanges().subscribe();
      authStatusSubject.next({ eventName: 'login', eventData: {} });

      setTimeout(() => {
        expect(mockRouter.navigate).toHaveBeenCalledWith(['/secure/program-selection']);
        subscription.unsubscribe();
        done();
      }, 10);
    });

    it('should stay on landing when auto-redirect disabled', (done) => {
      // Configure to stay on landing
      mockLssConfig.pages = { landing: { autoRedirectOnLogin: false } };
      mockLssConfig.workflow = { type: 'simple', multiTenant: false };
      service = TestBed.inject(PostLoginService);

      // Simulate login
      const subscription = service.subscribeToAuthChanges().subscribe();
      authStatusSubject.next({ eventName: 'login', eventData: {} });

      setTimeout(() => {
        expect(localStorage.removeItem).toHaveBeenCalledWith('authurllocked');
        expect(mockRouter.navigate).not.toHaveBeenCalled();
        subscription.unsubscribe();
        done();
      }, 10);
    });
  });
});

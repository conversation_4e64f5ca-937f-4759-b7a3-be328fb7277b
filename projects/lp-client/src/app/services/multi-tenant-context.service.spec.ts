import { MultiTenantContextService } from './multi-tenant-context.service';
import { LssConfig } from 'lp-client-api';

describe('MultiTenantContextService', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  const createService = (
    runtimeConfig: Partial<LssConfig>,
    environmentConfig?: any
  ): MultiTenantContextService => {
    const lssConfig = new LssConfig();
    Object.assign(lssConfig, runtimeConfig);
    return new MultiTenantContextService(lssConfig, environmentConfig);
  };

  it('defaults to environment navigation when runtime config lacks overrides', () => {
    const environment = {
      lssConfig: {
        workflow: {
          multiTenant: true,
          navigation: {
            programDependentRoutes: ['Profile'],
            alwaysVisibleRoutes: ['Programs'],
          },
        },
      },
    };

    const service = createService(
      {
        workflow: {
          multiTenant: true,
        } as any,
      },
      environment
    );

    expect(service.isNavigationItemVisible('Programs')).toBeTrue();
    expect(service.isNavigationItemVisible('Profile')).toBeFalse();
  });

  it('prefers runtime navigation arrays when provided', () => {
    const environment = {
      lssConfig: {
        workflow: {
          multiTenant: true,
          navigation: {
            programDependentRoutes: ['Profile'],
            alwaysVisibleRoutes: ['Programs'],
          },
        },
      },
    };

    const service = createService(
      {
        workflow: {
          multiTenant: true,
          navigation: {
            alwaysVisibleRoutes: ['Home'],
          },
        } as any,
      },
      environment
    );

    expect(service.isNavigationItemVisible('Home')).toBeTrue();
    expect(service.isNavigationItemVisible('Profile')).toBeFalse();
  });
});
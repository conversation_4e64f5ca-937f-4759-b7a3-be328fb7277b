import { Inject, Injectable, Optional } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { LssConfig } from 'lp-client-api';

export interface ProgramContext {
  programId: string;
  programName: string;
  mpacc: string;
  iconUrl?: string;
  enteredAt: string;
  // Extended program branding information
  programLogo?: string;      // Main program logo URL
  programIcon?: string;      // Program icon URL
  programHorizontal?: string; // Horizontal image URL
  programVertical?: string;   // Vertical image URL
}

@Injectable({
  providedIn: 'root'
})
export class MultiTenantContextService {
  private readonly STORAGE_KEY = 'current_program_context';
  
  // BehaviorSubject to track current program context
  private programContextSubject = new BehaviorSubject<ProgramContext | null>(null);
  
  constructor(
    private lssConfig: LssConfig,
    @Optional() @Inject('environment') private environment?: any
  ) {
    // Load stored context on service initialization
    this.loadStoredContext();
  }

  /**
   * Check if the current app is multi-tenant
   */
  get isMultiTenant(): boolean {
    const runtimeSetting = this.lssConfig.workflow?.multiTenant;
    if (runtimeSetting !== undefined) {
      return runtimeSetting === true;
    }

    const environmentSetting = this.environment?.lssConfig?.workflow?.multiTenant;
    return environmentSetting === true;
  }

  /**
   * Get observable for program context changes
   */
  get programContext$(): Observable<ProgramContext | null> {
    return this.programContextSubject.asObservable();
  }

  /**
   * Get current program context (synchronous)
   */
  get currentProgramContext(): ProgramContext | null {
    return this.programContextSubject.value;
  }

  /**
   * Check if user has selected a program (only relevant for multi-tenant)
   */
  get hasProgramSelected(): boolean {
    if (!this.isMultiTenant) {
      return true; // Single-tenant always has "program selected"
    }
    return this.currentProgramContext !== null;
  }

  /**
   * Check if a navigation item should be visible based on multi-tenant rules
   * Uses dynamic configuration from environment
   */
  isNavigationItemVisible(routeId: string): boolean {
    console.log(`🔍 Checking visibility for route: ${routeId}`);
    console.log('  - Is multi-tenant:', this.isMultiTenant);
    
    // If not multi-tenant, all items are always visible
    if (!this.isMultiTenant) {
      console.log('  ✅ Single-tenant mode: showing all items');
      return true;
    }

  // Get navigation configuration from runtime config (with environment fallback)
    const navigationConfig = this.resolveNavigationConfig();
    console.log('  - Navigation config:', navigationConfig);
    
    if (!navigationConfig) {
      // Fallback to default behavior if no configuration
      console.log('  ✅ No navigation config: showing all items');
      return true;
    }

    console.log('  - Has program selected:', this.hasProgramSelected);
    console.log('  - Current program context:', this.currentProgramContext);
    console.log('  - Always visible routes:', navigationConfig.alwaysVisibleRoutes);
    console.log('  - Program dependent routes:', navigationConfig.programDependentRoutes);

    // Check if this route is always visible
    if (navigationConfig.alwaysVisibleRoutes?.includes(routeId)) {
      console.log('  ✅ Route is always visible');
      return true;
    }

    // Check if this route requires program selection
    if (navigationConfig.programDependentRoutes?.includes(routeId)) {
      const shouldShow = this.hasProgramSelected;
      console.log(`  ${shouldShow ? '✅' : '❌'} Route is program dependent, has program: ${shouldShow}`);
      return shouldShow;
    }

    // By default, routes not specified are visible
    console.log('  ✅ Route not in any config list: showing by default');
    return true;
  }

  /**
   * Set the current program context
   */
  setProgramContext(context: ProgramContext): void {
    this.programContextSubject.next(context);
    this.storeContext(context);
    console.log('Program context updated:', context);
  }

  /**
   * Clear the current program context (e.g., on logout)
   */
  clearProgramContext(): void {
    this.programContextSubject.next(null);
    this.removeStoredContext();
    console.log('Program context cleared');
  }

  /**
   * Get display text for current program (for UI)
   */
  getCurrentProgramDisplayText(): string {
    const context = this.currentProgramContext;
    if (!context) {
      return this.isMultiTenant ? 'Select Program' : '';
    }
    return context.programName;
  }

  /**
   * Get icon for current program (for UI)
   */
  getCurrentProgramIcon(): string {
    const context = this.currentProgramContext;
    if (!context) {
      return this.isMultiTenant ? 'list-outline' : '';
    }
    return context.iconUrl || 'apps-outline';
  }

  /**
   * Load stored context from localStorage
   */
  private loadStoredContext(): void {
    try {
      const storedData = localStorage.getItem(this.STORAGE_KEY);
      if (storedData) {
        const context = JSON.parse(storedData) as ProgramContext;
        // Validate the stored context has required fields
        if (context.programId && context.mpacc) {
          this.programContextSubject.next(context);
          console.log('Loaded stored program context:', context);
        } else {
          console.warn('Stored program context is invalid, removing it');
          this.removeStoredContext();
        }
      }
    } catch (error) {
      console.error('Error loading stored program context:', error);
      this.removeStoredContext();
    }
  }

  /**
   * Store context to localStorage
   */
  private storeContext(context: ProgramContext): void {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(context));
    } catch (error) {
      console.error('Error storing program context:', error);
    }
  }

  /**
   * Remove stored context from localStorage
   */
  private removeStoredContext(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('Error removing stored program context:', error);
    }
  }

  private resolveNavigationConfig():
    | {
        programDependentRoutes?: string[];
        alwaysVisibleRoutes?: string[];
        [key: string]: any;
      }
    | undefined {
    const runtimeNavigation = this.lssConfig.workflow?.navigation;
    const environmentNavigation = this.environment?.lssConfig?.workflow?.navigation;

    if (!runtimeNavigation && !environmentNavigation) {
      return undefined;
    }

    if (!runtimeNavigation) {
      return environmentNavigation;
    }

    if (!environmentNavigation) {
      return runtimeNavigation;
    }

    return {
      ...environmentNavigation,
      ...runtimeNavigation,
      programDependentRoutes:
        runtimeNavigation.programDependentRoutes ?? environmentNavigation.programDependentRoutes,
      alwaysVisibleRoutes:
        runtimeNavigation.alwaysVisibleRoutes ?? environmentNavigation.alwaysVisibleRoutes,
    };
  }

  /**
   * Check if user should be redirected to program selection
   * (multi-tenant apps without program context)
   */
  shouldRedirectToProgramSelection(): boolean {
    return this.isMultiTenant && !this.hasProgramSelected;
  }

  /**
   * Get the route for program selection
   */
  getProgramSelectionRoute(): string {
    return '/secure/program-management';
  }

  /**
   * Force clear program context for debugging
   */
  debugClearProgramContext(): void {
    console.log('🧹 DEBUG: Force clearing program context');
    this.clearProgramContext();
  }
}
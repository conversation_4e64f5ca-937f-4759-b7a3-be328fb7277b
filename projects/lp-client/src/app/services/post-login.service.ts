import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { filter, map, take } from 'rxjs/operators';
import { KeyCloakService, LssConfig } from 'lp-client-api';
import { MultiTenantContextService } from './multi-tenant-context.service';

export interface PostLoginRoute {
  path: string;
  reason: 'single-tenant-home' | 'multi-tenant-program-selection' | 'multi-tenant-home' | 'stay-on-landing';
}

@Injectable({
  providedIn: 'root'
})
export class PostLoginService {
  private _autoRedirectEnabled$ = new BehaviorSubject<boolean>(true);

  constructor(
    private router: Router,
    private keycloak: KeyCloakService,
    private lssConfig: LssConfig,
    private multiTenantContext: MultiTenantContextService
  ) {
    this.initializeConfig();
  }

  private initializeConfig(): void {
    // Read the autoRedirectOnLogin flag from config
    const autoRedirect = this.lssConfig?.pages?.landing?.autoRedirectOnLogin;
    // Default to true if not specified
    this._autoRedirectEnabled$.next(autoRedirect !== false);
    console.log('PostLoginService - autoRedirectOnLogin:', autoRedirect !== false);
  }

  /**
   * Get the current auto-redirect setting
   */
  get autoRedirectEnabled(): boolean {
    return this._autoRedirectEnabled$.value;
  }

  /**
   * Observable for auto-redirect setting changes
   */
  get autoRedirectEnabled$(): Observable<boolean> {
    return this._autoRedirectEnabled$.asObservable();
  }

  /**
   * Determine the appropriate post-login route based on tenant configuration
   */
  getPostLoginRoute(): PostLoginRoute {
    if (!this.autoRedirectEnabled) {
      return {
        path: '/public/landing',
        reason: 'stay-on-landing'
      };
    }

    const workflowConfig = this.lssConfig?.workflow;
    const isMultiTenant = workflowConfig?.multiTenant || false;
    const workflowType = workflowConfig?.type;

    console.log('PostLoginService - Determining route:', {
      isMultiTenant,
      workflowType,
      autoRedirectEnabled: this.autoRedirectEnabled,
      hasProgramSelected: this.multiTenantContext.hasProgramSelected
    });

    if (isMultiTenant && workflowType === 'program-selection') {
      // Multi-tenant mode
      if (!this.multiTenantContext.hasProgramSelected) {
        return {
          path: '/secure/program-selection',
          reason: 'multi-tenant-program-selection'
        };
      } else {
        return {
          path: '/app/home',
          reason: 'multi-tenant-home'
        };
      }
    } else {
      // Single-tenant mode - go directly to home
      return {
        path: '/app/home',
        reason: 'single-tenant-home'
      };
    }
  }

  /**
   * Handle post-login navigation with race condition protection
   */
  handlePostLogin(): void {
    if (!this.autoRedirectEnabled) {
      console.log('PostLoginService - Auto-redirect disabled, staying on landing');
      // Clear any stored redirect URL to prevent KeyCloak from navigating away
      localStorage.removeItem('authurllocked');
      return;
    }

    // Use setTimeout to avoid race conditions with router state
    setTimeout(() => {
      if (this.router.isActive('/public/landing', false)) {
        const route = this.getPostLoginRoute();
        console.log('PostLoginService - Navigating to:', route);
        
        if (route.reason !== 'stay-on-landing') {
          this.router.navigate([route.path]);
        }
      } else {
        console.log('PostLoginService - User not on landing page, skipping redirect');
      }
    }, 0);
  }

  /**
   * Handle the case when user is already authenticated on page load
   */
  handleAuthenticatedPageLoad(): void {
    if (!this.autoRedirectEnabled) {
      console.log('PostLoginService - Auto-redirect disabled for authenticated user');
      return;
    }

    // Check if we're on the landing page and should redirect
    if (this.router.isActive('/public/landing', false) && this.keycloak.authSuccess) {
      const lockedUrl = localStorage.getItem('authurllocked');
      
      if (!lockedUrl) {
        // No pending redirect, use our routing logic
        setTimeout(() => {
          const route = this.getPostLoginRoute();
          console.log('PostLoginService - Authenticated page load, navigating to:', route);
          
          if (route.reason !== 'stay-on-landing') {
            this.router.navigate([route.path]);
          }
        }, 100); // Slightly longer delay for page load scenario
      }
      // If there's a locked URL, let KeyCloak handle it
    }
  }

  /**
   * Subscribe to auth status changes and handle post-login routing
   */
  subscribeToAuthChanges(): Observable<void> {
    return this.keycloak.authStatus.pipe(
      filter(event => event?.eventName === 'login'),
      map(() => {
        console.log('PostLoginService - Login event detected');
        this.handlePostLogin();
        return void 0;
      })
    );
  }
}

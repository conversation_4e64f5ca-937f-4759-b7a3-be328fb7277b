import { KeyCloakService, LssConfig, MemberProfile } from 'lp-client-api';

/**
 * Resolve the productId to use in single-tenant mode.
 * Applies multiple fallbacks between runtime config, environment defaults and a final hardcoded fallback.
 */
export function resolveSingleTenantProductId(
  lssConfig: LssConfig,
  environmentConfig?: any,
  fallbackProductId = 'rmic'
): string | null {
  const candidates: Array<string | number | undefined | null> = [
    lssConfig.workflow?.productId,
    environmentConfig?.lssConfig?.workflow?.productId,
    lssConfig.productId,
    environmentConfig?.lssConfig?.productId,
    fallbackProductId
  ];

  for (const candidate of candidates) {
    const value = sanitiseContextValue(candidate);
    if (value) {
      return value;
    }
  }

  return null;
}

/**
 * Resolve the MPACC identifier to use in single-tenant mode.
 * Draws from member profile data with Keycloak fallbacks for legacy identifiers.
 */
export function resolveSingleTenantMpacc(
  profileSource: MemberProfile | null | undefined,
  keycloak?: KeyCloakService | null
): string | null {
  const candidates: Array<string | number | undefined | null> = [
    profileSource?.membershipNumber,
    profileSource?.newMembershipNumber,
    profileSource?.mpacc,
    profileSource?.uniqueId,
    keycloak?.lpUniueReference,
    keycloak?.userProfile?.LP_MPACC
  ];

  for (const candidate of candidates) {
    const value = sanitiseContextValue(candidate);
    if (value) {
      return value;
    }
  }

  return null;
}

function sanitiseContextValue(value: string | number | undefined | null): string | null {
  if (value === undefined || value === null) {
    return null;
  }

  const normalised = String(value).trim();
  if (!normalised || normalised.toLowerCase() === 'undefined' || normalised.toLowerCase() === 'null' || normalised === 'Not Set') {
    return null;
  }

  return normalised;
}

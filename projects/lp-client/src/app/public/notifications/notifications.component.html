<!-- Debug Information -->
<div style="padding: 20px; background: #f0f0f0; margin: 10px; color: #333; border: 2px solid #007acc; border-radius: 5px;">
  <h3 style="color: #007acc; margin-top: 0;">Debug Info:</h3>
  <p><strong>Notifications count:</strong> {{ notifications.length || 0 }}</p>
  <p><strong>Raw notifications count:</strong> {{ rawNotifications.length || 0 }}</p>
  <div style="background: #fff; padding: 10px; border-radius: 3px; margin-top: 10px;">
    <strong>Notifications Data:</strong>
    <pre style="color: #333; font-size: 12px; margin: 5px 0; white-space: pre-wrap;">{{ notifications | json }}</pre>
  </div>
</div>

<!-- Direct notification display for debugging -->
<ion-content>
  <div style="padding: 20px;">
    <h2 style="color: white; text-align: center;">Direct Notification Display</h2>
    
    <div *ngIf="!notifications || notifications.length === 0" style="color: white; text-align: center; padding: 40px;">
      <h3>No Notifications Found</h3>
      <p>Check the debug info above for data details</p>
    </div>
    
    <div *ngFor="let notification of notifications; let i = index" style="background: white; margin: 10px 0; padding: 15px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
      <h4 style="margin: 0 0 8px 0; color: #333;">{{ notification.shortDescription }}</h4>
      <p style="margin: 0 0 10px 0; color: #666; font-size: 14px;">{{ notification.message }}</p>
      <button 
        (click)="notiRead(notification.noteSeq)"
        style="background: #007acc; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
        Mark as Read (ID: {{ notification.noteSeq }})
      </button>
    </div>
  </div>
</ion-content>

<!-- Keep the original component commented for comparison -->
<!-- <lib-notification-list
  [items]="notifications"
  (markRead)="notiRead($event)"
></lib-notification-list> -->

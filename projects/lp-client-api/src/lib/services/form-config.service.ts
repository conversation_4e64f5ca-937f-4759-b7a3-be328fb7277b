import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class FormConfigService {
  getSignupFormConfig(productId: string, env: string): Observable<any> {
    // Mock dynamic form configuration
    const cfg = {
      productId,
      env,
      version: 1,
      form: {
        title: 'Sign Up',
        sections: [
          {
            id: 'personal',
            title: 'Personal Information',
            icon: 'person-outline',
            order: 20,
            visible: true,
            fields: [
              { key: 'givenNames', type: 'text', label: { default: 'First Name' }, required: true },
              { key: 'surname', type: 'text', label: { default: 'Last Name' }, required: true },
              { key: 'birthDate', type: 'date', label: { default: 'Birth Date' }, required: false },
              {
                key: 'gender', type: 'select', label: { default: 'Gender' }, required: false,
                options: { source: 'STATIC', values: [ { value: 'M', label: 'Male' }, { value: 'F', label: 'Female' } ] }
              }
            ]
          },
          {
            id: 'identification',
            title: 'Identification',
            icon: 'id-card-outline',
            order: 30,
            visible: true,
            fields: [
              { key: 'idType', type: 'select', label: { default: 'ID Type' }, required: true,
                options: { source: 'STATIC', values: [ { value: 'nationalId', label: 'South African ID' }, { value: 'passport', label: 'Passport' } ] }
              },
              { key: 'nationalIdNum', type: 'text', label: { default: 'South African ID Number' }, required: true,
                when: [ { field: 'idType', equals: 'nationalId' } ], validation: { pattern: '^[0-9]{13}$' }
              },
              { key: 'passortNum', type: 'text', label: { default: 'Passport Number' }, required: true,
                when: [ { field: 'idType', equals: 'passport' } ]
              }
            ]
          },
          {
            id: 'contact',
            title: 'Contact Information',
            icon: 'call-outline',
            order: 40,
            visible: true,
            fields: [
              { key: 'phoneNumber', type: 'text', label: { default: 'Mobile Number' }, required: true, placeholder: 'e.g. 0821234567' },
              { key: 'emailAddress', type: 'text', label: { default: 'Email Address' }, required: true }
            ]
          },
          {
            id: 'preferences',
            title: 'Preferences',
            icon: 'options-outline',
            order: 50,
            visible: true,
            fields: [
              { key: 'favoriteStore', type: 'store-selector', label: { default: 'Favorite Store' }, required: true }
            ]
          },
          {
            id: 'address',
            title: 'Address Information',
            icon: 'location-outline',
            order: 60,
            visible: true,
            mode: 'FREEFORM',
            fields: [
              { key: 'line1', type: 'text', label: { default: 'Address Line 1' }, required: true },
              { key: 'line2', type: 'text', label: { default: 'Address Line 2' }, required: false },
              { key: 'city', type: 'text', label: { default: 'City' }, required: true },
              { key: 'province', type: 'text', label: { default: 'Province' }, required: true },
              { key: 'postalCode', type: 'text', label: { default: 'Postal Code' }, required: true }
            ]
          },
          {
            id: 'security',
            title: 'Security',
            icon: 'shield-outline',
            order: 70,
            visible: true,
            fields: [
              { key: 'pin', type: 'text', label: { default: 'Pin' }, required: false },
              { key: 'password', type: 'password', label: { default: 'Password' }, required: true },
              { key: 'passwordConfirm', type: 'password', label: { default: 'Confirm Password' }, required: true }
            ]
          },
          {
            id: 'terms',
            title: 'Terms',
            icon: 'document-outline',
            order: 80,
            visible: true,
            fields: [ { key: 'terms', type: 'checkbox', label: { default: 'I agree to the Terms and Conditions' }, required: true } ]
          }
        ]
      }
    };
    return of(cfg);
  }
}

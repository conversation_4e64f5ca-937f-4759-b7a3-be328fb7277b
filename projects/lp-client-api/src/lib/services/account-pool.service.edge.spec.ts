import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { AccountPoolService } from './account-pool.service';
import { LssConfig } from '../types/lss-config';
import { SystemService } from './system.service';
import { KeyCloakService } from './key-cloak.service';

describe('AccountPoolService (Flag toggle behavior)', () => {
  let service: AccountPoolService;
  let httpMock: HttpTestingController;

  const baseConfig: LssConfig = Object.assign(new LssConfig(), {
    apiBaseUrl: 'https://example.test/',
    apiId: 'TEST_API_ID',
    apiIdKeyStart: 'START',
    apiIdKeyEnd: 'END'
  });

  const systemStub = {
    getUniqueId: jasmine.createSpy('getUniqueId').and.returnValue('UID-123')
  } as unknown as SystemService;

  afterEach(() => {
    httpMock.verify();
  });

  it('should use legacy POST when mutateEnabled is false', () => {
    const configWithMutateFalse = { ...baseConfig, workflow: { features: { pools: { extsecure: { mutateEnabled: false } } } } };
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AccountPoolService,
        { provide: LssConfig, useValue: configWithMutateFalse },
        { provide: SystemService, useValue: systemStub },
        { provide: KeyCloakService, useValue: { getUserIdForApi: () => 'U123' } }
      ]
    });
    service = TestBed.inject(AccountPoolService);
    httpMock = TestBed.inject(HttpTestingController);

    service.joinPool(123, '1000101', 'REQS', '1000101').subscribe();

    const req = httpMock.expectOne(r => r.method === 'POST' && r.url.includes('/member/accountpool/join'));
    expect(req.request.headers.get('Content-Type')).toBe('application/x-www-form-urlencoded');
    req.flush({});
  });

  it('should use legacy POST when mutateEnabled is undefined', () => {
    const configNoFlag = { ...baseConfig };
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AccountPoolService,
        { provide: LssConfig, useValue: configNoFlag },
        { provide: SystemService, useValue: systemStub },
        { provide: KeyCloakService, useValue: { getUserIdForApi: () => 'U123' } }
      ]
    });
    service = TestBed.inject(AccountPoolService);
    httpMock = TestBed.inject(HttpTestingController);

    service.joinPool(123, '1000101', 'REQS', '1000101').subscribe();

    const req = httpMock.expectOne(r => r.method === 'POST' && r.url.includes('/member/accountpool/join'));
    expect(req.request.headers.get('Content-Type')).toBe('application/x-www-form-urlencoded');
    req.flush({});
  });

  it('should fall back to legacy POST when productId is missing', () => {
    const configMutateTrue = { ...baseConfig, workflow: { features: { pools: { extsecure: { mutateEnabled: true } } } } };
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AccountPoolService,
        { provide: LssConfig, useValue: configMutateTrue },
        { provide: SystemService, useValue: systemStub },
        { provide: KeyCloakService, useValue: { getUserIdForApi: () => 'U123' } }
      ]
    });
    service = TestBed.inject(AccountPoolService);
    httpMock = TestBed.inject(HttpTestingController);

    service.joinPool(123, '1000101', 'REQS', '1000101').subscribe();

    const req = httpMock.expectOne(r => r.method === 'POST' && r.url.includes('/member/accountpool/join'));
    expect(req.request.headers.get('Content-Type')).toBe('application/x-www-form-urlencoded');
    req.flush({});
  });
});

describe('AccountPoolService (Error handling)', () => {
  let service: AccountPoolService;
  let httpMock: HttpTestingController;

  const configMutateTrue: LssConfig = Object.assign(new LssConfig(), {
    apiBaseUrl: 'https://example.test/',
    apiId: 'TEST_API_ID',
    apiIdKeyStart: 'START',
    apiIdKeyEnd: 'END',
    workflow: { productId: 'prod-1', features: { pools: { extsecure: { mutateEnabled: true } } } }
  });

  const systemStub = {
    getUniqueId: jasmine.createSpy('getUniqueId').and.returnValue('UID-123')
  } as unknown as SystemService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AccountPoolService,
        { provide: LssConfig, useValue: configMutateTrue },
        { provide: SystemService, useValue: systemStub },
        { provide: KeyCloakService, useValue: { getUserIdForApi: () => 'U123' } }
      ]
    });
    service = TestBed.inject(AccountPoolService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should handle 404 errors for joinPool', () => {
    let errorReceived: any = null;
    service.joinPool(999, '1000404', 'REQS', '1000101').subscribe({
      next: () => {},
      error: (err) => errorReceived = err
    });

    const req = httpMock.expectOne(r => r.method === 'PUT');
    req.flush({ message: 'Pool not found' }, { status: 404, statusText: 'Not Found' });

    expect(errorReceived).toBeTruthy();
    expect(errorReceived.status).toBe(404);
  });

  it('should handle 409 conflicts for processPoolInvite', () => {
    let errorReceived: any = null;
    service.processPoolInvite(123, '1000409', 'ACCP', '1000101').subscribe({
      next: () => {},
      error: (err) => errorReceived = err
    });

    const req = httpMock.expectOne(r => r.method === 'PUT');
    req.flush({ message: 'Member already in another pool' }, { status: 409, statusText: 'Conflict' });

    expect(errorReceived).toBeTruthy();
    expect(errorReceived.status).toBe(409);
  });

  it('should handle 204 response for declineInvite', () => {
    let success = false;
    service.declineInvite(123, '1000204', '1000101').subscribe({
      next: () => success = true,
      error: () => {}
    });

    const req = httpMock.expectOne(r => r.method === 'DELETE');
    req.flush(null, { status: 204, statusText: 'No Content' });

    expect(success).toBe(true);
  });
});

describe('AccountPoolService (Tenant context resolution)', () => {
  let service: AccountPoolService;
  let httpMock: HttpTestingController;

  const baseConfig: LssConfig = Object.assign(new LssConfig(), {
    apiBaseUrl: 'https://example.test/',
    apiId: 'TEST_API_ID',
    apiIdKeyStart: 'START',
    apiIdKeyEnd: 'END'
  });

  const systemStub = {
    getUniqueId: jasmine.createSpy('getUniqueId').and.returnValue('UID-123')
  } as unknown as SystemService;

  afterEach(() => {
    httpMock.verify();
  });

  it('should use workflow.productId for single-tenant apps', () => {
    const singleTenantConfig = { 
      ...baseConfig, 
      workflow: { 
        productId: 'single-app-prod',
        features: { pools: { extsecure: { mutateEnabled: true } } }
      } 
    };
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AccountPoolService,
        { provide: LssConfig, useValue: singleTenantConfig },
        { provide: SystemService, useValue: systemStub },
        { provide: KeyCloakService, useValue: { getUserIdForApi: () => 'U123' } }
      ]
    });
    service = TestBed.inject(AccountPoolService);
    httpMock = TestBed.inject(HttpTestingController);

    service.joinPool(123, '1000101', 'REQS', '1000101').subscribe();

    const req = httpMock.expectOne(r => 
      r.method === 'PUT' && 
      r.url.includes('/products/single-app-prod/')
    );
    req.flush({});
  });

  it('should use KeyCloak userId when available', () => {
    const configWithProductId = { 
      ...baseConfig, 
      workflow: { 
        productId: 'prod-kc',
        features: { pools: { extsecure: { mutateEnabled: true } } }
      } 
    };
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AccountPoolService,
        { provide: LssConfig, useValue: configWithProductId },
        { provide: SystemService, useValue: systemStub },
        { provide: KeyCloakService, useValue: { getUserIdForApi: () => 'KC_USER_456' } }
      ]
    });
    service = TestBed.inject(AccountPoolService);
    httpMock = TestBed.inject(HttpTestingController);

    service.joinPool(123, '1000101', 'REQS', '1000101').subscribe();

    const req = httpMock.expectOne(r => 
      r.method === 'PUT' && 
      r.url.includes('/member/KC_USER_456/')
    );
    req.flush({});
  });

  it('should fall back to auditUser when KeyCloak getUserIdForApi is unavailable', () => {
    const configWithProductId = { 
      ...baseConfig, 
      workflow: { 
        productId: 'prod-fallback',
        features: { pools: { extsecure: { mutateEnabled: true } } }
      } 
    };
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AccountPoolService,
        { provide: LssConfig, useValue: configWithProductId },
        { provide: SystemService, useValue: systemStub },
        { provide: KeyCloakService, useValue: {} } // No getUserIdForApi method
      ]
    });
    service = TestBed.inject(AccountPoolService);
    httpMock = TestBed.inject(HttpTestingController);

    service.joinPool(123, '1000101', 'REQS', '1000101').subscribe();

    const req = httpMock.expectOne(r => 
      r.method === 'PUT' && 
      r.url.includes('/member/1000101/')
    );
    req.flush({});
  });
});

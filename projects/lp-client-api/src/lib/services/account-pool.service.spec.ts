import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { AccountPoolService } from './account-pool.service';
import { LssConfig } from '../types/lss-config';
import { SystemService } from './system.service';
import { KeyCloakService } from './key-cloak.service';

describe('AccountPoolService (read endpoints)', () => {
  let service: AccountPoolService;
  let httpMock: HttpTestingController;

  const lssConfig: LssConfig = Object.assign(new LssConfig(), {
    apiBaseUrl: 'https://example.test/',
    apiId: 'TEST_API_ID',
    apiIdKeyStart: 'START',
    apiIdKeyEnd: 'END',
    workflow: {
      features: {
        pools: {
          extsecure: { readEnabled: true }
        }
      }
    }
  });

  const systemStub = {
    getUniqueId: jasmine.createSpy('getUniqueId').and.returnValue('UID-123')
  } as unknown as SystemService;

  const kcStub = { getUserIdForApi: jasmine.createSpy('getUserIdForApi').and.returnValue('U123') } as unknown as KeyCloakService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AccountPoolService,
        { provide: LssConfig, useValue: lssConfig },
        { provide: SystemService, useValue: systemStub },
        { provide: KeyCloakService, useValue: kcStub }
      ]
    });
    service = TestBed.inject(AccountPoolService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should GET list, details and members using long-form path for findPool', () => {
    service.findPool('1000101', { userId: 'U123', productId: 'prod-1' }).subscribe();

    // 1) list pools
    const listReq = httpMock.expectOne(r => r.method === 'GET' && r.url === 'https://example.test/extsecure/member/U123/products/prod-1/member/1000101/accountpool/');
    listReq.flush([{ poolId: ********* }]);

    // 2) pool details
    const detailsReq = httpMock.expectOne(r => r.method === 'GET' && r.url === 'https://example.test/extsecure/member/U123/products/prod-1/member/1000101/accountpool/*********');
    detailsReq.flush({
      poolId: *********,
      poolMembershipNr: '1000145',
      poolMpacc: '1000145',
      poolAlias: null,
      poolSplit: 3272126,
      poolSplitDescr: 'Pool 50% - Member 50%',
      status: 'STAA',
      statusDescr: 'Active',
      beginDate: '2025-08-29',
      endDate: null,
      poolName: "Emil's Pool",
      language: 'en',
      email: '<EMAIL>',
      countryCode: '+27',
      telephone: '*********',
      balance: 0,
      role: 'ADMN',
      linkStatus: 'STAA',
      linkStatusDescr: 'Active',
      invitationStatus: null
    });

    // 3) members
    const membersReq = httpMock.expectOne(r => r.method === 'GET' && r.url === 'https://example.test/extsecure/member/U123/products/prod-1/member/1000101/accountpool/*********/members');
    membersReq.flush([
      {
        mpacc: '1000101',
        alias: null,
        membershipNr: '1000101',
        role: 'ADMN',
        privacy: 'Y',
        linkStatus: 'STAA',
        linkStatusDescr: 'Active',
        balance: 0,
        allowActivity: 'Y',
        allowAward: 'Y',
        invitationStatus: null,
        name: 'Peet Stander'
      }
    ]);
  });

  it('should GET pool details by pool mpacc using long-form path', () => {
    service.findPoolByMpacc('1000145', { userId: 'U123', productId: 'prod-1' }).subscribe();

    const detailsReq = httpMock.expectOne(r => r.method === 'GET' && r.url === 'https://example.test/extsecure/member/U123/products/prod-1/member/accountpool/1000145');
    detailsReq.flush({
      poolId: *********,
      poolMembershipNr: '1000145',
      poolMpacc: '1000145',
      poolAlias: null,
      poolSplit: 3272126,
      poolSplitDescr: 'Pool 50% - Member 50%',
      status: 'STAA',
      statusDescr: 'Active',
      beginDate: '2025-08-29',
      endDate: null,
      poolName: "Emil's Pool",
      language: 'en',
      email: '<EMAIL>',
      countryCode: '+27',
      telephone: '*********',
      balance: 0
    });
  });
});


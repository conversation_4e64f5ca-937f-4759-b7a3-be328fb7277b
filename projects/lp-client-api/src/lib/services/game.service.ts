import { Injectable, Injector, Inject } from '@angular/core';
import { Observable, throwError, of } from 'rxjs';
import { map, catchError, switchMap } from 'rxjs/operators';
import { AbstractService } from './abstract.service';
import { Router } from '@angular/router';
import { MemberService } from './member.service';
import {
  Game,
  SpinWheelConfig,
  GeoSpaceConfig,
  MemoryConfig,
  Game2048Config,
  SnakeConfig,
  MinesweeperConfig,
  SudokuConfig,
  TetrisConfig,
  GameProgress,
  BaseGameProgress,
  GameInstance,
} from '../types/game';
import { HttpParams, HttpErrorResponse } from '@angular/common/http';

// Interfaces
interface CanPlayResponse {
  canPlay: boolean;
  id: number;
  href: string;
}

interface AccountGameConfig {
  gameConfig: number;
  gameInstance: number | null;
  gameEvent: GameEvent | null;
}

interface GameEvent {
  id: number;
  level: number;
  score: number;
  duration: number;
  state: string;
  payload: string;
}

interface GameProgressResponse extends BaseGameProgress {}

// Add a type for converting between API and component types
type GameProgressType<T extends BaseGameProgress> = Omit<T, 'lastPlayed'> & {
  lastPlayed: Date;
};

const environmentGames =  {
  globalConfig: {
    version: '1.0.0',
    defaultLanguage: 'en',
    saveUserProgress: true,
    enableSound: true,
    enableMusic: true,
    themes: {
      defaultBackgroundImage: 'assets/images/default-bg.jpg',
    },
  },
  categories: {
    business: {
      name: 'Business Games',
      backgroundImage: 'assets/images/business-bg.jpg',
      games: {
        // spinthewheel: {
        //   name: 'Spin a Wheel',
        //   backgroundImage: 'assets/images/wheel-bg.jpg',
        //   config: {
        //     sections: [
        //       {
        //         id: 1,
        //         label: '10 Points',
        //         value: 10,
        //         probability: 0.3,
        //         backgroundColor: '#FF5733',
        //       },
        //       {
        //         id: 2,
        //         label: '20 Points',
        //         value: 20,
        //         probability: 0.2,
        //         backgroundColor: '#33FF57',
        //       },
        //     ],
        //     spinFrequency: {
        //       type: 'daily',
        //       resetsAt: '00:00',
        //       timezone: 'UTC',
        //       spinsAllowed: 3,
        //     },
        //     wheelSections: 8,
        //     spinDuration: 5000,
        //     minSpinSpeed: 0.1,
        //     maxSpinSpeed: 2.0,
        //     prizes: [],
        //     wheelDesign: {
        //       borderColor: '#000000',
        //       borderWidth: 2,
        //       centerImage: 'assets/images/wheel-center.png',
        //       pointerImage: 'assets/images/pointer.png',
        //     },
        //   },
        // },
        // locationBased: {
        //   name: 'Location Based Photo',
        //   backgroundImage: 'assets/images/hot-or-cold.jpg',
        //   config: {
        //     locations: [
        //       {
        //         id: 'loc1',
        //         name: 'Central Park',
        //         latitude: 40.785091,
        //         longitude: -73.968285,
        //         radius: 100,
        //         points: 100,
        //         description: 'Take a selfie at Central Park',
        //         requiredTags: ['nature', 'park'],
        //       },
        //     ],
        //     maxPhotoSize: 5242880,
        //     allowedFileTypes: ['jpg', 'jpg', 'png'],
        //     requireLocation: true,
        //     maxDistanceMeters: 100,
        //     photoQualityMin: 0.7,
        //   },
        // },
      },
    },
    arcade: {
      name: 'Arcade Games',
      backgroundImage: 'assets/images/arcade-bg.jpg',
      games: {
        wordle: {
          name: 'Wordle',
          backgroundImage: 'assets/images/wordle.png',
          config: {
            wordLength: 5,
            maxAttempts: 6,
            theme: 'light',
          },
          faceImages: {
            correct: 'assets/images/games/faces/2nd Win Kawaii face.png',
            present: 'assets/images/games/faces/3rd loose Kawaii face.png',
            absent: 'assets/images/games/faces/1st loose Kawaii face.png',
            win: 'assets/images/games/faces/3rd Win Kawaii face.png',
            lose: 'assets/images/games/faces/3rd loose Kawaii face.png'
          }
        },
        memory: {
          name: 'Memory',
          backgroundImage: 'assets/images/memory-bg.jpg',
          config: {
            levels: [
              {
                level: 1,
                gridSize: { rows: 2, columns: 2 },
                timeLimit: 30,
                points: 100,
              },
              {
                level: 2,
                gridSize: { rows: 4, columns: 4 },
                timeLimit: 60,
                points: 300,
              },
              {
                level: 3,
                gridSize: { rows: 6, columns: 6 },
                timeLimit: 120,
                points: 500,
              },
            ],
            cardImages: [
              {
                id: 1,
                url: 'assets/images/memory/card1.jpg',
                name: 'Card 1',
              },
              {
                id: 2,
                url: 'assets/images/memory/card2.jpg',
                name: 'Card 2',
              },
            ],
            cardBackImage: 'assets/images/memory/card-back.jpg',
            matchTime: 1000,
            difficulty: 'medium',
            themeOptions: ['classic', 'animals', 'numbers'],
          },
        },

        game2048: {
          name: '2048',
          backgroundImage: 'assets/images/2048-bg.jpg',
          config: {
            gridSize: 4,
            winningTile: 2048,
            animationSpeed: 200,
            swipeThreshold: 50,
            colors: {
              '2': '#EEE4DA',
              '4': '#EDE0C8',
              '8': '#F2B179',
            },
            tileDesign: {
              borderRadius: '3px',
              fontSize: {
                small: '24px',
                medium: '32px',
                large: '48px',
              },
            },
            customTileImages: {
              enabled: false,
              images: {
                '2': 'assets/images/2048/tile2.png',
                '4': 'assets/images/2048/tile4.png',
              },
            },
          },
        },
        snake: {
          name: 'Snake',
          backgroundImage: 'assets/images/snake-bg.jpg',
          config: {
            levels: [
              {
                level: 1,
                gridSize: { width: 10, height: 10 },
                initialSpeed: 100,
                speedIncrease: 2,
                pointsPerFood: 10,
              },
              {
                level: 2,
                gridSize: { width: 15, height: 15 },
                initialSpeed: 150,
                speedIncrease: 3,
                pointsPerFood: 20,
              },
              {
                level: 3,
                gridSize: { width: 20, height: 20 },
                initialSpeed: 200,
                speedIncrease: 4,
                pointsPerFood: 30,
              },
            ],
            foodTypes: [
              {
                type: 'regular',
                points: 1,
                imageUrl: 'assets/images/snake/apple.png',
                probability: 0.7,
              },
              {
                type: 'bonus',
                points: 3,
                imageUrl: 'assets/images/snake/golden-apple.png',
                probability: 0.3,
              },
            ],
            snakeDesign: {
              headImage: 'assets/images/snake/head.png',
              bodyImage: 'assets/images/snake/body.png',
            },
          },
        },
        minesweeper: {
          name: 'Minesweeper',
          backgroundImage: 'assets/images/minesweeper-bg.jpg',
          config: {
            difficulties: {
              beginner: {
                width: 9,
                height: 9,
                mines: 10,
              },
              intermediate: {
                width: 16,
                height: 16,
                mines: 40,
              },
              expert: {
                width: 30,
                height: 16,
                mines: 99,
              },
            },
            defaultDifficulty: 'beginner',
            customization: {
              tileSize: 30,
              mineImage: 'assets/images/minesweeper/mine.png',
              flagImage: 'assets/images/minesweeper/flag.png',
              tileImages: {
                covered: 'assets/images/minesweeper/covered.png',
                uncovered: 'assets/images/minesweeper/uncovered.png',
              },
            },
            animations: {
              reveal: true,
              explosion: true,
            },
          },
        },
        sudoku: {
          name: 'Sudoku',
          backgroundImage: 'assets/images/sudoku-bg.jpg',
          config: {
            difficulties: ['easy', 'medium', 'hard', 'expert'],
            defaultDifficulty: 'medium',
            highlightSameNumbers: true,
            showMistakes: true,
            hints: {
              maximum: 3,
              penaltyMinutes: 5,
            },
            design: {
              gridLineWidth: {
                normal: 1,
                bold: 2,
              },
              colors: {
                highlight: '#f0f0f0',
                error: '#ffdddd',
                success: '#ddffdd',
              },
              fonts: {
                numbers: 'Arial',
                notes: 'Arial',
              },
            },
            timer: {
              enabled: true,
              format: 'mm:ss',
            },
          },
        },
        tetris: {
          name: 'Tetris',
          backgroundImage: 'assets/images/games/tetris.jpg',
          config: {
            startLevel: 1,
            maxLevel: 20,
            boardSize: {
              width: 10,
              height: 20,
            },
            ghostPiece: true,
            holdPiece: true,
            showNext: 3,
            scoringSystem: {
              singleLine: 100,
              doubleLine: 300,
              tripleLine: 500,
              tetris: 800,
            },
            design: {
              blockSize: 30,
              colors: {
                I: '#00f0f0',
                O: '#f0f000',
                T: '#a000f0',
                S: '#00f000',
                Z: '#f00000',
                J: '#0000f0',
                L: '#f0a000',
              },
              customBlocks: {
                enabled: false,
                blockImages: {
                  I: 'assets/images/tetris/i-block.png',
                  O: 'assets/images/tetris/o-block.png',
                },
              },
            },
            particles: {
              enabled: true,
              lineComplete: true,
            },
          },
        },
        'simon-says': {
          name: 'Simon Says',
          backgroundImage: 'assets/images/games/simon-says.jpg',
          config: {
            rounds: 10,
            initialSequenceLength: 3,
            sequenceIncrement: 1,
            flashDuration: 500,
            pauseBetweenFlashes: 300,
            colors: ['red', 'blue', 'green', 'yellow']
          },
        },
        'tower-defense': {
          name: 'Tower Defense',
          backgroundImage: 'assets/images/games/tower-defense.jpg',
          config: {
            initialGold: 100,
            initialLives: 20,
            mapSize: { width: 10, height: 10 },
            waves: 10,
            towerTypes: [
              { name: 'Basic', cost: 50, damage: 10, range: 3 },
              { name: 'Sniper', cost: 100, damage: 25, range: 5 },
              { name: 'Splash', cost: 150, damage: 15, range: 2 }
            ]
          },
        },
        'candy-crush': {
          name: 'Candy Crush',
          backgroundImage: 'assets/images/games/candy-crush.jpg',
          config: {
            boardSize: { width: 8, height: 8 },
            candyTypes: 6,
            movesLimit: 20,
            targetScore: 1000
          },
        },
        chess: {
          name: 'Chess',
          backgroundImage: 'assets/images/games/chess.jpg',
          config: {
            aiDifficulty: 'medium',
            timeLimit: 600,
            showHints: true
          },
        },
        'flappy-bird': {
          name: 'Flappy Bird',
          backgroundImage: 'assets/images/games/flappy-bird.jpg',
          config: {
            gravity: 0.25,
            pipeSpeed: 2,
            gapSize: 150,
            birdSize: 30
          },
        },
        breakout: {
          name: 'Breakout',
          backgroundImage: 'assets/images/games/breakout.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            paddle: {
              width: 75,
              height: 10,
              speed: 7
            },
            ball: {
              radius: 8,
              initialSpeed: 4,
              speedIncrement: 0.5
            },
            bricks: {
              rows: 5,
              columns: 9,
              padding: 10,
              offsetTop: 60,
              offsetLeft: 30
            },
            levels: 5
          },
        },
        hangman: {
          name: 'Hangman',
          backgroundImage: 'assets/images/games/hangman.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 6,
            wordLists: [
              [
                ['CAT', 'DOG', 'RAT', 'BAT', 'HAT'],
                ['BIRD', 'FISH', 'LION', 'BEAR', 'WOLF'],
                ['TIGER', 'MONKEY', 'GIRAFFE', 'ELEPHANT', 'PENGUIN'],
                ['CROCODILE', 'HIPPOPOTAMUS', 'RHINOCEROS', 'CHIMPANZEE', 'KANGAROO'],
                ['TYRANNOSAURUS', 'BRACHIOSAURUS', 'VELOCIRAPTOR', 'STEGOSAURUS', 'TRICERATOPS']
              ],
              [
                ['APPLE', 'GRAPE', 'PEACH', 'LEMON', 'MELON'],
                ['BANANA', 'ORANGE', 'CHERRY', 'MANGO', 'KIWI'],
                ['AVOCADO', 'COCONUT', 'PAPAYA', 'APRICOT', 'GUAVA'],
                ['PINEAPPLE', 'BLUEBERRY', 'RASPBERRY', 'STRAWBERRY', 'BLACKBERRY'],
                ['WATERMELON', 'POMEGRANATE', 'DRAGONFRUIT', 'PASSIONFRUIT', 'CANTALOUPE']
              ],
              [
                ['RED', 'BLUE', 'PINK', 'GOLD', 'GRAY'],
                ['GREEN', 'PURPLE', 'YELLOW', 'BLACK', 'WHITE'],
                ['ORANGE', 'VIOLET', 'INDIGO', 'SILVER', 'BRONZE'],
                ['CRIMSON', 'MAGENTA', 'LAVENDER', 'TURQUOISE', 'MAROON'],
                ['CERULEAN', 'VERMILION', 'CHARTREUSE', 'PERIWINKLE', 'BURGUNDY']
              ]
            ]
          }
        },
        'hot-or-cold': {
          name: 'Hot or Cold',
          backgroundImage: 'assets/images/games/hot-or-cold.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            timeLimit: 300,
            targetRadius: 20,
            maxDistance: 1000,
            hintsAllowed: 3
          },
        },
        poker: {
          name: 'Poker',
          backgroundImage: 'assets/images/games/poker.jpg',
          config: {
            initialChips: 1000,
            smallBlind: 10,
            bigBlind: 20,
            maxPlayers: 6
          },
        },
        pong: {
          name: 'Pong',
          backgroundImage: 'assets/images/games/pong.jpg',
          config: {
            ballSpeed: 5,
            paddleSpeed: 10,
            paddleWidth: 10,
            paddleHeight: 50,
            ballSize: 10
          },
        },
        quiz: {
          name: 'Quiz',
          backgroundImage: 'assets/images/games/trivia.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            timeLimit: 60,
            categories: ['General Knowledge', 'Science', 'Geography'],
            questionsPerRound: 5,
            passingScore: 60
          },
        },
        'rock-paper-scissors': {
          name: 'Rock Paper Scissors',
          backgroundImage: 'assets/images/games/rock-paper-scissors.jpg',
          config: {
            rounds: 3,
            timeLimit: 10
          },
        },
        'tic-tac-toe': {
          name: 'Tic Tac Toe',
          backgroundImage: 'assets/images/games/tic-tac-toe.jpg',
          config: {
            aiDifficulty: 'medium',
            timeLimit: 60
          },
        },
        blackjack: {
          name: 'Blackjack',
          backgroundImage: 'assets/images/games/black-jack.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            startingChips: 1000,
            minBet: 10,
            maxBet: 100
          },
        },
        crossword: {
          name: 'Crossword',
          backgroundImage: 'assets/images/games/crossword.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            hintsAllowed: 3,
            timeLimit: 300,
            levels: 10
          },
        },
        'pac-man': {
          name: 'Pac-Man',
          backgroundImage: 'assets/images/games/pacman.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            ghostSpeed: 500,
            powerModeDuration: 10000,
            levels: 5,
            gridSize: { width: 10, height: 9 }
          },
        },
        platformer: {
          name: 'Platformer',
          backgroundImage: 'assets/images/games/platformer.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            totalLevels: 3,
            jumpForce: 15,
            playerSpeed: 5,
            platformGapMin: 150,
            platformGapMax: 300,
            platformHeightVariance: 100,
            enemySpeed: 2,
            coinValue: 10
          },
        },
        puzzle: {
          name: 'Puzzle',
          backgroundImage: 'assets/images/games/puzzle.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            puzzleSize: 3,
            timeLimit: 100,
            levels: 10
          },
        },
        racing: {
          name: 'Racing',
          backgroundImage: 'assets/images/games/racing.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            carSpeed: 10,
            obstacleSpeed: 5,
            obstacleFrequency: 0.05,
            levels: 5
          },
        },
        solitaire: {
          name: 'Solitaire',
          backgroundImage: 'assets/images/games/solitaire.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            variation: 'klondike',
            drawCount: 1,
            scoring: {
              wasteToTableau: 5,
              wasteToFoundation: 10,
              tableauToFoundation: 10,
              turnOverTableau: 5,
              foundationToTableau: -15
            }
          },
        },
        'treasure-hunt': {
          name: 'Treasure Hunt',
          backgroundImage: 'assets/images/games/treasure-hunt.jpg',
          config: {
            difficulty: 'MEDIUM',
            maxAttempts: 3,
            gridSize: 8,
            treasureCount: 5,
            trapCount: 3,
            timeLimit: 180,
            hintCount: 3
          },
        },
      },
    },
  },
}

@Injectable({
  providedIn: 'root',
})
export class GameService extends AbstractService {
  private readonly API_BASE = this.lssConfig.apiBaseUrl + 'mobile-games/api/v1';
  private activeGame: Game | null = null;
  private useApi: boolean = false; // Set to true to use the API endpoints; false for local mocks

  constructor(
    injector: Injector,
    @Inject('environment') env: any,
    private router: Router,
    private memberService: MemberService
  ) {
    super(injector, env);
  }

  // Get all game configurations
  getAllGameConfigs(): Observable<Game[]> {
    if (!this.useApi) {
      const localGameConfigs: Game[] = [];
      const categories = (environmentGames as any).categories;
      let idCounter = 100;
      Object.keys(categories).forEach(categoryKey => {
        const category = categories[categoryKey];
        Object.keys(category.games).forEach(gameKey => {
          const gameData = category.games[gameKey];
          localGameConfigs.push({
            id: idCounter++,
            name: gameData.name,
            startDate: '2024-11-01',
            endDate: '2024-12-31',
            owner: 0,
            status: 420,
            gameType: {
              createdBy: 'LOCAL',
              createdOn: new Date().toISOString(),
              version: 0,
              id: idCounter++,
              category: categoryKey === 'businessgames' ? 362 : 363,
              categoryDescription: category.name,
              type: 375,
              typeDescription: {
                code: gameKey.toUpperCase(),
                description: gameData.name
              }
            },
            gameParticipation: [],
            gameReward: [],
            gameConfig: [{
              id: 1,
              difficulty: 'MEDIUM',
              frequency: 'DAILY',
              frequencyAttempts: 3
            }]
          });
        });
      });
      return of(localGameConfigs);
    }
    return this.httpClient
      .get<Game[]>({
        url: `${this.API_BASE}/config`,
        key: 'ALL-GAME-CONFIGS',
        cacheMins: 5
      })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('Error fetching game configs:', error);
          return throwError(() => error);
        })
      );
  }

  // Get specific game by ID
  getGameById(gameId: number): Observable<Game> {
    return this.httpClient
      .get<Game>({
        url: `${this.API_BASE}/config/${gameId}`,
        key: `GAME-${gameId}`,
        cacheMins: 5,
      })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('Error fetching game:', error);
          return throwError(() => error);
        })
      );
  }

  // Check if game can be played
  checkGameAvailability(gameId: number): Observable<boolean> {
    return this.httpClient
      .get<CanPlayResponse>({
        url: `${this.API_BASE}/config/${gameId}/can-play`,
        key: `GAME-AVAILABILITY-${gameId}`,
        cacheMins: 0,
      })
      .pipe(
        map((response) => response.canPlay),
        catchError((error: HttpErrorResponse) => {
          console.error('Error checking game availability:', error);
          return throwError(() => error);
        })
      );
  }

  // Get game progress
  getGameProgress<T extends BaseGameProgress>(
    gameId: number
  ): Observable<GameProgressType<T>> {
    return this.httpClient
      .get<T>({
        url: `${this.API_BASE}/config/${gameId}/progress`,
        key: `GAME-PROGRESS-${gameId}`,
        cacheMins: 0,
      })
      .pipe(
        map(
          (progress) =>
            ({
              ...progress,
              lastPlayed: new Date(progress.lastPlayed),
            } as GameProgressType<T>)
        ),
        catchError((error: HttpErrorResponse) => {
          console.error('Error fetching game progress:', error);
          return throwError(() => error);
        })
      );
  }

  // Update game progress
  updateGameProgress<T extends BaseGameProgress>(
    gameId: number,
    progress: Partial<GameProgressType<T>>
  ): Observable<GameProgressType<T>> {
    const apiProgress = {
      ...progress,
      lastPlayed: progress.lastPlayed?.toISOString(),
    };

    return this.httpClient
      .put<T>({
        url: `${this.API_BASE}/config/${gameId}/progress`,
        key: `GAME-PROGRESS-${gameId}`,
        body: apiProgress,
      })
      .pipe(
        map(
          (response) =>
            ({
              ...response,
              lastPlayed: new Date(response.lastPlayed),
            } as GameProgressType<T>)
        ),
        catchError((error: HttpErrorResponse) => {
          console.error('Error updating game progress:', error);
          return throwError(() => error);
        })
      );
  }

  // Save game score
  saveGameScore(gameId: number, score: number): Observable<any> {
    return this.httpClient
      .post<any>({
        url: `${this.API_BASE}/config/${gameId}/score`,
        key: `GAME-SCORE-${gameId}`,
        body: { score },
      })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('Error saving game score:', error);
          return throwError(() => error);
        })
      );
  }

  // Existing methods...
  getAccountGameConfigs(accountId: number): Observable<AccountGameConfig[]> {
    if (!this.useApi) {
      // First, get all game configs to make all games playable
      return this.getAllGameConfigs().pipe(
        map(games => {
          // Create account configs for all games
          return games.map(game => ({
            gameConfig: game.id,
            gameInstance: null,
            gameEvent: null
          }));
        })
      );
    }
    console.log('Getting account game configs for account:', accountId);
    console.log('--------------------------------');
    return this.httpClient
      .get<AccountGameConfig[]>({
        url: `${this.API_BASE}/config/account/${accountId}`,
        key: `GAME-CONFIGS-${accountId}`,
        cacheMins: 0
      })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('Error fetching account game configs:', error);
          return throwError(() => error);
        })
      );
  }

  setActiveGame(game: Game) {
    console.log('Setting active game:', game);
    console.log('--------------------------------');
    this.activeGame = game;
    const configId = game.id;
    console.log('Config ID:', configId);
    console.log('--------------------------------');
    if (configId) {
      console.log('Config ID:', configId);
      console.log('--------------------------------');
      const participation = game.gameParticipation?.[0];
      const accountId = participation?.gameInstance?.[0]?.account || 517193;
      this.getAccountGameConfigs(accountId).subscribe({
        next: (configs: AccountGameConfig[]) => {
          console.log('Received game configs:', configs);
          const config = configs.find(
            (c: AccountGameConfig) => c.gameConfig === configId
          );
          if (config?.gameInstance) {
            console.log('Found config:', config);
            console.log('Found existing game instance:', config.gameInstance);
            localStorage.setItem(
              'currentGameInstanceId',
              config.gameInstance.toString()
            );
          }
        },
        error: (error: Error) => {
          console.error('Error getting game configs:', error);
        },
      });
    }
  }

  getActiveGame(): Game | null {
    // If activeGame is not set, try to get it from localStorage
    if (!this.activeGame) {
      const gameData = localStorage.getItem('activeGame');
      if (gameData) {
        try {
          this.activeGame = JSON.parse(gameData);
          console.log('Retrieved active game from localStorage:', this.activeGame);
        } catch (error) {
          console.error('Error parsing active game from localStorage:', error);
        }
      }
    }
    return this.activeGame;
  }

  getGameInstance(configId: number, instanceId: number): Observable<GameInstance> {
    if (!this.useApi) {
      const localInstanceDetail: GameInstance = {
        id: instanceId,
        createdBy: 'demo-user',
        createdOn: new Date().toISOString(),
        version: 1,
        account: 517193,
        endDate: '',
        gameEvents: [],
        startDate: new Date().toISOString(),
        status: 'active'
      };
      return of(localInstanceDetail);
    }

    return this.httpClient
      .get<GameInstance>({
        url: `${this.API_BASE}/config/${configId}/instance/${instanceId}`,
        key: `GAME-INSTANCES-${configId}-${instanceId}`,
        cacheMins: 0
      })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('Error fetching game instance:', error);
          // Return demo data as fallback in case of error
          const fallbackInstance: GameInstance = {
            id: instanceId,
            createdBy: 'demo-user',
            createdOn: new Date().toISOString(),
            version: 1,
            account: 517193,
            endDate: '',
            gameEvents: [],
            startDate: new Date().toISOString(),
            status: 'active'
          };
          return of(fallbackInstance);
        })
      );
  }

  createGameInstance(configId: number): Observable<GameInstance> {
    if (!this.useApi) {
      const localInstance: GameInstance = {
        id: Math.floor(Math.random() * 1000) + 1,
        createdBy: 'demo-user',
        createdOn: new Date().toISOString(),
        version: 1,
        account: 517193,
        endDate: '',
        gameEvents: [],
        startDate: new Date().toISOString(),
        status: 'active'
      };
      return of(localInstance);
    }

    const ACCOUNT_ID = 517193;
    return this.httpClient
      .post<GameInstance>({
        url: `${this.API_BASE}/config/${configId}/instance`,
        key: `GAME-INSTANCE-${configId}`,
        body: {
          account: ACCOUNT_ID
        }
      })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('Error creating game instance:', error);
          // Return demo instance as fallback in case of error
          const fallbackInstance: GameInstance = {
            id: Math.floor(Math.random() * 1000) + 1,
            createdBy: 'demo-user',
            createdOn: new Date().toISOString(),
            version: 1,
            account: 517193,
            endDate: '',
            gameEvents: [],
            startDate: new Date().toISOString(),
            status: 'active'
          };
          return of(fallbackInstance);
        })
      );
  }

  // Get all game instances for a config
  getAllGameInstances(configId: number): Observable<GameInstance[]> {
    if (!this.useApi) {
      return of([]);
    }

    return this.httpClient
      .get<GameInstance[]>({
        url: `${this.API_BASE}/config/${configId}/instance`,
        key: `GAME-INSTANCES-${configId}`,
        cacheMins: 0,
      })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('Error fetching game instances:', error);
          return throwError(() => error);
        })
      );
  }

  // Add method to create game event
  createGameEvent(
    configId: number,
    instanceId: number,
    event: Partial<GameEvent>
  ): Observable<GameEvent> {
    if (!this.useApi) {
      const mockEvent: GameEvent = {
        id: Math.floor(Math.random() * 1000) + 1,
        level: event.level || 1,
        score: event.score || 0,
        duration: event.duration || 0,
        state: event.state || 'completed',
        payload: event.payload || ''
      };
      return of(mockEvent);
    }

    console.log('Creating game event:', event);
    return this.httpClient
      .post<GameEvent>({
        url: `${this.API_BASE}/config/${configId}/instance/${instanceId}/event`,
        key: `GAME-EVENT-${instanceId}-${Date.now()}`,
        body: event,
        cacheMins: 0
      })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('Error creating game event:', error);
          return throwError(() => error);
        })
      );
  }

  // Add method to reset game progress
  resetGameProgress(instanceId: number): Observable<any> {
    if (!this.useApi) {
      return of({ success: true });
    }
    return this.httpClient
      .post<any>({
        url: `${this.API_BASE}/instance/${instanceId}/reset`,
        key: `RESET-GAME-${instanceId}`,
        body: {}
      })
      .pipe(
        catchError((error: HttpErrorResponse) => {
          console.error('Error resetting game progress:', error);
          return throwError(() => error);
        })
      );
  }
}

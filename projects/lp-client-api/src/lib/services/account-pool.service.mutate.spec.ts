import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { AccountPoolService } from './account-pool.service';
import { LssConfig } from '../types/lss-config';
import { SystemService } from './system.service';
import { KeyCloakService } from './key-cloak.service';

describe('AccountPoolService (mutating)', () => {
  let service: AccountPoolService;
  let httpMock: HttpTestingController;

  const lssConfig: LssConfig = Object.assign(new LssConfig(), {
    apiBaseUrl: 'https://example.test/',
    apiId: 'TEST_API_ID',
    apiIdKeyStart: 'START',
    apiIdKeyEnd: 'END',
    workflow: {
      productId: 'prod-1',
      features: {
        pools: {
          extsecure: { mutateEnabled: true }
        }
      }
    }
  });

  const systemStub = {
    getUniqueId: jasmine.createSpy('getUniqueId').and.returnValue('UID-456')
  } as unknown as SystemService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AccountPoolService,
        { provide: LssConfig, useValue: lssConfig },
        { provide: SystemService, useValue: systemStub },
        { provide: KeyCloakService, useValue: { getUserIdForApi: () => 'U123' } }
      ]
    });
    service = TestBed.inject(AccountPoolService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should PUT long-form REQS with correct path, headers and JSON body when productId and userId are available', () => {
    service.joinPool(123, '1000101', 'REQS', '1000101').subscribe();

    const expectedUrl = 'https://example.test/extsecure/member/U123/products/prod-1/member/1000101/accountpool/123/members/1000101';
    const req = httpMock.expectOne(r => r.method === 'PUT' && r.url === expectedUrl);

    expect(req.request.headers.get('LP_APIID')).toBe('TEST_API_ID');
    expect(req.request.headers.get('LP_UniqueId')).toBe('UID-456');
    expect(req.request.headers.get('Content-Type')).toBe('application/json');

    const body = req.request.body as any;
    expect(body).toEqual({ mpacc: '1000101', role: 'MEMB', privacy: 'N', action: 'REQS' });

    req.flush({});
  });

  it('should PUT long-form INVT with correct path, headers and JSON body when productId and userId are available', () => {
    service.joinPool(456, '1000202', 'INVT', '1000101').subscribe();

    const expectedUrl = 'https://example.test/extsecure/member/U123/products/prod-1/member/1000101/accountpool/456/members/1000202';
    const req = httpMock.expectOne(r => r.method === 'PUT' && r.url === expectedUrl);

    expect(req.request.headers.get('LP_APIID')).toBe('TEST_API_ID');
    expect(req.request.headers.get('LP_UniqueId')).toBe('UID-456');
    expect(req.request.headers.get('Content-Type')).toBe('application/json');

    const body = req.request.body as any;
    expect(body).toEqual({ mpacc: '1000202', role: 'MEMB', privacy: 'N', action: 'INVT' });

    req.flush({});
  });

  it('should fall back to form-encoded POST when productId is missing', () => {
    // Remove productId to trigger fallback
    (lssConfig as any).workflow.productId = undefined;

    service.joinPool(789, '1000303', 'REQS', '1000101').subscribe();

    const req = httpMock.expectOne(r => r.method === 'POST' && r.url === 'https://example.test/extsecure/member/accountpool/join');

    const body = req.request.body as string;
    const params = new URLSearchParams(body);
    expect(params.get('poolId')).toBe('789');
    expect(params.get('mpacc')).toBe('1000303');
    expect(params.get('action')).toBe('REQS');

    req.flush({});

    // Restore productId for isolation
    (lssConfig as any).workflow.productId = 'prod-1';
  });
});


import { TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { AccountPoolService } from './account-pool.service';
import { LssConfig } from '../types/lss-config';
import { SystemService } from './system.service';
import { KeyCloakService } from './key-cloak.service';

describe('AccountPoolService (admin/member actions)', () => {
  let service: AccountPoolService;
  let httpMock: HttpTestingController;

  const lssConfig: LssConfig = Object.assign(new LssConfig(), {
    apiBaseUrl: 'https://example.test/',
    apiId: 'TEST_API_ID',
    apiIdKeyStart: 'START',
    apiIdKeyEnd: 'END',
    workflow: {
      productId: 'prod-1',
      features: {
        pools: {
          extsecure: { mutateEnabled: true }
        }
      }
    }
  });

  const systemStub = {
    getUniqueId: jasmine.createSpy('getUniqueId').and.returnValue('UID-789')
  } as unknown as SystemService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [
        AccountPoolService,
        { provide: LssConfig, useValue: lssConfig },
        { provide: SystemService, useValue: systemStub },
        { provide: KeyCloakService, useValue: { getUserIdForApi: () => 'U999' } }
      ]
    });
    service = TestBed.inject(AccountPoolService);
    httpMock = TestBed.inject(HttpTestingController);
  });

  afterEach(() => {
    httpMock.verify();
  });

  it('should PUT long-form ACCP (approve) with correct body and headers', () => {
    service.processPoolInvite(123, '1000202', 'ACCP', '1000101').subscribe();

    const expectedUrl = 'https://example.test/extsecure/member/U999/products/prod-1/member/1000101/accountpool/123/members/1000202';
    const req = httpMock.expectOne(r => r.method === 'PUT' && r.url === expectedUrl);

    expect(req.request.headers.get('LP_APIID')).toBe('TEST_API_ID');
    expect(req.request.headers.get('LP_UniqueId')).toBe('UID-789');
    expect(req.request.body).toEqual({ mpacc: '1000202', role: 'MEMB', privacy: 'N', action: 'ACCP' });

    req.flush({});
  });

  it('should PUT long-form REMV (remove) with correct body and headers', () => {
    service.processPoolInvite(456, '1000303', 'REMV', '1000101').subscribe();

    const expectedUrl = 'https://example.test/extsecure/member/U999/products/prod-1/member/1000101/accountpool/456/members/1000303';
    const req = httpMock.expectOne(r => r.method === 'PUT' && r.url === expectedUrl);

    expect(req.request.headers.get('LP_APIID')).toBe('TEST_API_ID');
    expect(req.request.headers.get('LP_UniqueId')).toBe('UID-789');
    expect(req.request.body).toEqual({ mpacc: '1000303', role: 'MEMB', privacy: 'N', action: 'REMV' });

    req.flush({});
  });

  it('should PUT long-form EXIT with correct body and headers', () => {
    service.processPoolInvite(321, '1000101', 'EXIT', '1000101').subscribe();

    const expectedUrl = 'https://example.test/extsecure/member/U999/products/prod-1/member/1000101/accountpool/321/members/1000101';
    const req = httpMock.expectOne(r => r.method === 'PUT' && r.url === expectedUrl);

    expect(req.request.headers.get('LP_APIID')).toBe('TEST_API_ID');
    expect(req.request.headers.get('LP_UniqueId')).toBe('UID-789');
    expect(req.request.body).toEqual({ mpacc: '1000101', role: 'MEMB', privacy: 'N', action: 'EXIT' });

    req.flush({});
  });

  it('should DELETE long-form on declineInvite with correct headers', () => {
    service.declineInvite(654, '1000404', '1000101').subscribe();

    const expectedUrl = 'https://example.test/extsecure/member/U999/products/prod-1/member/1000101/accountpool/654/members/1000404';
    const req = httpMock.expectOne(r => r.method === 'DELETE' && r.url === expectedUrl);

    expect(req.request.headers.get('LP_APIID')).toBe('TEST_API_ID');
    expect(req.request.headers.get('LP_UniqueId')).toBe('UID-789');
    req.flush({}, { status: 204, statusText: 'No Content' });
  });
  it('should POST long-form updatePool with correct body', () => {
  service.updatePool(
    *********,
    '1000101',
    'en',
    "Emil's Pool",
    '<EMAIL>',
    '+27',
    '*********',
    3272126
  ).subscribe();

  const expectedUrl = 'https://example.test/extsecure/member/U999/products/prod-1/member/1000101/accountpool/*********';
  const req = httpMock.expectOne(r => r.method === 'POST' && r.url === expectedUrl);

  expect(req.request.body).toEqual({
    poolId: *********,
    language: 'en',
    poolName: "Emil's Pool",
    email: '<EMAIL>',
    countryCode: '+27',
    telephone: '*********',
    poolSplit: 3272126
  });

  req.flush({});
  });
});


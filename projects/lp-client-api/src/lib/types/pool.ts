export interface AccountPoolApi {
  poolId: number;
  poolMembershipNr: string;
  poolMpacc: string;
  poolAlias: string | null;
  poolSplit: number;
  poolSplitDescr?: string;
  status: string;
  statusDescr?: string;
  beginDate: string;
  endDate: string | null;
  poolName: string;
  language?: string;
  email?: string;
  countryCode?: string;
  telephone?: string;
  balance?: number;
  role?: string;
  linkStatus?: string;
  linkStatusDescr?: string;
  invitationStatus?: string | null;
}

export interface PoolMemberApi {
  mpacc: string;
  alias?: string | null;
  membershipNr?: string;
  role: string; // e.g. ADMN or MEMB
  privacy: string; // 'Y' | 'N'
  linkStatus: string; // STAA, STPR, etc.
  linkStatusDescr?: string;
  balance?: number;
  allowActivity?: string;
  allowAward?: string;
  invitationStatus?: string | null; // INVT, REQS, null
  name?: string;
}

// Runtime type guards
export function isAccountPoolApi(obj: any): obj is AccountPoolApi {
  return obj &&
    typeof obj.poolId === 'number' &&
    typeof obj.poolMpacc === 'string' &&
    typeof obj.poolName === 'string' &&
    typeof obj.status === 'string';
}

export function isPoolMemberApi(obj: any): obj is PoolMemberApi {
  return obj && typeof obj.mpacc === 'string' && typeof obj.role === 'string' && typeof obj.privacy === 'string' && typeof obj.linkStatus === 'string';
}


import { PhoneNumberUtil } from 'google-libphonenumber';

export interface Member {
  mpacc?: string;
  cis?: number;
  status?: string;
  enrolDate?: Date;
  enrolSource?: string;
  birthdate?: string;
  accountAlias?: string;
}

export interface Telephone {
  telephoneType: string;
  countryCode?: string;
  telephoneCode?: string;
  telephoneNumber?: string;
  telephoneExtension?: string;
  phoneable?: string;
  smsable?: string;
}
export interface Address {
  addressType: string;
  addressTypeDesc?: string;
  line1?: string;
  line2?: string;
  line3?: string;
  suburb?: string;
  suburbDesc?: string;
  city?: string;
  cityDesc?: string;
  district?: string;
  province?: string;
  provinceDesc?: string;
  country?: string;
  countryDesc?: string;
  postCode?: string;
  postalCode?: string;
  mailable?: string;
  returnToSenderCount?: string;
  mailPreference?: string;
  landmark?: string;
}
export interface Token {
  partnerId: string;
  partnerName: string;
  tokenIndicator: string;
  tokenIndicatorDescr: string;
  tokenCode: string;
  tokenDescr: string;
  value: number;
  valueRequired: number;
  tokenValidityDate: Date;
  image1URL: string;
  image2URL: string;
  image3URL: string;
}

export interface Passenger {
  paxName: string;
  paxSurname: string;
  paxTitle: string;
  PassengerType: 'Infant' | 'Child' | 'Adult';
  membershipNumber: string;
}
export interface FunctionAccessItem {
  functionCode: string;
  functionDescription: string;
  access: Boolean;
}
export interface Preference {
  level1: string;
  level2: string;
  level2Boolean: Boolean;
  level3: string;
  level4: string;
  level5: string;
  other: string;
}
export interface CommunicationPreference {
  commuType: string;
  preference: string;
  mailAdrOption: string;
  description: string;
}
export interface MemberProfile {
  uniqueId: string;
  apiId: string;
  mpacc?: string;
  terminalId?: string;
  productId?: string;
  externalId?: string;
  personTelephone?: Telephone[];
  personAddress?: Address[];
  givenNames?: string;
  surname?: string;
  nickName?: string;
  emailAddress?: string;
  currentBalance?: number;
  previousStatementBalance?: number;
  previousStatementDate?: string;
  baseMiles?: number;
  bonusMiles?: number;
  usedMiles?: number;
  expiredMiles?: number;
  currentTier?: string;
  availRands?: number;
  availUnits?: number;
  currentTierDescription?: string;
  currentTierExpDate?: Date;
  currentStarTier?: string;
  currentStarTierDescription?: string;
  spouseBalance?: number;
  passortNum?: string;
  nationality?: string;
  issueCountry?: string;
  birthDate?: Date;
  issueDate?: Date;
  natIdExpDate?: Date;
  expiryDate?: Date;
  gender?: string;
  memberType?: string;
  title?: string;
  nationalIdNum?: string;
  language?: string;
  promoCode?: string;
  membershipNumber?: string;
  newMembershipNumber?: string;
  sessionId?: string;
  currentTierBalance?: number;
  stayTierPoints?: number;
  nextTierPoints?: number;
  nextTierType?: string;
  nextTierDescription?: string;
  forcePasswordChange?: string;
  notifyType?: string;
  patronymic?: string;
  verifiedRequiredFields?: string[];
  verifiedChangedFields?: string[];
  firstnameUTF8?: string;
  lastnameUTF8?: string;
  enrolSource?: string;
  enrolDate?: Date;
  accessList?: FunctionAccessItem[];
  preferenceList?: Preference[];
  communicationPreferenceList?: CommunicationPreference[];
  statusCode?: string;
  statusDescription?: string;
  beneficiaries?: Passenger[];
  subscriptionCode?: string;
  subscriptionDescr?: string;
  photoURL?: string;
  communityId?: string;
  tokenBalance?: number;
  tokenWallet?: Token[];
  monthlyIncome?: number;
  virtualCard?: string;
  productDesc?: string;
  communityDescription?: string;
  nextExpiryDate?: Date;
  nextExpiryUnits?: number;
  isClaim?: Boolean;
  referal?: string;
  verificationStatus?: string;
  verificationStatusDescr?: string;
  deactivateDate?: Date;
  fieldValue?: string;
  userId?: string;
}
export interface BasicProfile {
  membershipNumber?: string;
  availRands?: number;
  availUnits?: number;
  baseUnits?: number;
  bonusUnits?: number;
  expUnits?: number;
  usedUnits?: number;
  currTier?: string;
  currTierDescr?: string;
  productId?: string;
  productDescr?: string;
  communityId?: string;
  communityDescr?: string;
  memberStatus?: string;
  memberStatusDescr?: string;
  firstname?: string;
  lastname?: string;
  memberType?: string;
  memberTypeDescr?: string;
  emailAddress?: string;
  mobileNumber?: string;
  birthDate?: string;
  enrolDate?: string;
  idNumber?: string;
}

export interface Transaction {
  transactionType?: string; //todo: make enum or list of values
  activityDate?: Date;
  loadDate?: Date;
  partnerId?: string;
  partner?: string;
  units?: number;
  description?: string;
  invoiceNumber?: string;
  checkInDate?: Date;
  checkOutDate?: Date;
}

export interface TransactionDetail {
  description?: string;
  linenumber: number;
  quantity: number;
  amount: number;
  productCode?: string;
  productClass?: string;
  certificateNumber?: string;
}
export interface LoadTransactionRequest {
  authkey?: string;
  amount: number;
  attendant?: string;
  receiptDateTime?: Date;
  receiptNumber?: string;
  sequenceNumber?: number;
  terminalID?: string;
  type:
    | 'Accrual'
    | 'Refund'
    | 'Redemption'
    | 'Token'
    | 'TokenRedemption'
    | 'RefundRedemption';
  cardNumber?: string;
  language?: string;
  transactionDetail?: TransactionDetail[];
  communityId?: string;
  productId?: string;
  apiId?: string;
  checkInDate?: Date;
  checkOutDate?: Date;
  pin?: string;
}

export interface TransactionReponse {
  authCode?: string;
  basicProfile?: BasicProfile;
}

export enum TransactionType {
  'Accrual',
  'Refund',
  'Redemption',
  'Token',
  'TokenRedemption',
  'RefundRedemption',
}

export interface TransactionType2 {
  type:
    | 'Accrual'
    | 'Refund'
    | 'Redemption'
    | 'Token'
    | 'TokenRedemption'
    | 'RefundRedemption';
}

export class LPMemberEntityTools {
  static phoneUtil = PhoneNumberUtil.getInstance();

  static getAddressType(profile: MemberProfile, adrType: string): Address {
    let adr: Address | null = null;
    profile.personAddress = profile.personAddress || [];
    if (profile.personAddress != null) {
      profile.personAddress.forEach((row) => {
        if (row.addressType === adrType) {
          adr = row;
        }
      });
    }
    if (adr == null) {
      adr = LPMemberEntityTools.getEmptyAddress(adrType);
      profile.personAddress.push(adr);
    }
    return adr;
  }
  static getTelephoneType(profile: MemberProfile, telType: string): Telephone {
    let tel: Telephone | null = null;
    profile.personTelephone = profile.personTelephone || [];
    if (profile.personTelephone != null) {
      profile.personTelephone.forEach((row) => {
        if (row.telephoneType === telType) {
          tel = row;
        }
      });
    }
    if (tel == null) {
      tel = LPMemberEntityTools.getEmptyPhone(telType);
      profile.personTelephone.push(tel);
    }
    return tel;
  }

  static getEmptyPhone(telType: string): Telephone {
    return { telephoneType: telType };
  }
  static getEmptyAddress(adrType: string): Address {
    return { addressType: adrType };
  }
  static getIONTelephoneFromLPTel(tel: Telephone) {
    try {
      const mobileFullNumber =
        tel.countryCode + ' ' + tel.telephoneCode + tel.telephoneNumber; //.replace(/0/gi,'1');
      const validNumber =
        LPMemberEntityTools.phoneUtil.parseAndKeepRawInput(mobileFullNumber);
      return {
        nationalNumber: validNumber.getNationalNumber(),
        isoCode: LPMemberEntityTools.phoneUtil
          .getRegionCodeForNumber(validNumber)
          ?.toLowerCase(),
        dialCode: validNumber.getCountryCode(),
        internationalNumber: mobileFullNumber,
      };
    } catch (e) {
      //console.log(e);
    }
    return {
      nationalNumber: '',
      isoCode: '',
      dialCode: '',
      internationalNumber: '',
    };
  }
}

export interface Statement {
  actValue?: number;
  expDate?: Date;
  invoiceNumber?: string;
  label?: Date;
  loadDate?: string;
  quantity?: number;
  tierPoints?: number;
  transactionDate?: Date;
  transactionPoints?: number;
  transactionType?: string;
}

export interface ContactForm {
  uniqueId: string;
  apiId: string;
  givenNames?: string;
  surname?: string;
  nickName?: string;
  emailAddress?: string;
  externalId?: string;
}

export interface GiftCard {
  giftCode: string;
  value: number;
  balance: number;
  status: string;
  validity?: Date;
  canFund: boolean;
}

export interface GiftCardRequest {
  giftCode: string;
  amount: number;
  invoiceNumber: string;
}

import { KeyCloakService } from '../services/key-cloak.service';
import { MemberService } from '../services/member.service';
import { MemberProfile } from '../types/member';

export interface MemberIdResolutionResult {
  memberId: string | null;
  source: 'profile' | 'keycloak_mpacc' | 'keycloak_username' | 'program_context' | 'storage' | 'none';
  isAuthenticated: boolean;
}

/**
 * Utility class for resolving member ID from various sources when profile data is not available.
 * This is particularly useful for Firebase token linking during authentication flow.
 */
export class MemberIdResolver {
  
  /**
   * Resolve member ID from multiple sources in order of preference:
   * 1. Member profile (newMembershipNumber, mpacc, uniqueId)
   * 2. Keycloak token LP_MPACC 
   * 3. Keycloak token preferred_username (for new users)
   * 4. Program context (multi-tenant)
   * 5. Local storage fallback
   */
  static resolveMemberId(
    memberService: MemberService, 
    keyCloakService: KeyCloakService
  ): MemberIdResolutionResult {
    
    // Check if user is authenticated
    if (!keyCloakService.authSuccess) {
      return {
        memberId: null,
        source: 'none',
        isAuthenticated: false
      };
    }

    // 1. Try to get member ID from loaded profile
    try {
      const memberProfile = memberService.profileSubject?.value;
      if (memberProfile) {
        const profileMemberId = memberProfile.newMembershipNumber || 
                               memberProfile.mpacc || 
                               memberProfile.uniqueId;
        if (profileMemberId && String(profileMemberId).trim() !== '') {
          return {
            memberId: String(profileMemberId),
            source: 'profile',
            isAuthenticated: true
          };
        }
      }
    } catch (error) {
      console.warn('[MemberIdResolver] Error accessing member profile:', error);
    }

    // 2. Try to get member ID from Keycloak token LP_MPACC
    try {
      const lpMpacc = keyCloakService.lpUniueReference;
      if (lpMpacc && lpMpacc !== 'Not Set' && lpMpacc.trim() !== '') {
        return {
          memberId: lpMpacc,
          source: 'keycloak_mpacc',
          isAuthenticated: true
        };
      }
    } catch (error) {
      console.warn('[MemberIdResolver] Error accessing Keycloak LP_MPACC:', error);
    }

    // 3. For new users or when MPACC is not set, use Keycloak username
    // This is especially important for users who need registration or are in multi-tenant setups
    try {
      const userProfile = keyCloakService.userProfile;
      if (userProfile) {
        const username = userProfile.preferred_username || userProfile.email;
        if (username && String(username).trim() !== '') {
          console.log('[MemberIdResolver] Using Keycloak username for member identification:', username);
          return {
            memberId: String(username),
            source: 'keycloak_username',
            isAuthenticated: true
          };
        }
      }
    } catch (error) {
      console.warn('[MemberIdResolver] Error accessing Keycloak user profile:', error);
    }

    // 4. Try program context for multi-tenant applications
    try {
      const programContext = (memberService as any).getCurrentProgramContext?.();
      if (programContext?.mpacc && String(programContext.mpacc).trim() !== '') {
        return {
          memberId: String(programContext.mpacc),
          source: 'program_context',
          isAuthenticated: true
        };
      }
    } catch (error) {
      console.warn('[MemberIdResolver] Error accessing program context:', error);
    }

    // 5. Local storage fallback
    try {
      const storedMemberId = localStorage.getItem('membershipNumber');
      if (storedMemberId && storedMemberId.trim() !== '') {
        return {
          memberId: storedMemberId,
          source: 'storage',
          isAuthenticated: true
        };
      }
    } catch (error) {
      console.warn('[MemberIdResolver] Error accessing local storage:', error);
    }

    // No member ID could be resolved
    return {
      memberId: null,
      source: 'none',
      isAuthenticated: true
    };
  }

  /**
   * Get user ID for API calls - handles both existing members and new multi-tenant users
   * This method prioritizes LP_MPACC but falls back to username for new users
   */
  static getUserIdForApi(keyCloakService: KeyCloakService): string | null {
    if (!keyCloakService.authSuccess) {
      return null;
    }

    // Use the existing method from KeyCloakService which already handles this logic
    return keyCloakService.getUserIdForApi();
  }

  /**
   * Check if the current user likely needs registration
   * This helps determine if we should use username vs MPACC for identification
   */
  static userNeedsRegistration(keyCloakService: KeyCloakService): boolean {
    if (!keyCloakService.authSuccess || !keyCloakService.userProfile) {
      return false;
    }

    // Check if user has mustRegister flag
    if (keyCloakService.userProfile.mustRegister) {
      return true;
    }

    // Check if LP_MPACC is missing or invalid
    const mpacc = keyCloakService.lpUniueReference;
    return !mpacc || mpacc === 'Not Set' || mpacc.trim() === '';
  }
}
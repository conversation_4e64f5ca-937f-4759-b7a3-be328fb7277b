# LP-Client-Test Dynamic Page System Documentation

## Overview

The `lp-client-test` project implements a **fully dynamic page system** where ALL pages are loaded from environment configuration rather than hardcoded routes. This system provides maximum flexibility for configuring pages, components, and navigation without code changes.

## Architecture

### Core Components

1. **BaseDynamicPageComponent** (`src/app/shared/base-dynamic-page.component.ts`)
   - Abstract base class that handles dynamic page loading logic
   - Manages component instantiation, authentication checks, and page configuration
   - Provides common functionality for both public and secure pages

2. **DynamicPublicPageComponent** (`src/app/public/dynamic-public-page.component.ts`)
   - Extends BaseDynamicPageComponent for public pages
   - Handles non-authenticated and authenticated public pages
   - Redirects to login for secure pages when user is not authenticated

3. **DynamicSecurePageComponent** (`src/app/secure/dynamic-secure-page.component.ts`)
   - Extends BaseDynamicPageComponent for secure pages
   - Requires authentication for all pages
   - Redirects to login if user is not authenticated

### Routing Structure

The routing system supports nested paths up to 3 levels deep:

```typescript
// Main App Routes
{ path: '', redirectTo: 'public/home', pathMatch: 'full' }
{ path: 'app/:page', redirectTo: 'public/app/:page' } // Legacy support
{ path: 'public', loadChildren: () => PublicModule }
{ path: 'secure', loadChildren: () => SecureModule }

// Public Routes
{ path: ':page/:subpage/:subsubpage', component: DynamicPublicPageComponent }
{ path: ':page/:subpage', component: DynamicPublicPageComponent }
{ path: ':page', component: DynamicPublicPageComponent }

// Secure Routes (same pattern)
{ path: ':page/:subpage/:subsubpage', component: DynamicSecurePageComponent }
{ path: ':page/:subpage', component: DynamicSecurePageComponent }
{ path: ':page', component: DynamicSecurePageComponent }
```

### Path Resolution

The system constructs page IDs from route parameters:

- `/public/home` → `pageId = "home"`
- `/public/games/categories` → `pageId = "games/categories"`
- `/public/app/stores` → `pageId = "app/stores"`

## Environment Configuration

All pages are defined in `src/environments/environment.ts` under `lssConfig.pages[]`:

```typescript
export const environment = {
  lssConfig: {
    pages: [
      {
        title: 'home',
        path: 'home',
        secure: false,
        class: 'app-background',
        components: [
          {
            type: 'PagesLoginTheme1Component',
            showWhen: 'anonymous',
            inputs: { kc: 'kc' }
          },
          {
            type: 'PagesLandingTheme1Component', 
            showWhen: 'authenticated',
            inputs: { profile: 'profile' }
          }
        ]
      },
      {
        title: 'games-home',
        path: 'games/home',
        secure: false,
        class: '',
        components: [
          {
            type: 'PagesLandingThemesGamesComponent',
            inputs: {}
          }
        ]
      }
      // ... more pages
    ]
  }
}
```

### Page Configuration Properties

- **title**: Unique identifier for the page
- **path**: URL path that matches the route (supports nested paths)
- **secure**: Boolean indicating if authentication is required
- **class**: CSS classes to apply to the page container
- **components**: Array of components to render on the page

### Component Configuration

Each component in a page can have:

- **type**: Component class name (must be exported from mobile-components)
- **showWhen**: Conditional rendering ('anonymous', 'authenticated', or always show)
- **inputs**: Object of input properties to pass to the component

## Supported Page Types

### Public Pages (secure: false)
- **Authentication**: login, signup, otp, validate, password
- **Landing**: landing, home, tab2
- **Games**: games/home, games/categories, games/single, games/all, games/dashboard, games/favourites, games/how-to-play
- **Shopping**: stores, store-detail, virtual, app/stores
- **Communication**: chat, notifications

### Secure Pages (secure: true)
- **Account**: profile, profile-details, profileremove, security
- **Financial**: transactions, statements, virtualcard
- **Dashboard**: dashboard (secure home page)
- **Settings**: settings, notification-settings, notification-admin, notification-analytics
- **Support**: contactus

## Component Library Integration

All page components are sourced from the `mobile-components` library (`projects/mobile-components/`). The dynamic system automatically:

1. Resolves component names to actual component classes
2. Creates component instances dynamically
3. Passes configured inputs to components
4. Handles component lifecycle and cleanup

### Available Components

Key components available for pages include:
- **Layout**: HeadLogoComponent, HomeHeaderComponent, HomeNavigationComponent
- **UI**: ButtonComponent, CardComponent, ListComponent, InputComponent
- **Games**: PagesLandingThemesGamesComponent, AllGamesComponent, CategoriesComponent, GamesSingleComponent
- **Auth**: PagesLoginTheme1Component, SignupComponent, ValidateComponent
- **Content**: ParagraphComponent, MessageComponent

## Authentication Integration

The system integrates with KeyCloak authentication:

- **Anonymous users**: Can access public pages (secure: false)
- **Authenticated users**: Can access both public and secure pages
- **Conditional rendering**: Components can show/hide based on auth status using `showWhen`
- **Auto-redirect**: Secure pages automatically redirect to login if not authenticated

## Navigation System

The app includes a dynamic navigation system that reads from environment configuration:

```typescript
// Navigation routes in environment
navigation: {
  routes: [
    {
      path: '/public/home',
      icon: 'home-outline', 
      label: 'Home',
      main: true,
      sidebar: false,
      more: false
    }
    // ... more routes
  ]
}
```

## Development Workflow

### Adding a New Page

1. **Define page in environment.ts**:
```typescript
{
  title: 'my-new-page',
  path: 'my-new-page', // or 'category/my-new-page' for nested
  secure: false, // or true for authenticated pages
  class: 'bg-base h-full',
  components: [
    {
      type: 'HeadLogoComponent',
      inputs: {
        logo: 'assets/images/logo.png',
        title: 'My New Page'
      }
    }
  ]
}
```

2. **Ensure components exist in mobile-components library**
3. **Navigate to page**: `/public/my-new-page` or `/secure/my-new-page`

### Debugging

Enable debug mode by setting `debug = true` in the dynamic page components to see detailed console logs about:
- Page loading process
- Component resolution
- Authentication checks
- Route parameter parsing

## Key Benefits

1. **No Code Deployment**: Pages can be added/modified via environment configuration
2. **Component Reusability**: All components are sourced from shared library
3. **Flexible Routing**: Supports nested paths and complex URL structures  
4. **Authentication Integration**: Built-in auth checks and conditional rendering
5. **Maintainability**: Single source of truth for page configuration
6. **Scalability**: Easy to add new pages without touching routing code

## Migration from Hardcoded Routes

The system has been fully migrated from hardcoded routes:
- ✅ All component directories removed (games/, contactus/, profile/, etc.)
- ✅ All routing modules use only dynamic patterns
- ✅ All pages defined in environment configuration
- ✅ Legacy route redirects in place for compatibility
- ✅ Build and runtime validation successful

This documentation reflects the current state where **ALL pages are loaded dynamically** with no hardcoded routes remaining.

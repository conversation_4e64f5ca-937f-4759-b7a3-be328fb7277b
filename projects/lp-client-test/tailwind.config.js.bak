/** @type {import('tailwindcss').Config} */
import { withLoyaltyUI } from "./src/tailwind/index.js";
import colors from "tailwindcss/colors";
import { fontFamily } from "tailwindcss/defaultTheme";
import { defaultTheme } from "./src/tailwind/themes.js";
import plugin from "tailwindcss/plugin";
import messagePlugin from "./src/tailwind/plugins/message.js";
import accordionPlugin from "./src/tailwind/plugins/accordian.js";
import autocompletePlugin from "./src/tailwind/plugins/autocomplete.js";
import { safelist, headingPlugin } from "./src/tailwind/plugins/heading.js";
import avatarPlugin from "./src/tailwind/plugins/avatar.js";
import avatarGroup from "./src/tailwind/plugins/avatargroup.js";
import breadcrumb from "./src/tailwind/plugins/breadcrumb.js";
import button from "./src/tailwind/plugins/button.js";
import buttonAction from "./src/tailwind/plugins/buttonAction.js";
import buttonClose from "./src/tailwind/plugins/buttonClose.js";
import buttonGroup from "./src/tailwind/plugins/buttonGroup.js";
import buttonIcon from "./src/tailwind/plugins/buttonIcon.js";
// import card from "./src/tailwind/plugins/card.js";
import checkbox from "./src/tailwind/plugins/checkbox.js";
import dropdownDivider from "./src/tailwind/plugins/dropdownDivider.js";
import dropdownItem from "./src/tailwind/plugins/dropdownItem.js";
import dropdown from "./src/tailwind/plugins/dropdown.js";
import focus from "./src/tailwind/plugins/focus.js";
import fullscreenDropfile from "./src/tailwind/plugins/fullscreenDropfile.js";

import iconBox from "./src/tailwind/plugins/iconBox.js";
import inputFileRegular from "./src/tailwind/plugins/inputFileRegular.js";
import inputFile from "./src/tailwind/plugins/inputFile.js";
import input from "./src/tailwind/plugins/input.js";
import inputNumber from "./src/tailwind/plugins/inputNumber.js";
import inputHelpText from "./src/tailwind/plugins/inputHelpText.js";
import kbd from "./src/tailwind/plugins/kbd.js";
import label from "./src/tailwind/plugins/label.js";
import link from "./src/tailwind/plugins/link.js";
import list from "./src/tailwind/plugins/list.js";
import listBox from "./src/tailwind/plugins/listBox.js";
import mark from "./src/tailwind/plugins/mark.js";
import mask from "./src/tailwind/plugins/mask.js";
import messageText from "./src/tailwind/plugins/messageText.js";
import message from "./src/tailwind/plugins/message.js";

import modal from "./src/tailwind/plugins/modal.js";
import pagination from "./src/tailwind/plugins/pagination.js";
import paragraph from "./src/tailwind/plugins/paragraph.js";
import placeholderPage from "./src/tailwind/plugins/placeholderPage.js";
import placeload from "./src/tailwind/plugins/placeload.js";
import progressCircle from "./src/tailwind/plugins/progressCircle.js";
import progress from "./src/tailwind/plugins/progress.js";
import prose from "./src/tailwind/plugins/prose.js";
import radio from "./src/tailwind/plugins/radio.js";
import select from "./src/tailwind/plugins/select.js";
import slimscroll from "./src/tailwind/plugins/slimscroll.js";
import snack from "./src/tailwind/plugins/snack.js";
import switchBall from "./src/tailwind/plugins/switchBall.js";

import switchThin from "./src/tailwind/plugins/switchThin.js";
import tabSlider from "./src/tailwind/plugins/tabSlider.js";
import tabs from "./src/tailwind/plugins/tabs.js";
import tag from "./src/tailwind/plugins/tag.js";
import text from "./src/tailwind/plugins/text.js";
import textarea from "./src/tailwind/plugins/textarea.js";
import themeSwitch from "./src/tailwind/plugins/themeSwitch.js";
import themeToggle from "./src/tailwind/plugins/themeToggle.js";
import toast from "./src/tailwind/plugins/toast.js";
import tooltips from "./src/tailwind/plugins/tooltip.js";
import typography from "@tailwindcss/typography";
// import { textShades } from "./src/tailwind/plugins/utilities.js";
/**
 * This is the Tailwind config file for the demo.
 * It extends the default config from @shuriken-ui/tailwind
 *
 * You can add/override your own customizations here.
 */

export default withLoyaltyUI({
  content: [
    "./src/**/*.{html,ts,css}",
    "../../dist/mobile-components/**/*.{html,ts,js,mjs}",
  ],
  theme: {
    extend: {
      colors: {
        secondary: colors.orange,
        primary: colors.blue,
        info: colors.teal,
        success: colors.green,
        warning: colors.yellow,
        danger: colors.red,
        muted: colors.gray,
      },
      keyframes: {
        bounceIn: {
          "0%": { transform: "scale(0.3)", opacity: "0" },
          "50%": { transform: "scale(1.05)" },
          "70%": { transform: "scale(0.9)" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        fadeInUp: {
          "0%": { opacity: "0", transform: "translateY(10px)" },
          "100%": { opacity: "1", transform: "translateY(0)" },
        },
      },
      animation: {
        bounceIn: "bounceIn 0.6s ease-out",
        fadeIn: "fadeIn 0.5s ease-out",
        fadeInUp: "fadeInUp 0.5s ease-out",
      },
      backgroundColor: {
        "nui-white": "var(--tw-bg-white, #ffffff)",
        "nui-50": "var(--tw-bg-muted-50, #f8fafc)",
        "nui-100": "var(--tw-bg-muted-100, #f1f5f9)",
        "nui-200": "var(--tw-bg-muted-200, #e2e8f0)",
        "nui-300": "var(--tw-bg-muted-300, #cbd5e1)",
        "nui-400": "var(--tw-bg-muted-400, #94a3b8)",
        "nui-500": "var(--tw-bg-muted-500, #64748b)",
        "nui-600": "var(--tw-bg-muted-600, #475569)",
        "nui-700": "var(--tw-bg-muted-700, #334155)",
        "nui-800": "var(--tw-bg-muted-800, #1e293b)",
        "nui-900": "var(--tw-bg-muted-900, #0f172a)",
        "nui-950": "var(--tw-bg-muted-950, #0a0e14)",
        "nui-black": "var(--tw-bg-muted-900, #000000)",
        "blue-50": "#e6f3fb",
        "blue-100": "#cce7f7",
        "blue-200": "#99cfef",
        "blue-300": "#66b7e7",
        "blue-400": "#339fdf",
        "blue-500": "#0576BC",
        "blue-600": "#045e96",
        "blue-700": "#034771",
        "blue-800": "#022f4b",
        "blue-900": "#011824",
        "green-50": "#e6f7ed",
        "green-100": "#ccefdb",
        "green-200": "#99dfb7",
        "green-300": "#66cf93",
        "green-500": "#0B9C49",
        "green-600": "#097d3a",
        "green-700": "#075e2c",
        "green-800": "#043e1d",
        "green-900": "#021f0f",
        "navy-50": "#e9e9ee",
        "navy-100": "#d3d3dd",
        "navy-200": "#a7a7bb",
        "navy-300": "#7b7b99",
        "navy-400": "#4f4f77",
        "navy-500": "#242057",
        "navy-600": "#1d1a46",
        "navy-700": "#161334",
        "navy-800": "#0e0d23",
        "navy-900": "#070611",
        "red-50": "#f5e8e8",
        "red-100": "#ebd1d2",
        "red-200": "#d7a3a5",
        "red-300": "#c37578",
        "red-400": "#af474b",
        "red-500": "#88181A",
        "red-600": "#6d1315",
        "red-700": "#520e10",
        "red-800": "#36090a",
        "red-900": "#1b0505",
        "teal-50": "#e6f3f4",
        "teal-100": "#cce7e9",
        "teal-200": "#99cfd3",
        "teal-300": "#66b7bd",
        "teal-400": "#339fa7",
        "teal-500": "#00747D",
        "yellow-50": "#fff9e6",
        "yellow-100": "#fff3cc",
        "yellow-200": "#ffe799",
        "yellow-300": "#ffdb66",
        "yellow-400": "#ffcf33",
        "yellow-500": "#FFC645",
        "yellow-600": "#cc9e37",
        "yellow-700": "#997729",
        "yellow-800": "#664f1c",
        "yellow-900": "#33280e",
        "purple-50": "#f4ebf6",
        "purple-100": "#e9d7ed",
        "purple-200": "#d3afdb",
        "purple-300": "#bd87c9",
        "purple-400": "#a75fb7",
        "purple-500": "#7F4392",
        "purple-600": "#663675",
        "purple-700": "#4c2858",
        "purple-800": "#331b3b",
        "purple-900": "#190d1d",
        "orange-50": "#fdeee9",
        "orange-100": "#fbddd3",
        "orange-200": "#f7bba7",
        "orange-300": "#f3997b",
        "orange-400": "#ef774f",
        "orange-500": "#F2652B",
        "orange-600": "#c25122",
        "orange-700": "#913d1a",
        "orange-800": "#612811",
      },
      textColor: {
        "nui-white": "var(--tw-text-white, #ffffff)",
        "nui-50": "var(--tw-text-muted-50, #f8fafc)",
        "nui-100": "var(--tw-text-muted-100, #f1f5f9)",
        "nui-200": "var(--tw-text-muted-200, #e2e8f0)",
        "nui-300": "var(--tw-text-muted-300, #cbd5e1)",
        "nui-400": "var(--tw-text-muted-400, #94a3b8)",
        "nui-500": "var(--tw-text-muted-500, #64748b)",
        "nui-600": "var(--tw-text-muted-600, #475569)",
        "nui-700": "var(--tw-text-muted-700, #334155)",
        "nui-800": "var(--tw-text-muted-800, #1e293b)",
        "nui-900": "var(--tw-text-muted-900, #0f172a)",
        "nui-950": "var(--tw-text-muted-950, #0a0e14)",
        "nui-black": "var(--tw-text-black, #000000)",
        "blue-50": "#e6f3fb",
        "blue-100": "#cce7f7",
        "blue-200": "#99cfef",
        "blue-300": "#66b7e7",
        "blue-400": "#339fdf",
        "blue-500": "#0576BC",
        "blue-600": "#045e96",
        "blue-700": "#034771",
        "blue-800": "#022f4b",
        "blue-900": "#011824",
        "green-50": "#e6f7ed",
        "green-100": "#ccefdb",
        "green-200": "#99dfb7",
        "green-300": "#66cf93",
        "green-500": "#0B9C49",
        "green-600": "#097d3a",
        "green-700": "#075e2c",
        "green-800": "#043e1d",
        "green-900": "#021f0f",
        "navy-50": "#e9e9ee",
        "navy-100": "#d3d3dd",
        "navy-200": "#a7a7bb",
        "navy-300": "#7b7b99",
        "navy-400": "#4f4f77",
        "navy-500": "#242057",
        "navy-600": "#1d1a46",
        "navy-700": "#161334",
        "navy-800": "#0e0d23",
        "navy-900": "#070611",
        "red-50": "#f5e8e8",
        "red-100": "#ebd1d2",
        "red-200": "#d7a3a5",
        "red-300": "#c37578",
        "red-400": "#af474b",
        "red-500": "#88181A",
        "red-600": "#6d1315",
        "red-700": "#520e10",
        "red-800": "#36090a",
        "red-900": "#1b0505",
        "teal-50": "#e6f3f4",
        "teal-100": "#cce7e9",
        "teal-200": "#99cfd3",
        "teal-300": "#66b7bd",
        "teal-400": "#339fa7",
        "teal-500": "#00747D",
        "yellow-50": "#fff9e6",
        "yellow-100": "#fff3cc",
        "yellow-200": "#ffe799",
        "yellow-300": "#ffdb66",
        "yellow-400": "#ffcf33",
        "yellow-500": "#FFC645",
        "yellow-600": "#cc9e37",
        "yellow-700": "#997729",
        "yellow-800": "#664f1c",
        "yellow-900": "#33280e",
        "purple-50": "#f4ebf6",
        "purple-100": "#e9d7ed",
        "purple-200": "#d3afdb",
        "purple-300": "#bd87c9",
        "purple-400": "#a75fb7",
        "purple-500": "#7F4392",
        "purple-600": "#663675",
        "purple-700": "#4c2858",
        "purple-800": "#331b3b",
        "purple-900": "#190d1d",
        "orange-50": "#fdeee9",
        "orange-100": "#fbddd3",
        "orange-200": "#f7bba7",
        "orange-300": "#f3997b",
        "orange-400": "#ef774f",
        "orange-500": "#F2652B",
        "orange-600": "#c25122",
        "orange-700": "#913d1a",
        "orange-800": "#612811",
      },
    },
  },
  darkMode: "class",
  safelist,
  plugins: [
    // headingPlugin,
    // messagePlugin,
    // accordionPlugin,
    // autocompletePlugin,
    // avatarPlugin,
    // avatarGroup,
    // breadcrumb,
    // button,
    // buttonAction,
    // buttonClose,
    // buttonGroup,
    // buttonIcon,
    // button,
    // // card,
    // checkbox,
    // dropdownDivider,
    // dropdownItem,
    // dropdown,
    // focus,
    // fullscreenDropfile,
    // iconBox,
    // inputFileRegular,
    // inputFile,
    // input,
    // inputNumber,
    // inputHelpText,
    // kbd,
    // label,
    // link,
    // list,
    // listBox,
    // mark,
    // mask,
    // messageText,
    // message,
    // modal,
    // pagination,
    // paragraph,
    // placeholderPage,
    // placeload,
    // progressCircle,
    // progress,
    // prose,
    // radio,
    // select,
    // slimscroll,
    // snack,
    // switchBall,
    // switchThin,
    // tabSlider,
    // tabs,
    // tag,
    // text,
    // textarea,
    // themeSwitch,
    // themeToggle,
    // toast,
    // tooltips,
    plugin(({ addUtilities }) => {}),
    typography,
  ],
});

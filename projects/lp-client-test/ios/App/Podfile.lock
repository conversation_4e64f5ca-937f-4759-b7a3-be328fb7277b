PODS:
  - Capacitor (6.1.2):
    - Capac<PERSON><PERSON><PERSON>ova
  - Capacitor<PERSON>pp (6.0.1):
    - Capacitor
  - CapacitorBrowser (6.0.2):
    - Capacitor
  - CapacitorCamera (6.0.2):
    - Capacitor
  - Capacitor<PERSON>ordova (6.1.2)
  - CapacitorDevice (6.0.1):
    - Capacitor
  - CapacitorFilesystem (6.0.1):
    - Capacitor
  - CapacitorGeolocation (6.0.1):
    - Capacitor
  - CapacitorGoogleMaps (5.4.1):
    - Capacitor
    - Google-Maps-iOS-Utils (~> 4.1)
    - GoogleMaps (~> 7.3)
  - CapacitorHaptics (6.0.1):
    - Capacitor
  - CapacitorKeyboard (6.0.2):
    - Capacitor
  - CapacitorNetwork (6.0.2):
    - Capacitor
  - CapacitorPreferences (6.0.2):
    - Capacitor
  - CapacitorStatusBar (6.0.1):
    - Capacitor
  - Google-Maps-iOS-Utils (4.2.2):
    - Google-Maps-iOS-Utils/Clustering (= 4.2.2)
    - Google-Maps-iOS-Utils/Geometry (= 4.2.2)
    - Google-Maps-iOS-Utils/GeometryUtils (= 4.2.2)
    - Google-Maps-iOS-Utils/Heatmap (= 4.2.2)
    - Google-Maps-iOS-Utils/QuadTree (= 4.2.2)
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Clustering (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Geometry (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/GeometryUtils (4.2.2):
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/Heatmap (4.2.2):
    - Google-Maps-iOS-Utils/QuadTree
    - GoogleMaps (~> 7.3)
  - Google-Maps-iOS-Utils/QuadTree (4.2.2):
    - GoogleMaps (~> 7.3)
  - GoogleMaps (7.4.0):
    - GoogleMaps/Maps (= 7.4.0)
  - GoogleMaps/Base (7.4.0)
  - GoogleMaps/Maps (7.4.0):
    - GoogleMaps/Base

DEPENDENCIES:
  - "Capacitor (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorApp (from `../../node_modules/@capacitor/app`)"
  - "CapacitorBrowser (from `../../node_modules/@capacitor/browser`)"
  - "CapacitorCamera (from `../../node_modules/@capacitor/camera`)"
  - "CapacitorCordova (from `../../node_modules/@capacitor/ios`)"
  - "CapacitorDevice (from `../../node_modules/@capacitor/device`)"
  - "CapacitorFilesystem (from `../../node_modules/@capacitor/filesystem`)"
  - "CapacitorGeolocation (from `../../node_modules/@capacitor/geolocation`)"
  - "CapacitorGoogleMaps (from `../../node_modules/@capacitor/google-maps`)"
  - "CapacitorHaptics (from `../../node_modules/@capacitor/haptics`)"
  - "CapacitorKeyboard (from `../../node_modules/@capacitor/keyboard`)"
  - "CapacitorNetwork (from `../../node_modules/@capacitor/network`)"
  - "CapacitorPreferences (from `../../node_modules/@capacitor/preferences`)"
  - "CapacitorStatusBar (from `../../node_modules/@capacitor/status-bar`)"

SPEC REPOS:
  trunk:
    - Google-Maps-iOS-Utils
    - GoogleMaps

EXTERNAL SOURCES:
  Capacitor:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorApp:
    :path: "../../node_modules/@capacitor/app"
  CapacitorBrowser:
    :path: "../../node_modules/@capacitor/browser"
  CapacitorCamera:
    :path: "../../node_modules/@capacitor/camera"
  CapacitorCordova:
    :path: "../../node_modules/@capacitor/ios"
  CapacitorDevice:
    :path: "../../node_modules/@capacitor/device"
  CapacitorFilesystem:
    :path: "../../node_modules/@capacitor/filesystem"
  CapacitorGeolocation:
    :path: "../../node_modules/@capacitor/geolocation"
  CapacitorGoogleMaps:
    :path: "../../node_modules/@capacitor/google-maps"
  CapacitorHaptics:
    :path: "../../node_modules/@capacitor/haptics"
  CapacitorKeyboard:
    :path: "../../node_modules/@capacitor/keyboard"
  CapacitorNetwork:
    :path: "../../node_modules/@capacitor/network"
  CapacitorPreferences:
    :path: "../../node_modules/@capacitor/preferences"
  CapacitorStatusBar:
    :path: "../../node_modules/@capacitor/status-bar"

SPEC CHECKSUMS:
  Capacitor: 679f9673fdf30597493a6362a5d5bf233d46abc2
  CapacitorApp: 0bc633b4eae40a1f32cd2834788fad3bc42da6a1
  CapacitorBrowser: 83ec661a9ccd56c28a6851e4dd1ac33b181227ec
  CapacitorCamera: ed022171dbf3853e68eec877b4d78995378af6b7
  CapacitorCordova: f48c89f96c319101cd2f0ce8a2b7449b5fb8b3dd
  CapacitorDevice: 7097a1deb4224b77fd13a6e60a355d0062a5d772
  CapacitorFilesystem: 37fb3aa5c945b4539ab11c74a5c57925a302bf24
  CapacitorGeolocation: 39dca51d755f08ed1d43e51be55291a402cdc64f
  CapacitorGoogleMaps: 7764613bb8eab28f290c1ca76b6ff4ee32d3c50f
  CapacitorHaptics: fe689ade56ef20ec9b041a753c6da70c5d8ec9a9
  CapacitorKeyboard: 2700f9b18687be021e28b5a09b59eb151a46d5e0
  CapacitorNetwork: 8796cf1f1104a00b289957b6150b7c60e1c2a8d3
  CapacitorPreferences: e8284bf740cf8c6d3f25409af3c01df87dfeb5a1
  CapacitorStatusBar: b81d4fb5d4e0064c712018071b3ab4b810b39a63
  Google-Maps-iOS-Utils: f77eab4c4326d7e6a277f8e23a0232402731913a
  GoogleMaps: 032f676450ba0779bd8ce16840690915f84e57ac

PODFILE CHECKSUM: 6e651ab3a764690371d5dafd9721216baf962151

COCOAPODS: 1.15.2

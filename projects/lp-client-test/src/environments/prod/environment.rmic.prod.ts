// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: true,
  env: 'LIVE',
  lssConfig: {
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '991162987',
    apiIdKeyStart: 'RMICMOBLIVE_3456517457_START',
    apiIdKeyEnd: 'RMICMOBLIVE_75498653454_END',
    appCode: 'start',
    appName: 'Make It With Mica',
    appVersion: '0.0.1',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.49975999318031,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'http://alpine',
    apiBaseUrl: 'https://mica.loyaltyplus.aero/',
    logEndpoint: 'https://mica.loyaltyplus.aero/extsecure/tools/logservice',
    configAPIUrl: 'http://{hostname}/config/api/v1/',
    termsConditions:
      'https://rmic.loyaltyplus.aero/rmic/rmic/terms/Mica_Terms_Conditions.pdf',
    pointsExpire: false,
    pointsTitle: 'Points',
    memberPhone: {
      dialCode: '+27',
      nationalNumber: '',
    },
    memberCard: '',

    telephone: {
      selectFirstCountry: true,
      preferredCountries: ['za'],
      onlyCountries: ['za', 'ls', 'bw', 'na', 'sz', 'mz'],
    },
    theme: {
      layout: 'sidebar',
      backgroundImage: '',
      colours: {
        primary: '#f5821f',
        primaryContrast: '#ffffff',
        primaryShade: '#FFA500',
        primaryTint: '#ff6600',

        secondary: '#ffd400',
        secondaryContrast: '#ffffff',
        secondaryShade: '#4854e0',
        secondaryTint: '#6370ff',

        tertiary: '#ffd400',
        tertiaryContrast: '#ffffff',
        tertiaryShade: '#4854e0',
        tertiaryTint: '#6370ff',

        success: '#2dd36f',
        successContrast: '#000000',
        successShade: '#28ba62',
        successTint: '#42d77d',

        warning: '#ffc409',
        warningContrast: '#000000',
        warningShade: '#e0ac08',
        warningTint: '#ffca22',

        danger: '#eb445a',
        dangerContrast: '#ffffff',
        dangerShade: '#cf3c4f',
        dangerTint: '#ed576b',

        medium: '#92949c',
        mediumContrast: '#000000',
        mediumShade: '#808289',
        mediumTint: '#9d9fa6',

  base: '#16315d',
        baseContrast: '#ffffff',
        baseShade: '#16315d',
        baseTint: '#4c8dff',

        light: '#f4f5f8',
        lightContrast: '#000000',
        lightShade: '#d7d8da',
        lightTint: '#f5f6f9',

        //--ion-background-color: '#f6f6f6',
        //--ion-background-color: transparent',

        // --ion-text-color: '#737373',
        // --ion-text-color: '#fff',

        // --ion-item-color: '#737373',

        step50: '#f8f8f8',
        step100: '#f1f1f1',
        step150: '#eaeaea',
        step200: '#e3e3e3',
        step250: '#dcdcdc',
        step300: '#d5d5d5',
        step350: '#cecece',
        step400: '#c7c7c7',
        step450: '#c0c0c0',
        step500: '#b9b9b9',
        step550: '#b2b2b2',
        step600: '#ababab',
        step650: '#a4a4a4',
        step700: '#9d9d9d',
        step750: '#969696',
        step800: '#8f8f8f',
        step850: '#888888',
        step900: '#818181',
        step950: '#7a7a7a',
      },
    },
    forms: {
      login: {
        options: {
          email: true,
          google: true,
        },
      },
      contactus: {
        categories: ['General', 'Complaint'],
      },
    },
    pages: {
      landing: {
        themes: ['theme-1', 'theme-2', 'theme-3', 'theme-4'],
        theme: 'theme-1',
        class: 'bg-base h-full',
        title: {
          text: 'Welcome',
          class: 'text-primary-500',
        },
        subtitle: {
          text: 'Welcome',
          class: 'text-primary-500',
        },
        balance: {
          text: 'Welcome',
          class: 'text-center w-90 primary rounded-lg mx-auto p-4 shadow mt-8',
        },
        action_card: {
          class: 'mt-8 px-2',
          profile: {
            text: 'Profile',
            class:
              'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex',
            class_icon_outer: 'action-icon text-baseShade',
            class_icon: 'w-4 h-4 -mt-4',
            class_text: 'text-xl -mt-5',

            icon: 'person-circle-outline',
          },
          card: {
            text: 'Card',
            class:
              'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex',
            class_icon_outer: 'action-icon text-baseShade',
            class_icon: 'w-4 h-4 -mt-4',
            class_text: 'text-xl -mt-5',

            icon: 'card-outline',
          },
          history: {
            text: 'Transactions',
            class:
              'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex',
            class_icon_outer: 'action-icon text-baseShade',
            class_icon: 'w-4 h-4 -mt-4',
            class_text: 'text-xl -mt-5',

            icon: 'cart-outline',
          },
          stores: {
            text: 'Stores',
            class:
              'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex',
            class_icon_outer: 'action-icon text-baseShade',
            class_icon: 'w-4 h-4 -mt-4',
            class_text: 'text-xl -mt-5',

            icon: 'location-outline',
          },
          contact: {
            text: 'Contact Us',
            class:
              'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex',
            class_icon_outer: 'action-icon text-baseShade',
            class_icon: 'w-4 h-4 -mt-4',
            class_text: 'text-xl -mt-5',

            icon: 'call-outline',
          },
        },
        icon: 'assets/images/make-it.png',
        loggedinIcon: 'assets/images/make-it.png',
      },
      login: {
        themes: ['theme-1', 'theme-2', 'theme-3', 'theme-4'],
        class: 'bg-base h-screen',
        theme: 'theme-1',
        title: {
          text: 'Welcome',
          class: 'text-primaryContrast text-4xl text-center',
        },

        subtitle: {
          text: 'to Make It With Mica',
          class: 'text-primaryContrast text-center w-full',
        },
        icon: {
          src: 'assets/images/make-it.png',
          class: 'rounded-large text-center w-80 mx-auto',
        },
        auth_buttons: {
          class: ' px-4 mt-6',
          login: {
            text: 'Sign in',
            class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
          },
          signup: {
            text: 'Sign up',
            class:
              'primary text-sm text-center w-full p-5 rounded-lg shadow my-3',
          },
          password: {
            text: 'Forgot Password',
            class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
          },
        },
        social_buttons: {
          class: 'flex w-full text-center mt-6',
          facebook: {
            icon: 'logo-facebook',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
            size: 'large',
          },
          twitter: {
            icon: 'logo-x',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          linkedin: {
            icon: 'logo-linkedin',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          youtube: {
            icon: 'logo-youtube',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          pinterest: {
            icon: 'logo-pinterest',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          instagram: {
            icon: 'logo-instagram',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
        },
        loggedinIcon: 'assets/images/make-it.png',
      },
      contact: {
        categoryCode: 'CNCT',
        icon: 'assets/images/make-it.png',
        title: 'Welcome',
        subtitle: 'Let us demo!',
      },
      profile: {
        preferencesCode: 'PART',
      },
    },
    contact: {
      callCenter: '012 141 3596',
    },
    socials: {
      facebook: 'https://www.facebook.com/micahardware',
      twitter: 'https://twitter.com/micahardware',
      linkedin:
        'https://www.linkedin.com/company/mica-hardware?originalSubdomain=za',
      youtube: 'https://www.youtube.com/channel/UCrCGdzTbu8xWKpubdHz9VjA',
      pinterest: 'https://za.pinterest.com/micahardware/',
      instagram: 'https://www.instagram.com/micahardware/?hl=en',
    },
    navigation: {
      sidebarTitle: 'Mica',
      sidebarIcon: 'assets/images/make-it.png',
      type: 'sidebar',
      routes: [
        {
          id: 'Login',
          text: 'Login',
          link: 'login',
          icon: '',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
        },
        {
          id: 'Register',
          text: 'Register',
          link: 'register',
          icon: '',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
        },
        {
          id: 'Logout',
          text: 'Logout',
          link: 'logout',
          icon: '',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
        },
        {
          id: 'Home',
          text: 'Home',
          link: '/',
          icon: 'home',
          active: true,
          sidebar: true,
          admin: false,

          sort: 0,
        },
        {
          id: 'Profile',
          text: 'Profile',
          link: '/app/account',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,

          sort: 0,
        },
        {
          id: 'Card',
          text: 'Card',
          link: '/app/virtualcard',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
        },
        {
          id: 'Transactions',
          text: 'Transactions',
          link: '/app/transactions',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
        },
        {
          id: 'Stores',
          text: 'Stores',
          link: '/app/stores',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
        },
        {
          id: 'Contact',
          text: 'Contact Us',
          link: '/secure/contactus',
          icon: '',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
        },
        // {
        //   id: 'Settings',
        //   text: 'Settings',
        //   link: '/secure/profile',
        //   icon: '',
        //   active: true,
        //   sidebar: true,
        //   admin: false,
        //   sort: 0,
        // },
      ],
    },
    authConfig: {
      issuer: 'https://auth.loyaltyplus.aero/auth/realms/Mica',
      clientId: 'mobile-app',
      logoutUrl: '/',
      url: 'https://auth.loyaltyplus.aero/auth',
      realm: 'Mica',
      initOptions: {
        adapter: 'capacitor-native',
        responseType: 'code',
        scope: 'openid profile email offline_access',
        redirectUri: 'micaloyalty://home',
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          window.location.origin.replace('.aero', '.aero/lp-mobile') +
          '/assets/silent-check-sso.html',
      },
    },
    games: {
      globalConfig: {
        version: '1.0.0',
        defaultLanguage: 'en',
        saveUserProgress: true,
        enableSound: true,
        enableMusic: true,
        themes: {
          defaultBackgroundImage: 'assets/images/default-bg.jpg',
        },
      },
      categories: {
        business: {
          name: 'Business Games',
          backgroundImage: 'assets/images/business-bg.jpg',
          games: {
            spinthewheel: {
              name: 'Spin a Wheel',
              backgroundImage: 'assets/images/wheel-bg.jpg',
              config: {
                sections: [
                  {
                    id: 1,
                    label: '10 Points',
                    value: 10,
                    probability: 0.3,
                    backgroundColor: '#FF5733',
                  },
                  {
                    id: 2,
                    label: '20 Points',
                    value: 20,
                    probability: 0.2,
                    backgroundColor: '#33FF57',
                  },
                ],
                spinFrequency: {
                  type: 'daily',
                  resetsAt: '00:00',
                  timezone: 'UTC',
                  spinsAllowed: 3,
                },
                wheelSections: 8,
                spinDuration: 5000,
                minSpinSpeed: 0.1,
                maxSpinSpeed: 2.0,
                prizes: [],
                wheelDesign: {
                  borderColor: '#000000',
                  borderWidth: 2,
                  centerImage: 'assets/images/wheel-center.png',
                  pointerImage: 'assets/images/pointer.png',
                },
              },
            },
            locationBased: {
              name: 'Location Based Photo',
              backgroundImage: 'assets/images/location-bg.jpg',
              config: {
                locations: [
                  {
                    id: 'loc1',
                    name: 'Central Park',
                    latitude: 40.785091,
                    longitude: -73.968285,
                    radius: 100,
                    points: 100,
                    description: 'Take a selfie at Central Park',
                    requiredTags: ['nature', 'park'],
                  },
                ],
                maxPhotoSize: 5242880,
                allowedFileTypes: ['jpg', 'jpeg', 'png'],
                requireLocation: true,
                maxDistanceMeters: 100,
                photoQualityMin: 0.7,
              },
            },
          },
        },
        arcade: {
          name: 'Arcade Games',
          backgroundImage: 'assets/images/arcade-bg.jpg',
          games: {
            memory: {
              name: 'Memory',
              backgroundImage: 'assets/images/memory-bg.jpg',
              config: {
                levels: [
                  {
                    level: 1,
                    gridSize: { rows: 2, columns: 2 },
                    timeLimit: 30,
                    points: 100,
                  },
                  {
                    level: 2,
                    gridSize: { rows: 4, columns: 4 },
                    timeLimit: 60,
                    points: 300,
                  },
                  {
                    level: 3,
                    gridSize: { rows: 6, columns: 6 },
                    timeLimit: 120,
                    points: 500,
                  },
                ],
                cardImages: [
                  {
                    id: 1,
                    url: 'assets/images/memory/card1.jpg',
                    name: 'Card 1',
                  },
                  {
                    id: 2,
                    url: 'assets/images/memory/card2.jpg',
                    name: 'Card 2',
                  },
                ],
                cardBackImage: 'assets/images/memory/card-back.jpg',
                matchTime: 1000,
                difficulty: 'medium',
                themeOptions: ['classic', 'animals', 'numbers'],
              },
            },
            game2048: {
              name: '2048',
              backgroundImage: 'assets/images/2048-bg.jpg',
              config: {
                gridSize: 4,
                winningTile: 2048,
                animationSpeed: 200,
                swipeThreshold: 50,
                colors: {
                  '2': '#EEE4DA',
                  '4': '#EDE0C8',
                  '8': '#F2B179',
                },
                tileDesign: {
                  borderRadius: '3px',
                  fontSize: {
                    small: '24px',
                    medium: '32px',
                    large: '48px',
                  },
                },
                customTileImages: {
                  enabled: false,
                  images: {
                    '2': 'assets/images/2048/tile2.png',
                    '4': 'assets/images/2048/tile4.png',
                  },
                },
              },
            },
            snake: {
              name: 'Snake',
              backgroundImage: 'assets/images/snake-bg.jpg',
              config: {
                levels: [
                  {
                    level: 1,
                    gridSize: { width: 10, height: 10 },
                    initialSpeed: 100,
                    speedIncrease: 2,
                    pointsPerFood: 10,
                  },
                  {
                    level: 2,
                    gridSize: { width: 15, height: 15 },
                    initialSpeed: 150,
                    speedIncrease: 3,
                    pointsPerFood: 20,
                  },
                  {
                    level: 3,
                    gridSize: { width: 20, height: 20 },
                    initialSpeed: 200,
                    speedIncrease: 4,
                    pointsPerFood: 30,
                  },
                ],
                foodTypes: [
                  {
                    type: 'regular',
                    points: 1,
                    imageUrl: 'assets/images/snake/apple.png',
                    probability: 0.7,
                  },
                  {
                    type: 'bonus',
                    points: 3,
                    imageUrl: 'assets/images/snake/golden-apple.png',
                    probability: 0.3,
                  },
                ],
                snakeDesign: {
                  headImage: 'assets/images/snake/head.png',
                  bodyImage: 'assets/images/snake/body.png',
                },
              },
            },
            minesweeper: {
              name: 'Minesweeper',
              backgroundImage: 'assets/images/minesweeper-bg.jpg',
              config: {
                difficulties: {
                  beginner: {
                    width: 9,
                    height: 9,
                    mines: 10,
                  },
                  intermediate: {
                    width: 16,
                    height: 16,
                    mines: 40,
                  },
                  expert: {
                    width: 30,
                    height: 16,
                    mines: 99,
                  },
                },
                defaultDifficulty: 'beginner',
                customization: {
                  tileSize: 30,
                  mineImage: 'assets/images/minesweeper/mine.png',
                  flagImage: 'assets/images/minesweeper/flag.png',
                  tileImages: {
                    covered: 'assets/images/minesweeper/covered.png',
                    uncovered: 'assets/images/minesweeper/uncovered.png',
                  },
                },
                animations: {
                  reveal: true,
                  explosion: true,
                },
              },
            },
            sudoku: {
              name: 'Sudoku',
              backgroundImage: 'assets/images/sudoku-bg.jpg',
              config: {
                difficulties: ['easy', 'medium', 'hard', 'expert'],
                defaultDifficulty: 'medium',
                highlightSameNumbers: true,
                showMistakes: true,
                hints: {
                  maximum: 3,
                  penaltyMinutes: 5,
                },
                design: {
                  gridLineWidth: {
                    normal: 1,
                    bold: 2,
                  },
                  colors: {
                    highlight: '#f0f0f0',
                    error: '#ffdddd',
                    success: '#ddffdd',
                  },
                  fonts: {
                    numbers: 'Arial',
                    notes: 'Arial',
                  },
                },
                timer: {
                  enabled: true,
                  format: 'mm:ss',
                },
              },
            },
            tetris: {
              name: 'Tetris',
              backgroundImage: 'assets/images/games/tetris.jpeg',
              config: {
                startLevel: 1,
                maxLevel: 20,
                boardSize: {
                  width: 10,
                  height: 20,
                },
                ghostPiece: true,
                holdPiece: true,
                showNext: 3,
                scoringSystem: {
                  singleLine: 100,
                  doubleLine: 300,
                  tripleLine: 500,
                  tetris: 800,
                },
                design: {
                  blockSize: 30,
                  colors: {
                    I: '#00f0f0',
                    O: '#f0f000',
                    T: '#a000f0',
                    S: '#00f000',
                    Z: '#f00000',
                    J: '#0000f0',
                    L: '#f0a000',
                  },
                  customBlocks: {
                    enabled: false,
                    blockImages: {
                      I: 'assets/images/tetris/i-block.png',
                      O: 'assets/images/tetris/o-block.png',
                    },
                  },
                },
                particles: {
                  enabled: true,
                  lineComplete: true,
                },
              },
            },
          },
        },
      },
    },
  },
};

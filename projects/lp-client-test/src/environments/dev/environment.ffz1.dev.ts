// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  env: 'DEV',
  client: 'ffz1',
  lssConfig: {
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '812275411',
    apiIdKeyStart: 'ITMANQA_038958288_START',
    apiIdKeyEnd: 'ITMANQA_039785672_END',
    appCode: 'start',
    appName: 'Loyalty Demo',
    appVersion: '0.0.12',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.49975999318031,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'http://alpine',
    apiBaseUrl: 'https://ffz1dev.loyaltyplus.aero/',
    logEndpoint: 'https://ffz1dev.loyaltyplus.aero/extsecure/tools/logservice',
    configAPIUrl: 'http://{hostname}/config/api/v1/',
    termsConditions: 'http://payroll.dv.lss.si/servlet/systemImage',
    icon: 'assets/images/logo.png',
    contact: {
      callCenter: '012 141 3596',
    },
    navigation: {
      sidebarTitle: 'Loyalty Plus',
      sidebarIcon: 'assets/images/logo.png',
      type: 'sidebar',
      routes: [
        {
          path: '/public/landing',
          label: 'Home',
          icon: 'home-outline',
          main: true,
          sidebar: true,
          more: false
        },
        {
          path: '/public/home',
          label: 'Home',
          icon: 'home-outline',
          main: true,
          sidebar: true,
          more: false
        },
        {
          path: '/public/games/home',
          label: 'Games',
          icon: 'game-controller-outline',
          main: false,
          sidebar: false,
          more: false
        },
        {
          path: '/secure/dashboard',
          label: 'Dashboard',
          icon: 'grid-outline',
          main: true,
          sidebar: true,
          more: false
        },
        {
          path: '/secure/virtualcard',
          label: 'Cards',
          icon: 'add-circle-outline',
          main: true,
          sidebar: false,
          more: false
        },
        {
          path: '/public/games/favourites',
          label: 'Favourites',
          icon: 'heart-outline',
          main: false,
          sidebar: false,
          more: false
        },
        {
          path: '/secure/profile',
          label: 'Profile',
          icon: 'person-outline',
          main: false,
          sidebar: false,
          more: true
        },
        {
          path: '/public/profile-details',
          label: 'Profile Details',
          icon: 'person-outline',
          main: false,
          sidebar: false,
          more: false
        },
        {
          id: 'Login',
          label: 'Login',
          link: 'login',
          icon: 'log-in-outline',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Register',
          label: 'Register',
          link: 'register',
          icon: 'person-add-outline',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Logout',
          label: 'Logout',
          link: 'logout',
          icon: 'log-out-outline',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Home',
          label: 'Home',
          link: '/',
          icon: 'home',
          active: true,
          sidebar: true,
          main: false,
          admin: false,

          sort: 0,
          exact: true,
        },
        {
          id: 'Profile',
          label: 'Profile',
          path: '/secure/profile',
          icon: 'person-outline',
          active: true,
          sidebar: true,
          more: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Card',
          label: 'Card',
          path: '/secure/virtualcard',
          icon: 'card-outline',
          active: true,
          sidebar: true,
          main: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Transactions',
          label: 'Transactions',
          path: '/secure/transactions',
          icon: 'card-outline',
          active: true,
          sidebar: true,
          admin: false,
          main: true,
          sort: 0,
          exact: false,
        },
        {
          id: 'Games',
          label: 'Games',
          path: '/public/games/home',
          icon: 'game-controller-outline',
          active: true,
          sidebar: true,
          admin: false,
          more: true,
          sort: 0,
        },
        {
          id: 'Stores',
          label: 'Stores',
          path: '/app/stores',
          icon: 'location-outline',
          active: true,
          sidebar: true,
          admin: false,
          more: true,
          sort: 0,
          exact: false,
        },
        {
          id: 'Contact',
          label: 'Contact Us',
          path: '/secure/contactus',
          icon: 'call-outline',
          active: true,
          sidebar: true,
          admin: true,
          more: true,
          sort: 0,
          exact: false,
        },
      ],
    },
    pages: [
      {
        title: 'home',
        path: 'home',
        secure: true,
        class: 'bg-red-500 h-full',
        components: [
          {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              padding: true,
              className: 'bg-black rounded shadow',
              title: 'Welcome',
              childrenConfig: [
                {
                  type: 'WelcomeComponent',
                  inputs: {
                    name: 'John',
                    surname: 'Doe'
                  }
                }
              ]
            }
          },
          {
            type: 'PagesLoginTheme1Component',
            showWhen: 'anonymous',
            inputs: {
              kc: 'kc',
              memberService: 'memberService',
              router: 'router',
              title: {
                text: 'Welcome',
                class: 'text-primaryContrast text-4xl text-center',
              },

              subtitle: {
                text: 'to Loyalty Plus Demo',
                class: 'text-primaryContrast text-center w-full',
              },
              icon: {
                src: 'assets/images/logo.png',
                class: 'rounded-large text-center w-80 mx-auto',
              },
              auth_buttons: {
                class: ' px-4 mt-6',
                login: {
                  text: 'Sign in',
                  class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
                },
                signup: {
                  text: 'Sign up',
                  class:
                    'primary text-sm text-center w-full p-5 rounded-lg shadow my-3',
                },
                password: {
                  text: 'Forgot Password',
                  class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
                },
              },
              social_buttons: {
                class: 'flex w-full text-center mt-6',
                facebook: {
                  icon: 'logo-facebook',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                  size: 'large',
                },
                twitter: {
                  icon: 'logo-x',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                linkedin: {
                  icon: 'logo-linkedin',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                youtube: {
                  icon: 'logo-youtube',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                pinterest: {
                  icon: 'logo-pinterest',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                instagram: {
                  icon: 'logo-instagram',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
              },
              loggedinIcon: 'assets/images/logo.png',
            }
          },
        ],
      },
      {
        title: 'profile',
        path: 'profile',
        secure: false,
        class: 'bg-blue-500 h-full ion-padding',
        components: [
          {
            type: 'ProfileComponent',
            showWhen: 'authenticated',
            config: {
              avatarUrl: '{profile.avatarUrl}',
              name: '{profile.name}',
              email: '{profile.email}',
              balance: '{profile.balance}',
              buttons: [
                {
                  text: 'Edit Profile',
                  navigation: '/public/profile-details',
                },
                {
                  text: 'View Transactions',
                  action: 'viewTransactions',
                },
              ],
            },
          },
          {
            type: 'ButtonComponent',
            showWhen: 'authenticated',
            config: {
              text: 'Go Home',
              expand: 'block',
              fill: 'solid',
              color: 'primary',
              navigation: '/public/home',
              class: 'ion-margin'
            },
          },
          {
            type: 'HeadLogoComponent',
            showWhen: 'anonymous',
            config: {
              logoUrl: 'assets/images/logo.png',
              title: 'View Your Profile',
              subtitle: 'Please login or register to access your full profile details and benefits.',
              height: 'auto',
              width: 'auto',
              class: 'ion-text-center ion-padding'
            }
          },
          {
            type: 'ButtonComponent',
            showWhen: 'anonymous',
            inputs: {
              label: 'Login Now',
              color: 'primary',
              expand: 'block',
              routerLink: '/public/login',
              class: 'ion-margin'
            }
          },
          {
            type: 'ButtonComponent',
            showWhen: 'anonymous',
            inputs: {
              label: 'Create an Account',
              color: 'secondary',
              expand: 'block',
              routerLink: '/public/register',
              class: 'ion-margin-horizontal'
            }
          }
        ]
      },
      {
        title: 'profile-details',
        path: 'profile-details',
        secure: true,
        class: 'bg-green-500 h-full',
        components: [
          {
            type: 'ProfileDetailsComponent',
            config: {
              title: 'Update Profile Details',
              profile: '{profile}', // Pass the entire profile object
            },
          },
          {
            type: 'ButtonComponent',
            config: {
              text: 'Back to Profile',
              expand: 'block',
              fill: 'solid',
              color: 'primary',
              navigation: '/public/profile',
            },
          },
        ],
      },
      {
        title: 'login',
        path: 'login',
        secure: false,
        class: 'bg-yellow-500 h-full',
        components: [
          {
            type: 'PagesLoginTheme1Component',
            config: {
              title: 'Login to your account',
              logo: 'assets/images/logo.png',
              backgroundImage: 'assets/images/login-bg.jpg',
              registerLink: '/public/register',
            },
            "inputs": {
              "kc": "kc",
              "memberService": "memberService",
              "router": "router",
              "title": {
                "text": "Welcome",
                "class": "text-primaryContrast text-4xl text-center"
              },
              "subtitle": {
                "text": "to Loyalty Plus Demo",
                "class": "text-primaryContrast text-center w-full"
              },
              "icon": {
                "src": "assets/images/logo.png",
                "class": "rounded-large text-center w-80 mx-auto"
              },
              "auth_buttons": {
                "class": " px-4 mt-6",
                "login": {
                  "text": "Sign in",
                  "class": "primary text-sm text-center w-full p-5 rounded-lg shadow"
                },
                "signup": {
                  "text": "Sign up",
                  "class": "primary text-sm text-center w-full p-5 rounded-lg shadow my-3"
                },
                "password": {
                  "text": "Forgot Password",
                  "class": "primary text-sm text-center w-full p-5 rounded-lg shadow"
                }
              },
              "social_buttons": {
                "class": "flex w-full text-center mt-6",
                "facebook": {
                  "icon": "logo-facebook",
                  "class": "bg-clear text-primaryContrast rounded-full p-1 text-2xl",
                  "size": "large"
                },
                "twitter": {
                  "icon": "logo-x",
                  "size": "large",
                  "class": "bg-clear text-primaryContrast rounded-full p-1 text-2xl"
                },
                "linkedin": {
                  "icon": "logo-linkedin",
                  "size": "large",
                  "class": "bg-clear text-primaryContrast rounded-full p-1 text-2xl"
                },
                "youtube": {
                  "icon": "logo-youtube",
                  "size": "large",
                  "class": "bg-clear text-primaryContrast rounded-full p-1 text-2xl"
                },
                "pinterest": {
                  "icon": "logo-pinterest",
                  "size": "large",
                  "class": "bg-clear text-primaryContrast rounded-full p-1 text-2xl"
                },
                "instagram": {
                  "icon": "logo-instagram",
                  "size": "large",
                  "class": "bg-clear text-primaryContrast rounded-full p-1 text-2xl"
                }
              },
              "loggedinIcon": "assets/images/logo.png"
            }
          },
        ],
      },
      {
        title: 'register',
        path: 'register',
        secure: false,
        class: 'bg-purple-500 h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            config: {
              logo: 'assets/images/logo.png',
              title: 'Register Account',
              subtitle: 'Join us now!',
            },
          },
          {
            type: 'ButtonComponent',
            config: {
              text: 'Already have an account? Login',
              expand: 'block',
              fill: 'clear',
              color: 'primary',
              navigation: '/public/login',
            },
          },
        ],
      },
      {
        title: 'Terms and Conditions',
        path: 'terms',
        secure: false,
        class: 'ion-padding h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            config: {
              title: 'Terms and Conditions',
              subtitle: 'Please review our terms and conditions.'
            }
          },
          {
            type: 'ButtonComponent',
            config: {
              text: 'View Terms and Conditions',
              action: 'openTermsLink',
              expand: 'block',
              color: 'primary',
              class: 'ion-margin-top'
            }
          },
          {
            type: 'ButtonComponent',
            config: {
              text: 'Back to Home',
              navigation: '/public/home',
              expand: 'block',
              color: 'light',
              class: 'ion-margin-top'
            }
          }
        ]
      },
      {
        title: 'dashboard',
        path: 'dashboard',
        secure: true,
        class: 'bg-base h-full',
        components: [
          {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              kc: 'kc',
              profile: 'profile',
              router: 'router',
              logo: 'assets/images/logo.png',
              backgroundImage: 'assets/images/background.jpg',
              title: 'Dashboard',
              subtitle: 'Welcome to your dashboard.',
            },
          },
        ],
      },
      {
        title: 'landing',
        path: 'landing',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              kc: 'kc',
              profile: 'profile',
              router: 'router',
              logo: 'assets/images/logo.png',
              backgroundImage: 'assets/images/background.jpg',
              title: 'Welcome to the Loyalty App',
              subtitle: 'Your rewards journey starts here.',
              buttons: [
                {
                  text: 'Login',
                  navigation: '/public/login',
                  color: 'primary',
                  fill: 'solid',
                },
                {
                  text: 'Register',
                  navigation: '/public/register',
                  color: 'secondary',
                  fill: 'outline',
                },
              ],
              features: [
                {
                  icon: 'star-outline',
                  title: 'Earn Points',
                  description: 'Get rewarded for every purchase.',
                },
                {
                  icon: 'gift-outline',
                  title: 'Redeem Rewards',
                  description: 'Use points for exclusive benefits.',
                },
                {
                  icon: 'flash-outline',
                  title: 'Exclusive Offers',
                  description: 'Access special deals and promotions.',
                },
              ],
            },
          },
          {
            type: 'PagesLoginTheme1Component',
            showWhen: 'anonymous',
            inputs: {
              kc: 'kc',
              memberService: 'memberService',
              router: 'router',
              title: {
                text: 'Welcome',
                class: 'text-primaryContrast text-4xl text-center',
              },

              subtitle: {
                text: 'to Loyalty Plus App',
                class: 'text-primaryContrast text-center w-full',
              },
              icon: {
                src: 'assets/images/logo.png',
                class: 'rounded-large text-center w-80 mx-auto',
              },
              auth_buttons: {
                class: 'px-4 mt-6',
              },
            },
          },
        ],
      },
      {
        title: 'otp-validation',
        path: 'otp-validation',
        secure: false, // Usually requires some temporary state, not full auth
        class: 'bg-gray-200 h-full flex items-center justify-center',
        components: [
          {
            type: 'OtpValidatorComponent',
            config: {
              title: 'Verify Your Identity',
              subtitle: 'Enter the OTP sent to your device',
              successNavigation: '/public/home', // Navigate on success
              resendAction: 'resendOtp' // Define action for resend
            }
          }
        ]
      },
      {
        title: 'home-test',
        path: 'home-test',
        secure: false,
        class: 'home-test-page bg-gray-100 min-h-screen',
        components: [
          {
            type: 'HomeHeaderComponent',
            showWhen: 'always',
            inputs: {
              config: {
                showLogo: true,
                logoUrl: 'assets/images/logo.png',
                title: 'Welcome to Loyalty Plus',
                subtitle: 'Your dynamic rewards journey starts here',
                titleClass: 'text-white text-2xl font-bold',
                subtitleClass: 'text-white opacity-90',
                showUserInfo: true,
                class: 'mb-4'
              }
            }
          },
          {
            type: 'HomeNavigationComponent',
            showWhen: 'authenticated',
            inputs: {
              config: {
                title: 'Quick Actions',
                showArrows: true,
                gridColumns: 2,
                items: [
                  {
                    label: 'Profile',
                    description: 'View and edit your profile',
                    icon: 'person-outline',
                    path: '/secure/profile',
                    showWhen: 'authenticated'
                  },
                  {
                    label: 'Transactions',
                    description: 'View your transaction history',
                    icon: 'card-outline',
                    path: '/secure/transactions',
                    showWhen: 'authenticated'
                  },
                  {
                    label: 'Virtual Card',
                    description: 'Access your loyalty card',
                    icon: 'wallet-outline',
                    path: '/secure/virtualcard',
                    showWhen: 'authenticated'
                  },
                  {
                    label: 'Games',
                    description: 'Play games and earn points',
                    icon: 'game-controller-outline',
                    path: '/public/games/home',
                    showWhen: 'always'
                  }
                ]
              }
            }
          },
          {
            type: 'HomeActionsComponent',
            showWhen: 'anonymous',
            inputs: {
              config: {
                primaryTitle: 'Get Started Today',
                secondaryTitle: 'Explore Features',
                authTitle: 'Join Our Community',
                socialTitle: 'Connect With Us',
                primaryActions: [
                  {
                    label: 'Get Started',
                    description: 'Begin your loyalty journey today',
                    icon: 'rocket-outline',
                    action: 'getStarted',
                    showWhen: 'anonymous'
                  }
                ],
                secondaryActions: [
                  {
                    label: 'Games',
                    icon: 'game-controller-outline',
                    path: '/public/games/home',
                    showWhen: 'always'
                  },
                  {
                    label: 'Stores',
                    icon: 'location-outline',
                    path: '/public/stores',
                    showWhen: 'always'
                  }
                ],
                authActions: [
                  {
                    label: 'Sign In',
                    icon: 'log-in-outline',
                    path: '/public/login',
                    showWhen: 'anonymous'
                  },
                  {
                    label: 'Create Account',
                    icon: 'person-add-outline',
                    path: '/public/register',
                    showWhen: 'anonymous'
                  }
                ]
              }
            }
          },
          {
            type: 'HomeActionsComponent',
            showWhen: 'authenticated',
            inputs: {
              config: {
                primaryTitle: 'Welcome Back!',
                secondaryTitle: 'Quick Actions',
                primaryActions: [
                  {
                    label: 'View Dashboard',
                    description: 'See your account overview',
                    icon: 'speedometer-outline',
                    path: '/secure/dashboard',
                    showWhen: 'authenticated'
                  }
                ],
                secondaryActions: [
                  {
                    label: 'Games',
                    icon: 'game-controller-outline',
                    path: '/public/games/home',
                    showWhen: 'always'
                  },
                  {
                    label: 'Stores',
                    icon: 'location-outline',
                    path: '/public/stores',
                    showWhen: 'always'
                  }
                ]
              }
            }
          }
        ]
      },
      {
        title: 'dashboard-test',
        path: 'dashboard-test',
        secure: true,
        class: 'dashboard-test-page bg-gray-50 min-h-screen',
        components: [
          {
            type: 'DashboardHeaderComponent',
            showWhen: 'authenticated',
            inputs: {
              config: {
                showLogo: true,
                logoUrl: 'assets/images/logo.png',
                showBalance: true,
                showUserInfo: true,
                showStats: true,
                balanceLabel: 'Current Balance',
                balanceUnit: 'points',
                class: 'mb-4'
              }
            }
          },
          {
            type: 'DashboardWelcomeComponent',
            showWhen: 'authenticated',
            inputs: {
              config: {
                showIcon: true,
                showQuickStats: true,
                subtitle: 'Here\'s what\'s happening with your account today.',
                balanceLabel: 'Points',
                randLabel: 'Value',
                notificationLabel: 'Notifications',
                actions: [
                  {
                    label: 'View Profile',
                    icon: 'person-outline',
                    path: '/secure/profile',
                    class: 'secondary'
                  }
                ]
              }
            }
          },
          {
            type: 'DashboardQuickActionsComponent',
            showWhen: 'authenticated',
            inputs: {
              config: {
                title: 'Quick Actions',
                showArrows: true,
                gridColumns: 2,
                actions: [
                  {
                    label: 'Profile',
                    description: 'View and edit your profile information',
                    icon: 'person-outline',
                    path: '/secure/profile',
                    showWhen: 'authenticated'
                  },
                  {
                    label: 'Security',
                    description: 'Manage your account security settings',
                    icon: 'shield-checkmark-outline',
                    path: '/secure/security',
                    showWhen: 'authenticated'
                  },
                  {
                    label: 'Transactions',
                    description: 'View your transaction history',
                    icon: 'card-outline',
                    path: '/secure/transactions',
                    showWhen: 'authenticated'
                  },
                  {
                    label: 'Virtual Card',
                    description: 'Access your digital loyalty card',
                    icon: 'wallet-outline',
                    path: '/secure/virtualcard',
                    showWhen: 'authenticated'
                  }
                ]
              }
            }
          },
          {
            type: 'DashboardSummaryComponent',
            showWhen: 'authenticated',
            inputs: {
              config: {
                title: 'Account Summary',
                showBalance: true,
                showMembership: true,
                showActivity: true,
                balanceTitle: 'Current Balance',
                balanceUnit: 'points',
                membershipTitle: 'Membership',
                activityTitle: 'Recent Activity',
                actions: [
                  {
                    label: 'View All Transactions',
                    icon: 'list-outline',
                    path: '/secure/transactions',
                    class: 'primary'
                  },
                  {
                    label: 'Download Statement',
                    icon: 'download-outline',
                    action: 'downloadStatement',
                    class: 'secondary'
                  }
                ]
              }
            }
          }
        ]
      },
      {
        title: 'profile-test',
        path: 'profile-test',
        secure: true,
        class: 'profile-test-page bg-gray-50 min-h-screen',
        components: [
          {
            type: 'ProfileHeaderComponent',
            showWhen: 'authenticated',
            inputs: {
              config: {
                showLogo: true,
                logoUrl: 'assets/images/logo.png',
                showBalance: true,
                showProfileInfo: true,
                showAvatar: true,
                showQuickStats: true,
                allowAvatarEdit: true,
                balanceLabel: 'Current Balance',
                balanceUnit: 'points',
                randLabel: 'Value',
                completenessLabel: 'Complete',
                class: 'mb-4'
              }
            }
          },
          {
            type: 'ProfileFormComponent',
            showWhen: 'authenticated',
            inputs: {
              config: {
                title: 'Edit Profile',
                showPersonalInfo: true,
                showContactInfo: true,
                showPreferences: true,
                showCancelButton: true,
                personalInfoTitle: 'Personal Information',
                contactInfoTitle: 'Contact Information',
                preferencesTitle: 'Preferences',
                givenNamesLabel: 'First Name',
                surnameLabel: 'Last Name',
                emailLabel: 'Email Address',
                dobLabel: 'Date of Birth',
                genderLabel: 'Gender',
                phoneLabel: 'Phone Number',
                addressLabel: 'Address',
                cityLabel: 'City',
                postalCodeLabel: 'Postal Code',
                emailNotificationsLabel: 'Receive email notifications',
                smsNotificationsLabel: 'Receive SMS notifications',
                marketingEmailsLabel: 'Receive marketing emails',
                submitButtonLabel: 'Save Changes',
                cancelButtonLabel: 'Cancel'
              }
            }
          },
          {
            type: 'ProfileSettingsComponent',
            showWhen: 'authenticated',
            inputs: {
              config: {
                title: 'Account Settings',
                showAccountSettings: true,
                showPrivacySettings: true,
                showNotificationSettings: true,
                showAppSettings: true,
                accountSettingsTitle: 'Account Settings',
                privacySettingsTitle: 'Privacy & Security',
                notificationSettingsTitle: 'Notifications',
                appSettingsTitle: 'App Settings'
              }
            }
          },
          {
            type: 'ProfileHelpComponent',
            showWhen: 'authenticated',
            inputs: {
              config: {
                title: 'Help & Support',
                showQuickActions: true,
                showContactSupport: true,
                showFAQ: true,
                showAppInfo: true,
                showFeedback: true,
                quickActionsTitle: 'Quick Help',
                contactSupportTitle: 'Contact Support',
                faqTitle: 'Frequently Asked Questions',
                appInfoTitle: 'App Information',
                feedbackTitle: 'Send Feedback',
                feedbackDescription: 'Help us improve by sharing your feedback',
                feedbackButtonLabel: 'Send Feedback'
              }
            }
          }
        ]
      },
    ],
    authConfig: {
      issuer: 'https://authdev.loyaltyplus.aero/auth/realms/DemoZ1',
      clientId: 'mobile-app',
      logoutUrl: '/',
      url: 'https://authdev.loyaltyplus.aero/auth',
      realm: 'DemoZ1',
      initOptions: {
        adapter: 'default',
        responseType: 'code',
        scope: 'openid profile email offline_access',
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          window.location.origin.replace('.aero', '.aero/lp-mobile') +
          '/assets/silent-check-sso.html',
      },
    },
    games: {
      globalConfig: {
        version: '1.0.0',
        defaultLanguage: 'en',
        saveUserProgress: true,
        enableSound: true,
        enableMusic: true,
        themes: {
          defaultBackgroundImage: 'assets/images/default-bg.jpg',
        },
      },
      categories: {
        business: {
          name: 'Business Games',
          backgroundImage: 'assets/images/business-bg.jpg',
          games: {
            spinthewheel: {
              name: 'Spin a Wheel',
              backgroundImage: 'assets/images/wheel-bg.jpg',
              config: {
                sections: [
                  {
                    id: 1,
                    label: '10 Points',
                    value: 10,
                    probability: 0.3,
                    backgroundColor: '#FF5733',
                  },
                  {
                    id: 2,
                    label: '20 Points',
                    value: 20,
                    probability: 0.2,
                    backgroundColor: '#33FF57',
                  },
                ],
                spinFrequency: {
                  type: 'daily',
                  resetsAt: '00:00',
                  timezone: 'UTC',
                  spinsAllowed: 3,
                },
                wheelSections: 8,
                spinDuration: 5000,
                minSpinSpeed: 0.1,
                maxSpinSpeed: 2.0,
                prizes: [],
                wheelDesign: {
                  borderColor: '#000000',
                  borderWidth: 2,
                  centerImage: 'assets/images/wheel-center.png',
                  pointerImage: 'assets/images/pointer.png',
                },
              },
            },
            locationBased: {
              name: 'Location Based Photo',
              backgroundImage: 'assets/images/location-bg.jpg',
              config: {
                locations: [
                  {
                    id: 'loc1',
                    name: 'Loyalty Plus',
                    latitude: -25.8494938,
                    longitude: 28.1896346,
                    radius: 200,
                    points: 100,
                    description: 'Take a selfie at Loyalty Plus',
                    requiredTags: ['office', 'building'],
                  },
                  {
                    id: 'loc2',
                    name: 'Loyalty Plus 2',
                    latitude: -25.8494838,
                    longitude: 28.1885346,
                    radius: 200,
                    points: 100,
                    description: 'Take a selfie at Loyalty Plus 2',
                    requiredTags: ['office', 'building'],
                  },
                  {
                    id: 'loc3',
                    name: 'Loyalty Plus 3',
                    latitude: -25.8495637,
                    longitude: 28.1948585,
                    radius: 200,
                    points: 100,
                    description: 'Take a selfie at Loyalty Plus 3',
                    requiredTags: ['office', 'building'],
                  },
                ],
                maxPhotoSize: 5242880,
                allowedFileTypes: ['jpg', 'jpeg', 'png'],
                requireLocation: true,
                maxDistanceMeters: 200,
                photoQualityMin: 0.7,
              },
            },
          },
        },
        arcade: {
          name: 'Arcade Games',
          backgroundImage: 'assets/images/arcade-bg.jpg',
          games: {
            memory: {
              name: 'Memory',
              backgroundImage: 'assets/images/memory-bg.jpg',
              config: {
                levels: [
                  {
                    level: 1,
                    gridSize: { rows: 2, columns: 2 },
                    timeLimit: 30,
                    points: 100,
                  },
                  {
                    level: 2,
                    gridSize: { rows: 4, columns: 4 },
                    timeLimit: 60,
                    points: 300,
                  },
                  {
                    level: 3,
                    gridSize: { rows: 6, columns: 6 },
                    timeLimit: 120,
                    points: 500,
                  },
                ],
                cardImages: [
                  {
                    id: 1,
                    url: 'assets/images/memory/card1.jpg',
                    name: 'Card 1',
                  },
                  {
                    id: 2,
                    url: 'assets/images/memory/card2.jpg',
                    name: 'Card 2',
                  },
                ],
                cardBackImage: 'assets/images/memory/card-back.jpg',
                matchTime: 1000,
                difficulty: 'medium',
                themeOptions: ['classic', 'animals', 'numbers'],
              },
            },
            game2048: {
              name: '2048',
              backgroundImage: 'assets/images/2048-bg.jpg',
              config: {
                gridSize: 4,
                winningTile: 2048,
                animationSpeed: 200,
                swipeThreshold: 50,
                colors: {
                  '2': '#EEE4DA',
                  '4': '#EDE0C8',
                  '8': '#F2B179',
                },
                tileDesign: {
                  borderRadius: '3px',
                  fontSize: {
                    small: '24px',
                    medium: '32px',
                    large: '48px',
                  },
                },
                customTileImages: {
                  enabled: false,
                  images: {
                    '2': 'assets/images/2048/tile2.png',
                    '4': 'assets/images/2048/tile4.png',
                  },
                },
              },
            },
            snake: {
              name: 'Snake',
              backgroundImage: 'assets/images/snake-bg.jpg',
              config: {
                levels: [
                  {
                    level: 1,
                    gridSize: { width: 10, height: 10 },
                    initialSpeed: 100,
                    speedIncrease: 2,
                    pointsPerFood: 10,
                  },
                  {
                    level: 2,
                    gridSize: { width: 15, height: 15 },
                    initialSpeed: 150,
                    speedIncrease: 3,
                    pointsPerFood: 20,
                  },
                  {
                    level: 3,
                    gridSize: { width: 20, height: 20 },
                    initialSpeed: 200,
                    speedIncrease: 4,
                    pointsPerFood: 30,
                  },
                ],
                foodTypes: [
                  {
                    type: 'regular',
                    points: 1,
                    imageUrl: 'assets/images/snake/apple.png',
                    probability: 0.7,
                  },
                  {
                    type: 'bonus',
                    points: 3,
                    imageUrl: 'assets/images/snake/golden-apple.png',
                    probability: 0.3,
                  },
                ],
                snakeDesign: {
                  headImage: 'assets/images/snake/head.png',
                  bodyImage: 'assets/images/snake/body.png',
                },
              },
            },
            minesweeper: {
              name: 'Minesweeper',
              backgroundImage: 'assets/images/minesweeper-bg.jpg',
              config: {
                difficulties: {
                  beginner: {
                    width: 9,
                    height: 9,
                    mines: 10,
                  },
                  intermediate: {
                    width: 16,
                    height: 16,
                    mines: 40,
                  },
                  expert: {
                    width: 30,
                    height: 16,
                    mines: 99,
                  },
                },
                defaultDifficulty: 'beginner',
                customization: {
                  tileSize: 30,
                  mineImage: 'assets/images/minesweeper/mine.png',
                  flagImage: 'assets/images/minesweeper/flag.png',
                  tileImages: {
                    covered: 'assets/images/minesweeper/covered.png',
                    uncovered: 'assets/images/minesweeper/uncovered.png',
                  },
                },
                animations: {
                  reveal: true,
                  explosion: true,
                },
              },
            },
            sudoku: {
              name: 'Sudoku',
              backgroundImage: 'assets/images/sudoku-bg.jpg',
              config: {
                difficulties: ['easy', 'medium', 'hard', 'expert'],
                defaultDifficulty: 'medium',
                highlightSameNumbers: true,
                showMistakes: true,
                hints: {
                  maximum: 3,
                  penaltyMinutes: 5,
                },
                design: {
                  gridLineWidth: {
                    normal: 1,
                    bold: 2,
                  },
                  colors: {
                    highlight: '#f0f0f0',
                    error: '#ffdddd',
                    success: '#ddffdd',
                  },
                  fonts: {
                    numbers: 'Arial',
                    notes: 'Arial',
                  },
                },
                timer: {
                  enabled: true,
                  format: 'mm:ss',
                },
              },
            },
            tetris: {
              name: 'Tetris',
              backgroundImage: 'assets/images/games/tetris.jpeg',
              config: {
                startLevel: 1,
                maxLevel: 20,
                boardSize: {
                  width: 10,
                  height: 20,
                },
                ghostPiece: true,
                holdPiece: true,
                showNext: 3,
                scoringSystem: {
                  singleLine: 100,
                  doubleLine: 300,
                  tripleLine: 500,
                  tetris: 800,
                },
                design: {
                  blockSize: 30,
                  colors: {
                    I: '#00f0f0',
                    O: '#f0f000',
                    T: '#a000f0',
                    S: '#00f000',
                    Z: '#f00000',
                    J: '#0000f0',
                    L: '#f0a000',
                  },
                  customBlocks: {
                    enabled: false,
                    blockImages: {
                      I: 'assets/images/tetris/i-block.png',
                      O: 'assets/images/tetris/o-block.png',
                    },
                  },
                },
                particles: {
                  enabled: true,
                  lineComplete: true,
                },
              },
            },
          },
        },
      },
    },
    useDemoProfile: true,

  },
};

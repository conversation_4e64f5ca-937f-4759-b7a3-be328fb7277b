// Demo environment with component IDs for testing the app builder
export const environment = {
  production: false,
  env: 'DEV',
  lssConfig: {
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '812275411',
    apiIdKeyStart: 'ITMANQA_038958288_START',
    apiIdKeyEnd: 'ITMANQA_039785672_END',
    appCode: 'start',
    appName: 'App Builder Demo',
    appVersion: '0.0.1',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.49975999318031,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'http://alpine',
    apiBaseUrl: 'https://ffz1dev.loyaltyplus.aero/',
    logEndpoint: 'https://ffz1dev.loyaltyplus.aero/extsecure/tools/logservice',
    configAPIUrl: 'http://{hostname}/config/api/v1/',
    termsConditions: 'http://payroll.dv.lss.si/servlet/systemImage',
    pointsExpire: false,
    pointsTitle: 'Points',
    showEdit: true,
    memberPhone: {
      dialCode: '+27',
      nationalNumber: '',
    },
    memberCard: '',
    telephone: {
      selectFirstCountry: true,
      preferredCountries: ['za'],
      onlyCountries: ['za', 'ls', 'bw', 'na', 'sz', 'mz'],
    },
    theme: {
      layout: 'sidebar',
      backgroundImage: '',
      colours: {
        primary: '#0e428e',
        primaryContrast: '#ffffff',
        primaryShade: '#16315d',
        primaryTint: '#4c8dff',
        secondary: '#f5821f',
        secondaryContrast: '#ffffff',
        secondaryShade: '#4854e0',
        secondaryTint: '#6370ff',
        tertiary: '#ffd400',
        tertiaryContrast: '#ffffff',
        tertiaryShade: '#4854e0',
        tertiaryTint: '#6370ff',
        success: '#2dd36f',
        successContrast: '#000000',
        successShade: '#28ba62',
        successTint: '#42d77d',
        warning: '#ffc409',
        warningContrast: '#000000',
        warningShade: '#e0ac08',
        warningTint: '#ffca22',
        danger: '#eb445a',
        dangerContrast: '#ffffff',
        dangerShade: '#cf3c4f',
        dangerTint: '#ed576b',
        medium: '#92949c',
        mediumContrast: '#000000',
        mediumShade: '#808289',
        mediumTint: '#9d9fa6',
        base: '#fff',
        baseContrast: '#000000',
        baseShade: '#808289',
        baseTint: '#9d9fa6',
        light: '#f4f5f8',
        lightContrast: '#000000',
        lightShade: '#d7d8da',
        lightTint: '#f5f6f9',
      },
    },
    pages: [
      {
        path: 'demo',
        title: 'App Builder Demo',
        secure: false,
        components: [
          {
            id: 'demo-heading-1',
            type: 'HeadingComponent',
            showWhen: 'always',
            inputs: {
              text: 'Welcome to App Builder Demo',
              level: '1',
              size: '3xl',
              align: 'center',
              className: 'mb-8'
            }
          },
          {
            id: 'demo-card-1',
            type: 'CardComponent',
            showWhen: 'always',
            inputs: {
              title: 'Sample Card',
              subtitle: 'This is a demo card component',
              padding: true,
              shadow: true,
              className: 'mb-4'
            }
          },
          {
            id: 'demo-button-1',
            type: 'ButtonComponent',
            showWhen: 'always',
            inputs: {
              text: 'Click Me',
              variant: 'primary',
              size: 'md',
              fullWidth: false,
              className: 'mb-4'
            }
          },
          {
            id: 'demo-text-1',
            type: 'TextComponent',
            showWhen: 'always',
            inputs: {
              text: 'This is a sample text component with different styling options.',
              size: 'md',
              align: 'left',
              className: 'mb-4'
            }
          },
          {
            id: 'demo-input-1',
            type: 'InputComponent',
            showWhen: 'always',
            inputs: {
              placeholder: 'Enter some text...',
              type: 'text',
              size: 'md',
              variant: 'default',
              className: 'mb-4'
            }
          }
        ]
      }
    ],
    navigation: {
      sidebarTitle: 'App Builder Demo',
      sidebarIcon: 'assets/images/feature.png',
      type: 'sidebar',
      routes: [
        {
          id: 'Demo',
          text: 'Demo Page',
          link: '/public/demo',
          icon: 'construct',
          active: true,
          sidebar: true,
          admin: false,
          sort: 0,
          exact: true,
        }
      ],
    },
    authConfig: {
      issuer: 'https://authdev.loyaltyplus.aero/auth/realms/DemoZ1',
      clientId: 'mobile-app',
      logoutUrl: '/',
      url: 'https://authdev.loyaltyplus.aero/auth',
      realm: 'DemoZ1',
      initOptions: {
        adapter: 'default',
        responseType: 'code',
        scope: 'openid profile email offline_access',
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          window.location.origin.replace('.aero', '.aero/lp-mobile') +
          '/assets/silent-check-sso.html',
      },
    },
    useDemoProfile: false,
  },
};
// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  env: 'QA',
  client: 'ffz1qa.loyaltyplus.aero',
  lssConfig: {
    "googleApiKey": "AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0",
    "apiId": "812275411",
    "appCode": "start",
    "appName": "Loyalty Client Mobile App",
    "appVersion": "0.0.1",
    "useAuth": true,
    "useION": true,
    "useISO": false,
    "defaultNotAuthURL": "",
    "autoLogout": true,
    "autoLogoutTimeout": 180,
    "autoLogoutWarning": 120,
    "defaultLat": -28.83688693522886,
    "defaultLng": 25.**************,
    "loadIdentity": false,
    "identityBaseUrl": "http://payroll.dv.lss.si/servlet/systemImage",
    "appBaseUrl": "http://alpine",
    "apiBaseUrl": "https://ffz1qa.loyaltyplus.aero/",
    "logEndpoint": "https://ffz1qa.loyaltyplus.aero/extsecure/tools/logservice",
    "configAPIUrl": "http://ffz1qa.loyaltyplus.aero/config/api/v1/",
    "memberPhone": {
      "dialCode": "+27",
      "nationalNumber": ""
    },
    "memberCard": "",
    "telephone": {
      "selectFirstCountry": true,
      "preferredCountries": [
        "za"
      ],
      "onlyCountries": [
        "za",
        "ls",
        "bw",
        "na",
        "sz",
        "mz"
      ]
    },
    "theme": {
      "layout": "sidebar",
      "backgroundImage": "",
      "colours": {
        "primary": "#f5821f",
        "primaryContrast": "#ffffff",
        "primaryShade": "#FFA500",
        "primaryTint": "#ff6600",
        "secondary": "#ffd400",
        "secondaryContrast": "#ffffff",
        "secondaryShade": "#4854e0",
        "secondaryTint": "#6370ff",
        "tertiary": "#ffd400",
        "tertiaryContrast": "#ffffff",
        "tertiaryShade": "#4854e0",
        "tertiaryTint": "#6370ff",
        "success": "#2dd36f",
        "successContrast": "#000000",
        "successShade": "#28ba62",
        "successTint": "#42d77d",
        "warning": "#ffc409",
        "warningContrast": "#000000",
        "warningShade": "#e0ac08",
        "warningTint": "#ffca22",
        "danger": "#eb445a",
        "dangerContrast": "#ffffff",
        "dangerShade": "#cf3c4f",
        "dangerTint": "#ed576b",
        "medium": "#92949c",
        "mediumContrast": "#000000",
        "mediumShade": "#808289",
        "mediumTint": "#9d9fa6",
  "base": "#16315d",
        "baseContrast": "#ffffff",
        "baseShade": "#16315d",
        "baseTint": "#4c8dff",
        "light": "#f4f5f8",
        "lightContrast": "#000000",
        "lightShade": "#d7d8da",
        "lightTint": "#f5f6f9",
        "step50": "#f8f8f8",
        "step100": "#f1f1f1",
        "step150": "#eaeaea",
        "step200": "#e3e3e3",
        "step250": "#dcdcdc",
        "step300": "#d5d5d5",
        "step350": "#cecece",
        "step400": "#c7c7c7",
        "step450": "#c0c0c0",
        "step500": "#b9b9b9",
        "step550": "#b2b2b2",
        "step600": "#ababab",
        "step650": "#a4a4a4",
        "step700": "#9d9d9d",
        "step750": "#969696",
        "step800": "#8f8f8f",
        "step850": "#888888",
        "step900": "#818181",
        "step950": "#7a7a7a"
      }
    },
    "forms": {
      "login": {
        "options": {
          "email": true,
          "google": true
        }
      },
      "contactus": {
        "categories": [
          "General",
          "Complaint"
        ]
      }
    },
    navigation: {
      sidebarTitle: 'Loyalty Plus',
      sidebarIcon: 'assets/images/logo.png',
      type: 'sidebar',
      routes: [
        {
          path: '/public/home',
          label: 'Home',
          icon: 'home-outline',
          main: true,
          sidebar: true,
          more: false
        },
        {
          path: '/public/games/home',
          label: 'Games',
          icon: 'game-controller-outline',
          main: false,
          sidebar: false,
          more: false
        },
        {
          path: '/public/virtual',
          label: 'Cards',
          icon: 'add-circle-outline',
          main: true,
          sidebar: false,
          more: false
        },
        {
          path: '/public/games/favourites',
          label: 'Favourites',
          icon: 'heart-outline',
          main: false,
          sidebar: false,
          more: false
        },
        {
          path: '/public/profile',
          label: 'Profile',
          icon: 'person-outline',
          main: false,
          sidebar: false,
          more: true
        },
        {
          path: '/public/profile-details',
          label: 'Profile Details',
          icon: 'person-outline',
          main: false,
          sidebar: false,
          more: false
        },
        {
          id: 'Login',
          label: 'Login',
          link: 'login',
          icon: 'log-in-outline',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Register',
          label: 'Register',
          link: 'register',
          icon: 'person-add-outline',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Logout',
          label: 'Logout',
          link: 'logout',
          icon: 'log-out-outline',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Home',
          label: 'Home',
          link: '/',
          icon: 'home',
          active: true,
          sidebar: true,
          main: false,
          admin: false,

          sort: 0,
          exact: true,
        },
        {
          id: 'Profile',
          label: 'Profile',
          path: '/app/account',
          icon: 'person-outline',
          active: true,
          sidebar: true,
          more: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Card',
          label: 'Card',
          path: '/app/virtualcard',
          icon: 'card-outline',
          active: true,
          sidebar: true,
          main: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Transactions',
          label: 'Transactions',
          path: '/public/transactions',
          icon: 'card-outline',
          active: true,
          sidebar: true,
          admin: false,
          main: true,
          sort: 0,
          exact: false,
        },
        {
          id: 'Games',
          label: 'Games',
          path: '/public/games/home',
          icon: 'game-controller-outline',
          active: true,
          sidebar: true,
          admin: false,
          more: true,
          sort: 0,
        },
        {
          id: 'Stores',
          label: 'Stores',
          path: '/app/stores',
          icon: 'location-outline',
          active: true,
          sidebar: true,
          admin: false,
          more: true,
          sort: 0,
          exact: false,
        },
        {
          id: 'Contact',
          label: 'Contact Us',
          path: '/public/contactus',
          icon: 'call-outline',
          active: true,
          sidebar: true,
          admin: true,
          more: true,
          sort: 0,
          exact: false,
        },
      ],
    },
    pages: {
      home: {
        path: 'home',
        secure: true,
        class: 'bg-red-500 h-full',

        components: [

          {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              padding: true,
              className: 'bg-black rounded shadow',
              title: 'Welcome',
              childrenConfig: [
                {
                  type: 'WelcomeComponent',
                  inputs: {
                    name: 'John',
                    surname: 'Doe'
                  }
                }
              ]
            }
          },
          {
            type: 'PagesLoginTheme1Component',
            showWhen: 'anonymous',
            inputs: {
              kc: 'kc',
              memberService: 'memberService',
              router: 'router',
              title: {
                text: 'Welcome',
                class: 'text-primaryContrast text-4xl text-center',
              },
      
              subtitle: {
                text: 'to Loyalty Plus Demo',
                class: 'text-primaryContrast text-center w-full',
              },
              icon: {
                src: 'assets/images/logo.png',
                class: 'rounded-large text-center w-80 mx-auto',
              },
              auth_buttons: {
                class: ' px-4 mt-6',
                login: {
                  text: 'Sign in',
                  class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
                },
                signup: {
                  text: 'Sign up',
                  class:
                    'primary text-sm text-center w-full p-5 rounded-lg shadow my-3',
                },
                password: {
                  text: 'Forgot Password',
                  class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
                },
              },
              social_buttons: {
                class: 'flex w-full text-center mt-6',
                facebook: {
                  icon: 'logo-facebook',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                  size: 'large',
                },
                twitter: {
                  icon: 'logo-x',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                linkedin: {
                  icon: 'logo-linkedin',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                youtube: {
                  icon: 'logo-youtube',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                pinterest: {
                  icon: 'logo-pinterest',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                instagram: {
                  icon: 'logo-instagram',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
              },
              loggedinIcon: 'assets/images/logo.png',
            }
          },
          
          // {
          //   type: 'ContainerComponent',
          //   inputs: {
          //     padding: true,
          //     className: 'bg-green-500 rounded shadow flex justify-between items-center',
          //     childrenConfig: [
          //       {
          //         type: 'ButtonComponent',
          //         inputs: {
          //           label: 'Go to About Us',
          //           color: 'primary',
          //           class: 'mt-2 p-4 rounded',
          //           style: 'background-color: #1976d2; color: #fff;',
          //           routerLink: '/public/about-us'
          //         }
          //       },
          //       {
          //         type: 'ButtonComponent',
          //         inputs: {
          //           label: 'Go to About Us',
          //           color: 'primary',
          //           class: 'mt-2',
          //           style: 'background-color: #1976d2; color: #fff;',
          //           routerLink: '/public/about-us'
          //         }
          //       },
          //     ]
          //   }
          // },
          // {
          //   type: 'ButtonComponent',
          //   inputs: {
          //     label: 'Go to About Us',
          //     color: 'primary',
          //     class: 'mt-2',
          //     style: 'background-color: #1976d2; color: #fff;',
          //     routerLink: '/public/about-us'
          //   }
          // },
          // {
          //   type: 'ContainerComponent',
          //   inputs: {
          //     className: 'p-4 bg-white rounded shadow m-4',
          //     childrenConfig: [
          //       { 
          //         type: 'HeadingComponent', 
          //         inputs: { 
          //           text: 'OTP Validation',
          //           level: 1,
          //           className: 'text-center mb-4'
          //         } 
          //       },
          //       { 
          //         type: 'ButtonComponent', 
          //         inputs: { 
          //           label: 'Request OTP',
          //           color: 'primary',
          //           className: 'w-full mb-4',
          //           onClick: 'function() { alert("OTP request would happen here"); }'
          //         } 
          //       },
          //       { 
          //         type: 'ButtonComponent', 
          //         inputs: { 
          //           label: 'Back to Home',
          //           color: 'light',
          //           className: 'w-full',
          //           routerLink: '/public/home'
          //         } 
          //       }
          //     ]
          //   }
          // }
        ]
      },
      'validate': {
        path: 'validate',
        secure: false,
        class: 'bg-blue-500',
        components: [
          {
            type: 'OtpValidatorComponent',
            inputs: {
            }
          }
        ]
      },
      'profile': {
        path: 'profile',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'ProfileDetailsComponent',
            inputs: {
            }
          }
        ]
      },
      'profile-details': {
        path: 'profile-details',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'ProfileComponent',
            inputs: {
            }
          }
        ]
      },
      'about-us': {
        path: 'about-us',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'ButtonComponent',
            inputs: {
              label: 'Back to Home',
              color: 'success',
              class: 'ion-margin ion-padding',
              style: 'background-color: #43a047; color: #fff;',
              routerLink: '/public/home'
            }
          }
        ]
      },
      landing: {
        path: 'landing',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'header',
            inputs: {
              title: 'Welcome',
              subtitle: 'to Make It With Mica',
              icon: 'assets/images/logo.png'
            }
          },
          {
            type: 'action-grid',
            inputs: {
              actions: [
                {
                  text: 'Profile',
                  icon: 'person-circle-outline',
                  link: '/app/profile',
                  class: 'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex'
                },
                {
                  text: 'Card',
                  icon: 'card-outline',
                  link: '/app/card',
                  class: 'baseShade hover-primary text-center rounded-sm shadow-sm relative h-16 w-full flex'
                }
              ]
            }
          }
        ]
      },
      // profile: {
      //   path: 'profile',
      //   secure: true,
      //   class: 'profile-page',
      //   components: [
      //     {
      //       type: 'header',
      //       inputs: {
      //         title: 'Profile',
      //         subtitle: 'Manage your account'
      //       }
      //     }
      //   ]
      // },
     
      settings: {
        path: 'settings',
        secure: true,
        class: 'settings-page',
        components: [
          {
            type: 'header',
            inputs: {
              title: 'Settings',
              subtitle: 'Customize your preferences'
            }
          }
        ]
      },
      // signup: {
      //   path: 'signup',
      //   secure: false,
      //   class: 'signup-page',
      //   components: [
      //     {
      //       type: 'header',
      //       inputs: {
      //         title: 'Create Account',
      //         subtitle: 'Join our community'
      //       }
      //     }
      //   ]
      // },
      stores: {
        path: 'stores',
        secure: false,
        class: 'stores-page',
        components: [
          {
            type: 'header',
            inputs: {
              title: 'Our Stores',
              subtitle: 'Find a store near you'
            }
          }
        ]
      },
      games: {
        path: 'games',
        secure: false,
        class: 'games-page',
        components: [
          {
            type: 'header',
            inputs: {
              title: 'Games',
              subtitle: 'Play and earn rewards'
            }
          }
        ]
      },
      login: {
        themes: ['theme-1', 'theme-2', 'theme-3', 'theme-4'],
        class: 'bg-base h-screen',
        theme: 'theme-1',
        title: {
          text: 'Welcome',
          class: 'text-primaryContrast text-4xl text-center',
        },

        subtitle: {
          text: 'to Make It With Mica',
          class: 'text-primaryContrast text-center w-full',
        },
        icon: {
          src: 'assets/images/logo.png',
          class: 'rounded-large text-center w-80 mx-auto',
        },
        auth_buttons: {
          class: ' px-4 mt-6',
          login: {
            text: 'Sign in',
            class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
          },
          signup: {
            text: 'Sign up',
            class:
              'primary text-sm text-center w-full p-5 rounded-lg shadow my-3',
          },
          password: {
            text: 'Forgot Password',
            class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
          },
        },
        social_buttons: {
          class: 'flex w-full text-center mt-6',
          facebook: {
            icon: 'logo-facebook',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
            size: 'large',
          },
          twitter: {
            icon: 'logo-x',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          linkedin: {
            icon: 'logo-linkedin',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          youtube: {
            icon: 'logo-youtube',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          pinterest: {
            icon: 'logo-pinterest',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
          instagram: {
            icon: 'logo-instagram',
            size: 'large',
            class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
          },
        },
        loggedinIcon: 'assets/images/logo.png',
      },
      contactus: {
        path: 'contactus',
        secure: false,
        class: 'bg-base h-full',

        components: [
          {
            type: 'ContactusComponent'
          }
        ]
      },
      signup: {
        class: 'signup-page',
        components: [
          {
            type: 'signup-form',
            inputs: {
              title: 'Create Account',
              subtitle: 'Join our community'
            }
          }
        ]
      },
      transactions: {
        path: 'transactions',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'TransactionsComponent'
          }
        ]
      },
    },
    "contact": {
      "callCenter": "************",
      "email": ""
    },
    "socials": {
      "facebook": "https://www.facebook.com/micahardware",
      "twitter": "https://twitter.com/micahardware",
      "linkedin": "https://www.linkedin.com/company/mica-hardware?originalSubdomain=za"
    },
    "useDemoProfile": false,
    "version": 98,
    "client": "ffz1qa.loyaltyplus.aero",
    "env": "QA",
    authConfig: {
      "issuer": "https://authqa.loyaltyplus.aero/auth/realms/DemoZ1",
      "clientId": "mobile-app",
      "logoutUrl": "/",
      "url": "https://authqa.loyaltyplus.aero/auth",
      "realm": "DemoZ1",
      "initOptions": {
          "adapter": "default",
          "responseType": "code",
          "scope": "openid profile email offline_access",
          "onLoad": "check-sso",
          "redirectUri": "https://ffz1qa.loyaltyplus.aero/lp-mobile/",
          "silentCheckSsoRedirectUri": "https://ffz1qa.loyaltyplus.aero/lp-mobile/assets/silent-check-sso.html"
      }
  },
  },
};

// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  env: 'DEV',
  client: 'ffz1',
  clientType: 'web',
  lssConfig: {
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '812275411',
    hostName: 'ffz1dev.loyaltyplus.aero', // Default host for web development
    apiIdKeyStart: 'ITMANQA_038958288_START',
    apiIdKeyEnd: 'ITMANQA_039785672_END',
    appCode: 'start',
    appName: 'Loyalty Demo',
    appVersion: '0.0.1',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.49975999318031,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'http://{hostname}',
    apiBaseUrl: 'https://{hostname}/',
    logEndpoint: 'https://{hostname}/extsecure/tools/logservice',
    configAPIUrl: 'https://{hostname}/config/api/v1/',
    termsConditions: 'http://payroll.dv.lss.si/servlet/systemImage',
    navigation: {
      sidebarTitle: 'Loyalty Plus',
      sidebarIcon: 'assets/images/logo.png',
      type: 'sidebar',
      routes: [
        {
          path: '/public/home',
          label: 'Home',
          icon: 'home-outline',
          main: true,
          sidebar: true,
          more: false
        },
        {
          path: '/public/games/home',
          label: 'Games',
          icon: 'game-controller-outline',
          main: false,
          sidebar: false,
          more: false
        },
        {
          path: '/public/virtual',
          label: 'Cards',
          icon: 'add-circle-outline',
          main: true,
          sidebar: false,
          more: false
        },
        {
          path: '/public/games/favourites',
          label: 'Favourites',
          icon: 'heart-outline',
          main: false,
          sidebar: false,
          more: false
        },
        {
          path: '/public/profile',
          label: 'Profile',
          icon: 'person-outline',
          main: false,
          sidebar: false,
          more: true
        },
        {
          path: '/public/profile-details',
          label: 'Profile Details',
          icon: 'person-outline',
          main: false,
          sidebar: false,
          more: false
        },
        {
          id: 'Login',
          label: 'Login',
          link: 'login',
          icon: 'log-in-outline',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Register',
          label: 'Register',
          link: 'register',
          icon: 'person-add-outline',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Logout',
          label: 'Logout',
          link: 'logout',
          icon: 'log-out-outline',
          active: true,
          sidebar: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Home',
          label: 'Home',
          link: '/',
          icon: 'home',
          active: true,
          sidebar: true,
          main: false,
          admin: false,

          sort: 0,
          exact: true,
        },
        {
          id: 'Profile',
          label: 'Profile',
          path: '/app/account',
          icon: 'person-outline',
          active: true,
          sidebar: true,
          more: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Card',
          label: 'Card',
          path: '/app/virtualcard',
          icon: 'card-outline',
          active: true,
          sidebar: true,
          main: false,
          admin: false,
          sort: 0,
          exact: false,
        },
        {
          id: 'Transactions',
          label: 'Transactions',
          path: '/public/transactions',
          icon: 'card-outline',
          active: true,
          sidebar: true,
          admin: false,
          main: true,
          sort: 0,
          exact: false,
        },
        {
          id: 'Games',
          label: 'Games',
          path: '/public/games/home',
          icon: 'game-controller-outline',
          active: true,
          sidebar: true,
          admin: false,
          more: true,
          sort: 0,
        },
        {
          id: 'Stores',
          label: 'Stores',
          path: '/app/stores',
          icon: 'location-outline',
          active: true,
          sidebar: true,
          admin: false,
          more: true,
          sort: 0,
          exact: false,
        },
        {
          id: 'Contact',
          label: 'Contact Us',
          path: '/public/contactus',
          icon: 'call-outline',
          active: true,
          sidebar: true,
          admin: true,
          more: true,
          sort: 0,
          exact: false,
        },
      ],
    },
    pages: [
      {
        title: 'home',
        path: 'home',
        secure: true,
        class: 'bg-red-500 h-full',
        components: [
          {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              padding: true,
              className: 'bg-black rounded shadow',
              title: 'Welcome',
              childrenConfig: [
                {
                  type: 'WelcomeComponent',
                  inputs: {
                    name: 'John',
                    surname: 'Doe'
                  }
                }
              ]
            }
          },
          {
            type: 'PagesLoginTheme1Component',
            showWhen: 'anonymous',
            inputs: {
              kc: 'kc',
              memberService: 'memberService',
              router: 'router',
              title: {
                text: 'Welcome',
                class: 'text-primaryContrast text-4xl text-center',
              },
      
              subtitle: {
                text: 'to Loyalty Plus Demo',
                class: 'text-primaryContrast text-center w-full',
              },
              icon: {
                src: 'assets/images/logo.png',
                class: 'rounded-large text-center w-80 mx-auto',
              },
              auth_buttons: {
                class: ' px-4 mt-6',
                login: {
                  text: 'Sign in',
                  class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
                },
                signup: {
                  text: 'Sign up',
                  class:
                    'primary text-sm text-center w-full p-5 rounded-lg shadow my-3',
                },
                password: {
                  text: 'Forgot Password',
                  class: 'primary text-sm text-center w-full p-5 rounded-lg shadow',
                },
              },
              social_buttons: {
                class: 'flex w-full text-center mt-6',
                facebook: {
                  icon: 'logo-facebook',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                  size: 'large',
                },
                twitter: {
                  icon: 'logo-x',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                linkedin: {
                  icon: 'logo-linkedin',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                youtube: {
                  icon: 'logo-youtube',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                pinterest: {
                  icon: 'logo-pinterest',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
                instagram: {
                  icon: 'logo-instagram',
                  size: 'large',
                  class: 'bg-clear text-primaryContrast rounded-full p-1 text-2xl',
                },
              },
              loggedinIcon: 'assets/images/logo.png',
            }
          },
        ],
      },
      {
        title: 'profile',
        path: 'profile',
        secure: true,
        class: 'bg-blue-500 h-full',
        components: [
          {
            type: 'ProfileComponent',
            config: {
              avatarUrl: '{profile.avatarUrl}',
              name: '{profile.name}',
              email: '{profile.email}',
              balance: '{profile.balance}',
              buttons: [
                {
                  text: 'Edit Profile',
                  navigation: '/public/profile-details',
                },
                {
                  text: 'View Transactions',
                  action: 'viewTransactions',
                },
              ],
            },
          },
          {
            type: 'ButtonComponent',
            config: {
              text: 'Go Home',
              expand: 'block',
              fill: 'solid',
              color: 'primary',
              navigation: '/public/home',
            },
          },
        ],
      },
      {
        title: 'profile-details',
        path: 'profile-details',
        secure: true,
        class: 'bg-green-500 h-full',
        components: [
          {
            type: 'ProfileDetailsComponent',
            config: {
              title: 'Update Profile Details',
              profile: '{profile}', // Pass the entire profile object
            },
          },
          {
            type: 'ButtonComponent',
            config: {
              text: 'Back to Profile',
              expand: 'block',
              fill: 'solid',
              color: 'primary',
              navigation: '/public/profile',
            },
          },
        ],
      },
      {
        title: 'login',
        path: 'login',
        secure: false,
        class: 'bg-yellow-500 h-full',
        components: [
          {
            type: 'PagesLoginTheme1Component',
            config: {
              title: 'Login to your account',
              logo: 'assets/images/logo.png',
              backgroundImage: 'assets/images/login-bg.jpg',
              registerLink: '/public/register',
            },
          },
        ],
      },
      {
        title: 'register',
        path: 'register',
        secure: false,
        class: 'bg-purple-500 h-full',
        components: [
          {
            type: 'HeadLogoComponent',
            config: {
              logo: 'assets/images/logo.png',
              title: 'Register Account',
              subtitle: 'Join us now!',
            },
          },
          {
            type: 'ButtonComponent',
            config: {
              text: 'Already have an account? Login',
              expand: 'block',
              fill: 'clear',
              color: 'primary',
              navigation: '/public/login',
            },
          },
        ],
      },
      {
        title: 'landing',
        path: 'landing',
        secure: false,
        class: 'bg-base h-full',
        components: [
          {
            type: 'PagesLandingTheme1Component',
            showWhen: 'authenticated',
            inputs: {
              kc: 'kc',
              profile: 'profile',
              router: 'router',
              logo: 'assets/images/logo.png',
              backgroundImage: 'assets/images/background.jpg',
              title: 'Welcome to the Loyalty App',
              subtitle: 'Your rewards journey starts here.',
              buttons: [
                {
                  text: 'Login',
                  navigation: '/public/login',
                  color: 'primary',
                  fill: 'solid',
                },
                {
                  text: 'Register',
                  navigation: '/public/register',
                  color: 'secondary',
                  fill: 'outline',
                },
              ],
              features: [
                {
                  icon: 'star-outline',
                  title: 'Earn Points',
                  description: 'Get rewarded for every purchase.',
                },
                {
                  icon: 'gift-outline',
                  title: 'Redeem Rewards',
                  description: 'Use points for exclusive benefits.',
                },
                {
                  icon: 'flash-outline',
                  title: 'Exclusive Offers',
                  description: 'Access special deals and promotions.',
                },
              ],
            },
          },
          {
            type: 'PagesLoginTheme1Component',
            showWhen: 'anonymous',
            inputs: {
              kc: 'kc',
              memberService: 'memberService',
              router: 'router',
              title: {
                text: 'Welcome',
                class: 'text-primaryContrast text-4xl text-center',
              },
              subtitle: {
                text: 'to Loyalty Plus App',
                class: 'text-primaryContrast text-center w-full',
              },
              icon: {
                src: 'assets/images/logo.png',
                class: 'rounded-large text-center w-80 mx-auto',
              },
              auth_buttons: {
                class: 'px-4 mt-6',
              },
            },
          },
        ],
      },
      {
        title: 'otp-validation',
        path: 'otp-validation',
        secure: false, // Usually requires some temporary state, not full auth
        class: 'bg-gray-200 h-full flex items-center justify-center',
        components: [
          {
            type: 'OtpValidatorComponent',
            config: {
              title: 'Verify Your Identity',
              subtitle: 'Enter the OTP sent to your device',
              successNavigation: '/public/home', // Navigate on success
              resendAction: 'resendOtp' // Define action for resend
            }
          }
        ]
      },
    ],
    contact: {
      callCenter: '************',
      email: '',
    },
    socials: {
      facebook: 'https://www.facebook.com/micahardware',
      twitter: 'https://twitter.com/micahardware',
      linkedin:
        'https://www.linkedin.com/company/mica-hardware?originalSubdomain=za',
    },
    authConfig: {
      issuer: 'https://auth{env}.loyaltyplus.aero/auth/realms/{client}',
      clientId: 'mobile-app',
      logoutUrl: '/',
      url: 'https://auth{env}.loyaltyplus.aero/auth',
      realm: 'Mica',
      initOptions: {
        adapter: 'default',
        responseType: 'code',
        scope: 'openid profile email offline_access',
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          window.location.origin.replace('.aero', '.aero/lp-mobile') +
          '/assets/silent-check-sso.html',
      },
    },
    useDemoProfile: true, 
    games: {
      globalConfig: {
        version: '1.0.0',
        defaultLanguage: 'en',
        saveUserProgress: true,
        enableSound: true,
        enableMusic: true,
        themes: {
          defaultBackgroundImage: 'assets/images/default-bg.jpg',
        },
      },
      categories: {
        business: {
          name: 'Business Games',
          backgroundImage: 'assets/images/business-bg.jpg',
          games: {
            spinthewheel: {
              name: 'Spin a Wheel',
              backgroundImage: 'assets/images/wheel-bg.jpg',
              config: {
                sections: [
                  {
                    id: 1,
                    label: '10 Points',
                    value: 10,
                    probability: 0.3,
                    backgroundColor: '#FF5733',
                  },
                  {
                    id: 2,
                    label: '20 Points',
                    value: 20,
                    probability: 0.2,
                    backgroundColor: '#33FF57',
                  },
                ],
                spinFrequency: {
                  type: 'daily',
                  resetsAt: '00:00',
                  timezone: 'UTC',
                  spinsAllowed: 3,
                },
                wheelSections: 8,
                spinDuration: 5000,
                minSpinSpeed: 0.1,
                maxSpinSpeed: 2.0,
                prizes: [],
                wheelDesign: {
                  borderColor: '#000000',
                  borderWidth: 2,
                  centerImage: 'assets/images/wheel-center.png',
                  pointerImage: 'assets/images/pointer.png',
                },
              },
            },
            locationBased: {
              name: 'Location Based Photo',
              backgroundImage: 'assets/images/location-bg.jpg',
              config: {
                locations: [
                  {
                    id: 'loc1',
                    name: 'Central Park',
                    latitude: 40.785091,
                    longitude: -73.968285,
                    radius: 100,
                    points: 100,
                    description: 'Take a selfie at Central Park',
                    requiredTags: ['nature', 'park'],
                  },
                ],
                maxPhotoSize: 5242880,
                allowedFileTypes: ['jpg', 'jpeg', 'png'],
                requireLocation: true,
                maxDistanceMeters: 100,
                photoQualityMin: 0.7,
              },
            },
          },
        },
        arcade: {
          name: 'Arcade Games',
          backgroundImage: 'assets/images/arcade-bg.jpg',
          games: {
            memory: {
              name: 'Memory',
              backgroundImage: 'assets/images/memory-bg.jpg',
              config: {
                levels: [
                  {
                    level: 1,
                    gridSize: { rows: 2, columns: 2 },
                    timeLimit: 30,
                    points: 100,
                  },
                  {
                    level: 2,
                    gridSize: { rows: 4, columns: 4 },
                    timeLimit: 60,
                    points: 300,
                  },
                  {
                    level: 3,
                    gridSize: { rows: 6, columns: 6 },
                    timeLimit: 120,
                    points: 500,
                  },
                ],
                cardImages: [
                  {
                    id: 1,
                    url: 'assets/images/memory/card1.jpg',
                    name: 'Card 1',
                  },
                  {
                    id: 2,
                    url: 'assets/images/memory/card2.jpg',
                    name: 'Card 2',
                  },
                ],
                cardBackImage: 'assets/images/memory/card-back.jpg',
                matchTime: 1000,
                difficulty: 'medium',
                themeOptions: ['classic', 'animals', 'numbers'],
              },
              faceImages: {
                match: 'assets/images/games/faces/2nd Win Kawaii face.png',
                mismatch: 'assets/images/games/faces/1st loose Kawaii face.png',
                win: 'assets/images/games/faces/3rd Win Kawaii face.png',
                lose: 'assets/images/games/faces/3rd loose Kawaii face.png'
              },
              messages: {
                win: {
                  message: 'Congratulations! You matched all pairs! ',
                  face: 'assets/images/games/faces/3rd Win Kawaii face.png'
                },
                lose: {
                  message: 'Game Over! Better luck next time!',
                  face: 'assets/images/games/faces/3rd loose Kawaii face.png'
                },
                match: {
                  message: 'Great match!',
                  face: 'assets/images/games/faces/2nd Win Kawaii face.png'
                },
                mismatch: {
                  message: 'Try again!',
                  face: 'assets/images/games/faces/1st loose Kawaii face.png'
                }
              }
            },
            wordle: {
              name: 'Wordle',
              backgroundImage: 'assets/images/wordle.png',
              config: {
                wordLength: 5,
                maxAttempts: 6,
                theme: 'light',
              },
              faceImages: {
                correct: 'assets/images/games/faces/2nd Win Kawaii face.png',
                present: 'assets/images/games/faces/3rd loose Kawaii face.png',
                absent: 'assets/images/games/faces/1st loose Kawaii face.png',
                win: 'assets/images/games/faces/3rd Win Kawaii face.png',
                lose: 'assets/images/games/faces/3rd loose Kawaii face.png'
              },
              words: [
                'SMILE', 'BRAVE', 'HAPPY', 'PEACE', 'DREAM',
                'LIGHT', 'DANCE', 'SHINE', 'SPARK', 'HEART',
                'MAGIC', 'POWER', 'STORM', 'OCEAN', 'EARTH',
                'FLAME', 'SWIFT', 'GRACE', 'FAITH', 'BLISS',
                'CHARM', 'GLORY', 'QUEST', 'PRIZE', 'CROWN'
              ],
              win: {
                message: 'Congratulations! You won! ',
                face: 'assets/images/games/faces/3rd Win Kawaii face.png'
              },
              lose: {
                message: 'Game Over! The word was ${this.word}',
                face: 'assets/images/games/faces/3rd loose Kawaii face.png'
              }
            },
            game2048: {
              name: '2048',
              backgroundImage: 'assets/images/2048-bg.jpg',
              config: {
                gridSize: 4,
                winningTile: 2048,
                animationSpeed: 200,
                swipeThreshold: 50,
                colors: {
                  '2': '#EEE4DA',
                  '4': '#EDE0C8',
                  '8': '#F2B179',
                },
                tileDesign: {
                  borderRadius: '3px',
                  fontSize: {
                    small: '24px',
                    medium: '32px',
                    large: '48px',
                  },
                },
                customTileImages: {
                  enabled: false,
                  images: {
                    '2': 'assets/images/2048/tile2.png',
                    '4': 'assets/images/2048/tile4.png',
                  },
                },
              },
            },
            snake: {
              name: 'Snake',
              backgroundImage: 'assets/images/snake-bg.jpg',
              config: {
                levels: [
                  {
                    level: 1,
                    gridSize: { width: 10, height: 10 },
                    initialSpeed: 100,
                    speedIncrease: 2,
                    pointsPerFood: 10,
                  },
                  {
                    level: 2,
                    gridSize: { width: 15, height: 15 },
                    initialSpeed: 150,
                    speedIncrease: 3,
                    pointsPerFood: 20,
                  },
                  {
                    level: 3,
                    gridSize: { width: 20, height: 20 },
                    initialSpeed: 200,
                    speedIncrease: 4,
                    pointsPerFood: 30,
                  },
                ],
                foodTypes: [
                  {
                    type: 'regular',
                    points: 1,
                    imageUrl: 'assets/images/snake/apple.png',
                    probability: 0.7,
                  },
                  {
                    type: 'bonus',
                    points: 3,
                    imageUrl: 'assets/images/snake/golden-apple.png',
                    probability: 0.3,
                  },
                ],
                snakeDesign: {
                  headImage: 'assets/images/snake/head.png',
                  bodyImage: 'assets/images/snake/body.png',
                },
              },
            },
            minesweeper: {
              name: 'Minesweeper',
              backgroundImage: 'assets/images/minesweeper-bg.jpg',
              config: {
                difficulties: {
                  beginner: {
                    width: 9,
                    height: 9,
                    mines: 10,
                  },
                  intermediate: {
                    width: 16,
                    height: 16,
                    mines: 40,
                  },
                  expert: {
                    width: 30,
                    height: 16,
                    mines: 99,
                  },
                },
                defaultDifficulty: 'beginner',
                customization: {
                  tileSize: 30,
                  mineImage: 'assets/images/minesweeper/mine.png',
                  flagImage: 'assets/images/minesweeper/flag.png',
                  tileImages: {
                    covered: 'assets/images/minesweeper/covered.png',
                    uncovered: 'assets/images/minesweeper/uncovered.png',
                  },
                },
                animations: {
                  reveal: true,
                  explosion: true,
                },
              },
            },
            sudoku: {
              name: 'Sudoku',
              backgroundImage: 'assets/images/sudoku-bg.jpg',
              config: {
                difficulties: ['easy', 'medium', 'hard', 'expert'],
                defaultDifficulty: 'medium',
                highlightSameNumbers: true,
                showMistakes: true,
                hints: {
                  maximum: 3,
                  penaltyMinutes: 5,
                },
                design: {
                  gridLineWidth: {
                    normal: 1,
                    bold: 2,
                  },
                  colors: {
                    highlight: '#f0f0f0',
                    error: '#ffdddd',
                    success: '#ddffdd',
                  },
                  fonts: {
                    numbers: 'Arial',
                    notes: 'Arial',
                  },
                },
                timer: {
                  enabled: true,
                  format: 'mm:ss',
                },
              },
            },
            tetris: {
              name: 'Tetris',
              backgroundImage: 'assets/images/games/tetris.jpeg',
              config: {
                startLevel: 1,
                maxLevel: 20,
                boardSize: {
                  width: 10,
                  height: 20,
                },
                ghostPiece: true,
                holdPiece: true,
                showNext: 3,
                scoringSystem: {
                  singleLine: 100,
                  doubleLine: 300,
                  tripleLine: 500,
                  tetris: 800,
                },
                design: {
                  blockSize: 30,
                  colors: {
                    I: '#00f0f0',
                    O: '#f0f000',
                    T: '#a000f0',
                    S: '#00f000',
                    Z: '#f00000',
                    J: '#0000f0',
                    L: '#f0a000',
                  },
                  customBlocks: {
                    enabled: false,
                    blockImages: {
                      I: 'assets/images/tetris/i-block.png',
                      O: 'assets/images/tetris/o-block.png',
                    },
                  },
                },
                particles: {
                  enabled: true,
                  lineComplete: true,
                },
              },
            },
          },
        },
      },
    },
  },
};

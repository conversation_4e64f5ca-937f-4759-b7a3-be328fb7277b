/**
 * TypeScript interfaces for dynamic page configurations
 * Used to provide type safety and validation for environment page configurations
 */

// Authentication state conditions for component visibility
export type ShowWhenCondition = 'authenticated' | 'anonymous' | 'always';

// Component input configuration - can be any key-value pairs
export interface ComponentInputs {
  [key: string]: any;
}

// Component configuration for dynamic loading
export interface ComponentConfiguration {
  /** Component type name (must match registered component) */
  type: string;
  
  /** When to show this component based on authentication state */
  showWhen?: ShowWhenCondition;
  
  /** Input properties to pass to the component */
  inputs?: ComponentInputs;
  
  /** Alternative property name for inputs (for backward compatibility) */
  config?: ComponentInputs;
  
  /** CSS classes to apply to the component wrapper */
  class?: string;
  
  /** Unique identifier for the component instance */
  id?: string;
  
  /** Whether the component should be rendered conditionally */
  conditional?: boolean;
  
  /** Custom data attributes */
  data?: { [key: string]: any };
}

// Page configuration
export interface PageConfiguration {
  /** Page title */
  title: string;
  
  /** Page path/route */
  path: string;
  
  /** Whether the page requires authentication */
  secure?: boolean;
  
  /** CSS classes to apply to the page container */
  class?: string;
  
  /** Array of components to render on this page */
  components: ComponentConfiguration[];
  
  /** Page metadata */
  meta?: {
    description?: string;
    keywords?: string[];
    author?: string;
  };
  
  /** Page-specific configuration */
  config?: {
    [key: string]: any;
  };
}

// Navigation route configuration
export interface NavigationRoute {
  /** Route identifier */
  id?: string;
  
  /** Display label */
  label: string;
  
  /** Route path */
  path?: string;
  
  /** Alternative link property */
  link?: string;
  
  /** Icon name (Ionic icon) */
  icon: string;
  
  /** Whether the route is active */
  active?: boolean;
  
  /** Whether to show in sidebar navigation */
  sidebar?: boolean;
  
  /** Whether to show in main navigation */
  main?: boolean;
  
  /** Whether to show in more menu */
  more?: boolean;
  
  /** Whether the route requires admin privileges */
  admin?: boolean;
  
  /** Sort order */
  sort?: number;
  
  /** Whether the route match should be exact */
  exact?: boolean;
}

// Navigation configuration
export interface NavigationConfiguration {
  /** Sidebar title */
  sidebarTitle?: string;
  
  /** Sidebar icon */
  sidebarIcon?: string;
  
  /** Navigation type */
  type?: 'sidebar' | 'tabs' | 'menu';
  
  /** Array of navigation routes */
  routes: NavigationRoute[];
}

// Complete pages configuration (standardized array format)
export interface PagesConfiguration {
  /** Array of page configurations */
  pages: PageConfiguration[];
  
  /** Navigation configuration */
  navigation?: NavigationConfiguration;
}

// Validation result interface
export interface ValidationResult {
  /** Whether the configuration is valid */
  isValid: boolean;
  
  /** Array of error messages */
  errors: string[];
  
  /** Array of warning messages */
  warnings: string[];
  
  /** Validation context information */
  context?: {
    pageCount?: number;
    componentCount?: number;
    validatedAt?: Date;
  };
}

// Configuration validation options
export interface ValidationOptions {
  /** Whether to perform strict validation */
  strict?: boolean;
  
  /** Whether to validate component registrations */
  validateComponents?: boolean;
  
  /** Whether to validate navigation routes */
  validateNavigation?: boolean;
  
  /** Whether to show detailed warnings */
  showWarnings?: boolean;
  
  /** Custom validation rules */
  customRules?: ValidationRule[];
}

// Custom validation rule interface
export interface ValidationRule {
  /** Rule name */
  name: string;
  
  /** Rule description */
  description: string;
  
  /** Validation function */
  validate: (config: any) => ValidationResult;
  
  /** Rule severity */
  severity: 'error' | 'warning' | 'info';
}

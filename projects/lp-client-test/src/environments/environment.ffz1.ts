// Environment configuration for FFZ1 Demo client
export const environment = {
  production: false,
  env: 'DEV',
  client: 'ffz1',
  lssConfig: {
    googleApiKey: 'AIzaSyBw8kDBn79ehC9HnA1hjy9BCBsQwYXNnr0',
    apiId: '812275411',
    apiIdKeyStart: 'ITMANQA_038958288_START',
    apiIdKeyEnd: 'ITMANQA_039785672_END',
    appCode: 'start',
    appName: 'Loyalty Demo - FFZ1',
    appVersion: '0.0.1',
    useAuth: true,
    useION: true,
    useISO: false,
    defaultNotAuthURL: '',
    autoLogout: true,
    autoLogoutTimeout: 180,
    autoLogoutWarning: 120,
    defaultLat: -28.83688693522886,
    defaultLng: 25.49975999318031,
    loadIdentity: false,
    identityBaseUrl: 'http://payroll.dv.lss.si/servlet/systemImage',
    appBaseUrl: 'http://alpine',
    apiBaseUrl: 'https://ffz1dev.loyaltyplus.aero/',
    logEndpoint: 'https://ffz1dev.loyaltyplus.aero/extsecure/tools/logservice',
    configAPIUrl: 'http://{hostname}/config/api/v1/',
    termsConditions: 'http://payroll.dv.lss.si/servlet/systemImage',
    navigation: {
      sidebarTitle: 'Loyalty Plus Demo',
      sidebarIcon: 'assets/images/logo.png',
      type: 'sidebar',
      routes: [
        // Copy your navigation routes here...
        {
          path: '/public/home',
          label: 'Home',
          icon: 'home-outline',
          main: true,
          sidebar: true,
          more: false,
          exact: true
        }
      ]
    },
    // Copy your pages configuration here...
    pages: [],
    contact: {
      callCenter: '************',
      email: '',
    },
    socials: {
      facebook: 'https://www.facebook.com/LoyaltyPlusSA/',
      twitter: 'https://twitter.com/LoyaltyPlusSA',
      linkedin: 'https://www.linkedin.com/company/loyaltyplus-accolades-pty-ltd?trk=biz-companies-cym',
    },
    authConfig: {
      issuer: 'https://authdev.loyaltyplus.aero/auth/realms/DemoZ1',
      clientId: 'mobile-app',
      logoutUrl: '/',
      url: 'https://authdev.loyaltyplus.aero/auth',
      realm: 'DemoZ1',
      initOptions: {
        adapter: 'default',
        responseType: 'code',
        scope: 'openid profile email offline_access',
        onLoad: 'check-sso',
        silentCheckSsoRedirectUri:
          window.location.origin.replace('.aero', '.aero/lp-mobile') +
          '/assets/silent-check-sso.html',
      },
    },
    useDemoProfile: true,
    games: {
      // Copy your games configuration here if needed
    }
  },
};
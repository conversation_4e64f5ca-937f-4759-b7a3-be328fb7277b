import { enableProdMode, APP_INITIALIZER } from '@angular/core';
import { bootstrapApplication } from '@angular/platform-browser';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, RouteReuseStrategy } from '@angular/router';
import { importProvidersFrom } from '@angular/core';

import { AppComponent } from './app/app.component';
import { BrowserModule, HammerModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app/app-routing.module';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import { HttpClientModule, HTTP_INTERCEPTORS } from '@angular/common/http';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { GoogleMapsModule } from '@angular/google-maps';
import { InterceptorService, KeyCloakService, LogPublishersService, LogService, LssConfig } from 'lp-client-api';
import { ConfigService } from './app/services/config.service';
import { BuilderBridgeReceiverService } from './app/services/builder-bridge-receiver.service';
import { BridgeApiService } from './app/services/bridge-api.service';
import { environment } from './environments/environment';

if (environment.production) {
  enableProdMode();
}

function initializeKeycloak(config: ConfigService) {
  return () => config.loadConfig();
}

bootstrapApplication(AppComponent, {
  providers: [
    provideAnimations(),
    importProvidersFrom(
      BrowserModule,
      FontAwesomeModule,
      HttpClientModule,
      IonicModule.forRoot(),
      AppRoutingModule,
      HammerModule,
      GoogleMapsModule
    ),
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    { provide: 'environment', useValue: environment },
    ConfigService,
    {
      provide: APP_INITIALIZER,
      useFactory: initializeKeycloak,
      multi: true,
      deps: [ConfigService],
    },
    {
      provide: LssConfig,
      useFactory: (config: ConfigService) => {
        return config.sysConfig;
      },
      deps: [ConfigService],
    },
    KeyCloakService,
    LogService,
    {
      provide: HTTP_INTERCEPTORS,
      useClass: InterceptorService,
      multi: true,
      deps: [KeyCloakService],
    },
    LogPublishersService,
    BuilderBridgeReceiverService,
    BridgeApiService,
    { provide: 'componentProperties', useValue: {} }
  ]
}).catch(err => console.error(err));

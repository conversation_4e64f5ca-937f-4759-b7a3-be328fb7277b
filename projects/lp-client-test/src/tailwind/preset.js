import typography from "@tailwindcss/typography";
import containerQueries from "@tailwindcss/container-queries";
import { base, components, utilities } from "./plugins/index.js";
import { defaultTheme } from "./themes.js";
import "tailwindcss/plugin";
import "defu";
import "tailwindcss/colors";
import "tailwindcss/defaultTheme";

const ShurikenUISymbol = "__is_shuriken_ui";
function hasPreset(config) {
  if (config.presets && Array.isArray(config.presets)) {
    return config.presets.some(
      (preset2) => preset2 && ShurikenUISymbol in preset2
    );
  }
  return false;
}
function createPreset({
  theme = defaultTheme,
  plugins = [
    // tailwindcss plugins
    typography,
    containerQueries,
    // shuriken-ui plugins
    base,
    components,
    utilities,
  ],
} = {}) {
  const config = {
    darkMode: "class",
    content: [],
    plugins,
    theme,
  };
  Object.defineProperty(config, ShurikenUISymbol, {
    value: true,
    enumerable: false,
    writable: false,
  });
  return config;
}
const preset = createPreset();

export { createPreset, hasPreset, preset };

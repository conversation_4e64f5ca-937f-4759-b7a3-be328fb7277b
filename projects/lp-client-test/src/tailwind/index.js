import { hasPreset, preset } from "./preset.js";
export { createPreset } from "./preset.js";
import "@tailwindcss/typography";
import "@tailwindcss/container-queries";
import "./plugins/index.js";
import "tailwindcss/plugin";
import "defu";
import "./themes.js";
import "tailwindcss/colors";
import "tailwindcss/defaultTheme";

function withLoyaltyUI(config) {
  if (hasPreset(config)) {
    return config;
  }
  config.presets ?? (config.presets = []);
  config.presets.push(preset);
  return config;
}

export { hasPreset, preset, withLoyaltyUI };

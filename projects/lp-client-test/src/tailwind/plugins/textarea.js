import plugin from "tailwindcss/plugin";

const key$4 = "textarea";
const defaultConfig$4 = {
  rounded: {
    none: "none",
    sm: "md",
    md: "lg",
    lg: "xl",
    full: "full",
  },
  label: {
    float: {
      height: "5",
      font: {
        family: "sans",
        color: "primary-500",
        lead: "none",
      },
      transition: {
        property: "all",
        duration: "300",
      },
    },
  },
  textarea: {
    width: "full",
    font: {
      family: "sans",
    },
    focus: {
      label: {
        float: {
          font: {
            color: {
              light: "primary-500",
              dark: "primary-500",
            },
          },
        },
      },
      border: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    placeload: {
      size: "full",
      space: "2",
    },
    addon: {
      size: "12",
      padding: "2",
      border: {
        light: "muted-300",
        dark: "muted-700",
      },
      background: {
        light: "muted-50",
        dark: "muted-900/50",
      },
    },
    transition: {
      property: "colors",
      duration: "300",
    },
  },
  loaded: {
    font: {
      color: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
  },
  size: {
    sm: {
      height: "[2rem]",
      font: {
        size: "xs",
      },
      label: {
        size: "xs",
        float: {
          size: "xs",
        },
      },
    },
    md: {
      height: "[2.5rem]",
      font: {
        size: "sm",
      },
      label: {
        size: "sm",
        float: {
          size: {
            base: "xs",
            focus: "xs",
            unfocus: "[0.825rem]",
          },
        },
      },
    },
    lg: {
      height: "[3rem]",
      font: {
        size: "sm",
      },
      label: {
        size: "sm",
        float: {
          size: {
            base: "xs",
            focus: "xs",
            unfocus: "[0.825rem]",
          },
        },
      },
    },
  },
  color: {
    default: {
      background: {
        light: "white",
        dark: "muted-900",
      },
      border: {
        base: {
          light: "muted-300",
          dark: "muted-700",
        },
        hover: {
          light: "muted-300",
          dark: "muted-700",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-500",
      },
    },
    defaultContrast: {
      background: {
        light: "white",
        dark: "muted-950",
      },
      border: {
        base: {
          light: "muted-300",
          dark: "muted-800",
        },
        hover: {
          light: "muted-300",
          dark: "muted-800",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
    muted: {
      background: {
        light: "muted-100",
        dark: "muted-900",
      },
      border: {
        base: {
          light: "muted-100",
          dark: "muted-700",
        },
        hover: {
          light: "muted-100",
          dark: "muted-700",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-500",
      },
    },
    mutedContrast: {
      background: {
        light: "muted-100",
        dark: "muted-950",
      },
      border: {
        base: {
          light: "muted-100",
          dark: "muted-800",
        },
        hover: {
          light: "muted-100",
          dark: "muted-800",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
  },
  error: {
    textarea: {
      border: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    icon: {
      color: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    font: {
      family: "sans",
      size: "[0.65rem]",
      weight: "medium",
      color: {
        light: "danger-600",
        dark: "danger-600",
      },
    },
  },
};

const config$4 = {
  theme: {
    nui: {
      [key$4]: defaultConfig$4,
    },
  },
};
const textarea = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$4}`);
  addComponents({
    //Wrapper
    ".nui-textarea-wrapper": {
      "@apply relative": {},
      //Textarea:label
      ".nui-textarea-label, .nui-label-float": {
        "@apply nui-label": {},
      },
      //Label:float
      ".nui-label-float": {
        [`@apply h-${config2.label.float.height} absolute inline-flex items-center select-none pointer-events-none`]:
          {},
        //Font
        [`@apply font-${config2.label.float.font.family} text-${config2.label.float.font.color} leading-${config2.label.float.font.lead}`]:
          {},
        //Transition
        [`@apply transition-${config2.label.float.transition.property} duration-${config2.label.float.transition.duration}`]:
          {},
      },
      //Textarea:outer
      ".nui-textarea-outer": {
        "@apply relative flex flex-col": {},
      },
      //Textarea
      ".nui-textarea": {
        [`@apply nui-focus w-${config2.textarea.width} enabled:cursor-text cursor-not-allowed`]:
          {},
        //Focus:label:float
        "&:focus-visible ~ .nui-label-float": {
          [`@apply !text-${config2.label.float.font.color} dark:!text-${config2.label.float.font.color}`]:
            {},
        },
        //Textarea:focus
        "&.nui-textarea-focus": {
          //Focus:color
          [`@apply focus:!border-${config2.textarea.focus.border.color.light} dark:focus:!border-${config2.textarea.focus.border.color.dark}`]:
            {},
          //Transition
          [`@apply transition-${config2.textarea.transition.property} duration-${config2.textarea.transition.duration}`]:
            {},
        },
      },
      //Textrea:placeload
      ".nui-textarea-placeload": {
        [`@apply absolute start-0 top-4 flex h-${config2.textarea.placeload.size} w-${config2.textarea.placeload.size} flex-col space-y-${config2.textarea.placeload.space} px-3`]:
          {},
        ".nui-placeload": {
          "@apply h-3 w-full rounded": {},
          "&:first-child": {
            "@apply max-w-[85%]": {},
          },
          "&:nth-child(2)": {
            "@apply max-w-[65%]": {},
          },
          "&:nth-child(3)": {
            "@apply max-w-[35%]": {},
          },
        },
      },
      //Rounded:sm
      "&.nui-textarea-rounded-sm": {
        ".nui-textarea": {
          [`@apply rounded-${config2.rounded.sm}`]: {},
        },
        ".nui-textarea-addon": {
          [`@apply rounded-b-${config2.rounded.sm}`]: {},
        },
      },
      //Rounded:md
      "&.nui-textarea-rounded-md": {
        ".nui-textarea": {
          [`@apply rounded-${config2.rounded.md}`]: {},
        },
        ".nui-textarea-addon": {
          [`@apply rounded-b-${config2.rounded.md}`]: {},
        },
      },
      //Rounded:lg
      "&.nui-textarea-rounded-lg": {
        ".nui-textarea": {
          [`@apply rounded-${config2.rounded.lg}`]: {},
        },
        ".nui-textarea-addon": {
          [`@apply rounded-b-${config2.rounded.lg}`]: {},
        },
      },
      //Color:default
      "&.nui-textarea-default": {
        ".nui-textarea": {
          //Font
          [`@apply text-${config2.color.default.color.light} dark:text-${config2.color.default.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.default.placeholder.light} dark:placeholder:text-${config2.color.default.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.default.background.light} dark:bg-${config2.color.default.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.default.border.base.light} dark:border-${config2.color.default.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.default.border.hover.light} dark:hover:border-${config2.color.default.border.hover.dark}`]:
            {},
        },
      },
      //Color:defaut-contrast
      "&.nui-textarea-default-contrast": {
        ".nui-textarea": {
          //Font
          [`@apply text-${config2.color.defaultContrast.color.light} dark:text-${config2.color.defaultContrast.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.defaultContrast.placeholder.light} dark:placeholder:text-${config2.color.defaultContrast.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.defaultContrast.background.light} dark:bg-${config2.color.defaultContrast.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.defaultContrast.border.base.light} dark:border-${config2.color.defaultContrast.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.defaultContrast.border.hover.light} dark:hover:border-${config2.color.defaultContrast.border.hover.dark}`]:
            {},
        },
      },
      //Color:muted
      "&.nui-textarea-muted": {
        ".nui-textarea": {
          //Font
          [`@apply text-${config2.color.muted.color.light} dark:text-${config2.color.muted.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.muted.placeholder.light} dark:placeholder:text-${config2.color.muted.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.muted.background.light} dark:bg-${config2.color.muted.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.muted.border.base.light} dark:border-${config2.color.muted.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.muted.border.hover.light} dark:hover:border-${config2.color.muted.border.hover.dark}`]:
            {},
        },
      },
      //Color:muted-contrast
      "&.nui-textarea-muted-contrast": {
        ".nui-textarea": {
          //Font
          [`@apply text-${config2.color.mutedContrast.color.light} dark:text-${config2.color.mutedContrast.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.mutedContrast.placeholder.light} dark:placeholder:text-${config2.color.mutedContrast.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.mutedContrast.background.light} dark:bg-${config2.color.mutedContrast.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.mutedContrast.border.base.light} dark:border-${config2.color.mutedContrast.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.mutedContrast.border.hover.light} dark:hover:border-${config2.color.mutedContrast.border.hover.dark}`]:
            {},
        },
      },
      //Textarea:loaded
      "&:not(.nui-textarea-loading)": {
        ".nui-textarea:placeholder-shown ~ .nui-label-float": {
          [`@apply text-${config2.loaded.font.color.light} dark:text-${config2.loaded.font.color.dark}`]:
            {},
        },
      },
      //Textarea:loading
      "&.nui-textarea-loading": {
        ".nui-textarea": {
          "@apply !text-transparent placeholder:!text-transparent dark:placeholder:!text-transparent":
            {},
        },
        ".nui-textarea:placeholder-shown ~ .nui-label-float": {
          "@apply text-transparent": {},
        },
      },
      //Label:float
      "&.nui-textarea-label-float": {
        ".nui-textarea": {
          "@apply placeholder:text-transparent dark:placeholder:text-transparent":
            {},
        },
      },
      //Textarea:error
      "&.nui-textarea-error": {
        ".nui-textarea": {
          [`@apply !border-${config2.error.textarea.border.light} dark:!border-${config2.error.textarea.border.dark}`]:
            {},
        },
      },
      //Resize:none
      "&.nui-textarea-no-resize": {
        ".nui-textarea": {
          "@apply resize-none": {},
        },
      },
      //Size:sm
      "&.nui-textarea-sm": {
        [`@apply min-h-${config2.size.sm.height} text-${config2.size.sm.font.size}`]:
          {},
        ".nui-textarea-label": {
          [`@apply pb-1 text-${config2.size.sm.label.size}`]: {},
        },
      },
      //Size:md
      "&.nui-textarea-md": {
        [`@apply min-h-${config2.size.md.height} text-${config2.size.md.font.size}`]:
          {},
        ".nui-textarea-label": {
          [`@apply pb-1 text-${config2.size.md.label.size}`]: {},
        },
      },
      //Size:lg
      "&.nui-textarea-lg": {
        [`@apply min-h-${config2.size.lg.height} text-${config2.size.lg.font.size}`]:
          {},
        ".nui-textarea-label": {
          [`@apply pb-1 text-${config2.size.lg.label.size}`]: {},
        },
      },
      //Addon:false
      "&:not(.nui-has-addon)": {
        ".nui-textarea": {
          "@apply p-2": {},
        },
      },
      //Addon:true
      "&.nui-has-addon": {
        ".nui-textarea": {
          "@apply px-2 pb-14 pt-2": {},
        },
        ".nui-textarea-addon": {
          //Base
          "@apply absolute bottom-0 start-0 flex items-center justify-between":
            {},
          //size
          [`@apply w-full h-${config2.textarea.addon.size} p-${config2.textarea.addon.padding}`]:
            {},
          //Background
          [`@apply bg-${config2.textarea.addon.background.light} dark:bg-${config2.textarea.addon.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.textarea.addon.border.light} dark:border-${config2.textarea.addon.border.dark}`]:
            {},
        },
        ".nui-textarea-addon-start": {
          "@apply flex items-center gap-2": {},
        },
        ".nui-textarea-addon-end": {
          "@apply flex items-center justify-end gap-2": {},
        },
      },
      //Label:float && Size:sm
      "&.nui-textarea-label-float.nui-textarea-sm": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-7 text-${config2.size.sm.label.float.size}`]:
            {},
        },
        ".nui-textarea:focus-visible ~ .nui-label-float": {
          "@apply !-ms-3 !-mt-6": {},
        },
        ".nui-textarea:placeholder-shown ~ .nui-label-float": {
          "@apply ms-0 mt-[0.35rem]": {},
        },
      },
      //Label:float && Size:md
      "&.nui-textarea-label-float.nui-textarea-md": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-8 text-${config2.size.md.label.float.size.base}`]:
            {},
        },
        ".nui-textarea:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-7 !text-${config2.size.md.label.float.size.focus}`]:
            {},
        },
        ".nui-textarea:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-2.5 text-${config2.size.md.label.float.size.unfocus}`]:
            {},
        },
      },
      //Label:float && Size:lg
      "&.nui-textarea-label-float.nui-textarea-lg": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-8 text-${config2.size.lg.label.float.size.base}`]:
            {},
        },
        ".nui-textarea:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-7 !text-${config2.size.lg.label.float.size.focus}`]:
            {},
        },
        ".nui-textarea:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-3 text-${config2.size.lg.label.float.size.unfocus}`]:
            {},
        },
      },
    },
  });
}, config$4);
module.export = textarea;

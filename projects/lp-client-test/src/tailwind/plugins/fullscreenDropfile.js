import plugin from "tailwindcss/plugin";

const key$D = "fullscreenDropfile";
const defaultConfig$C = {
  height: "[230px]",
  width: "[500px]",
  rounded: "rounded-md",
  label: {
    font: {
      size: "base",
    },
  },
  icon: {
    size: "10",
    font: {
      size: "6xl",
    },
  },
  color: {
    primary: {
      light: "primary-500",
      dark: "primary-500",
    },
    dark: {
      light: "muted-900",
      dark: "muted-100",
    },
    black: {
      light: "black",
      dark: "white",
    },
  },
  border: {
    light: "muted-200",
    dark: "muted-700",
  },
};

const config$D = {
  theme: {
    nui: {
      [key$D]: defaultConfig$C,
    },
  },
};
const fullscreenDropfile = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$D}`);
  addComponents({
    //Wrapper
    ".nui-fullscreen-dropfile": {
      //Dropfile:outer
      ".nui-fullscreen-dropfile-outer": {
        "@apply bg-muted-100/50 dark:bg-muted-800/20 fixed inset-0 z-40 backdrop-blur-sm transition-all hover:backdrop-blur-none":
          {},
      },
      //Dropfile:inner
      ".nui-fullscreen-dropfile-inner": {
        "@apply fixed inset-0 z-50": {},
        //Dropfile:container
        ".nui-fullscreen-dropfile-container": {
          "@apply flex h-full flex-1 items-center justify-center": {},
          //Dropfile:content
          ".nui-fullscreen-dropfile-content": {
            [`@apply h-${config2.height} w-${config2.width} mx-auto flex flex-col items-center justify-center gap-6 drop-shadow-sm`]:
              {},
            //Background
            "@apply bg-muted-100 dark:bg-muted-800": {},
            //Border
            [`@apply ${config2.rounded} border-2 border-dashed border-${config2.border.light} dark:border-${config2.border.dark}`]:
              {},
            //Icon
            ".nui-fullscreen-dropfile-icon": {
              [`@apply h-${config2.icon.size} w-${config2.icon.size}`]: {},
            },
            //Label
            ".nui-fullscreen-dropfile-label": {
              [`@apply text-${config2.label.font.size} text-muted-500 dark:text-muted-400`]:
                {},
            },
          },
        },
      },
      //Color: primary
      "&.nui-dropfile-primary": {
        ".nui-fullscreen-dropfile-inner": {
          ".nui-fullscreen-dropfile-container": {
            ".nui-fullscreen-dropfile-content": {
              //Icon
              ".nui-fullscreen-dropfile-icon": {
                [`@apply text-${config2.color.primary.light} dark:text-${config2.color.primary.dark}`]:
                  {},
              },
            },
          },
        },
      },
      //Color: dark
      "&.nui-dropfile-dark": {
        ".nui-fullscreen-dropfile-inner": {
          ".nui-fullscreen-dropfile-container": {
            ".nui-fullscreen-dropfile-content": {
              //Icon
              ".nui-fullscreen-dropfile-icon": {
                [`@apply text-${config2.color.dark.light} dark:text-${config2.color.dark.dark}`]:
                  {},
              },
            },
          },
        },
      },
      //Color: black
      "&.nui-dropfile-black": {
        ".nui-fullscreen-dropfile-inner": {
          ".nui-fullscreen-dropfile-container": {
            ".nui-fullscreen-dropfile-content": {
              //Icon
              ".nui-fullscreen-dropfile-icon": {
                [`@apply text-${config2.color.black.light} dark:text-${config2.color.black.dark}`]:
                  {},
              },
            },
          },
        },
      },
    },
  });
}, config$D);

module.export = fullscreenDropfile;

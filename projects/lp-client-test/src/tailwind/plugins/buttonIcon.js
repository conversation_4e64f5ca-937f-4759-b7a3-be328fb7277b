import plugin from "tailwindcss/plugin";

const key$L = "buttonIcon";
const defaultConfig$K = {
  font: {
    family: "sans",
    size: "sm",
    weight: "normal",
  },
  rounded: {
    sm: "rounded",
    md: "rounded-md",
    lg: "rounded-lg",
    full: "rounded-full",
  },
  size: {
    sm: {
      size: "8",
      padding: "1",
    },
    md: {
      size: "10",
      padding: "2",
    },
    lg: {
      size: "12",
      padding: "3",
    },
    xl: {
      size: "14",
      padding: "4",
    },
  },
  color: {
    default: {
      background: {
        base: {
          light: "white",
          dark: "muted-700",
        },
        hover: {
          light: "muted-50",
          dark: "muted-600",
        },
      },
      text: {
        color: {
          light: "muted-700",
          dark: "muted-100",
        },
      },
      border: {
        light: "muted-200",
        dark: "muted-600",
      },
    },
    defaultContrast: {
      background: {
        base: {
          light: "white",
          dark: "muted-950",
        },
        hover: {
          light: "muted-50",
          dark: "muted-900",
        },
      },
      text: {
        color: {
          light: "muted-700",
          dark: "muted-100",
        },
      },
      border: {
        light: "muted-300",
        dark: "muted-800",
      },
    },
    muted: {
      background: {
        base: {
          light: "muted-200",
          dark: "muted-700",
        },
        hover: {
          light: "muted-100",
          dark: "muted-600",
        },
      },
      text: {
        color: {
          light: "muted-500",
          dark: "muted-100",
        },
      },
    },
    mutedContrast: {
      background: {
        base: {
          light: "muted-200",
          dark: "muted-950",
        },
        hover: {
          light: "muted-100",
          dark: "muted-900",
        },
      },
      text: {
        color: {
          light: "muted-500",
          dark: "muted-100",
        },
      },
    },
    light: {
      background: {
        base: {
          light: "muted-200",
          dark: "muted-700",
        },
        hover: {
          light: "muted-100",
          dark: "muted-600",
        },
      },
      text: {
        color: {
          light: "muted-500",
          dark: "muted-100",
        },
      },
    },
    dark: {
      background: {
        base: {
          light: "muted-900",
          dark: "muted-100",
        },
        hover: {
          light: "muted-800",
          dark: "muted-50",
        },
      },
      text: {
        color: {
          light: "muted-100",
          dark: "muted-900",
        },
      },
    },
    black: {
      background: {
        base: {
          light: "black",
          dark: "white",
        },
        hover: {
          light: "black/90",
          dark: "white/90",
        },
      },
      text: {
        color: {
          light: "white",
          dark: "black",
        },
      },
    },
    primary: {
      background: {
        base: {
          light: "primary-500/20",
          dark: "primary-500/20",
        },
        hover: {
          light: "primary-500/10",
          dark: "primary-500/10",
        },
      },
      text: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    info: {
      background: {
        base: {
          light: "info-500/20",
          dark: "info-500/20",
        },
        hover: {
          light: "info-500/10",
          dark: "info-500/10",
        },
      },
      text: {
        color: {
          light: "info-500",
          dark: "info-500",
        },
      },
    },
    success: {
      background: {
        base: {
          light: "success-500/20",
          dark: "success-500/20",
        },
        hover: {
          light: "success-500/10",
          dark: "success-500/10",
        },
      },
      text: {
        color: {
          light: "success-500",
          dark: "success-500",
        },
      },
    },
    warning: {
      background: {
        base: {
          light: "warning-500/20",
          dark: "warning-500/20",
        },
        hover: {
          light: "warning-500/10",
          dark: "warning-500/10",
        },
      },
      text: {
        color: {
          light: "warning-500",
          dark: "warning-500",
        },
      },
    },
    danger: {
      background: {
        base: {
          light: "danger-500/20",
          dark: "danger-500/20",
        },
        hover: {
          light: "danger-500/10",
          dark: "danger-500/10",
        },
      },
      text: {
        color: {
          light: "danger-500",
          dark: "danger-500",
        },
      },
    },
  },
  transition: {
    property: "all",
    duration: "300",
  },
};

const config$L = {
  theme: {
    nui: {
      [key$L]: defaultConfig$K,
    },
  },
};
const buttonIcon = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$L}`);
  addComponents({
    ".nui-button-icon": {
      //Base
      "@apply nui-focus relative inline-flex items-center justify-center space-x-1 leading-5 no-underline disabled:opacity-60 disabled:cursor-not-allowed hover:shadow-none":
        {},
      //Font
      [`@apply font-${config2.font.family} text-${config2.font.size} font-${config2.font.weight}`]:
        {},
      //Transition
      [`@apply transition-${config2.transition.property} duration-${config2.transition.duration}`]:
        {},
      //Size:sm
      "&.nui-button-sm": {
        [`@apply h-${config2.size.sm.size} w-${config2.size.sm.size} p-${config2.size.sm.padding}`]:
          {},
      },
      //Size:md
      "&.nui-button-md": {
        [`@apply h-${config2.size.md.size} w-${config2.size.md.size} p-${config2.size.md.padding}`]:
          {},
      },
      //Size:lg
      "&.nui-button-lg": {
        [`@apply h-${config2.size.lg.size} w-${config2.size.lg.size} p-${config2.size.lg.padding}`]:
          {},
      },
      //Size:xl
      "&.nui-button-xl": {
        [`@apply h-${config2.size.xl.size} w-${config2.size.xl.size} p-${config2.size.xl.padding}`]:
          {},
      },
      //Rounded:sm
      "&.nui-button-rounded-sm": {
        [`@apply ${config2.rounded.sm}`]: {},
      },
      //Rounded:md
      "&.nui-button-rounded-md": {
        [`@apply ${config2.rounded.md}`]: {},
      },
      //Rounded:lg
      "&.nui-button-rounded-lg": {
        [`@apply ${config2.rounded.lg}`]: {},
      },
      //Rounded:full
      "&.nui-button-rounded-full": {
        [`@apply ${config2.rounded.full}`]: {},
      },
      //Loading
      "&.nui-button-loading": {
        "@apply !text-transparent": {},
      },
      //Color:default
      "&.nui-button-default": {
        //Font
        [`@apply text-${config2.color.default.text.color.light} dark:text-${config2.color.default.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.default.background.base.light} dark:bg-${config2.color.default.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.default.background.hover.light} dark:hover:bg-${config2.color.default.background.hover.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.color.default.border.light} dark:border-${config2.color.default.border.dark}`]:
          {},
      },
      //Color:default-contrast
      "&.nui-button-default-contrast": {
        //Font
        [`@apply text-${config2.color.defaultContrast.text.color.light} dark:text-${config2.color.defaultContrast.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.defaultContrast.background.base.light} dark:bg-${config2.color.defaultContrast.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.defaultContrast.background.hover.light} dark:hover:bg-${config2.color.defaultContrast.background.hover.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.color.defaultContrast.border.light} dark:border-${config2.color.defaultContrast.border.dark}`]:
          {},
      },
      //Color:muted
      "&.nui-button-muted": {
        //Font
        [`@apply text-${config2.color.muted.text.color.light} dark:text-${config2.color.muted.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.muted.background.base.light} dark:bg-${config2.color.muted.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.muted.background.hover.light} dark:hover:bg-${config2.color.muted.background.hover.dark}`]:
          {},
      },
      //Color:muted-contrast
      "&.nui-button-muted-contrast": {
        //Font
        [`@apply text-${config2.color.mutedContrast.text.color.light} dark:text-${config2.color.mutedContrast.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.mutedContrast.background.base.light} dark:bg-${config2.color.mutedContrast.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.mutedContrast.background.hover.light} dark:hover:bg-${config2.color.mutedContrast.background.hover.dark}`]:
          {},
      },
      //Color:light
      "&.nui-button-light": {
        //Font
        [`@apply text-${config2.color.light.text.color.light} dark:text-${config2.color.light.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.light.background.base.light} dark:bg-${config2.color.light.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.light.background.hover.light} dark:hover:bg-${config2.color.light.background.hover.dark}`]:
          {},
      },
      //Color:dark
      "&.nui-button-dark": {
        //Font
        [`@apply text-${config2.color.dark.text.color.light} dark:text-${config2.color.dark.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.dark.background.base.light} dark:bg-${config2.color.dark.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.dark.background.hover.light} dark:hover:bg-${config2.color.dark.background.hover.dark}`]:
          {},
      },
      //Color:black
      "&.nui-button-black": {
        //Font
        [`@apply text-${config2.color.black.text.color.light} dark:text-${config2.color.black.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.black.background.base.light} dark:bg-${config2.color.black.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.black.background.hover.light} dark:hover:bg-${config2.color.black.background.hover.dark}`]:
          {},
      },
      //Color:primary
      "&.nui-button-primary": {
        //Font
        [`@apply text-${config2.color.primary.text.color.light} dark:text-${config2.color.primary.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.primary.background.base.light} dark:bg-${config2.color.primary.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.primary.background.hover.light} dark:hover:bg-${config2.color.primary.background.hover.dark}`]:
          {},
      },
      //Color:info
      "&.nui-button-info": {
        //Font
        [`@apply text-${config2.color.info.text.color.light} dark:text-${config2.color.info.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.info.background.base.light} dark:bg-${config2.color.info.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.info.background.hover.light} dark:hover:bg-${config2.color.info.background.hover.dark}`]:
          {},
      },
      //Color:success
      "&.nui-button-success": {
        //Font
        [`@apply text-${config2.color.success.text.color.light} dark:text-${config2.color.success.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.success.background.base.light} dark:bg-${config2.color.success.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.success.background.hover.light} dark:hover:bg-${config2.color.success.background.hover.dark}`]:
          {},
      },
      //Color:warning
      "&.nui-button-warning": {
        //Font
        [`@apply text-${config2.color.warning.text.color.light} dark:text-${config2.color.warning.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.warning.background.base.light} dark:bg-${config2.color.warning.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.warning.background.hover.light} dark:hover:bg-${config2.color.warning.background.hover.dark}`]:
          {},
      },
      //Color:danger
      "&.nui-button-danger": {
        //Font
        [`@apply text-${config2.color.danger.text.color.light} dark:text-${config2.color.danger.text.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.danger.background.base.light} dark:bg-${config2.color.danger.background.base.dark}`]:
          {},
        //Background:hover
        [`@apply hover:bg-${config2.color.danger.background.hover.light} dark:hover:bg-${config2.color.danger.background.hover.dark}`]:
          {},
      },
    },
  });
}, config$L);

module.export = buttonIcon;

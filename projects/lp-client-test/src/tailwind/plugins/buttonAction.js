import plugin from "tailwindcss/plugin";

const key$O = "buttonAction";
const defaultConfig$N = {
  rounded: {
    none: "none",
    sm: "md",
    md: "lg",
    lg: "xl",
    full: "full",
  },
  text: {
    font: {
      family: "sans",
      weight: "normal",
      size: "sm",
    },
  },
  color: {
    default: {
      font: {
        color: {
          light: "muted-700",
          dark: "muted-100",
        },
      },
      background: {
        base: {
          light: "white",
          dark: "muted-700",
        },
        hover: {
          light: "muted-50",
          dark: "muted-600",
        },
        active: {
          light: "muted-100",
          dark: "muted-700",
        },
      },
      border: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
    defaultContrast: {
      font: {
        color: {
          light: "muted-700",
          dark: "muted-100",
        },
      },
      background: {
        base: {
          light: "white",
          dark: "muted-950",
        },
        hover: {
          light: "muted-50",
          dark: "muted-900",
        },
        active: {
          light: "muted-100",
          dark: "muted-950",
        },
      },
      border: {
        light: "muted-300",
        dark: "muted-800",
      },
    },
    muted: {
      font: {
        color: {
          light: "muted-500",
          dark: "muted-100",
        },
      },
      background: {
        base: {
          light: "muted-200",
          dark: "muted-700/40",
        },
        hover: {
          light: "muted-100",
          dark: "muted-700/60",
        },
        active: {
          light: "muted-200/50",
          dark: "muted-800",
        },
      },
      border: {
        light: "muted-200",
        dark: "muted-700/40",
      },
    },
    mutedContrast: {
      font: {
        color: {
          light: "muted-500",
          dark: "muted-100",
        },
      },
      background: {
        base: {
          light: "muted-200",
          dark: "muted-950",
        },
        hover: {
          light: "muted-100",
          dark: "muted-900",
        },
        active: {
          light: "muted-200/50",
          dark: "muted-950",
        },
      },
      border: {
        light: "muted-200",
        dark: "muted-800",
      },
    },
    light: {
      font: {
        color: {
          light: "muted-500",
          dark: "muted-100",
        },
      },
      background: {
        base: {
          light: "muted-200",
          dark: "muted-700",
        },
        hover: {
          light: "muted-100",
          dark: "muted-600",
        },
        active: {
          light: "muted-200",
          dark: "muted-700",
        },
      },
      border: {
        light: "muted-200",
        dark: "muted-700",
      },
    },
    dark: {
      font: {
        color: {
          light: "muted-100",
          dark: "muted-900",
        },
      },
      background: {
        base: {
          light: "muted-900",
          dark: "muted-100",
        },
        hover: {
          light: "muted-800",
          dark: "muted-50",
        },
        active: {
          light: "muted-900",
          dark: "muted-100",
        },
      },
      border: {
        light: "muted-900",
        dark: "muted-100",
      },
    },
    black: {
      font: {
        color: {
          light: "white",
          dark: "black",
        },
      },
      background: {
        base: {
          light: "black",
          dark: "white",
        },
        hover: {
          light: "muted-800",
          dark: "white/90",
        },
        active: {
          light: "black",
          dark: "white",
        },
      },
      border: {
        light: "black",
        dark: "white",
      },
    },
    primary: {
      font: {
        color: {
          light: "white",
          dark: "white",
        },
      },
      background: {
        base: {
          light: "primary-500",
          dark: "primary-500",
        },
        hover: {
          light: "primary-400",
          dark: "primary-400",
        },
        active: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
      border: {
        light: "primary-500",
        dark: "primary-500",
      },
    },
    info: {
      font: {
        color: {
          light: "white",
          dark: "white",
        },
      },
      background: {
        base: {
          light: "info-500",
          dark: "info-500",
        },
        hover: {
          light: "info-400",
          dark: "info-400",
        },
        active: {
          light: "info-500",
          dark: "info-500",
        },
      },
      border: {
        light: "info-500",
        dark: "info-500",
      },
    },
    success: {
      font: {
        color: {
          light: "white",
          dark: "white",
        },
      },
      background: {
        base: {
          light: "success-500",
          dark: "success-500",
        },
        hover: {
          light: "success-400",
          dark: "success-400",
        },
        active: {
          light: "success-500",
          dark: "success-500",
        },
      },
      border: {
        light: "success-500",
        dark: "success-500",
      },
    },
    warning: {
      font: {
        color: {
          light: "white",
          dark: "white",
        },
      },
      background: {
        base: {
          light: "warning-500",
          dark: "warning-500",
        },
        hover: {
          light: "warning-400",
          dark: "warning-400",
        },
        active: {
          light: "warning-500",
          dark: "warning-500",
        },
      },
      border: {
        light: "warning-500",
        dark: "warning-500",
      },
    },
    danger: {
      font: {
        color: {
          light: "white",
          dark: "white",
        },
      },
      background: {
        base: {
          light: "danger-500",
          dark: "danger-500",
        },
        hover: {
          light: "danger-400",
          dark: "danger-400",
        },
        active: {
          light: "danger-500",
          dark: "danger-500",
        },
      },
      border: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
  },
  transition: {
    property: "all",
    duration: "300",
  },
};

const config$O = {
  theme: {
    nui: {
      [key$O]: defaultConfig$N,
    },
  },
};
const buttonAction = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$O}`);
  addComponents({
    ".nui-button-action": {
      //Wrapper
      "@apply nui-focus relative inline-flex items-center justify-center border leading-5 no-underline h-8 px-3 py-2 space-x-1":
        {},
      //Font
      [`@apply font-${config2.text.font.family} font-${config2.text.font.weight} text-${config2.text.font.size}`]:
        {},
      //State
      "@apply disabled:opacity-60 disabled:cursor-not-allowed hover:enabled:shadow-none":
        {},
      //Transition
      [`@apply transition-${config2.transition.property} duration-${config2.transition.duration}`]:
        {},
      //Rounded:sm
      "&.nui-button-rounded-sm": {
        [`@apply rounded-${config2.rounded.sm}`]: {},
      },
      //Rounded:md
      "&.nui-button-rounded-md": {
        [`@apply rounded-${config2.rounded.md}`]: {},
      },
      //Rounded:lg
      "&.nui-button-rounded-lg": {
        [`@apply rounded-${config2.rounded.lg}`]: {},
      },
      //Rounded:full
      "&.nui-button-rounded-full": {
        [`@apply rounded-${config2.rounded.full}`]: {},
      },
      //ButtonAction:loading
      "&.nui-button-loading": {
        "@apply !text-transparent": {},
      },
      //Color:default
      "&.nui-button-default": {
        //Font
        [`@apply text-${config2.color.default.font.color.light} dark:text-${config2.color.default.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.default.background.base.light} dark:bg-${config2.color.default.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.default.background.hover.light} dark:hover:enabled:bg-${config2.color.default.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.default.background.active.light} dark:active:enabled:bg-${config2.color.default.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.default.border.light} dark:border-${config2.color.default.border.dark}`]:
          {},
      },
      //Color:default-contrast
      "&.nui-button-default-contrast": {
        //Font
        [`@apply text-${config2.color.defaultContrast.font.color.light} dark:text-${config2.color.defaultContrast.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.defaultContrast.background.base.light} dark:bg-${config2.color.defaultContrast.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.defaultContrast.background.hover.light} dark:hover:enabled:bg-${config2.color.defaultContrast.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.defaultContrast.background.active.light} dark:active:enabled:bg-${config2.color.defaultContrast.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.defaultContrast.border.light} dark:border-${config2.color.defaultContrast.border.dark}`]:
          {},
      },
      //Color:muted
      "&.nui-button-muted": {
        //Font
        [`@apply text-${config2.color.muted.font.color.light} dark:text-${config2.color.muted.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.muted.background.base.light} dark:bg-${config2.color.muted.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.muted.background.hover.light} dark:hover:enabled:bg-${config2.color.muted.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.muted.background.active.light} dark:active:enabled:bg-${config2.color.muted.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.muted.border.light} dark:border-${config2.color.muted.border.dark}`]:
          {},
      },
      //Color:muted-contrast
      "&.nui-button-muted-contrast": {
        //Font
        [`@apply text-${config2.color.mutedContrast.font.color.light} dark:text-${config2.color.mutedContrast.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.mutedContrast.background.base.light} dark:bg-${config2.color.mutedContrast.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.mutedContrast.background.hover.light} dark:hover:enabled:bg-${config2.color.mutedContrast.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.mutedContrast.background.active.light} dark:active:enabled:bg-${config2.color.mutedContrast.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.mutedContrast.border.light} dark:border-${config2.color.mutedContrast.border.dark}`]:
          {},
      },
      //Color:light
      "&.nui-button-light": {
        //Font
        [`@apply text-${config2.color.light.font.color.light} dark:text-${config2.color.light.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.light.background.base.light} dark:bg-${config2.color.light.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.light.background.hover.light} dark:hover:enabled:bg-${config2.color.light.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.light.background.active.light} dark:active:enabled:bg-${config2.color.light.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.light.border.light} dark:border-${config2.color.light.border.dark}`]:
          {},
      },
      //Color:dark
      "&.nui-button-dark": {
        //Font
        [`@apply text-${config2.color.dark.font.color.light} dark:text-${config2.color.dark.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.dark.background.base.light} dark:bg-${config2.color.dark.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.dark.background.hover.light} dark:hover:enabled:bg-${config2.color.dark.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.dark.background.active.light} dark:active:enabled:bg-${config2.color.dark.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.dark.border.light} dark:border-${config2.color.dark.border.dark}`]:
          {},
      },
      //Color:black
      "&.nui-button-black": {
        //Font
        [`@apply text-${config2.color.black.font.color.light} dark:text-${config2.color.black.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.black.background.base.light} dark:bg-${config2.color.black.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.black.background.hover.light} dark:hover:enabled:bg-${config2.color.black.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.black.background.active.light} dark:active:enabled:bg-${config2.color.black.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.black.border.light} dark:border-${config2.color.black.border.dark}`]:
          {},
      },
      //Color:primary
      "&.nui-button-primary": {
        //Font
        [`@apply text-${config2.color.primary.font.color.light} dark:text-${config2.color.primary.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.primary.background.base.light} dark:bg-${config2.color.primary.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.primary.background.hover.light} dark:hover:enabled:bg-${config2.color.primary.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.primary.background.active.light} dark:active:enabled:bg-${config2.color.primary.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.primary.border.light} dark:border-${config2.color.primary.border.dark}`]:
          {},
      },
      //Color:info
      "&.nui-button-info": {
        //Font
        [`@apply text-${config2.color.info.font.color.light} dark:text-${config2.color.info.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.info.background.base.light} dark:bg-${config2.color.info.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.info.background.hover.light} dark:hover:enabled:bg-${config2.color.info.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.info.background.active.light} dark:active:enabled:bg-${config2.color.info.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.info.border.light} dark:border-${config2.color.info.border.dark}`]:
          {},
      },
      //Color:success
      "&.nui-button-success": {
        //Font
        [`@apply text-${config2.color.success.font.color.light} dark:text-${config2.color.success.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.success.background.base.light} dark:bg-${config2.color.success.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.success.background.hover.light} dark:hover:enabled:bg-${config2.color.success.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.success.background.active.light} dark:active:enabled:bg-${config2.color.success.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.success.border.light} dark:border-${config2.color.success.border.dark}`]:
          {},
      },
      //Color:warning
      "&.nui-button-warning": {
        //Font
        [`@apply text-${config2.color.warning.font.color.light} dark:text-${config2.color.warning.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.warning.background.base.light} dark:bg-${config2.color.warning.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.warning.background.hover.light} dark:hover:enabled:bg-${config2.color.warning.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.warning.background.active.light} dark:active:enabled:bg-${config2.color.warning.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.warning.border.light} dark:border-${config2.color.warning.border.dark}`]:
          {},
      },
      //Color:danger
      "&.nui-button-danger": {
        //Font
        [`@apply text-${config2.color.danger.font.color.light} dark:text-${config2.color.danger.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.danger.background.base.light} dark:bg-${config2.color.danger.background.base.dark}`]:
          {},
        //Hover
        [`@apply hover:enabled:bg-${config2.color.danger.background.hover.light} dark:hover:enabled:bg-${config2.color.danger.background.hover.dark}`]:
          {},
        //Active
        [`@apply active:enabled:bg-${config2.color.danger.background.active.light} dark:active:enabled:bg-${config2.color.danger.background.active.dark}`]:
          {},
        //Border
        [`@apply border-${config2.color.danger.border.light} dark:border-${config2.color.danger.border.dark}`]:
          {},
      },
    },
  });
}, config$O);

module.export = buttonAction;

import plugin from "tailwindcss/plugin";

const key$e = "radio";
const defaultConfig$d = {
  outer: {
    size: "5",
  },
  inner: {
    size: "full",
    rounded: "rounded-full",
    border: {
      light: "muted-400",
      dark: "muted-600",
    },
    background: {
      light: "white",
      dark: "muted-700",
    },
  },
  dot: {
    size: "1",
    rounded: "rounded-full",
    background: {
      light: "current",
      dark: "current",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  input: {
    size: "5",
  },
  label: {
    font: {
      family: "sans",
      size: "sm",
      color: {
        light: "muted-400",
        dark: "muted-400",
      },
    },
  },
  error: {
    font: {
      family: "sans",
      size: "sm",
      color: {
        light: "danger-600",
        dark: "danger-500",
      },
    },
  },
  color: {
    default: {
      light: "muted-600",
      dark: "muted-200",
    },
    muted: {
      light: "muted-400",
      dark: "muted-400",
    },
    light: {
      light: "muted-100",
      dark: "muted-100",
    },
    dark: {
      light: "muted-900",
      dark: "muted-100",
    },
    black: {
      light: "black",
      dark: "white",
    },
    primary: {
      light: "primary-500",
      dark: "primary-500",
    },
    info: {
      light: "info-500",
      dark: "info-500",
    },
    success: {
      light: "success-500",
      dark: "success-500",
    },
    warning: {
      light: "warning-500",
      dark: "warning-500",
    },
    danger: {
      light: "danger-500",
      dark: "danger-500",
    },
  },
};

const config$e = {
  theme: {
    nui: {
      [key$e]: defaultConfig$d,
    },
  },
};
const radio = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$e}`);
  addComponents({
    ".nui-radio": {
      "@apply relative inline-flex items-start gap-1": {},
      ".nui-radio-outer": {
        "@apply nui-focus relative flex items-center justify-center shrink-0 cursor-pointer overflow-hidden rounded-full":
          {},
        //Size
        [`@apply h-${config2.outer.size} w-${config2.outer.size}`]: {},
      },
      //Radio:inner
      ".nui-radio-inner": {
        //Base
        [`@apply absolute start-0 top-0 z-0 ${config2.inner.rounded}`]: {},
        //Size
        [`@apply h-${config2.inner.size} w-${config2.inner.size}`]: {},
        //Background
        [`@apply bg-${config2.inner.background.light} dark:bg-${config2.inner.background.dark}`]:
          {},
        //Border
        [`@apply border-2 border-${config2.inner.border.light} dark:border-${config2.inner.border.dark}`]:
          {},
      },
      //Radio:dot
      ".nui-radio-dot": {
        //Base
        [`@apply pointer-events-none z-10 block scale-0 ${config2.dot.rounded}`]:
          {},
        //Size
        [`@apply h-${config2.dot.size} w-${config2.dot.size}`]: {},
        //Background
        [`@apply bg-${config2.dot.background.light} dark:bg-${config2.dot.background.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.dot.transition.property} duration-${config2.dot.transition.duration}`]:
          {},
      },
      //Radio:input
      ".nui-radio-input": {
        [`@apply absolute z-20 h-${config2.input.size} w-${config2.input.size} cursor-pointer opacity-0`]:
          {},
        //Input:checked:inner
        "&:checked ~ .nui-radio-inner": {
          "@apply border-current": {},
        },
        //Input:checked:dot
        "&:checked ~ .nui-radio-dot": {
          "@apply scale-100": {},
        },
      },
      //Radio:label
      ".nui-radio-label-wrapper": {
        "@apply inline-flex flex-col": {},
      },
      //Label:text
      ".nui-radio-label-text": {
        "@apply ms-1 cursor-pointer select-none": {},
        //Font
        [`@apply font-${config2.label.font.family} text-${config2.label.font.size} text-${config2.label.font.color.light} dark:text-${config2.label.font.color.dark}`]:
          {},
      },
      //Radio:error
      ".nui-radio-error": {
        "@apply ms-1 inline-block": {},
      },
      //Color:default
      "&.nui-radio-default": {
        [`@apply text-${config2.color.default.light} dark:text-${config2.color.default.dark}`]:
          {},
      },
      //Color:muted
      "&.nui-radio-muted": {
        [`@apply text-${config2.color.muted.light} dark:text-${config2.color.muted.dark}`]:
          {},
      },
      //Color:light
      "&.nui-radio-light": {
        [`@apply text-${config2.color.light.light} dark:text-${config2.color.light.dark}`]:
          {},
      },
      //Color:dark
      "&.nui-radio-dark": {
        [`@apply text-${config2.color.dark.light} dark:text-${config2.color.dark.dark}`]:
          {},
      },
      //Color:black
      "&.nui-radio-black": {
        [`@apply text-${config2.color.black.light} dark:text-${config2.color.black.dark}`]:
          {},
      },
      //Color:primary
      "&.nui-radio-primary": {
        [`@apply text-${config2.color.primary.light} dark:text-${config2.color.primary.dark}`]:
          {},
      },
      //Color:info
      "&.nui-radio-info": {
        [`@apply text-${config2.color.info.light} dark:text-${config2.color.info.dark}`]:
          {},
      },
      //Color:success
      "&.nui-radio-success": {
        [`@apply text-${config2.color.success.light} dark:text-${config2.color.default.dark}`]:
          {},
      },
      //Color:warning
      "&.nui-radio-warning": {
        [`@apply text-${config2.color.warning.light} dark:text-${config2.color.warning.dark}`]:
          {},
      },
      //Color:danger
      "&.nui-radio-danger": {
        [`@apply text-${config2.color.danger.light} dark:text-${config2.color.danger.dark}`]:
          {},
      },
    },
  });
}, config$e);

module.export = radio;

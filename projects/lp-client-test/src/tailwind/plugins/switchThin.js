import plugin from "tailwindcss/plugin";

const key$9 = "switchThin";
const defaultConfig$9 = {
  input: {
    size: "full",
  },
  handle: {
    size: "6",
    rounded: "rounded-full",
    border: {
      light: "muted-300",
      dark: "muted-600",
    },
    background: {
      light: "white",
      dark: "muted-700",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  track: {
    rounded: "rounded-full",
    background: {
      light: "muted-300",
      dark: "muted-600",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  label: {
    single: {
      font: {
        family: "sans",
        size: "sm",
        color: {
          light: "muted-400",
          dark: "muted-400",
        },
      },
    },
    dual: {
      label: {
        font: {
          family: "sans",
          weight: "medium",
          size: "sm",
          color: {
            light: "muted-800",
            dark: "white",
          },
        },
      },
      sublabel: {
        font: {
          family: "sans",
          size: "xs",
          color: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
    },
  },
  color: {
    primary: {
      light: "primary-400",
      dark: "primary-400",
    },
    info: {
      light: "info-400",
      dark: "info-400",
    },
    success: {
      light: "success-400",
      dark: "success-400",
    },
    warning: {
      light: "warning-400",
      dark: "warning-400",
    },
    danger: {
      light: "danger-400",
      dark: "danger-400",
    },
    dark: {
      light: "muted-900",
      dark: "muted-100",
    },
    black: {
      light: "black",
      dark: "white",
    },
  },
};

const config$9 = {
  theme: {
    nui: {
      [key$9]: defaultConfig$9,
    },
  },
};
const switchThin = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$9}`);
  addComponents({
    //Wrapper
    ".nui-switch-thin": {
      "@apply flex cursor-pointer items-center": {},
      //Switch:outer
      ".nui-switch-thin-outer": {
        [`@apply nui-focus relative block h-4 ${config2.track.rounded}`]: {},
      },
      //Switch:handle
      ".nui-switch-thin-handle": {
        [`@apply absolute -start-1 top-1/2 -translate-y-1/2 flex items-center justify-center ${config2.handle.rounded}`]:
          {},
        //Size
        [`@apply h-${config2.handle.size} w-${config2.handle.size}`]: {},
        //Background
        [`@apply bg-${config2.handle.background.light} dark:bg-${config2.handle.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.handle.border.light} dark:border-${config2.handle.border.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.track.transition.property} duration-${config2.track.transition.duration}`]:
          {},
      },
      //Switch:track
      ".nui-switch-thin-track": {
        //Base
        [`@apply block h-4 w-10 ${config2.track.rounded}`]: {},
        //Background
        [`@apply bg-${config2.track.background.light} dark:bg-${config2.track.background.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.track.transition.property} duration-${config2.track.transition.duration}`]:
          {},
      },
      //Label:single
      ".nui-switch-thin-single-label": {
        //Base
        "@apply relative ms-3 cursor-pointer select-none": {},
        //Font
        [`@apply font-${config2.label.single.font.family} text-${config2.label.single.font.size}`]:
          {},
        //Color
        [`@apply text-${config2.label.single.font.color.light} dark:text-${config2.label.single.font.color.dark}`]:
          {},
      },
      //Label:dual
      ".nui-switch-thin-dual-label": {
        "@apply ms-3": {},
        ".nui-switch-thin-label": {
          //Base
          "@apply block": {},
          //Font
          [`@apply font-${config2.label.dual.label.font.family} text-${config2.label.dual.label.font.size}`]:
            {},
          //Color
          [`@apply text-${config2.label.dual.label.font.color.light} dark:text-${config2.label.dual.label.font.color.dark}`]:
            {},
        },
        //Label:sublabel
        ".nui-switch-thin-sublabel": {
          //Base
          "@apply block": {},
          //Font
          [`@apply font-${config2.label.dual.sublabel.font.family} text-${config2.label.dual.sublabel.font.size}`]:
            {},
          //Color
          [`@apply text-${config2.label.dual.sublabel.font.color.light} dark:text-${config2.label.dual.sublabel.font.color.dark}`]:
            {},
        },
      },
      //Switch:input
      ".nui-switch-thin-input": {
        [`@apply absolute z-20 h-${config2.input.size} w-${config2.input.size} cursor-pointer opacity-0`]:
          {},
        //Input:checked
        "&:checked ~ .nui-switch-thin-handle": {
          "@apply -translate-y-1/2 translate-x-full rtl:-translate-x-full": {},
        },
      },
      //color:primary
      "&.nui-switch-thin-primary .nui-switch-thin-input:checked ~ .nui-switch-thin-track":
        {
          [`@apply bg-${config2.color.primary.light} dark:bg-${config2.color.primary.dark}`]:
            {},
        },
      //color:info
      "&.nui-switch-thin-info .nui-switch-thin-input:checked ~ .nui-switch-thin-track":
        {
          [`@apply bg-${config2.color.info.light} dark:bg-${config2.color.info.dark}`]:
            {},
        },
      //color:success
      "&.nui-switch-thin-success .nui-switch-thin-input:checked ~ .nui-switch-thin-track":
        {
          [`@apply bg-${config2.color.success.light} dark:bg-${config2.color.success.dark}`]:
            {},
        },
      //color:warning
      "&.nui-switch-thin-warning .nui-switch-thin-input:checked ~ .nui-switch-thin-track":
        {
          [`@apply bg-${config2.color.warning.light} dark:bg-${config2.color.warning.dark}`]:
            {},
        },
      //color:danger
      "&.nui-switch-thin-danger .nui-switch-thin-input:checked ~ .nui-switch-thin-track":
        {
          [`@apply bg-${config2.color.danger.light} dark:bg-${config2.color.danger.dark}`]:
            {},
        },
      //color:dark
      "&.nui-switch-thin-dark .nui-switch-thin-input:checked ~ .nui-switch-thin-track":
        {
          [`@apply bg-${config2.color.dark.light} dark:bg-${config2.color.dark.dark}`]:
            {},
        },
      //color:black
      "&.nui-switch-thin-black .nui-switch-thin-input:checked ~ .nui-switch-thin-track":
        {
          [`@apply bg-${config2.color.black.light} dark:bg-${config2.color.black.dark}`]:
            {},
        },
    },
  });
}, config$9);

module.export = switchThin;

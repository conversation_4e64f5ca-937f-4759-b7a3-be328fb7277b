import plugin from "tailwindcss/plugin";
const key$w = "inputHelpText";
const defaultConfig$v = {
  font: {
    family: "sans",
    size: "[0.65rem]",
    weight: "medium",
  },
};

const config$w = {
  theme: {
    nui: {
      [key$w]: defaultConfig$v,
    },
  },
};
const inputHelpText = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$w}`);
  addComponents({
    ".nui-input-help-text": {
      //Base
      "@apply mt-1 block": {},
      //Font
      [`@apply font-${config2.font.family} text-${config2.font.size} font-${config2.font.weight} leading-none`]:
        {},
    },
  });
}, config$w);

module.export = inputHelpText;

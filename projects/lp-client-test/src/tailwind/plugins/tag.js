import plugin from "tailwindcss/plugin";

const key$6 = "tag";
const defaultConfig$6 = {
  rounded: {
    sm: "rounded",
    md: "rounded-md",
    lg: "rounded-lg",
    full: "rounded-full",
  },
  padding: {
    x: "3",
  },
  font: {
    family: "sans",
  },
  size: {
    sm: {
      padding: {
        y: "1",
      },
      font: {
        size: "[0.65rem]",
      },
    },
    md: {
      padding: {
        y: "1.5",
      },
      font: {
        size: "xs",
      },
    },
  },
  variant: {
    solid: {
      default: {
        font: {
          color: {
            light: "muted-600",
            dark: "muted-300",
          },
        },
        background: {
          light: "white",
          dark: "muted-800",
        },
        border: {
          light: "muted-300",
          dark: "muted-700",
        },
        shadow: {
          size: "xl",
        },
      },
      defaultContrast: {
        font: {
          color: {
            light: "muted-600",
            dark: "muted-100",
          },
        },
        background: {
          light: "white",
          dark: "muted-950",
        },
        border: {
          light: "muted-300",
          dark: "muted-800",
        },
        shadow: {
          size: "xl",
        },
      },
      muted: {
        font: {
          color: {
            light: "muted-500",
            dark: "muted-200",
          },
        },
        background: {
          light: "muted-100",
          dark: "muted-800",
        },
        border: {
          light: "muted-100",
          dark: "muted-800",
        },
        shadow: {
          size: "xl",
        },
      },
      mutedContrast: {
        font: {
          color: {
            light: "muted-500",
            dark: "muted-200",
          },
        },
        background: {
          light: "muted-100",
          dark: "muted-950",
        },
        border: {
          light: "muted-100",
          dark: "muted-950",
        },
        shadow: {
          size: "xl",
        },
      },
      light: {
        font: {
          color: {
            light: "muted-500",
            dark: "muted-100",
          },
        },
        background: {
          light: "muted-200",
          dark: "muted-700",
        },
        border: {
          light: "muted-200",
          dark: "muted-700",
        },
        shadow: {
          size: "xl",
        },
      },
      dark: {
        font: {
          color: {
            light: "muted-100",
            dark: "muted-900",
          },
        },
        background: {
          light: "muted-900",
          dark: "muted-100",
        },
        border: {
          light: "muted-900",
          dark: "muted-100",
        },
        shadow: {
          size: "xl",
        },
      },
      black: {
        font: {
          color: {
            light: "white",
            dark: "black",
          },
        },
        background: {
          light: "black",
          dark: "white",
        },
        border: {
          light: "black",
          dark: "white",
        },
        shadow: {
          size: "xl",
        },
      },
      primary: {
        font: {
          color: {
            light: "white",
            dark: "white",
          },
        },
        background: {
          light: "primary-500",
          dark: "primary-500",
        },
        border: {
          light: "primary-500",
          dark: "primary-500",
        },
        shadow: {
          size: "xl",
          light: "primary-500/30",
          dark: "primary-500/20",
        },
      },
      info: {
        font: {
          color: {
            light: "white",
            dark: "white",
          },
        },
        background: {
          light: "info-500",
          dark: "info-500",
        },
        border: {
          light: "info-500",
          dark: "info-500",
        },
        shadow: {
          size: "xl",
          light: "info-500/30",
          dark: "info-500/20",
        },
      },
      success: {
        font: {
          color: {
            light: "white",
            dark: "white",
          },
        },
        background: {
          light: "success-500",
          dark: "success-500",
        },
        border: {
          light: "success-500",
          dark: "success-500",
        },
        shadow: {
          size: "xl",
          light: "success-500/30",
          dark: "success-500/20",
        },
      },
      secondary: {
        font: {
          color: {
            light: "white",
            dark: "white",
          },
        },
        background: {
          light: "secondary-500",
          dark: "secondary-500",
        },
        border: {
          light: "secondary-500",
          dark: "secondary-500",
        },
        shadow: {
          size: "xl",
          light: "secondary-500/30",
          dark: "secondary-500/20",
        },
      },
      warning: {
        font: {
          color: {
            light: "white",
            dark: "white",
          },
        },
        background: {
          light: "warning-500",
          dark: "warning-500",
        },
        border: {
          light: "warning-500",
          dark: "warning-500",
        },
        shadow: {
          size: "xl",
          light: "warning-500/30",
          dark: "warning-500/20",
        },
      },
      danger: {
        font: {
          color: {
            light: "white",
            dark: "white",
          },
        },
        background: {
          light: "danger-500",
          dark: "danger-500",
        },
        border: {
          light: "danger-500",
          dark: "danger-500",
        },
        shadow: {
          size: "xl",
          light: "danger-500/30",
          dark: "danger-500/20",
        },
      },
    },
    pastel: {
      default: {
        font: {
          color: {
            light: "muted-500",
            dark: "muted-100",
          },
        },
        background: {
          light: "muted-100",
          dark: "muted-100/10",
        },
        border: {
          light: "muted-100",
          dark: "muted-100/10",
        },
        shadow: {
          size: "xl",
        },
      },
      muted: {
        font: {
          color: {
            light: "muted-500",
            dark: "muted-200",
          },
        },
        background: {
          light: "muted-100",
          dark: "muted-800",
        },
        border: {
          light: "muted-100",
          dark: "muted-800",
        },
        shadow: {
          size: "xl",
        },
      },
      light: {
        font: {
          color: {
            light: "muted-800",
            dark: "muted-100",
          },
        },
        background: {
          light: "white/30",
          dark: "white/20",
        },
        border: {
          light: "white/30",
          dark: "white/20",
        },
        shadow: {
          size: "xl",
        },
      },
      dark: {
        font: {
          color: {
            light: "muted-100",
            dark: "muted-900",
          },
        },
        background: {
          light: "muted-900",
          dark: "muted-100",
        },
        border: {
          light: "muted-900",
          dark: "muted-100",
        },
        shadow: {
          size: "xl",
        },
      },
      black: {
        font: {
          color: {
            light: "white",
            dark: "black",
          },
        },
        background: {
          light: "black",
          dark: "white",
        },
        border: {
          light: "black",
          dark: "white",
        },
        shadow: {
          size: "xl",
        },
      },
      primary: {
        background: {
          light: "primary-500/20",
          dark: "primary-500/10",
        },
        border: {
          light: "primary-500/20",
          dark: "primary-500/10",
        },
        font: {
          color: {
            light: "primary-500",
            dark: "primary-500",
          },
        },
      },
      info: {
        background: {
          light: "info-500/20",
          dark: "info-500/10",
        },
        border: {
          light: "info-500/20",
          dark: "info-500/10",
        },
        font: {
          color: {
            light: "info-500",
            dark: "info-500",
          },
        },
      },
      success: {
        background: {
          light: "success-500/20",
          dark: "success-500/10",
        },
        border: {
          light: "success-500/20",
          dark: "success-500/10",
        },
        font: {
          color: {
            light: "success-500",
            dark: "success-500",
          },
        },
      },
      warning: {
        background: {
          light: "warning-500/20",
          dark: "warning-500/10",
        },
        border: {
          light: "warning-500/20",
          dark: "warning-500/10",
        },
        font: {
          color: {
            light: "warning-500",
            dark: "warning-500",
          },
        },
      },
      danger: {
        background: {
          light: "danger-500/20",
          dark: "danger-500/10",
        },
        border: {
          light: "danger-500/20",
          dark: "danger-500/10",
        },
        font: {
          color: {
            light: "danger-500",
            dark: "danger-500",
          },
        },
      },
    },
    outline: {
      default: {
        background: {
          light: "transparent",
          dark: "transparent",
        },
        border: {
          light: "muted-500",
          dark: "muted-100",
        },
        font: {
          color: {
            light: "muted-500",
            dark: "muted-100",
          },
        },
      },
      muted: {
        background: {
          light: "transparent",
          dark: "transparent",
        },
        border: {
          light: "muted-400",
          dark: "muted-100",
        },
        font: {
          color: {
            light: "muted-400",
            dark: "muted-100",
          },
        },
      },
      light: {
        background: {
          light: "transparent",
          dark: "transparent",
        },
        border: {
          light: "muted-100",
          dark: "muted-100",
        },
        font: {
          color: {
            light: "muted-100",
            dark: "muted-100",
          },
        },
      },
      dark: {
        background: {
          light: "transparent",
          dark: "transparent",
        },
        border: {
          light: "muted-900",
          dark: "muted-100",
        },
        font: {
          color: {
            light: "muted-900",
            dark: "muted-100",
          },
        },
      },
      black: {
        background: {
          light: "transparent",
          dark: "transparent",
        },
        border: {
          light: "black",
          dark: "white",
        },
        font: {
          color: {
            light: "black",
            dark: "white",
          },
        },
      },
      primary: {
        background: {
          light: "transparent",
          dark: "transparent",
        },
        border: {
          light: "primary-500",
          dark: "primary-500",
        },
        font: {
          color: {
            light: "primary-500",
            dark: "primary-500",
          },
        },
      },
      info: {
        background: {
          light: "transparent",
          dark: "transparent",
        },
        border: {
          light: "info-500",
          dark: "info-500",
        },
        font: {
          color: {
            light: "info-500",
            dark: "info-500",
          },
        },
      },
      success: {
        background: {
          light: "transparent",
          dark: "transparent",
        },
        border: {
          light: "success-500",
          dark: "success-500",
        },
        font: {
          color: {
            light: "success-500",
            dark: "success-500",
          },
        },
      },
      secondary: {
        background: {
          light: "secondary-500/20",
          dark: "secondary-500/10",
        },
        border: {
          light: "secondary-500/20",
          dark: "secondary-500/10",
        },
        font: {
          color: {
            light: "secondary-500",
            dark: "secondary-500",
          },
        },
      },
      warning: {
        background: {
          light: "transparent",
          dark: "transparent",
        },
        border: {
          light: "warning-500",
          dark: "warning-500",
        },
        font: {
          color: {
            light: "warning-500",
            dark: "warning-500",
          },
        },
      },
      danger: {
        background: {
          light: "transparent",
          dark: "transparent",
        },
        border: {
          light: "danger-500",
          dark: "danger-500",
        },
        font: {
          color: {
            light: "danger-500",
            dark: "danger-500",
          },
        },
      },
    },
  },
  transition: {
    property: "all",
    duration: "300",
  },
};

const config$6 = {
  theme: {
    nui: {
      [key$6]: defaultConfig$6,
    },
  },
};
const tag = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$6}`);
  addComponents({
    //Wrapper
    ".nui-tag": {
      [`@apply inline-block px-${config2.padding.x} font-${config2.font.family} transition-${config2.transition.property} duration-${config2.transition.duration}`]:
        {},
      //Size:sm
      "&.nui-tag-sm": {
        //Padding
        [`@apply py-${config2.size.sm.padding.y}`]: {},
        //Size
        [`@apply text-${config2.size.sm.font.size}`]: {},
      },
      //Size:md
      "&.nui-tag-md": {
        //Padding
        [`@apply py-${config2.size.md.padding.y}`]: {},
        //Size
        [`@apply text-${config2.size.md.font.size}`]: {},
      },
      //Rounded:sm
      "&.nui-tag-rounded-sm": {
        [`@apply ${config2.rounded.sm}`]: {},
      },
      //Rounded:md
      "&.nui-tag-rounded-md": {
        [`@apply ${config2.rounded.md}`]: {},
      },
      //Rounded:lg
      "&.nui-tag-rounded-lg": {
        [`@apply ${config2.rounded.lg}`]: {},
      },
      //Rounded:full
      "&.nui-tag-rounded-full": {
        [`@apply ${config2.rounded.full}`]: {},
      },
      //Variant:solid
      "&.nui-tag-solid": {
        //Color:default
        "&.nui-tag-default": {
          //Color
          [`@apply text-${config2.variant.solid.default.font.color.light} dark:text-${config2.variant.solid.default.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.default.background.light} dark:bg-${config2.variant.solid.default.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.default.border.light} dark:border-${config2.variant.solid.default.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.default.shadow.size}`]: {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.default.shadow.size}`]:
              {},
          },
        },
        //Color:default-contrast
        "&.nui-tag-default-contrast": {
          //Color
          [`@apply text-${config2.variant.solid.defaultContrast.font.color.light} dark:text-${config2.variant.solid.defaultContrast.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.defaultContrast.background.light} dark:bg-${config2.variant.solid.defaultContrast.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.defaultContrast.border.light} dark:border-${config2.variant.solid.defaultContrast.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.defaultContrast.shadow.size}`]:
              {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.defaultContrast.shadow.size}`]:
              {},
          },
        },
        //Color:muted
        "&.nui-tag-muted": {
          //Color
          [`@apply text-${config2.variant.solid.muted.font.color.light} dark:text-${config2.variant.solid.muted.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.muted.background.light} dark:bg-${config2.variant.solid.muted.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.muted.border.light} dark:border-${config2.variant.solid.muted.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.muted.shadow.size}`]: {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.muted.shadow.size}`]:
              {},
          },
        },
        //Color:muted-contrast
        "&.nui-tag-muted-contrast": {
          //Color
          [`@apply text-${config2.variant.solid.mutedContrast.font.color.light} dark:text-${config2.variant.solid.mutedContrast.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.mutedContrast.background.light} dark:bg-${config2.variant.solid.mutedContrast.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.mutedContrast.border.light} dark:border-${config2.variant.solid.mutedContrast.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.mutedContrast.shadow.size}`]:
              {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.mutedContrast.shadow.size}`]:
              {},
          },
        },
        //Color:light
        "&.nui-tag-light": {
          //Color
          [`@apply text-${config2.variant.solid.light.font.color.light} dark:text-${config2.variant.solid.light.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.light.background.light} dark:bg-${config2.variant.solid.light.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.light.border.light} dark:border-${config2.variant.solid.light.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.light.shadow.size}`]: {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.light.shadow.size}`]:
              {},
          },
        },
        //Color:dark
        "&.nui-tag-dark": {
          //Color
          [`@apply text-${config2.variant.solid.dark.font.color.light} dark:text-${config2.variant.solid.dark.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.dark.background.light} dark:bg-${config2.variant.solid.dark.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.dark.border.light} dark:border-${config2.variant.solid.dark.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.dark.shadow.size}`]: {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.dark.shadow.size}`]:
              {},
          },
        },
        //Color:black
        "&.nui-tag-black": {
          //Color
          [`@apply text-${config2.variant.solid.black.font.color.light} dark:text-${config2.variant.solid.black.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.black.background.light} dark:bg-${config2.variant.solid.black.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.black.border.light} dark:border-${config2.variant.solid.black.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.black.shadow.size}`]: {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.black.shadow.size}`]:
              {},
          },
        },
        //Color:primary
        "&.nui-tag-primary": {
          //Color
          [`@apply text-${config2.variant.solid.primary.font.color.light} dark:text-${config2.variant.solid.primary.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.primary.background.light} dark:bg-${config2.variant.solid.primary.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.primary.border.light} dark:border-${config2.variant.solid.primary.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.primary.shadow.size} shadow-${config2.variant.solid.primary.shadow.light} shadow-${config2.variant.solid.primary.shadow.dark}`]:
              {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.primary.shadow.size} hover:shadow-${config2.variant.solid.primary.shadow.light} hover:shadow-${config2.variant.solid.primary.shadow.dark}`]:
              {},
          },
        },
        //Color:info
        "&.nui-tag-info": {
          //Color
          [`@apply text-${config2.variant.solid.info.font.color.light} dark:text-${config2.variant.solid.info.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.info.background.light} dark:bg-${config2.variant.solid.info.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.info.border.light} dark:border-${config2.variant.solid.info.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.info.shadow.size} shadow-${config2.variant.solid.info.shadow.light} shadow-${config2.variant.solid.info.shadow.dark}`]:
              {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.info.shadow.size} hover:shadow-${config2.variant.solid.info.shadow.light} hover:shadow-${config2.variant.solid.info.shadow.dark}`]:
              {},
          },
        },
        //Color:success
        "&.nui-tag-success": {
          //Color
          [`@apply text-${config2.variant.solid.success.font.color.light} dark:text-${config2.variant.solid.success.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.success.background.light} dark:bg-${config2.variant.solid.success.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.success.border.light} dark:border-${config2.variant.solid.success.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.success.shadow.size} shadow-${config2.variant.solid.success.shadow.light} shadow-${config2.variant.solid.success.shadow.dark}`]:
              {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.success.shadow.size} hover:shadow-${config2.variant.solid.success.shadow.light} hover:shadow-${config2.variant.solid.success.shadow.dark}`]:
              {},
          },
        },
        //Color:warning
        "&.nui-tag-warning": {
          //Color
          [`@apply text-${config2.variant.solid.warning.font.color.light} dark:text-${config2.variant.solid.warning.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.warning.background.light} dark:bg-${config2.variant.solid.warning.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.warning.border.light} dark:border-${config2.variant.solid.warning.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.warning.shadow.size} shadow-${config2.variant.solid.warning.shadow.light} shadow-${config2.variant.solid.warning.shadow.dark}`]:
              {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.warning.shadow.size} hover:shadow-${config2.variant.solid.warning.shadow.light} hover:shadow-${config2.variant.solid.warning.shadow.dark}`]:
              {},
          },
        },
        //Color:danger
        "&.nui-tag-danger": {
          //Color
          [`@apply text-${config2.variant.solid.danger.font.color.light} dark:text-${config2.variant.solid.danger.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.solid.danger.background.light} dark:bg-${config2.variant.solid.danger.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.solid.danger.border.light} dark:border-${config2.variant.solid.danger.border.dark}`]:
            {},
          //Shadow:static
          "&.nui-tag-shadow": {
            [`@apply shadow-${config2.variant.solid.danger.shadow.size} shadow-${config2.variant.solid.danger.shadow.light} shadow-${config2.variant.solid.danger.shadow.dark}`]:
              {},
          },
          //Shadow:hover
          "&.nui-tag-shadow-hover": {
            [`@apply hover:shadow-${config2.variant.solid.danger.shadow.size} hover:shadow-${config2.variant.solid.danger.shadow.light} hover:shadow-${config2.variant.solid.danger.shadow.dark}`]:
              {},
          },
        },
      },
      //Variant:pastel
      "&.nui-tag-pastel": {
        //Color:default
        "&.nui-tag-default, &.nui-tag-default-contrast": {
          //Color
          [`@apply text-${config2.variant.pastel.default.font.color.light} dark:text-${config2.variant.pastel.default.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.pastel.default.background.light} dark:bg-${config2.variant.pastel.default.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.pastel.default.border.light} dark:border-${config2.variant.pastel.default.border.dark}`]:
            {},
        },
        //Color:muted
        "&.nui-tag-muted, &.nui-tag-muted-contrast": {
          //Color
          [`@apply text-${config2.variant.pastel.muted.font.color.light} dark:text-${config2.variant.pastel.muted.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.pastel.muted.background.light} dark:bg-${config2.variant.pastel.muted.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.pastel.muted.border.light} dark:border-${config2.variant.pastel.muted.border.dark}`]:
            {},
        },
        //Color:light
        "&.nui-tag-light": {
          //Color
          [`@apply text-${config2.variant.pastel.light.font.color.light} dark:text-${config2.variant.pastel.light.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.pastel.light.background.light} dark:bg-${config2.variant.pastel.light.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.pastel.light.border.light} dark:border-${config2.variant.pastel.light.border.dark}`]:
            {},
        },
        //Color:dark
        "&.nui-tag-dark": {
          //Color
          [`@apply text-${config2.variant.pastel.dark.font.color.light} dark:text-${config2.variant.pastel.dark.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.pastel.dark.background.light} dark:bg-${config2.variant.pastel.dark.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.pastel.dark.border.light} dark:border-${config2.variant.pastel.dark.border.dark}`]:
            {},
        },
        //Color:black
        "&.nui-tag-black": {
          //Color
          [`@apply text-${config2.variant.pastel.black.font.color.light} dark:text-${config2.variant.pastel.black.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.pastel.black.background.light} dark:bg-${config2.variant.pastel.black.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.pastel.black.border.light} dark:border-${config2.variant.pastel.black.border.dark}`]:
            {},
        },
        //Color:primary
        "&.nui-tag-primary": {
          //Color
          [`@apply text-${config2.variant.pastel.primary.font.color.light} dark:text-${config2.variant.pastel.primary.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.pastel.primary.background.light} dark:bg-${config2.variant.pastel.primary.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.pastel.primary.border.light} dark:border-${config2.variant.pastel.primary.border.dark}`]:
            {},
        },
        //Color:info
        "&.nui-tag-info": {
          //Color
          [`@apply text-${config2.variant.pastel.info.font.color.light} dark:text-${config2.variant.pastel.info.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.pastel.info.background.light} dark:bg-${config2.variant.pastel.info.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.pastel.info.border.light} dark:border-${config2.variant.pastel.info.border.dark}`]:
            {},
        },
        //Color:success
        "&.nui-tag-success": {
          //Color
          [`@apply text-${config2.variant.pastel.success.font.color.light} dark:text-${config2.variant.pastel.success.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.pastel.success.background.light} dark:bg-${config2.variant.pastel.success.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.pastel.success.border.light} dark:border-${config2.variant.pastel.success.border.dark}`]:
            {},
        },
        //Color:warning
        "&.nui-tag-warning": {
          //Color
          [`@apply text-${config2.variant.pastel.warning.font.color.light} dark:text-${config2.variant.pastel.warning.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.pastel.warning.background.light} dark:bg-${config2.variant.pastel.warning.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.pastel.warning.border.light} dark:border-${config2.variant.pastel.warning.border.dark}`]:
            {},
        },
        //Color:danger
        "&.nui-tag-danger": {
          //Color
          [`@apply text-${config2.variant.pastel.danger.font.color.light} dark:text-${config2.variant.pastel.danger.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.pastel.danger.background.light} dark:bg-${config2.variant.pastel.danger.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.pastel.danger.border.light} dark:border-${config2.variant.pastel.danger.border.dark}`]:
            {},
        },
      },
      //Variant:outline
      "&.nui-tag-outline": {
        //Color:default
        "&.nui-tag-default, &.nui-tag-default-contrast": {
          //Color
          [`@apply text-${config2.variant.outline.default.font.color.light} dark:text-${config2.variant.outline.default.font.color.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.outline.default.border.light} dark:border-${config2.variant.outline.default.border.dark}`]:
            {},
        },
        //Color:muted
        "&.nui-tag-muted, &.nui-tag-muted-contrast": {
          //Color
          [`@apply text-${config2.variant.outline.muted.font.color.light} dark:text-${config2.variant.outline.muted.font.color.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.outline.muted.border.light} dark:border-${config2.variant.outline.muted.border.dark}`]:
            {},
        },
        //Color:light
        "&.nui-tag-light": {
          //Color
          [`@apply text-${config2.variant.outline.light.font.color.light} dark:text-${config2.variant.outline.light.font.color.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.outline.light.border.light} dark:border-${config2.variant.outline.light.border.dark}`]:
            {},
        },
        //Color:dark
        "&.nui-tag-dark": {
          //Color
          [`@apply text-${config2.variant.outline.dark.font.color.light} dark:text-${config2.variant.outline.dark.font.color.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.outline.dark.border.light} dark:border-${config2.variant.outline.dark.border.dark}`]:
            {},
        },
        //Color:black
        "&.nui-tag-black": {
          //Color
          [`@apply text-${config2.variant.outline.black.font.color.light} dark:text-${config2.variant.outline.black.font.color.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.outline.black.border.light} dark:border-${config2.variant.outline.black.border.dark}`]:
            {},
        },
        //Color:primary
        "&.nui-tag-primary": {
          //Color
          [`@apply text-${config2.variant.outline.primary.font.color.light} dark:text-${config2.variant.outline.primary.font.color.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.outline.primary.border.light} dark:border-${config2.variant.outline.primary.border.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.outline.primary.background.light} dark:bg-${config2.variant.outline.primary.background.dark}`]:
            {},
        },
        //Color:info
        "&.nui-tag-info": {
          //Color
          [`@apply text-${config2.variant.outline.info.font.color.light} dark:text-${config2.variant.outline.info.font.color.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.outline.info.border.light} dark:border-${config2.variant.outline.info.border.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.outline.info.background.light} dark:bg-${config2.variant.outline.info.background.dark}`]:
            {},
        },
        //Color:success
        "&.nui-tag-success": {
          //Color
          [`@apply text-${config2.variant.outline.success.font.color.light} dark:text-${config2.variant.outline.success.font.color.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.outline.success.border.light} dark:border-${config2.variant.outline.success.border.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.outline.success.background.light} dark:bg-${config2.variant.outline.success.background.dark}`]:
            {},
        },
        //Color:warning
        "&.nui-tag-warning": {
          //Color
          [`@apply text-${config2.variant.outline.warning.font.color.light} dark:text-${config2.variant.outline.warning.font.color.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.outline.warning.border.light} dark:border-${config2.variant.outline.warning.border.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.outline.warning.background.light} dark:bg-${config2.variant.outline.warning.background.dark}`]:
            {},
        },
        //Color:danger
        "&.nui-tag-danger": {
          //Color
          [`@apply text-${config2.variant.outline.danger.font.color.light} dark:text-${config2.variant.outline.danger.font.color.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.variant.outline.danger.border.light} dark:border-${config2.variant.outline.danger.border.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.variant.outline.danger.background.light} dark:bg-${config2.variant.outline.danger.background.dark}`]:
            {},
        },
      },
    },
  });
}, config$6);
module.export = tag;

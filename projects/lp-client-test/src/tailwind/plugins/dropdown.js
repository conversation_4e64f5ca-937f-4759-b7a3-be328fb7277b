import plugin from "tailwindcss/plugin";

const key$F = "dropdown";
const defaultConfig$E = {
  align: "start",
  button: {
    context: {
      size: "9",
      rounded: "full",
      inner: {
        size: "9",
        rounded: "full",
        background: {
          light: "white",
          dark: "muted-800",
        },
        border: {
          light: "muted-200",
          dark: "muted-700",
        },
      },
      icon: {
        size: "5",
        font: {
          color: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
      ring: {
        offset: {
          size: "4",
          color: {
            light: "white",
            dark: "muted-900",
          },
        },
        color: {
          hover: {
            light: "muted-200",
            dark: "muted-700",
          },
        },
      },
      transition: {
        property: "all",
        duration: "300",
      },
    },
    chevron: {
      size: "4",
      transition: {
        property: "transform",
        duration: "300",
      },
    },
  },
  menu: {
    header: {
      title: {
        font: {
          family: "sans",
          weight: "medium",
          size: "xs",
          color: {
            light: "muted-500",
            dark: "muted-200",
          },
        },
      },
    },
    rounded: {
      sm: "rounded-md",
      md: "rounded-lg",
      lg: "rounded-xl",
    },
    color: {
      default: {
        background: {
          light: "white",
          dark: "muted-800",
        },
        border: {
          light: "muted-200",
          dark: "muted-700",
        },
      },
      defaultContrast: {
        background: {
          light: "white",
          dark: "muted-950",
        },
        border: {
          light: "muted-200",
          dark: "muted-800",
        },
      },
      muted: {
        background: {
          light: "white",
          dark: "muted-800",
        },
        border: {
          light: "muted-200",
          dark: "muted-700",
        },
      },
      mutedContrast: {
        background: {
          light: "white",
          dark: "muted-950",
        },
        border: {
          light: "muted-200",
          dark: "muted-800",
        },
      },
    },
    shadow: {
      size: "lg",
      light: "muted-300/30",
      dark: "muted-800/20",
    },
  },
};

const config$F = {
  theme: {
    nui: {
      [key$F]: defaultConfig$E,
    },
  },
};
const dropdown = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$F}`);
  addComponents({
    //Wrapper
    ".nui-dropdown": {
      [`@apply text-${config2.align}`]: {},
      //Menu
      ".nui-menu": {
        "@apply relative inline-block": {},
      },
      //Menu Content
      ".nui-menu-content": {
        "@apply p-2": {},
      },
      //Button:context
      ".nui-context-button": {
        //Base
        [`@apply inline-flex items-center justify-center rounded-${config2.button.context.rounded}`]:
          {},
        //Size
        [`@apply h-${config2.button.context.size} w-${config2.button.context.size}`]:
          {},
        //Ring
        [`@apply ring-1 ring-transparent ring-offset-${config2.button.context.ring.offset.color.light} dark:ring-offset-${config2.button.context.ring.offset.color.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.button.context.transition.property} duration-${config2.button.context.transition.duration}`]:
          {},
        //Context:inner
        ".nui-context-button-inner": {
          //Base
          [`@apply flex items-center justify-center rounded-${config2.button.context.inner.rounded}`]:
            {},
          //Size
          [`@apply h-${config2.button.context.inner.size} w-${config2.button.context.inner.size}`]:
            {},
          //Background
          [`@apply bg-${config2.button.context.inner.background.light} dark:bg-${config2.button.context.inner.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.button.context.inner.border.light} dark:border-${config2.button.context.inner.border.dark}`]:
            {},
        },
        //Context:icon
        ".nui-context-icon": {
          //Size
          [`@apply h-${config2.button.context.icon.size} w-${config2.button.context.icon.size}`]:
            {},
          //Color
          [`@apply text-${config2.button.context.icon.font.color.light} dark:text-${config2.button.context.icon.font.color.dark}`]:
            {},
          //Transition
          [`@apply transition-${config2.button.context.transition.property} duration-${config2.button.context.transition.duration}`]:
            {},
        },
      },
      //Button:text
      ".nui-text-button": {
        //Base
        "@apply flex items-center space-x-1": {},
      },
      //Button:chevron
      ".nui-chevron": {
        [`@apply h-${config2.button.chevron.size} w-${config2.button.chevron.size}`]:
          {},
        //Transition
        [`@apply transition-${config2.button.chevron.transition.property} duration-${config2.button.chevron.transition.duration}`]:
          {},
      },
      //Orientation:start
      "&.nui-dropdown-start": {
        ".nui-dropdown-menu": {
          "@apply start-0 origin-top-left": {},
        },
      },
      //Orientation:end
      "&.nui-dropdown-end": {
        ".nui-dropdown-menu": {
          "@apply end-0 origin-top-right": {},
        },
      },
      //Context:hover
      "&:hover": {
        ".nui-context-button": {
          [`@apply ring-offset-${config2.button.context.ring.offset.size} ring-${config2.button.context.ring.color.hover.light} dark:ring-${config2.button.context.ring.color.hover.dark}`]:
            {},
        },
      },
    },
    //Dropdown:menu
    ".nui-dropdown-menu": {
      //Base
      "@apply absolute z-50 mt-2 focus:outline-none": {},
      //Shadow
      [`@apply shadow-${config2.menu.shadow.size} shadow-${config2.menu.shadow.light} dark:shadow-${config2.menu.shadow.dark}`]:
        {},
      //Menu:header
      ".nui-menu-header": {
        "@apply px-4 pt-5": {},
      },
      //Header:inner
      ".nui-menu-header-inner": {
        "@apply relative flex items-center justify-between": {},
      },
      //Header:title
      ".nui-menu-header-title": {
        //Base
        [`@apply font-${config2.menu.header.title.font.family} font-${config2.menu.header.title.font.weight} text-${config2.menu.header.title.font.size} uppercase`]:
          {},
        //Color
        [`@apply text-${config2.menu.header.title.font.color.light} dark:text-${config2.menu.header.title.font.color.dark}`]:
          {},
      },
      //Menu:content
      ".nui-menu-content": {
        "@apply p-2 space-y-1": {},
      },
      //Size:md
      "&.nui-menu-md": {
        "@apply w-56": {},
      },
      //Size:lg
      "&.nui-menu-lg": {
        "@apply w-72": {},
      },
      //Rounded:sm
      "&.nui-menu-rounded-sm": {
        [`@apply ${config2.menu.rounded.sm}`]: {},
      },
      //Rounded:md
      "&.nui-menu-rounded-md": {
        [`@apply ${config2.menu.rounded.md}`]: {},
      },
      //Rounded:lg
      "&.nui-menu-rounded-lg": {
        [`@apply ${config2.menu.rounded.lg}`]: {},
      },
      //Color:default
      "&.nui-menu-default": {
        //Border
        [`@apply border border-${config2.menu.color.default.border.light} dark:border-${config2.menu.color.default.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.menu.color.default.background.light} dark:bg-${config2.menu.color.default.background.dark}`]:
          {},
      },
      //Color:default-contrast
      "&.nui-menu-default-contrast": {
        //Border
        [`@apply border border-${config2.menu.color.defaultContrast.border.light} dark:border-${config2.menu.color.defaultContrast.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.menu.color.defaultContrast.background.light} dark:bg-${config2.menu.color.defaultContrast.background.dark}`]:
          {},
      },
      //Color:muted
      "&.nui-menu-muted": {
        //Border
        [`@apply border border-${config2.menu.color.muted.border.light} dark:border-${config2.menu.color.muted.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.menu.color.muted.background.light} dark:bg-${config2.menu.color.muted.background.dark}`]:
          {},
      },
      //Color:muted-contrast
      "&.nui-menu-muted-contrast": {
        //Border
        [`@apply border border-${config2.menu.color.mutedContrast.border.light} dark:border-${config2.menu.color.mutedContrast.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.menu.color.mutedContrast.background.light} dark:bg-${config2.menu.color.mutedContrast.background.dark}`]:
          {},
      },
    },
  });
}, config$F);

module.export = dropdown;

import plugin from "tailwindcss/plugin";
const key$z = "inputFile";
const defaultConfig$y = {
  drop: {
    height: "64",
    border: {
      base: {
        light: "muted-200",
        dark: "muted-600",
      },
      hover: {
        light: "muted-400",
        dark: "muted-400",
      },
    },
    background: {
      light: "white",
      dark: "muted-700",
    },
    transition: {
      property: "colors",
      duration: "300",
    },
    zone: {
      font: {
        family: "sans",
        weight: "normal",
        size: "sm",
        color: {
          primary: {
            base: {
              light: "muted-400",
              dark: "muted-400",
            },
            hover: {
              light: "primary-500",
              dark: "primary-500",
            },
          },
          dark: {
            base: {
              light: "muted-400",
              dark: "muted-400",
            },
            hover: {
              light: "muted-900",
              dark: "muted-100",
            },
          },
          black: {
            base: {
              light: "muted-400",
              dark: "muted-400",
            },
            hover: {
              light: "black",
              dark: "white",
            },
          },
        },
      },
      icon: {
        size: "10",
        transition: {
          property: "colors",
          duration: "300",
        },
      },
      separator: {
        font: {
          family: "sans",
          weight: "semibold",
          size: "xs",
        },
      },
      input: {
        size: "full",
      },
    },
  },
  button: {
    background: {
      light: "white",
      dark: "muted-700",
    },
    border: {
      primary: {
        base: {
          light: "muted-200",
          dark: "muted-600",
        },
        hover: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
      dark: {
        base: {
          light: "muted-200",
          dark: "muted-600",
        },
        hover: {
          light: "muted-900",
          dark: "muted-100",
        },
      },
      black: {
        base: {
          light: "muted-200",
          dark: "muted-600",
        },
        hover: {
          light: "black",
          dark: "white",
        },
      },
    },
    font: {
      color: {
        primary: {
          base: {
            light: "muted-400",
            dark: "muted-400",
          },
          hover: {
            light: "primary-500",
            dark: "primary-500",
          },
        },
        dark: {
          base: {
            light: "muted-400",
            dark: "muted-400",
          },
          hover: {
            light: "muted-900",
            dark: "muted-100",
          },
        },
        black: {
          base: {
            light: "muted-400",
            dark: "muted-400",
          },
          hover: {
            light: "black",
            dark: "white",
          },
        },
      },
    },
    icon: {
      size: "8",
    },
    label: {
      font: {
        family: "sans",
        size: "sm",
      },
    },
    transition: {
      property: "colors",
      duration: "300",
    },
  },
  combo: {
    padding: "1",
    font: {
      family: "sans",
    },
    background: {
      light: "white",
      dark: "muted-700",
    },
    border: {
      light: "muted-200",
      dark: "muted-600",
    },
    label: {
      font: {
        family: "sans",
        size: "sm",
      },
    },
    input: {
      width: "full",
      font: {
        size: "sm",
        color: {
          light: "muted-500",
          dark: "muted-400",
        },
      },
      file: {
        font: {
          size: "sm",
          color: {
            primary: {
              light: "primary-700",
              dark: "primary-700",
            },
            dark: {
              light: "muted-900",
              dark: "muted-100",
            },
            black: {
              light: "black",
              dark: "white",
            },
          },
        },
        background: {
          primary: {
            base: {
              light: "primary-500/10",
              dark: "primary-500/10",
            },
            hover: {
              light: "primary-500/20",
              dark: "primary-500/20",
            },
          },
          dark: {
            base: {
              light: "muted-900/10",
              dark: "muted-100/10",
            },
            hover: {
              light: "muted-900/20",
              dark: "muted-100/20",
            },
          },
          black: {
            base: {
              light: "black/10",
              dark: "white/10",
            },
            hover: {
              light: "black/20",
              dark: "white/20",
            },
          },
        },
      },
    },
  },
  rounded: {
    sm: "rounded-md",
    md: "rounded-lg",
    lg: "rounded-xl",
    full: "rounded-full",
  },
};

const config$z = {
  theme: {
    nui: {
      [key$z]: defaultConfig$y,
    },
  },
};
const inputFile = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$z}`);
  addComponents({
    //Wrapper
    ".nui-input-file": {
      "@apply relative block nui-focus": {},
      //Variant:drop
      "&.nui-input-file-drop": {
        [`@apply relative h-${config2.drop.height} flex justify-center items-center`]:
          {},
        //Background
        [`@apply bg-${config2.drop.background.light} dark:bg-${config2.drop.background.dark}`]:
          {},
        //Border
        [`@apply border-2 border-dashed border-${config2.drop.border.base.light} dark:border-${config2.drop.border.base.light}`]:
          {},
        //Border:hover
        [`@apply hover:border-${config2.drop.border.hover.light} dark:hover:border-${config2.drop.border.hover.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.drop.transition.property} duration-${config2.drop.transition.duration}`]:
          {},
        //Drop:inner
        ".nui-drop-area-inner": {
          "@apply absolute z-10": {},
        },
        //Drop:zone
        ".nui-drop-zone": {
          [`@apply flex flex-col items-center font-${config2.drop.zone.font.family} text-${config2.drop.zone.font.size}`]:
            {},
        },
        //Zone:icon
        ".nui-drop-zone-icon": {
          //Base
          [`@apply w-${config2.drop.zone.icon.size} h-${config2.drop.zone.icon.size} mb-2`]:
            {},
          //Transition
          [`@apply transition-${config2.drop.zone.icon.transition.property} duration-${config2.drop.zone.icon.transition.duration}`]:
            {},
        },
        //Zone:text
        ".nui-drop-zone-text": {
          //Base
          [`@apply block font-${config2.drop.zone.font.family} font-${config2.drop.zone.font.weight}`]:
            {},
        },
        //Zone:separator
        ".nui-drop-zone-separator": {
          //Base
          [`@apply block font-${config2.drop.zone.separator.font.family} font-${config2.drop.zone.separator.font.weight} leading-none py-1`]:
            {},
        },
        //Zone:input
        ".nui-drop-zone-input": {
          [`@apply absolute top-0 left-0 h-${config2.drop.zone.input.size} w-${config2.drop.zone.input.size} opacity-0 file:cursor-pointer z-20`]:
            {},
        },
        //Color:primary
        "&.nui-input-file-primary": {
          //Zone:icon
          ".nui-drop-zone-icon": {
            //Color
            [`@apply text-${config2.drop.zone.font.color.primary.base.light} dark:text-${config2.drop.zone.font.color.primary.base.dark}`]:
              {},
          },
          //Zone:text
          ".nui-drop-zone-text": {
            //Color
            [`@apply text-${config2.drop.zone.font.color.primary.base.light} dark:text-${config2.drop.zone.font.color.primary.base.dark}`]:
              {},
          },
          //Zone:separator
          ".nui-drop-zone-separator": {
            //Color
            [`@apply text-${config2.drop.zone.font.color.primary.base.light} dark:text-${config2.drop.zone.font.color.primary.base.dark}`]:
              {},
          },
          //Zone:hover
          "&:hover": {
            ".nui-drop-zone-icon": {
              [`@apply text-${config2.drop.zone.font.color.primary.hover.light} dark:text-${config2.drop.zone.font.color.primary.hover.dark}`]:
                {},
            },
          },
        },
        //Color:dark
        "&.nui-input-file-dark": {
          //Zone:icon
          ".nui-drop-zone-icon": {
            //Color
            [`@apply text-${config2.drop.zone.font.color.dark.base.light} dark:text-${config2.drop.zone.font.color.dark.base.dark}`]:
              {},
          },
          //Zone:text
          ".nui-drop-zone-text": {
            //Color
            [`@apply text-${config2.drop.zone.font.color.dark.base.light} dark:text-${config2.drop.zone.font.color.dark.base.dark}`]:
              {},
          },
          //Zone:separator
          ".nui-drop-zone-separator": {
            //Color
            [`@apply text-${config2.drop.zone.font.color.dark.base.light} dark:text-${config2.drop.zone.font.color.dark.base.dark}`]:
              {},
          },
          //Zone:hover
          "&:hover": {
            ".nui-drop-zone-icon": {
              [`@apply text-${config2.drop.zone.font.color.dark.hover.light} dark:text-${config2.drop.zone.font.color.dark.hover.dark}`]:
                {},
            },
          },
        },
        //Color:black
        "&.nui-input-file-black": {
          //Zone:icon
          ".nui-drop-zone-icon": {
            //Color
            [`@apply text-${config2.drop.zone.font.color.black.base.light} dark:text-${config2.drop.zone.font.color.black.base.dark}`]:
              {},
          },
          //Zone:text
          ".nui-drop-zone-text": {
            //Color
            [`@apply text-${config2.drop.zone.font.color.black.base.light} dark:text-${config2.drop.zone.font.color.black.base.dark}`]:
              {},
          },
          //Zone:separator
          ".nui-drop-zone-separator": {
            //Color
            [`@apply text-${config2.drop.zone.font.color.black.base.light} dark:text-${config2.drop.zone.font.color.black.base.dark}`]:
              {},
          },
          //Zone:hover
          "&:hover": {
            ".nui-drop-zone-icon": {
              [`@apply text-${config2.drop.zone.font.color.black.hover.light} dark:text-${config2.drop.zone.font.color.black.hover.dark}`]:
                {},
            },
          },
        },
      },
      //Variant:button
      "&.nui-input-file-button": {
        //Base
        "@apply w-64 max-w-full flex flex-col items-center px-4 py-8 tracking-wide cursor-pointer":
          {},
        //Background
        [`@apply bg-${config2.button.background.light} dark:bg-${config2.button.background.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.button.transition.property} duration-${config2.button.transition.duration}`]:
          {},
        //Button:icon
        ".nui-upload-button-icon": {
          [`@apply w-${config2.button.icon.size} h-${config2.button.icon.size}`]:
            {},
        },
        //Button:label
        ".nui-upload-button-label": {
          //Base
          "@apply block mt-2 leading-normal": {},
          //Font
          [`@apply font-${config2.button.label.font.family} text-${config2.button.label.font.size}`]:
            {},
        },
        //Color:primary
        "&.nui-input-file-primary": {
          //Font
          [`@apply text-${config2.button.font.color.primary.base.light} dark:text-${config2.button.font.color.primary.base.dark}`]:
            {},
          //Font:hover
          [`@apply hover:text-${config2.button.font.color.primary.hover.light} dark:hover:text-${config2.button.font.color.primary.hover.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.button.border.primary.base.light} dark:border-${config2.button.border.primary.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.button.border.primary.hover.light} dark:hover:border-${config2.button.border.primary.hover.dark}`]:
            {},
        },
        //Color:dark
        "&.nui-input-file-dark": {
          //Font
          [`@apply text-${config2.button.font.color.dark.base.light} dark:text-${config2.button.font.color.dark.base.dark}`]:
            {},
          //Font:hover
          [`@apply hover:text-${config2.button.font.color.dark.hover.light} dark:hover:text-${config2.button.font.color.dark.hover.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.button.border.dark.base.light} dark:border-${config2.button.border.dark.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.button.border.dark.hover.light} dark:hover:border-${config2.button.border.dark.hover.dark}`]:
            {},
        },
        //Color:black
        "&.nui-input-file-black": {
          //Font
          [`@apply text-${config2.button.font.color.black.base.light} dark:text-${config2.button.font.color.black.base.dark}`]:
            {},
          //Font:hover
          [`@apply hover:text-${config2.button.font.color.black.hover.light} dark:hover:text-${config2.button.font.color.black.hover.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.button.border.black.base.light} dark:border-${config2.button.border.black.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.button.border.black.hover.light} dark:hover:border-${config2.button.border.black.hover.dark}`]:
            {},
        },
      },
      //Variant:combo
      "&.nui-input-file-combo": {
        //Base
        [`@apply block font-${config2.combo.font.family} p-${config2.combo.padding}`]:
          {},
        //Background
        [`@apply bg-${config2.combo.background.light} dark:bg-${config2.combo.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.combo.border.light} dark:border-${config2.combo.border.dark}`]:
          {},
        //Combo:label
        ".nui-combo-label-text": {
          [`@apply font-${config2.combo.label.font.family} sr-only text-${config2.combo.label.font.size}`]:
            {},
        },
        //Combo:input
        ".nui-combo-input": {
          [`@apply outline-none block w-${config2.combo.input.width} file:me-4 file:py-2 file:px-4 file:border-0 file:cursor-pointer file:transition-colors file:duration-300`]:
            {},
          //Font
          [`@apply text-${config2.combo.input.font.size} text-${config2.combo.input.font.color.light} dark:text-${config2.combo.input.font.color.dark}`]:
            {},
          //File
          [`@apply file:text-${config2.combo.input.file.font.size}`]: {},
        },
        //Color:primary
        "&.nui-input-file-primary": {
          ".nui-combo-input": {
            //Background
            [`@apply file:bg-${config2.combo.input.file.background.primary.base.light} dark:file:bg-${config2.combo.input.file.background.primary.base.dark}`]:
              {},
            //Background:hover
            [`@apply file:hover:bg-${config2.combo.input.file.background.primary.hover.light} dark:file:hover:bg-${config2.combo.input.file.background.primary.hover.dark}`]:
              {},
            //File color
            [`@apply file:text-${config2.combo.input.file.font.color.primary.light} dark:file:text-${config2.combo.input.file.font.color.primary.dark}`]:
              {},
          },
        },
        //Color:dark
        "&.nui-input-file-dark": {
          ".nui-combo-input": {
            //Background
            [`@apply file:bg-${config2.combo.input.file.background.dark.base.light} dark:file:bg-${config2.combo.input.file.background.dark.base.dark}`]:
              {},
            //Background:hover
            [`@apply file:hover:bg-${config2.combo.input.file.background.dark.hover.light} dark:file:hover:bg-${config2.combo.input.file.background.dark.hover.dark}`]:
              {},
            //File color
            [`@apply file:text-${config2.combo.input.file.font.color.dark.light} dark:file:text-${config2.combo.input.file.font.color.dark.dark}`]:
              {},
          },
        },
        //Color:black
        "&.nui-input-file-black": {
          ".nui-combo-input": {
            //Background
            [`@apply file:bg-${config2.combo.input.file.background.black.base.light} dark:file:bg-${config2.combo.input.file.background.black.base.dark}`]:
              {},
            //Background:hover
            [`@apply file:hover:bg-${config2.combo.input.file.background.black.hover.light} dark:file:hover:bg-${config2.combo.input.file.background.black.hover.dark}`]:
              {},
            //File color
            [`@apply file:text-${config2.combo.input.file.font.color.black.light} dark:file:text-${config2.combo.input.file.font.color.black.dark}`]:
              {},
          },
        },
      },
      //Rounded:sm
      "&.nui-input-file-rounded-sm": {
        "&.nui-input-file-drop, &.nui-input-file-button": {
          [`@apply ${config2.rounded.sm}`]: {},
        },
        "&.nui-input-file-combo": {
          [`@apply ${config2.rounded.sm}`]: {},
        },
        "&.nui-input-file-combo .nui-combo-input": {
          [`@apply file:${config2.rounded.sm}`]: {},
        },
      },
      //Rounded:md
      "&.nui-input-file-rounded-md": {
        "&.nui-input-file-drop, &.nui-input-file-button": {
          [`@apply ${config2.rounded.md}`]: {},
        },
        "&.nui-input-file-combo": {
          [`@apply ${config2.rounded.md}`]: {},
        },
        "&.nui-input-file-combo .nui-combo-input": {
          [`@apply file:${config2.rounded.md}`]: {},
        },
      },
      //Rounded:lg
      "&.nui-input-file-rounded-lg": {
        "&.nui-input-file-drop, &.nui-input-file-button": {
          [`@apply ${config2.rounded.lg}`]: {},
        },
        "&.nui-input-file-combo": {
          [`@apply ${config2.rounded.lg}`]: {},
        },
        "&.nui-input-file-combo .nui-combo-input": {
          [`@apply file:${config2.rounded.lg}`]: {},
        },
      },
      //Rounded:full
      "&.nui-input-file-rounded-full": {
        "&.nui-input-file-drop, &.nui-input-file-button": {
          [`@apply ${config2.rounded.full}`]: {},
        },
        "&.nui-input-file-combo": {
          [`@apply ${config2.rounded.full}`]: {},
        },
        "&.nui-input-file-combo .nui-combo-input": {
          [`@apply file:${config2.rounded.full}`]: {},
        },
      },
    },
  });
}, config$z);

module.export = inputFile;

import plugin from "tailwindcss/plugin";

const key$A = "inputFileRegular";
const defaultConfig$z = {
  font: {
    color: {
      light: "muted-400",
      dark: "muted-400",
    },
  },
  rounded: {
    sm: "rounded-md",
    md: "rounded-lg",
    lg: "rounded-xl",
    full: "rounded-full",
  },
  inner: {
    width: "full",
    font: {
      family: "sans",
    },
    transition: {
      property: "colors",
      duration: "300",
    },
  },
  placeholder: {
    font: {
      family: "sans",
      size: "xs",
      color: {
        light: "muted-400",
        dark: "muted-400",
      },
    },
  },
  hover: {
    border: {
      light: "primary-500",
      dark: "primary-500",
    },
    font: {
      color: {
        light: "primary-500",
        dark: "primary-500",
      },
    },
  },
  focus: {
    border: {
      light: "primary-500",
      dark: "primary-500",
    },
    font: {
      color: {
        light: "primary-500",
        dark: "primary-500",
      },
    },
  },
  error: {
    font: {
      family: "sans",
      size: "[0.65rem]",
      weight: "medium",
      color: {
        light: "danger-600",
        dark: "danger-600",
      },
    },
    border: {
      light: "danger-500",
      dark: "danger-500",
    },
  },
  color: {
    default: {
      inner: {
        background: {
          light: "white",
          dark: "muted-800",
        },
        border: {
          light: "muted-300",
          dark: "muted-700",
        },
        font: {
          color: {
            light: "muted-600",
            dark: "muted-200",
          },
        },
        placeholder: {
          light: "muted-300",
          dark: "muted-500",
        },
      },
      addon: {
        background: {
          light: "muted-50",
          dark: "muted-800",
        },
        border: {
          light: "muted-300",
          dark: "muted-700",
        },
        font: {
          color: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
    },
    defaultContrast: {
      inner: {
        background: {
          light: "white",
          dark: "muted-950",
        },
        border: {
          light: "muted-300",
          dark: "muted-800",
        },
        font: {
          color: {
            light: "muted-600",
            dark: "muted-200",
          },
        },
        placeholder: {
          light: "muted-300",
          dark: "muted-600",
        },
      },
      addon: {
        background: {
          light: "muted-50",
          dark: "muted-950",
        },
        border: {
          light: "muted-300",
          dark: "muted-800",
        },
        font: {
          color: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
    },
  },
  icon: {
    disabled: {
      sm: {
        font: {
          size: "xs",
        },
      },
      md: {
        font: {
          size: "[0.825rem]",
        },
      },
      lg: {
        font: {
          size: "sm",
        },
      },
      xl: {
        font: {
          size: "sm",
        },
      },
    },
    enabled: {
      sm: {
        font: {
          size: "xs",
        },
        icon: {
          size: "3",
        },
      },
      md: {
        font: {
          size: "[0.825rem]",
        },
        icon: {
          size: "4",
        },
      },
      lg: {
        font: {
          size: "sm",
        },
        icon: {
          size: "5",
        },
      },
      xl: {
        font: {
          size: "sm",
        },
        icon: {
          size: "6",
        },
      },
    },
  },
};

const config$A = {
  theme: {
    nui: {
      [key$A]: defaultConfig$z,
    },
  },
};
const inputFileRegular = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$A}`);
  addComponents({
    //Wrapper
    ".nui-input-file-regular": {
      "@apply relative w-full": {},
      //Input:label
      ".nui-input-file-label": {
        "@apply nui-label w-full": {},
      },
      //Input:outer
      ".nui-input-file-outer": {
        "@apply relative": {},
      },
      //Input:inner
      ".nui-input-file-inner": {
        [`@apply relative nui-focus w-${config2.inner.width} flex cursor-pointer items-center overflow-hidden disabled:cursor-not-allowed disabled:opacity-75`]:
          {},
        //Font
        [`@apply font-${config2.inner.font.family}`]: {},
        //Transition
        [`@apply transition-${config2.inner.transition.property} duration-${config2.inner.transition.duration}`]:
          {},
      },
      //Input:addon
      ".nui-input-file-addon": {
        "@apply pointer-events-none flex shrink-0 items-center justify-center gap-1 transition-colors duration-100":
          {},
      },
      //Input:placeholder
      ".nui-input-file-placeholder": {
        //Base
        [`@apply font-${config2.placeholder.font.family} text-${config2.placeholder.font.size}`]:
          {},
        //Color
        [`@apply text-${config2.placeholder.font.color.light} dark:text-${config2.placeholder.font.color.dark}`]:
          {},
      },
      //Input:text
      ".nui-input-file-text": {
        [`@apply ms-2 inline-flex truncate text-${config2.font.color.light} dark:text-${config2.font.color.dark}`]:
          {},
      },
      //Input:placeload
      ".nui-input-file-placeload": {
        "@apply absolute -end-8 flex w-[70%] items-center": {},
        ".nui-placeload": {
          "@apply h-3 w-full max-w-[75%] rounded": {},
        },
      },
      //Color:default
      "&.nui-input-default": {
        //Input:inner
        ".nui-input-file-inner": {
          //Base
          [`@apply text-${config2.color.default.inner.font.color.light} dark:text-${config2.color.default.inner.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.default.inner.background.light} dark:bg-${config2.color.default.inner.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.default.inner.border.light} dark:border-${config2.color.default.inner.border.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.default.inner.placeholder.light} dark:placeholder:text-${config2.color.default.inner.placeholder.dark}`]:
            {},
        },
        //Input:addon
        ".nui-input-file-addon": {
          //Background
          [`@apply bg-${config2.color.default.addon.background.light} dark:bg-${config2.color.default.addon.background.dark}`]:
            {},
          //Color
          [`@apply text-${config2.color.default.addon.font.color.light} dark:text-${config2.color.default.addon.font.color.dark}`]:
            {},
          //Border
          [`@apply border-e border-${config2.color.default.addon.border.light} dark:border-${config2.color.default.addon.border.dark}`]:
            {},
        },
      },
      "&.nui-input-default-contrast": {
        //Input:inner
        ".nui-input-file-inner": {
          //Base
          [`@apply text-${config2.color.defaultContrast.inner.font.color.light} dark:text-${config2.color.defaultContrast.inner.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.defaultContrast.inner.background.light} dark:bg-${config2.color.defaultContrast.inner.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.defaultContrast.inner.border.light} dark:border-${config2.color.defaultContrast.inner.border.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.defaultContrast.inner.placeholder.light} dark:placeholder:text-${config2.color.defaultContrast.inner.placeholder.dark}`]:
            {},
        },
        //Input:addon
        ".nui-input-file-addon": {
          //Background
          [`@apply bg-${config2.color.defaultContrast.addon.background.light} dark:bg-${config2.color.defaultContrast.addon.background.dark}`]:
            {},
          //Color
          [`@apply text-${config2.color.defaultContrast.addon.font.color.light} dark:text-${config2.color.defaultContrast.addon.font.color.dark}`]:
            {},
          //Border
          [`@apply border-e border border-${config2.color.defaultContrast.addon.border.light} dark:border-${config2.color.defaultContrast.addon.border.dark}`]:
            {},
        },
      },
      //Input:focus
      "&.nui-input-file-color-focus": {
        ".nui-input-file-inner:focus": {
          [`@apply !border-${config2.focus.border.light} dark:!border-${config2.focus.border.dark}`]:
            {},
        },
      },
      //Input:loading
      "&.nui-input-file-loading": {
        ".nui-input-file-inner": {
          "@apply text-transparent placeholder:text-transparent": {},
        },
      },
      //Error:input
      "&.nui-input-file-error": {
        ".nui-input-file-inner": {
          [`@apply border-${config2.error.border.light} dark:border-${config2.error.border.dark}`]:
            {},
        },
      },
      //Rounded:sm
      "&.nui-input-rounded-sm": {
        ".nui-input-file-inner": {
          [`@apply ${config2.rounded.sm}`]: {},
        },
      },
      //Rounded:md
      "&.nui-input-rounded-md": {
        ".nui-input-file-inner": {
          [`@apply ${config2.rounded.md}`]: {},
        },
      },
      //Rounded:lg
      "&.nui-input-rounded-lg": {
        ".nui-input-file-inner": {
          [`@apply ${config2.rounded.lg}`]: {},
        },
      },
      //Rounded:full
      "&.nui-input-rounded-full": {
        ".nui-input-file-inner": {
          [`@apply ${config2.rounded.full}`]: {},
        },
      },
      //Without icon && Size:sm
      "&.nui-input-sm:not(.nui-has-icon)": {
        [`@apply text-${config2.icon.disabled.sm.font.size} leading-4`]: {},
        ".nui-input-file-inner": {
          "@apply h-8 py-2 gap-1": {},
        },
        ".nui-input-file-addon": {
          "@apply h-8 px-2": {},
        },
        ".nui-input-file-placeload": {
          "@apply top-2.5": {},
        },
      },
      //With icon && Size:sm
      "&.nui-input-sm.nui-has-icon": {
        [`@apply text-${config2.icon.enabled.sm.font.size} leading-4`]: {},
        ".nui-input-file-inner": {
          "@apply h-8 py-2 pe-3": {},
        },
        ".nui-input-file-addon": {
          "@apply h-8 px-2": {},
        },
        ".nui-input-file-icon": {
          [`@apply w-${config2.icon.enabled.sm.icon.size} h-${config2.icon.enabled.sm.icon.size}`]:
            {},
        },
        ".nui-input-file-placeload": {
          "@apply top-2.5": {},
        },
      },
      //Without icon && Size:md
      "&.nui-input-md:not(.nui-has-icon)": {
        [`@apply text-${config2.icon.disabled.md.font.size} leading-5`]: {},
        ".nui-input-file-inner": {
          "@apply h-10 gap-2": {},
        },
        ".nui-input-file-addon": {
          "@apply h-10 px-3": {},
        },
        ".nui-input-file-placeload": {
          "@apply top-3.5": {},
        },
      },
      //With icon && Size:md
      "&.nui-input-md.nui-has-icon": {
        [`@apply text-${config2.icon.enabled.md.font.size} leading-5`]: {},
        ".nui-input-file-inner": {
          "@apply h-10 pe-4": {},
        },
        ".nui-input-file-addon": {
          "@apply h-10 px-3": {},
        },
        ".nui-input-file-icon": {
          [`@apply w-${config2.icon.enabled.md.icon.size} h-${config2.icon.enabled.md.icon.size}`]:
            {},
        },
        ".nui-input-file-placeload": {
          "@apply top-3.5": {},
        },
      },
      //Without icon && Size:lg
      "&.nui-input-lg:not(.nui-has-icon)": {
        [`@apply text-${config2.icon.disabled.lg.font.size} leading-5`]: {},
        ".nui-input-file-inner": {
          "@apply h-12 gap-2": {},
        },
        ".nui-input-file-addon": {
          "@apply h-12 px-4": {},
        },
        ".nui-input-file-placeload": {
          "@apply top-[1.2rem]": {},
        },
      },
      //With icon && Size:lg
      "&.nui-input-lg.nui-has-icon": {
        [`@apply text-${config2.icon.enabled.lg.font.size} leading-5`]: {},
        ".nui-input-file-inner": {
          "@apply h-12 pe-4": {},
        },
        ".nui-input-file-addon": {
          "@apply h-12 px-4": {},
        },
        ".nui-input-file-icon": {
          [`@apply w-${config2.icon.enabled.lg.icon.size} h-${config2.icon.enabled.lg.icon.size}`]:
            {},
        },
        ".nui-input-file-placeload": {
          "@apply top-[1.2rem]": {},
        },
      },
      //Without icon && Size:xl
      "&.nui-input-xl:not(.nui-has-icon)": {
        [`@apply text-${config2.icon.disabled.xl.font.size} leading-5`]: {},
        ".nui-input-file-inner": {
          "@apply h-14 gap-2": {},
        },
        ".nui-input-file-addon": {
          "@apply h-14 px-5": {},
        },
        ".nui-input-file-placeload": {
          "@apply top-[1.4rem]": {},
        },
      },
      //With icon && Size:xl
      "&.nui-input-xl.nui-has-icon": {
        [`@apply text-${config2.icon.enabled.xl.font.size} leading-5`]: {},
        ".nui-input-file-inner": {
          "@apply h-14 pe-4": {},
        },
        ".nui-input-file-addon": {
          "@apply h-14 px-5": {},
        },
        ".nui-input-file-icon": {
          [`@apply w-${config2.icon.enabled.xl.icon.size} h-${config2.icon.enabled.xl.icon.size}`]:
            {},
        },
        ".nui-input-file-placeload": {
          "@apply top-[1.4rem]": {},
        },
      },
      //Input:hover
      "&:hover": {
        ".nui-input-file-addon": {
          [`@apply text-${config2.hover.font.color.light} dark:text-${config2.hover.font.color.dark}`]:
            {},
        },
      },
      //Input:disabled
      "&:disabled": {
        ".nui-input-file-addon": {
          "@apply cursor-not-allowed opacity-75": {},
        },
      },
      //Input:focus
      "&:focus-within": {
        ".nui-input-file-addon": {
          [`@apply text-${config2.focus.font.color.light} dark:text-${config2.focus.font.color.dark}`]:
            {},
        },
      },
    },
  });
}, config$A);

module.export = inputFileRegular;

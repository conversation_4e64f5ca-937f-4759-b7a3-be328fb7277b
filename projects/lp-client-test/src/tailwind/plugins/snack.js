import plugin from "tailwindcss/plugin";

const key$b = "snack";
const defaultConfig$a = {
  rounded: "rounded-full",
  icon: {
    rounded: "rounded-full",
    border: {
      light: "muted-200",
      dark: "muted-700",
    },
    background: {
      light: "white",
      dark: "muted-950",
    },
  },
  image: {
    rounded: "rounded-full",
    outer: {
      rounded: "rounded-full",
    },
    inner: {
      rounded: "rounded-full",
    },
  },
  font: {
    family: "sans",
    color: {
      light: "muted-600",
      dark: "muted-300",
    },
  },
  size: {
    xs: {
      size: "6",
      font: {
        size: "xs",
      },
      icon: {
        outer: {
          size: "6",
        },
        inner: {
          size: "3",
        },
      },
      image: {
        outer: {
          size: "6",
        },
        inner: {
          size: "6",
        },
      },
    },
    sm: {
      size: "8",
      font: {
        size: "sm",
      },
      icon: {
        outer: {
          size: "8",
        },
        inner: {
          size: "4",
        },
      },
      image: {
        outer: {
          size: "8",
        },
        inner: {
          size: "8",
        },
      },
    },
    md: {
      size: "10",
      font: {
        size: "sm",
      },
      icon: {
        outer: {
          size: "10",
        },
        inner: {
          size: "5",
        },
      },
      image: {
        outer: {
          size: "10",
        },
        inner: {
          size: "10",
        },
      },
    },
  },
  color: {
    default: {
      background: {
        light: "white",
        dark: "muted-700",
      },
      border: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
    defaultContrast: {
      background: {
        light: "white",
        dark: "muted-950",
      },
      border: {
        light: "muted-300",
        dark: "muted-800",
      },
    },
    muted: {
      background: {
        light: "muted-200",
        dark: "muted-700",
      },
    },
    mutedContrast: {
      background: {
        light: "muted-200",
        dark: "muted-950",
      },
    },
  },
};

const config$b = {
  theme: {
    nui: {
      [key$b]: defaultConfig$a,
    },
  },
};
const snack = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$b}`);
  addComponents({
    //Wrapper
    ".nui-snack": {
      [`@apply inline-flex items-center gap-1 ${config2.rounded} outline-transparent`]:
        {},
      ".nui-snack-icon": {
        //Base
        [`@apply -ms-0.5 flex items-center justify-center ${config2.icon.rounded}`]:
          {},
        //Background
        [`@apply bg-${config2.icon.background.light} dark:bg-${config2.icon.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.icon.border.light} dark:border-${config2.icon.border.dark}`]:
          {},
      },
      //Snack:image
      ".nui-snack-image": {
        [`@apply -ms-0.5 flex items-center justify-center ${config2.image.rounded} shrink-0`]:
          {},
      },
      //Image:inner
      ".nui-snack-image-inner": {
        [`@apply ${config2.image.rounded}`]: {},
      },
      //Snack:text
      ".nui-snack-text": {
        //Font
        [`@apply font-${config2.font.family}`]: {},
        //Color
        [`@apply text-${config2.font.color.light} dark:text-${config2.font.color.dark}`]:
          {},
      },
      //Size:xs
      "&.nui-snack-xs": {
        //Snack:media:xs
        "&:not(.nui-has-media)": {
          "@apply !ps-2": {},
        },
        //Snack:text
        ".nui-snack-text": {
          //Font
          [`@apply text-${config2.size.xs.font.size}`]: {},
        },
        //Size
        [`@apply h-${config2.size.xs.size}`]: {},
        //Snack:icon
        ".nui-snack-icon": {
          [`@apply w-${config2.size.xs.icon.outer.size} h-${config2.size.xs.icon.outer.size}`]:
            {},
        },
        //Icon:inner
        ".nui-snack-icon-inner": {
          [`@apply h-${config2.size.xs.icon.inner.size} w-${config2.size.xs.icon.inner.size}`]:
            {},
        },
        //Snack:image
        ".nui-snack-image, .nui-snack-image-inner": {
          [`@apply w-${config2.size.xs.image.outer.size} h-${config2.size.xs.image.outer.size}`]:
            {},
        },
      },
      //Size:sm
      "&.nui-snack-sm": {
        //Snack:media:sm
        "&:not(.nui-has-media)": {
          "@apply !ps-3": {},
        },
        //Snack:text
        ".nui-snack-text": {
          //Font
          [`@apply text-${config2.size.sm.font.size}`]: {},
        },
        //Size
        [`@apply h-${config2.size.sm.size}`]: {},
        //Snack:icon
        ".nui-snack-icon": {
          [`@apply w-${config2.size.sm.icon.outer.size} h-${config2.size.sm.icon.outer.size}`]:
            {},
        },
        //Icon:inner
        ".nui-snack-icon-inner": {
          [`@apply h-${config2.size.sm.icon.inner.size} w-${config2.size.sm.icon.inner.size}`]:
            {},
        },
        //Snack:image
        ".nui-snack-image, .nui-snack-image-inner": {
          [`@apply w-${config2.size.sm.image.outer.size} h-${config2.size.sm.image.outer.size}`]:
            {},
        },
      },
      //Size:md
      "&.nui-snack-md": {
        //Snack:media:md
        "&:not(.nui-has-media)": {
          "@apply !ps-4": {},
        },
        //Snack:text
        ".nui-snack-text": {
          //Font
          [`@apply text-${config2.size.md.font.size}`]: {},
        },
        //Size
        [`@apply h-${config2.size.md.size}`]: {},
        //Snack:icon
        ".nui-snack-icon": {
          [`@apply w-${config2.size.md.icon.outer.size} h-${config2.size.md.icon.outer.size}`]:
            {},
        },
        //Icon:inner
        ".nui-snack-icon-inner": {
          [`@apply h-${config2.size.md.icon.inner.size} w-${config2.size.md.icon.inner.size}`]:
            {},
        },
        //Snack:image
        ".nui-snack-image, .nui-snack-image-inner": {
          [`@apply w-${config2.size.md.image.outer.size} h-${config2.size.md.image.outer.size}`]:
            {},
        },
      },
      //Color:default
      "&.nui-snack-default": {
        //Background
        [`@apply bg-${config2.color.default.background.light} dark:bg-${config2.color.default.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.color.default.border.light} dark:border-${config2.color.default.border.dark}`]:
          {},
      },
      //Color:default-contrast
      "&.nui-snack-default-contrast": {
        //Background
        [`@apply bg-${config2.color.defaultContrast.background.light} dark:bg-${config2.color.defaultContrast.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.color.defaultContrast.border.light} dark:border-${config2.color.defaultContrast.border.dark}`]:
          {},
      },
      //Color:muted
      "&.nui-snack-muted": {
        //Background
        [`@apply bg-${config2.color.muted.background.light} dark:bg-${config2.color.muted.background.dark}`]:
          {},
      },
      //Color:muted-contrast
      "&.nui-snack-muted-contrast": {
        //Background
        [`@apply bg-${config2.color.mutedContrast.background.light} dark:bg-${config2.color.mutedContrast.background.dark}`]:
          {},
      },
    },
  });
}, config$b);

module.export = snack;

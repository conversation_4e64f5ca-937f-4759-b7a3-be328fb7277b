import plugin from "tailwindcss/plugin";

const key$t = "link";
const defaultConfig$s = {
  font: {
    family: "sans",
    color: {
      hover: {
        light: "primary-500",
        dark: "primary-400",
      },
      focus: {
        light: "primary-500",
        dark: "primary-400",
      },
    },
  },
};

const config$t = {
  theme: {
    nui: {
      [key$t]: defaultConfig$s,
    },
  },
};
const link = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$t}`);
  addComponents({
    ".nui-link": {
      //Base
      [`@apply font-${config2.font.family} underline-offset-4 hover:underline focus:underline`]:
        {},
      //Hover
      [`@apply hover:text-${config2.font.color.hover.light} dark:hover:text-${config2.font.color.hover.dark}`]:
        {},
      //Focus
      [`@apply focus:text-${config2.font.color.focus.light} dark:focus:text-${config2.font.color.focus.dark}`]:
        {},
    },
  });
}, config$t);

module.export = link;

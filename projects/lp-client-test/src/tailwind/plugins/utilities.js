import plugin from "tailwindcss/plugin";

const bgShades = plugin(({ addUtilities }) =>
  addUtilities({
    ".nui-bg-white": {
      "@apply bg-white dark:bg-muted-900": {},
    },
    ".nui-bg-50": {
      "@apply bg-muted-50 dark:bg-muted-950": {},
    },
    ".nui-bg-100": {
      "@apply bg-muted-100 dark:bg-muted-900": {},
    },
    ".nui-bg-200": {
      "@apply bg-muted-200 dark:bg-muted-800": {},
    },
    ".nui-bg-300": {
      "@apply bg-muted-300 dark:bg-muted-700": {},
    },
    ".nui-bg-400": {
      "@apply bg-muted-400 dark:bg-muted-600": {},
    },
    ".nui-bg-500": {
      "@apply bg-muted-500 dark:bg-muted-500": {},
    },
    ".nui-bg-600": {
      "@apply bg-muted-600 dark:bg-muted-400": {},
    },
    ".nui-bg-700": {
      "@apply bg-muted-700 dark:bg-muted-300": {},
    },
    ".nui-bg-800": {
      "@apply bg-muted-800 dark:bg-muted-200": {},
    },
    ".nui-bg-900": {
      "@apply bg-muted-900 dark:bg-muted-100": {},
    },
    ".nui-bg-950": {
      "@apply bg-muted-950 dark:bg-muted-50": {},
    },
    ".nui-bg-black": {
      "@apply bg-muted-900 dark:bg-white": {},
    },
  })
);

const textShades = plugin(({ addUtilities }) =>
  addUtilities({
    ".nui-text-white": {
      "@apply text-white dark:text-black": {},
    },
    ".nui-text-50": {
      "@apply text-muted-50 dark:text-muted-950": {},
    },
    ".nui-text-100": {
      "@apply text-muted-100 dark:text-muted-900": {},
    },
    ".nui-text-200": {
      "@apply text-muted-200 dark:text-muted-800": {},
    },
    ".nui-text-300": {
      "@apply text-muted-300 dark:text-muted-700": {},
    },
    ".nui-text-400": {
      "@apply text-muted-400 dark:text-muted-600": {},
    },
    ".nui-text-500": {
      "@apply text-muted-500 dark:text-muted-500": {},
    },
    ".nui-text-600": {
      "@apply text-muted-600 dark:text-muted-400": {},
    },
    ".nui-text-700": {
      "@apply text-muted-700 dark:text-muted-300": {},
    },
    ".nui-text-800": {
      "@apply text-muted-800 dark:text-muted-200": {},
    },
    ".nui-text-900": {
      "@apply text-muted-900 dark:text-muted-100": {},
    },
    ".nui-text-950": {
      "@apply text-muted-950 dark:text-muted-50": {},
    },
    ".nui-text-black": {
      "@apply text-black dark:text-white": {},
    },
  })
);

const base = plugin(({ addBase }) =>
  addBase({
    ".dark": { colorScheme: "dark" },
  })
);

module.export = { base, bgShades, textShades };

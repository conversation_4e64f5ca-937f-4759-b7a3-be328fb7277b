import plugin from "tailwindcss/plugin";

const key$K = "button";
const defaultConfig$J = {
  font: {
    family: "sans",
    weight: "normal",
  },
  transition: {
    property: "all",
    duration: "300",
  },
  size: {
    sm: {
      button: {
        text: "sm",
        height: "8",
        padding: {
          x: "3",
          y: "1",
        },
      },
      icon: {
        size: "3",
      },
    },
    md: {
      button: {
        text: "sm",
        height: "10",
        padding: {
          x: "5",
          y: "2",
        },
      },
      icon: {
        size: "4",
      },
    },
    lg: {
      button: {
        text: "base",
        height: "12",
        padding: {
          x: "6",
          y: "2",
        },
      },
      icon: {
        size: "5",
      },
    },
    xl: {
      button: {
        text: "base",
        height: "14",
        padding: {
          x: "10",
          y: "4",
        },
      },
      icon: {
        size: "5",
      },
    },
  },
  rounded: {
    none: "none",
    sm: "md",
    md: "lg",
    lg: "xl",
    full: "full",
  },
  variant: {
    solid: {
      default: {
        text: {
          light: "muted-700",
          dark: "muted-100",
        },
        background: {
          light: {
            base: "white",
            hover: "muted-50",
            active: "white",
            focus: "muted-50",
          },
          dark: {
            base: "muted-700",
            hover: "muted-600",
            active: "muted-700",
            focus: "muted-600",
          },
        },
        border: {
          light: {
            base: "muted-300",
            hover: "muted-200",
          },
          dark: {
            base: "muted-600",
            hover: "muted-500",
          },
        },
        shadow: {
          size: "xl",
          light: "muted-500/30",
          dark: "muted-800/20",
        },
      },
      defaultContrast: {
        text: {
          light: "muted-800",
          dark: "muted-100",
        },
        background: {
          light: {
            base: "white",
            hover: "muted-50",
            active: "white",
            focus: "muted-50",
          },
          dark: {
            base: "muted-950",
            hover: "muted-900",
            active: "muted-950",
            focus: "muted-900",
          },
        },
        border: {
          light: {
            base: "muted-300",
            hover: "muted-200",
          },
          dark: {
            base: "muted-800",
            hover: "muted-700",
          },
        },
        shadow: {
          size: "xl",
          light: "muted-500/30",
          dark: "muted-800/20",
        },
      },
      muted: {
        text: {
          light: "muted-500",
          dark: "muted-100",
        },
        background: {
          light: {
            base: "muted-200",
            hover: "muted-100",
            active: "muted-200",
            focus: "muted-100",
          },
          dark: {
            base: "muted-700",
            hover: "muted-600",
            active: "muted-700",
            focus: "muted-600",
          },
        },
        border: {
          light: {
            base: "muted-200",
            hover: "muted-100",
          },
          dark: {
            base: "muted-700",
            hover: "muted-600",
          },
        },
        shadow: {
          size: "xl",
          light: "muted-500/30",
          dark: "muted-800/20",
        },
      },
      mutedContrast: {
        text: {
          light: "muted-500",
          dark: "muted-100",
        },
        background: {
          light: {
            base: "muted-200",
            hover: "muted-100",
            active: "muted-200",
            focus: "muted-100",
          },
          dark: {
            base: "muted-950",
            hover: "muted-900",
            active: "muted-950",
            focus: "muted-900",
          },
        },
        border: {
          light: {
            base: "muted-200",
            hover: "muted-100",
          },
          dark: {
            base: "muted-800",
            hover: "muted-700",
          },
        },
        shadow: {
          size: "xl",
          light: "muted-500/30",
          dark: "muted-800/20",
        },
      },
      light: {
        text: {
          light: "muted-500",
          dark: "muted-100",
        },
        background: {
          light: {
            base: "muted-200",
            hover: "muted-100",
            active: "muted-200",
            focus: "muted-100",
          },
          dark: {
            base: "muted-700",
            hover: "muted-600",
            active: "muted-700",
            focus: "muted-600",
          },
        },
        border: {
          light: {
            base: "muted-200",
            hover: "muted-100",
          },
          dark: {
            base: "muted-700",
            hover: "muted-600",
          },
        },
        shadow: {
          size: "xl",
          light: "muted-500/30",
          dark: "muted-800/20",
        },
      },
      dark: {
        text: {
          light: "muted-100",
          dark: "muted-900",
        },
        background: {
          light: {
            base: "muted-900",
            hover: "muted-800",
            active: "muted-900",
            focus: "muted-800",
          },
          dark: {
            base: "muted-100",
            hover: "muted-50",
            active: "muted-100",
            focus: "muted-50",
          },
        },
        border: {
          light: {
            base: "muted-900",
            hover: "muted-800",
          },
          dark: {
            base: "muted-100",
            hover: "muted-50",
          },
        },
        shadow: {
          size: "xl",
          light: "muted-500/30",
          dark: "muted-800/20",
        },
      },
      black: {
        text: {
          light: "white",
          dark: "black",
        },
        background: {
          light: {
            base: "black",
            hover: "black/90",
            active: "black",
            focus: "black/90",
          },
          dark: {
            base: "white",
            hover: "white/90",
            active: "white",
            focus: "white/90",
          },
        },
        border: {
          light: {
            base: "black",
            hover: "black/90",
          },
          dark: {
            base: "white",
            hover: "white/90",
          },
        },
        shadow: {
          size: "xl",
          light: "muted-500/30",
          dark: "muted-800/20",
        },
      },
      primary: {
        text: {
          light: "primary-invert",
          dark: "primary-invert",
        },
        background: {
          light: {
            base: "primary-500",
            hover: "primary-400",
            active: "primary-500",
            focus: "primary-400",
          },
          dark: {
            base: "primary-500",
            hover: "primary-400",
            active: "primary-500",
            focus: "primary-400",
          },
        },
        shadow: {
          size: "xl",
          light: "primary-500/50",
          dark: "primary-800/50",
        },
      },
      info: {
        text: {
          light: "white",
          dark: "white",
        },
        background: {
          light: {
            base: "info-500",
            hover: "info-400",
            active: "info-500",
            focus: "info-400",
          },
          dark: {
            base: "info-500",
            hover: "info-400",
            active: "info-500",
            focus: "info-400",
          },
        },
        shadow: {
          size: "xl",
          light: "info-500/50",
          dark: "info-800/50",
        },
      },
      success: {
        text: {
          light: "white",
          dark: "white",
        },
        background: {
          light: {
            base: "success-500",
            hover: "success-400",
            active: "success-500",
            focus: "success-400",
          },
          dark: {
            base: "success-500",
            hover: "success-400",
            active: "success-500",
            focus: "success-400",
          },
        },
        shadow: {
          size: "xl",
          light: "success-500/50",
          dark: "success-800/50",
        },
      },
      warning: {
        text: {
          light: "white",
          dark: "white",
        },
        background: {
          light: {
            base: "warning-500",
            hover: "warning-400",
            active: "warning-500",
            focus: "warning-400",
          },
          dark: {
            base: "warning-500",
            hover: "warning-400",
            active: "warning-500",
            focus: "warning-400",
          },
        },
        shadow: {
          size: "xl",
          light: "warning-500/50",
          dark: "warning-800/50",
        },
      },
      danger: {
        text: {
          light: "white",
          dark: "white",
        },
        background: {
          light: {
            base: "danger-500",
            hover: "danger-400",
            active: "danger-500",
            focus: "danger-400",
          },
          dark: {
            base: "danger-500",
            hover: "danger-400",
            active: "danger-500",
            focus: "danger-400",
          },
        },
        shadow: {
          size: "xl",
          light: "danger-500/50",
          dark: "danger-800/50",
        },
      },
    },
    pastel: {
      default: {
        text: {
          light: "muted-800",
          dark: "muted-100",
        },
        background: {
          light: {
            base: "muted-500/10",
            hover: "muted-500/20",
            active: "muted-500/10",
            focus: "muted-500/20",
          },
          dark: {
            base: "muted-500/10",
            hover: "muted-500/20",
            active: "muted-500/10",
            focus: "muted-500/20",
          },
        },
      },
      muted: {
        text: {
          light: "muted-800",
          dark: "muted-100",
        },
        background: {
          light: {
            base: "muted-500/10",
            hover: "muted-500/20",
            active: "muted-500/10",
            focus: "muted-500/20",
          },
          dark: {
            base: "muted-500/10",
            hover: "muted-500/20",
            active: "muted-500/10",
            focus: "muted-500/20",
          },
        },
      },
      light: {
        text: {
          light: "muted-800",
          dark: "muted-100",
        },
        background: {
          light: {
            base: "white/30",
            hover: "white/20",
            active: "white/30",
            focus: "white/20",
          },
          dark: {
            base: "white/20",
            hover: "white/10",
            active: "white/20",
            focus: "white/10",
          },
        },
      },
      dark: {
        text: {
          light: "muted-800",
          dark: "muted-100",
        },
        background: {
          light: {
            base: "muted-500/10",
            hover: "muted-500/20",
            active: "muted-500/10",
            focus: "muted-500/20",
          },
          dark: {
            base: "muted-500/10",
            hover: "muted-500/20",
            active: "muted-500/10",
            focus: "muted-500/20",
          },
        },
      },
      black: {
        text: {
          light: "black",
          dark: "white",
        },
        background: {
          light: {
            base: "black/10",
            hover: "black/20",
            active: "black/10",
            focus: "black/20",
          },
          dark: {
            base: "black/10",
            hover: "black/20",
            active: "black/10",
            focus: "black/20",
          },
        },
      },
      primary: {
        text: {
          light: "primary-500",
          dark: "primary-500",
        },
        background: {
          light: {
            base: "primary-500/10",
            hover: "primary-500/20",
            active: "primary-500/10",
            focus: "primary-500/20",
          },
          dark: {
            base: "primary-500/10",
            hover: "primary-500/20",
            active: "primary-500/10",
            focus: "primary-500/20",
          },
        },
      },
      info: {
        text: {
          light: "info-500",
          dark: "info-500",
        },
        background: {
          light: {
            base: "info-500/10",
            hover: "info-500/20",
            active: "info-500/10",
            focus: "info-500/20",
          },
          dark: {
            base: "info-500/10",
            hover: "info-500/20",
            active: "info-500/10",
            focus: "info-500/20",
          },
        },
      },
      success: {
        text: {
          light: "success-500",
          dark: "success-500",
        },
        background: {
          light: {
            base: "success-500/10",
            hover: "success-500/20",
            active: "success-500/10",
            focus: "success-500/20",
          },
          dark: {
            base: "success-500/10",
            hover: "success-500/20",
            active: "success-500/10",
            focus: "success-500/20",
          },
        },
      },
      warning: {
        text: {
          light: "warning-500",
          dark: "warning-500",
        },
        background: {
          light: {
            base: "warning-500/10",
            hover: "warning-500/20",
            active: "warning-500/10",
            focus: "warning-500/20",
          },
          dark: {
            base: "warning-500/10",
            hover: "warning-500/20",
            active: "warning-500/10",
            focus: "warning-500/20",
          },
        },
      },
      danger: {
        text: {
          light: "danger-500",
          dark: "danger-500",
        },
        background: {
          light: {
            base: "danger-500/10",
            hover: "danger-500/20",
            active: "danger-500/10",
            focus: "danger-500/20",
          },
          dark: {
            base: "danger-500/10",
            hover: "danger-500/20",
            active: "danger-500/10",
            focus: "danger-500/20",
          },
        },
      },
    },
    outline: {
      default: {
        text: {
          light: {
            base: "muted-500",
            hover: "white",
            active: "muted-800",
            focus: "white",
          },
          dark: {
            base: "white",
            hover: "muted-800",
            active: "muted-800",
            focus: "muted-800",
          },
        },
        background: {
          light: {
            base: "transparent",
            hover: "muted-500",
            active: "muted-400",
            focus: "muted-500",
          },
          dark: {
            base: "transparent",
            hover: "white",
            active: "muted-700",
            focus: "muted-800",
          },
        },
        border: {
          light: "muted-500",
          dark: "white",
        },
        shadow: {
          size: "xl",
          light: "muted-500/50",
          dark: "muted-800/20",
        },
      },
      light: {
        text: {
          light: {
            base: "white",
            hover: "muted-800",
            active: "muted-800",
            focus: "muted-800",
          },
          dark: {
            base: "white",
            hover: "muted-800",
            active: "muted-800",
            focus: "muted-800",
          },
        },
        background: {
          light: {
            base: "transparent",
            hover: "white",
            active: "muted-800",
            focus: "white",
          },
          dark: {
            base: "transparent",
            hover: "white",
            active: "muted-800",
            focus: "white",
          },
        },
        border: {
          light: "white",
          dark: "white",
        },
        shadow: {
          size: "xl",
          light: "muted-500/50",
          dark: "muted-800/20",
        },
      },
      muted: {
        text: {
          light: {
            base: "muted-400",
            hover: "white",
            active: "muted-800",
            focus: "white",
          },
          dark: {
            base: "white",
            hover: "muted-800",
            active: "muted-800",
            focus: "muted-800",
          },
        },
        background: {
          light: {
            base: "transparent",
            hover: "muted-300",
            active: "muted-400",
            focus: "muted-300",
          },
          dark: {
            base: "transparent",
            hover: "white",
            active: "white/80",
            focus: "white",
          },
        },
        border: {
          light: "muted-300",
          dark: "white",
        },
        shadow: {
          size: "xl",
          light: "muted-500/50",
          dark: "muted-800/20",
        },
      },
      dark: {
        text: {
          light: {
            base: "muted-900",
            hover: "white",
            active: "muted-800",
            focus: "white",
          },
          dark: {
            base: "white",
            hover: "muted-900",
            active: "muted-800",
            focus: "muted-900",
          },
        },
        background: {
          light: {
            base: "transparent",
            hover: "muted-900",
            active: "muted-800",
            focus: "muted-800",
          },
          dark: {
            base: "transparent",
            hover: "muted-100",
            active: "muted-50",
            focus: "muted-50",
          },
        },
        border: {
          light: "muted-900",
          dark: "white",
        },
        shadow: {
          size: "xl",
          light: "muted-500/50",
          dark: "muted-800/20",
        },
      },
      black: {
        text: {
          light: {
            base: "black",
            hover: "white",
            active: "black/90",
            focus: "white",
          },
          dark: {
            base: "white",
            hover: "black",
            active: "black/90",
            focus: "black",
          },
        },
        background: {
          light: {
            base: "transparent",
            hover: "black",
            active: "black/90",
            focus: "black/90",
          },
          dark: {
            base: "transparent",
            hover: "white",
            active: "white/90",
            focus: "white/90",
          },
        },
        border: {
          light: "black",
          dark: "white",
        },
        shadow: {
          size: "xl",
          light: "muted-500/50",
          dark: "muted-800/20",
        },
      },
      primary: {
        text: {
          light: {
            base: "primary-500",
            hover: "white",
            active: "white",
            focus: "white",
          },
          dark: {
            base: "primary-500",
            hover: "white",
            active: "white",
            focus: "white",
          },
        },
        background: {
          light: {
            base: "transparent",
            hover: "primary-500",
            active: "primary-400",
            focus: "primary-500",
          },
          dark: {
            base: "transparent",
            hover: "primary-500",
            active: "primary-400",
            focus: "primary-500",
          },
        },
        border: {
          light: "primary-500",
          dark: "primary-500",
        },
        shadow: {
          size: "xl",
          light: "primary-500/30",
          dark: "primary-800/30",
        },
      },
      info: {
        text: {
          light: {
            base: "info-500",
            hover: "white",
            active: "white",
            focus: "white",
          },
          dark: {
            base: "info-500",
            hover: "white",
            active: "white",
            focus: "white",
          },
        },
        background: {
          light: {
            base: "transparent",
            hover: "info-500",
            active: "info-400",
            focus: "info-500",
          },
          dark: {
            base: "transparent",
            hover: "info-500",
            active: "info-400",
            focus: "info-500",
          },
        },
        border: {
          light: "info-500",
          dark: "info-500",
        },
        shadow: {
          size: "xl",
          light: "info-500/30",
          dark: "info-800/30",
        },
      },
      success: {
        text: {
          light: {
            base: "success-500",
            hover: "white",
            active: "white",
            focus: "white",
          },
          dark: {
            base: "success-500",
            hover: "white",
            active: "white",
            focus: "white",
          },
        },
        background: {
          light: {
            base: "transparent",
            hover: "success-500",
            active: "success-400",
            focus: "success-500",
          },
          dark: {
            base: "transparent",
            hover: "success-500",
            active: "success-400",
            focus: "success-500",
          },
        },
        border: {
          light: "success-500",
          dark: "success-500",
        },
        shadow: {
          size: "xl",
          light: "success-500/30",
          dark: "success-800/30",
        },
      },
      warning: {
        text: {
          light: {
            base: "warning-500",
            hover: "white",
            active: "white",
            focus: "white",
          },
          dark: {
            base: "warning-500",
            hover: "white",
            active: "white",
            focus: "white",
          },
        },
        background: {
          light: {
            base: "transparent",
            hover: "warning-500",
            active: "warning-400",
            focus: "warning-500",
          },
          dark: {
            base: "transparent",
            hover: "warning-500",
            active: "warning-400",
            focus: "warning-500",
          },
        },
        border: {
          light: "warning-500",
          dark: "warning-500",
        },
        shadow: {
          size: "xl",
          light: "warning-500/30",
          dark: "warning-800/30",
        },
      },
      danger: {
        text: {
          light: {
            base: "danger-500",
            hover: "white",
            active: "white",
            focus: "white",
          },
          dark: {
            base: "danger-500",
            hover: "white",
            active: "white",
            focus: "white",
          },
        },
        background: {
          light: {
            base: "transparent",
            hover: "danger-500",
            active: "danger-400",
            focus: "danger-500",
          },
          dark: {
            base: "transparent",
            hover: "danger-500",
            active: "danger-400",
            focus: "danger-500",
          },
        },
        border: {
          light: "danger-500",
          dark: "danger-500",
        },
        shadow: {
          size: "xl",
          light: "danger-500/30",
          dark: "danger-800/30",
        },
      },
    },
  },
  badge: {
    size: "2.5",
    colors: {
      primary: "primary-400",
      primaryDark: "primary-400",
      info: "info-400",
      success: "success-400",
      warning: "warning-400",
      danger: "danger-400",
    },
  },
};

const config$K = {
  theme: {
    nui: {
      button: defaultConfig$J,
    },
  },
};
const button = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$K}`);
  addComponents({
    ".nui-button": {
      // #region Base
      "@apply relative inline-flex justify-center items-center space-x-1": {},
      //Text styles
      [`@apply font-${config2.font.family} font-${config2.font.weight} leading-5 no-underline`]:
        {},
      //State styles
      [`@apply nui-focus transition-${config2.transition.property} duration-${config2.transition.duration} disabled:opacity-60 disabled:cursor-not-allowed hover:shadow-none`]:
        {},
      // #endregion
      // #region Badge
      ".nui-button-badge": {
        [`@apply flex absolute h-${config2.badge.size} w-${config2.badge.size} top-0 -end-0.5 -mt-0.5`]:
          {},
        ".nui-button-badge-pulse": {
          "@apply absolute inline-flex h-full w-full rounded-full opacity-75 bg-primary-500 animate-ping":
            {},
        },
        ".nui-button-badge-inner": {
          [`@apply relative inline-flex rounded-full h-${config2.badge.size} w-${config2.badge.size} bg-primary-500`]:
            {},
        },
        "&.nui-badge-primary": {
          ".nui-button-badge-pulse": {
            [`@apply bg-${config2.badge.colors.primary}`]: {},
          },
          ".nui-button-badge-inner": {
            [`@apply bg-${config2.badge.colors.primary}`]: {},
          },
        },
        "&.nui-badge-info": {
          ".nui-button-badge-pulse": {
            [`@apply bg-${config2.badge.colors.info}`]: {},
          },
          ".nui-button-badge-inner": {
            [`@apply bg-${config2.badge.colors.info}`]: {},
          },
        },
        "&.nui-badge-success": {
          ".nui-button-badge-pulse": {
            [`@apply bg-${config2.badge.colors.success}`]: {},
          },
          ".nui-button-badge-inner": {
            [`@apply bg-${config2.badge.colors.success}`]: {},
          },
        },
        "&.nui-badge-warning": {
          ".nui-button-badge-pulse": {
            [`@apply bg-${config2.badge.colors.warning}`]: {},
          },
          ".nui-button-badge-inner": {
            [`@apply bg-${config2.badge.colors.warning}`]: {},
          },
        },
        "&.nui-badge-danger": {
          ".nui-button-badge-pulse": {
            [`@apply bg-${config2.badge.colors.danger}`]: {},
          },
          ".nui-button-badge-inner": {
            [`@apply bg-${config2.badge.colors.danger}`]: {},
          },
        },
      },
      // #endregion
      // #region Loading
      "&.nui-button-loading": {
        "@apply !text-transparent": {},
      },
      // #endregion
      // #region Sizes
      "&.nui-button-sm": {
        [`@apply h-${config2.size.sm.button.height} px-${config2.size.sm.button.padding.x} py-${config2.size.sm.button.padding.y} text-${config2.size.sm.button.text}`]:
          {},
        ".nui-button-icon": {
          [`@apply w-${config2.size.sm.icon.size} h-${config2.size.sm.icon.size}`]:
            {},
        },
      },
      "&.nui-button-md": {
        [`@apply h-${config2.size.md.button.height} px-${config2.size.md.button.padding.x} py-${config2.size.md.button.padding.y} text-${config2.size.md.button.text}`]:
          {},
        ".nui-button-icon": {
          [`@apply w-${config2.size.md.icon.size} h-${config2.size.md.icon.size}`]:
            {},
        },
      },
      "&.nui-button-lg": {
        [`@apply h-${config2.size.lg.button.height} px-${config2.size.lg.button.padding.x} py-${config2.size.lg.button.padding.y} text-${config2.size.lg.button.text}`]:
          {},
        ".nui-button-icon": {
          [`@apply w-${config2.size.lg.icon.size} h-${config2.size.lg.icon.size}`]:
            {},
        },
      },
      "&.nui-button-xl": {
        [`@apply h-${config2.size.xl.button.height} px-${config2.size.xl.button.padding.x} py-${config2.size.xl.button.padding.y} text-${config2.size.xl.button.text}`]:
          {},
        ".nui-button-icon": {
          [`@apply w-${config2.size.xl.icon.size} h-${config2.size.xl.icon.size}`]:
            {},
        },
      },
      // #endregion
      // #region Rounded
      "&.nui-button-rounded-sm": {
        [`@apply rounded-${config2.rounded.sm}`]: {},
      },
      "&.nui-button-rounded-md": {
        [`@apply rounded-${config2.rounded.md}`]: {},
      },
      "&.nui-button-rounded-lg": {
        [`@apply rounded-${config2.rounded.lg}`]: {},
      },
      "&.nui-button-rounded-full": {
        [`@apply rounded-${config2.rounded.full}`]: {},
        ".nui-button-badge": {
          [`@apply flex absolute h-${config2.badge.size} w-${config2.badge.size} top-0 -end-1 -mt-0.5 me-2`]:
            {},
        },
      },
      // #endregion
      // #region Variants
      "&.nui-button-solid": {
        //Solid:default
        "&.nui-button-default": {
          //Text color
          [`@apply text-${config2.variant.solid.default.text.light} dark:text-${config2.variant.solid.default.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.default.background.light.base} dark:bg-${config2.variant.solid.default.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.default.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.default.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.default.background.light.active} dark:active:enabled:bg-${config2.variant.solid.default.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.default.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.default.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border border-${config2.variant.solid.default.border.light.base} dark:border-${config2.variant.solid.default.border.dark.base}`]:
            {},
          //Border hover
          [`@apply hover:enabled:border-${config2.variant.solid.default.border.light.hover} dark:hover:enabled:border-${config2.variant.solid.default.border.dark.hover}`]:
            {},
          //shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.default.shadow.size} enabled:shadow-${config2.variant.solid.default.shadow.light} dark:enabled:shadow-${config2.variant.solid.default.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.default.shadow.size} hover:enabled:shadow-${config2.variant.solid.default.shadow.light} dark:hover:enabled:shadow-${config2.variant.solid.default.shadow.dark}`]:
              {},
          },
        },
        //Solid:default-contrast
        "&.nui-button-default-contrast": {
          //Text color
          [`@apply text-${config2.variant.solid.defaultContrast.text.light} dark:text-${config2.variant.solid.defaultContrast.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.defaultContrast.background.light.base} dark:bg-${config2.variant.solid.defaultContrast.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.defaultContrast.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.defaultContrast.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.defaultContrast.background.light.active} dark:active:enabled:bg-${config2.variant.solid.defaultContrast.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.defaultContrast.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.defaultContrast.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border border-${config2.variant.solid.defaultContrast.border.light.base} dark:border-${config2.variant.solid.defaultContrast.border.dark.base}`]:
            {},
          //Border hover
          [`@apply hover:enabled:border-${config2.variant.solid.defaultContrast.border.light.hover} dark:hover:enabled:border-${config2.variant.solid.defaultContrast.border.dark.hover}`]:
            {},
          //shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.defaultContrast.shadow.size} enabled:shadow-${config2.variant.solid.defaultContrast.shadow.light} dark:enabled:shadow-${config2.variant.solid.defaultContrast.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.defaultContrast.shadow.size} hover:enabled:shadow-${config2.variant.solid.defaultContrast.shadow.light} dark:hover:enabled:shadow-${config2.variant.solid.defaultContrast.shadow.dark}`]:
              {},
          },
        },
        //Solid:muted
        "&.nui-button-muted": {
          //Text color
          [`@apply text-${config2.variant.solid.muted.text.light} dark:text-${config2.variant.solid.muted.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.muted.background.light.base} dark:bg-${config2.variant.solid.muted.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.muted.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.muted.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.muted.background.light.active} dark:active:enabled:bg-${config2.variant.solid.muted.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.muted.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.muted.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border border-${config2.variant.solid.muted.border.light.base} dark:border-${config2.variant.solid.muted.border.dark.base}`]:
            {},
          //Border hover
          [`@apply border-${config2.variant.solid.muted.border.light.hover} dark:border-${config2.variant.solid.muted.border.dark.hover}`]:
            {},
          //Shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.muted.shadow.size} enabled:shadow-${config2.variant.solid.muted.shadow.light} dark:enabled:shadow-${config2.variant.solid.muted.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.muted.shadow.size} hover:enabled:shadow-${config2.variant.solid.muted.shadow.light} dark:hover:enabled:shadow-${config2.variant.solid.muted.shadow.dark}`]:
              {},
          },
        },
        //Solid:muted-contrast
        "&.nui-button-muted-contrast": {
          //Text color
          [`@apply text-${config2.variant.solid.mutedContrast.text.light} dark:text-${config2.variant.solid.mutedContrast.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.mutedContrast.background.light.base} dark:bg-${config2.variant.solid.mutedContrast.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.mutedContrast.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.mutedContrast.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.mutedContrast.background.light.active} dark:active:enabled:bg-${config2.variant.solid.mutedContrast.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.mutedContrast.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.mutedContrast.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border border-${config2.variant.solid.mutedContrast.border.light.base} dark:border-${config2.variant.solid.mutedContrast.border.dark.base}`]:
            {},
          //Border hover
          [`@apply hover:enabled:border-${config2.variant.solid.mutedContrast.border.light.hover} dark:hover:enabled:border-${config2.variant.solid.mutedContrast.border.dark.hover}`]:
            {},
          //shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.mutedContrast.shadow.size} enabled:shadow-${config2.variant.solid.mutedContrast.shadow.light} dark:enabled:shadow-${config2.variant.solid.mutedContrast.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.mutedContrast.shadow.size} hover:enabled:shadow-${config2.variant.solid.mutedContrast.shadow.light} dark:hover:enabled:shadow-${config2.variant.solid.mutedContrast.shadow.dark}`]:
              {},
          },
        },
        //Solid:light
        "&.nui-button-light": {
          //Text color
          [`@apply text-${config2.variant.solid.light.text.light} dark:text-${config2.variant.solid.light.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.light.background.light.base} dark:bg-${config2.variant.solid.light.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.light.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.light.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.light.background.light.active} dark:active:enabled:bg-${config2.variant.solid.light.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.light.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.light.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border border-${config2.variant.solid.light.border.light.base} dark:border-${config2.variant.solid.light.border.dark.base}`]:
            {},
          //Border hover
          [`@apply border-${config2.variant.solid.light.border.light.hover} dark:border-${config2.variant.solid.light.border.dark.hover}`]:
            {},
          //Shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.light.shadow.size} enabled:shadow-${config2.variant.solid.light.shadow.light} dark:enabled:shadow-${config2.variant.solid.light.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.light.shadow.size} hover:enabled:shadow-${config2.variant.solid.light.shadow.light} dark:hover:enabled:shadow-${config2.variant.solid.light.shadow.dark}`]:
              {},
          },
        },
        //Solid:dark
        "&.nui-button-dark": {
          //Text color
          [`@apply text-${config2.variant.solid.dark.text.light} dark:text-${config2.variant.solid.dark.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.dark.background.light.base} dark:bg-${config2.variant.solid.dark.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.dark.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.dark.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.dark.background.light.active} dark:active:enabled:bg-${config2.variant.solid.dark.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.dark.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.dark.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border border-${config2.variant.solid.dark.border.light.base} dark:border-${config2.variant.solid.dark.border.dark.base}`]:
            {},
          //Border hover
          [`@apply border-${config2.variant.solid.dark.border.light.hover} dark:border-${config2.variant.solid.dark.border.dark.hover}`]:
            {},
          //Shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.dark.shadow.size} enabled:shadow-${config2.variant.solid.dark.shadow.dark} dark:enabled:shadow-${config2.variant.solid.dark.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.dark.shadow.size} hover:enabled:shadow-${config2.variant.solid.dark.shadow.dark} dark:hover:enabled:shadow-${config2.variant.solid.dark.shadow.dark}`]:
              {},
          },
        },
        //Solid:black
        "&.nui-button-black": {
          //Text color
          [`@apply text-${config2.variant.solid.black.text.light} dark:text-${config2.variant.solid.black.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.black.background.light.base} dark:bg-${config2.variant.solid.black.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.black.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.black.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.black.background.light.active} dark:active:enabled:bg-${config2.variant.solid.black.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.black.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.black.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border border-${config2.variant.solid.black.border.light.base} dark:border-${config2.variant.solid.black.border.dark.base}`]:
            {},
          //Border hover
          [`@apply border-${config2.variant.solid.black.border.light.hover} dark:border-${config2.variant.solid.black.border.dark.hover}`]:
            {},
          //Shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.black.shadow.size} enabled:shadow-${config2.variant.solid.black.shadow.dark} dark:enabled:shadow-${config2.variant.solid.black.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.black.shadow.size} hover:enabled:shadow-${config2.variant.solid.black.shadow.dark} dark:hover:enabled:shadow-${config2.variant.solid.black.shadow.dark}`]:
              {},
          },
        },
        //Solid:primary
        "&.nui-button-primary": {
          //Text color
          [`@apply text-${config2.variant.solid.primary.text.light} dark:text-${config2.variant.solid.primary.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.primary.background.light.base} dark:bg-${config2.variant.solid.primary.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.primary.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.primary.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.primary.background.light.active} dark:active:enabled:bg-${config2.variant.solid.primary.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.primary.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.primary.background.dark.focus}`]:
            {},
          //Shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.primary.shadow.size} enabled:shadow-${config2.variant.solid.primary.shadow.light} dark:enabled:shadow-${config2.variant.solid.primary.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.primary.shadow.size} hover:enabled:shadow-${config2.variant.solid.primary.shadow.light} dark:hover:enabled:shadow-${config2.variant.solid.primary.shadow.dark}`]:
              {},
          },
        },
        //Solid:info
        "&.nui-button-info": {
          //Text color
          [`@apply text-${config2.variant.solid.info.text.light} text-${config2.variant.solid.info.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.info.background.light.base} dark:bg-${config2.variant.solid.info.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.info.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.info.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.info.background.light.active} dark:active:enabled:bg-${config2.variant.solid.info.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.info.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.info.background.dark.focus}`]:
            {},
          //Shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.info.shadow.size} enabled:shadow-${config2.variant.solid.info.shadow.light} dark:enabled:shadow-${config2.variant.solid.info.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.info.shadow.size} hover:enabled:shadow-${config2.variant.solid.info.shadow.light} dark:hover:enabled:shadow-${config2.variant.solid.info.shadow.dark}`]:
              {},
          },
        },
        //Solid:success
        "&.nui-button-success": {
          //Text color
          [`@apply text-${config2.variant.solid.success.text.light} text-${config2.variant.solid.success.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.success.background.light.base} dark:bg-${config2.variant.solid.success.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.success.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.success.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.success.background.light.active} dark:active:enabled:bg-${config2.variant.solid.success.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.success.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.success.background.dark.focus}`]:
            {},
          //Shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.success.shadow.size} enabled:shadow-${config2.variant.solid.success.shadow.light} dark:enabled:shadow-${config2.variant.solid.success.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.success.shadow.size} hover:enabled:shadow-${config2.variant.solid.success.shadow.light} dark:hover:enabled:shadow-${config2.variant.solid.success.shadow.dark}`]:
              {},
          },
        },
        //Solid:warning
        "&.nui-button-warning": {
          //Text color
          [`@apply text-${config2.variant.solid.warning.text.light} text-${config2.variant.solid.warning.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.warning.background.light.base} dark:bg-${config2.variant.solid.warning.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.warning.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.warning.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.warning.background.light.active} dark:active:enabled:bg-${config2.variant.solid.warning.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.warning.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.warning.background.dark.focus}`]:
            {},
          //Shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.warning.shadow.size} enabled:shadow-${config2.variant.solid.warning.shadow.light} dark:enabled:shadow-${config2.variant.solid.warning.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.warning.shadow.size} hover:enabled:shadow-${config2.variant.solid.warning.shadow.light} dark:hover:enabled:shadow-${config2.variant.solid.warning.shadow.dark}`]:
              {},
          },
        },
        //Solid:danger
        "&.nui-button-danger": {
          //Text color
          [`@apply text-${config2.variant.solid.danger.text.light} text-${config2.variant.solid.danger.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.solid.danger.background.light.base} dark:bg-${config2.variant.solid.danger.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.solid.danger.background.light.hover} dark:hover:enabled:bg-${config2.variant.solid.danger.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.solid.danger.background.light.active} dark:active:enabled:bg-${config2.variant.solid.danger.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.solid.danger.background.light.focus} dark:focus-visible:bg-${config2.variant.solid.danger.background.dark.focus}`]:
            {},
          //Shadows
          "&.nui-button-shadow": {
            [`@apply enabled:shadow-${config2.variant.solid.danger.shadow.size} enabled:shadow-${config2.variant.solid.danger.shadow.light} dark:enabled:shadow-${config2.variant.solid.danger.shadow.dark}`]:
              {},
          },
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.solid.danger.shadow.size} hover:enabled:shadow-${config2.variant.solid.danger.shadow.light} dark:hover:enabled:shadow-${config2.variant.solid.danger.shadow.dark}`]:
              {},
          },
        },
      },
      "&.nui-button-pastel": {
        //Pastel:default
        "&.nui-button-default, &.nui-button-default-contrast": {
          //Text color
          [`@apply text-${config2.variant.pastel.default.text.light} dark:text-${config2.variant.pastel.default.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.pastel.default.background.light.base} dark:bg-${config2.variant.pastel.default.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.pastel.default.background.light.hover} dark:hover:enabled:bg-${config2.variant.pastel.default.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.pastel.default.background.light.active} dark:active:enabled:bg-${config2.variant.pastel.default.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.pastel.default.background.light.focus} dark:focus-visible:bg-${config2.variant.pastel.default.background.dark.focus}`]:
            {},
        },
        //Pastel:muted
        "&.nui-button-muted, &.nui-button-muted-contrast": {
          //Text color
          [`@apply text-${config2.variant.pastel.muted.text.light} dark:text-${config2.variant.pastel.muted.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.pastel.muted.background.light.base} dark:bg-${config2.variant.pastel.muted.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.pastel.muted.background.light.hover} dark:hover:enabled:bg-${config2.variant.pastel.muted.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.pastel.muted.background.light.active} dark:active:enabled:bg-${config2.variant.pastel.muted.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.pastel.muted.background.light.focus} dark:focus-visible:bg-${config2.variant.pastel.muted.background.dark.focus}`]:
            {},
        },
        //Pastel:light
        "&.nui-button-light": {
          //Text color
          [`@apply text-${config2.variant.pastel.light.text.light} dark:text-${config2.variant.pastel.light.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.pastel.light.background.light.base} dark:bg-${config2.variant.pastel.light.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.pastel.light.background.light.hover} dark:hover:enabled:bg-${config2.variant.pastel.light.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.pastel.light.background.light.active} dark:active:enabled:bg-${config2.variant.pastel.light.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.pastel.light.background.light.focus} dark:focus-visible:bg-${config2.variant.pastel.light.background.dark.focus}`]:
            {},
        },
        //Pastel:dark
        "&.nui-button-dark": {
          //Text color
          [`@apply text-${config2.variant.pastel.dark.text.light} dark:text-${config2.variant.pastel.dark.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.pastel.dark.background.light.base} dark:bg-${config2.variant.pastel.dark.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.pastel.dark.background.light.hover} dark:hover:enabled:bg-${config2.variant.pastel.dark.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.pastel.dark.background.light.active} dark:active:enabled:bg-${config2.variant.pastel.dark.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.pastel.dark.background.light.focus} dark:focus-visible:bg-${config2.variant.pastel.dark.background.dark.focus}`]:
            {},
        },
        //Pastel:black
        "&.nui-button-black": {
          //Text color
          [`@apply text-${config2.variant.pastel.black.text.light} dark:text-${config2.variant.pastel.black.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.pastel.black.background.light.base} dark:bg-${config2.variant.pastel.black.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.pastel.black.background.light.hover} dark:hover:enabled:bg-${config2.variant.pastel.black.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.pastel.black.background.light.active} dark:active:enabled:bg-${config2.variant.pastel.black.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.pastel.black.background.light.focus} dark:focus-visible:bg-${config2.variant.pastel.black.background.dark.focus}`]:
            {},
        },
        //Pastel:primary
        "&.nui-button-primary": {
          //Text color
          [`@apply text-${config2.variant.pastel.primary.text.light} dark:text-${config2.variant.pastel.primary.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.pastel.primary.background.light.base} dark:bg-${config2.variant.pastel.primary.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.pastel.primary.background.light.hover} dark:hover:enabled:bg-${config2.variant.pastel.primary.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.pastel.primary.background.light.active} dark:active:enabled:bg-${config2.variant.pastel.primary.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.pastel.primary.background.light.focus} dark:focus-visible:bg-${config2.variant.pastel.primary.background.dark.focus}`]:
            {},
        },
        //Pastel:info
        "&.nui-button-info": {
          //Text color
          [`@apply text-${config2.variant.pastel.info.text.light} dark:text-${config2.variant.pastel.info.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.pastel.info.background.light.base} dark:bg-${config2.variant.pastel.info.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.pastel.info.background.light.hover} dark:hover:enabled:bg-${config2.variant.pastel.info.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.pastel.info.background.light.active} dark:active:enabled:bg-${config2.variant.pastel.info.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.pastel.info.background.light.focus} dark:focus-visible:bg-${config2.variant.pastel.info.background.dark.focus}`]:
            {},
        },
        //Pastel:success
        "&.nui-button-success": {
          //Text color
          [`@apply text-${config2.variant.pastel.success.text.light} dark:text-${config2.variant.pastel.success.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.pastel.success.background.light.base} dark:bg-${config2.variant.pastel.success.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.pastel.success.background.light.hover} dark:hover:enabled:bg-${config2.variant.pastel.success.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.pastel.success.background.light.active} dark:active:enabled:bg-${config2.variant.pastel.success.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.pastel.success.background.light.focus} dark:focus-visible:bg-${config2.variant.pastel.success.background.dark.focus}`]:
            {},
        },
        //Pastel:warning
        "&.nui-button-warning": {
          //Text color
          [`@apply text-${config2.variant.pastel.warning.text.light} dark:text-${config2.variant.pastel.warning.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.pastel.warning.background.light.base} dark:bg-${config2.variant.pastel.warning.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.pastel.warning.background.light.hover} dark:hover:enabled:bg-${config2.variant.pastel.warning.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.pastel.warning.background.light.active} dark:active:enabled:bg-${config2.variant.pastel.warning.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.pastel.warning.background.light.focus} dark:focus-visible:bg-${config2.variant.pastel.warning.background.dark.focus}`]:
            {},
        },
        //Pastel:danger
        "&.nui-button-danger": {
          //Text color
          [`@apply text-${config2.variant.pastel.danger.text.light} dark:text-${config2.variant.pastel.danger.text.dark}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.pastel.danger.background.light.base} dark:bg-${config2.variant.pastel.danger.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.pastel.danger.background.light.hover} dark:hover:enabled:bg-${config2.variant.pastel.danger.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.pastel.danger.background.light.active} dark:active:enabled:bg-${config2.variant.pastel.danger.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.pastel.danger.background.light.focus} dark:focus-visible:bg-${config2.variant.pastel.danger.background.dark.focus}`]:
            {},
        },
      },
      //Variant:outline
      "&.nui-button-outline": {
        //Outline:default
        "&.nui-button-default, &.nui-button-default-contrast": {
          //Text color
          [`@apply text-${config2.variant.outline.default.text.light.base} dark:text-${config2.variant.outline.default.text.dark.base}`]:
            {},
          //Text hover
          [`@apply hover:enabled:text-${config2.variant.outline.default.text.light.hover} dark:hover:enabled:text-${config2.variant.outline.default.text.dark.hover}`]:
            {},
          //Text focus
          [`@apply focus-visible:text-${config2.variant.outline.default.text.light.active} dark:focus-visible:text-${config2.variant.outline.default.text.dark.active}`]:
            {},
          //Text active
          [`@apply active:enabled:text-${config2.variant.outline.default.text.light.focus} dark:active:enabled:text-${config2.variant.outline.default.text.dark.focus}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.outline.default.background.light.base} dark:bg-${config2.variant.outline.default.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.outline.default.background.light.hover} dark:hover:enabled:bg-${config2.variant.outline.default.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.outline.default.background.light.active} dark:active:enabled:bg-${config2.variant.outline.default.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.outline.default.background.light.focus} dark:focus-visible:bg-${config2.variant.outline.default.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border-2 border-${config2.variant.outline.default.border.light} dark:border-${config2.variant.outline.default.border.dark}`]:
            {},
          //Shadows
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.outline.default.shadow.size} hover:enabled:shadow-${config2.variant.outline.default.shadow.light} dark:hover:enabled:shadow-${config2.variant.outline.default.shadow.dark}`]:
              {},
          },
        },
        //Outline:light
        "&.nui-button-light": {
          //Text color
          [`@apply text-${config2.variant.outline.light.text.light.base} dark:text-${config2.variant.outline.light.text.dark.base}`]:
            {},
          //Text hover
          [`@apply hover:enabled:text-${config2.variant.outline.light.text.light.hover} dark:hover:enabled:text-${config2.variant.outline.light.text.dark.hover}`]:
            {},
          //Text focus
          [`@apply focus-visible:text-${config2.variant.outline.light.text.light.active} dark:focus-visible:text-${config2.variant.outline.light.text.dark.active}`]:
            {},
          //Text active
          [`@apply active:enabled:text-${config2.variant.outline.light.text.light.focus} dark:active:enabled:text-${config2.variant.outline.light.text.dark.focus}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.outline.light.background.light.base} dark:bg-${config2.variant.outline.light.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.outline.light.background.light.hover} dark:hover:enabled:bg-${config2.variant.outline.light.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.outline.light.background.light.active} dark:active:enabled:bg-${config2.variant.outline.light.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.outline.light.background.light.focus} dark:focus-visible:bg-${config2.variant.outline.light.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border-2 border-${config2.variant.outline.light.border.light} dark:border-${config2.variant.outline.light.border.dark}`]:
            {},
          //Shadows
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.outline.light.shadow.size} hover:enabled:shadow-${config2.variant.outline.light.shadow.light} dark:hover:enabled:shadow-${config2.variant.outline.light.shadow.dark}`]:
              {},
          },
        },
        //Outline:muted
        "&.nui-button-muted, &.nui-button-muted-contrast": {
          //Text color
          [`@apply text-${config2.variant.outline.muted.text.light.base} dark:text-${config2.variant.outline.muted.text.dark.base}`]:
            {},
          //Text hover
          [`@apply hover:enabled:text-${config2.variant.outline.muted.text.light.hover} dark:hover:enabled:text-${config2.variant.outline.muted.text.dark.hover}`]:
            {},
          //Text focus
          [`@apply focus-visible:text-${config2.variant.outline.muted.text.light.active} dark:focus-visible:text-${config2.variant.outline.muted.text.dark.active}`]:
            {},
          //Text active
          [`@apply active:enabled:text-${config2.variant.outline.muted.text.light.focus} dark:active:enabled:text-${config2.variant.outline.muted.text.dark.focus}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.outline.muted.background.light.base} dark:bg-${config2.variant.outline.muted.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.outline.muted.background.light.hover} dark:hover:enabled:bg-${config2.variant.outline.muted.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.outline.muted.background.light.active} dark:active:enabled:bg-${config2.variant.outline.muted.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.outline.muted.background.light.focus} dark:focus-visible:bg-${config2.variant.outline.muted.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border-2 border-${config2.variant.outline.muted.border.light} dark:border-${config2.variant.outline.muted.border.dark}`]:
            {},
          //Shadows
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.outline.muted.shadow.size} hover:enabled:shadow-${config2.variant.outline.muted.shadow.light} dark:hover:enabled:shadow-${config2.variant.outline.muted.shadow.dark}`]:
              {},
          },
        },
        //Outline:dark
        "&.nui-button-dark": {
          //Text color
          [`@apply text-${config2.variant.outline.dark.text.light.base} dark:text-${config2.variant.outline.dark.text.dark.base}`]:
            {},
          //Text hover
          [`@apply hover:enabled:text-${config2.variant.outline.dark.text.light.hover} dark:hover:enabled:text-${config2.variant.outline.dark.text.dark.hover}`]:
            {},
          //Text focus
          [`@apply focus-visible:text-${config2.variant.outline.dark.text.light.active} dark:focus-visible:text-${config2.variant.outline.dark.text.dark.active}`]:
            {},
          //Text active
          [`@apply active:enabled:text-${config2.variant.outline.dark.text.light.focus} dark:active:enabled:text-${config2.variant.outline.dark.text.dark.focus}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.outline.dark.background.light.base} dark:bg-${config2.variant.outline.dark.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.outline.dark.background.light.hover} dark:hover:enabled:bg-${config2.variant.outline.dark.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.outline.dark.background.light.active} dark:active:enabled:bg-${config2.variant.outline.dark.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.outline.dark.background.light.focus} dark:focus-visible:bg-${config2.variant.outline.dark.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border-2 border-${config2.variant.outline.dark.border.light} dark:border-${config2.variant.outline.dark.border.dark}`]:
            {},
          //Shadows
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.outline.dark.shadow.size} hover:enabled:shadow-${config2.variant.outline.dark.shadow.light} dark:hover:enabled:shadow-${config2.variant.outline.dark.shadow.dark}`]:
              {},
          },
        },
        //Outline:black
        "&.nui-button-black": {
          //Text color
          [`@apply text-${config2.variant.outline.black.text.light.base} dark:text-${config2.variant.outline.black.text.dark.base}`]:
            {},
          //Text hover
          [`@apply hover:enabled:text-${config2.variant.outline.black.text.light.hover} dark:hover:enabled:text-${config2.variant.outline.black.text.dark.hover}`]:
            {},
          //Text focus
          [`@apply focus-visible:text-${config2.variant.outline.black.text.light.active} dark:focus-visible:text-${config2.variant.outline.black.text.dark.active}`]:
            {},
          //Text active
          [`@apply active:enabled:text-${config2.variant.outline.black.text.light.focus} dark:active:enabled:text-${config2.variant.outline.black.text.dark.focus}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.outline.black.background.light.base} dark:bg-${config2.variant.outline.black.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.outline.black.background.light.hover} dark:hover:enabled:bg-${config2.variant.outline.black.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.outline.black.background.light.active} dark:active:enabled:bg-${config2.variant.outline.black.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.outline.black.background.light.focus} dark:focus-visible:bg-${config2.variant.outline.black.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border-2 border-${config2.variant.outline.black.border.light} dark:border-${config2.variant.outline.black.border.dark}`]:
            {},
          //Shadows
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.outline.black.shadow.size} hover:enabled:shadow-${config2.variant.outline.black.shadow.light} dark:hover:enabled:shadow-${config2.variant.outline.black.shadow.dark}`]:
              {},
          },
        },
        //Outline:primary
        "&.nui-button-primary": {
          //Text color
          [`@apply text-${config2.variant.outline.primary.text.light.base} dark:text-${config2.variant.outline.primary.text.dark.base}`]:
            {},
          //Text hover
          [`@apply hover:enabled:text-${config2.variant.outline.primary.text.light.hover} dark:hover:enabled:text-${config2.variant.outline.primary.text.dark.hover}`]:
            {},
          //Text focus
          [`@apply focus-visible:text-${config2.variant.outline.primary.text.light.active} dark:focus-visible:text-${config2.variant.outline.primary.text.dark.active}`]:
            {},
          //Text active
          [`@apply active:enabled:text-${config2.variant.outline.primary.text.light.focus} dark:active:enabled:text-${config2.variant.outline.primary.text.dark.focus}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.outline.primary.background.light.base} dark:bg-${config2.variant.outline.primary.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.outline.primary.background.light.hover} dark:hover:enabled:bg-${config2.variant.outline.primary.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.outline.primary.background.light.active} dark:active:enabled:bg-${config2.variant.outline.primary.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.outline.primary.background.light.focus} dark:focus-visible:bg-${config2.variant.outline.primary.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border-2 border-${config2.variant.outline.primary.border.light} dark:border-${config2.variant.outline.primary.border.dark}`]:
            {},
          //Shadows
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.outline.primary.shadow.size} hover:enabled:shadow-${config2.variant.outline.primary.shadow.light} dark:hover:enabled:shadow-${config2.variant.outline.primary.shadow.dark}`]:
              {},
          },
        },
        //Outline:info
        "&.nui-button-info": {
          //Text color
          [`@apply text-${config2.variant.outline.info.text.light.base} dark:text-${config2.variant.outline.info.text.dark.base}`]:
            {},
          //Text hover
          [`@apply hover:enabled:text-${config2.variant.outline.info.text.light.hover} dark:hover:enabled:text-${config2.variant.outline.info.text.dark.hover}`]:
            {},
          //Text focus
          [`@apply focus-visible:text-${config2.variant.outline.info.text.light.active} dark:focus-visible:text-${config2.variant.outline.info.text.dark.active}`]:
            {},
          //Text active
          [`@apply active:enabled:text-${config2.variant.outline.info.text.light.focus} dark:active:enabled:text-${config2.variant.outline.info.text.dark.focus}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.outline.info.background.light.base} dark:bg-${config2.variant.outline.info.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.outline.info.background.light.hover} dark:hover:enabled:bg-${config2.variant.outline.info.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.outline.info.background.light.active} dark:active:enabled:bg-${config2.variant.outline.info.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.outline.info.background.light.focus} dark:focus-visible:bg-${config2.variant.outline.info.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border-2 border-${config2.variant.outline.info.border.light} dark:border-${config2.variant.outline.info.border.dark}`]:
            {},
          //Shadows
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.outline.info.shadow.size} hover:enabled:shadow-${config2.variant.outline.info.shadow.light} dark:hover:enabled:shadow-${config2.variant.outline.info.shadow.dark}`]:
              {},
          },
        },
        //Outline:success
        "&.nui-button-success": {
          //Text color
          [`@apply text-${config2.variant.outline.success.text.light.base} dark:text-${config2.variant.outline.success.text.dark.base}`]:
            {},
          //Text hover
          [`@apply hover:enabled:text-${config2.variant.outline.success.text.light.hover} dark:hover:enabled:text-${config2.variant.outline.success.text.dark.hover}`]:
            {},
          //Text focus
          [`@apply focus-visible:text-${config2.variant.outline.success.text.light.active} dark:focus-visible:text-${config2.variant.outline.success.text.dark.active}`]:
            {},
          //Text active
          [`@apply active:enabled:text-${config2.variant.outline.success.text.light.focus} dark:active:enabled:text-${config2.variant.outline.success.text.dark.focus}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.outline.success.background.light.base} dark:bg-${config2.variant.outline.success.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.outline.success.background.light.hover} dark:hover:enabled:bg-${config2.variant.outline.success.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.outline.success.background.light.active} dark:active:enabled:bg-${config2.variant.outline.success.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.outline.success.background.light.focus} dark:focus-visible:bg-${config2.variant.outline.success.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border-2 border-${config2.variant.outline.success.border.light} dark:border-${config2.variant.outline.success.border.dark}`]:
            {},
          //Shadows
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.outline.success.shadow.size} hover:enabled:shadow-${config2.variant.outline.success.shadow.light} dark:hover:enabled:shadow-${config2.variant.outline.success.shadow.dark}`]:
              {},
          },
        },
        //Outline:warning
        "&.nui-button-warning": {
          //Text color
          [`@apply text-${config2.variant.outline.warning.text.light.base} dark:text-${config2.variant.outline.warning.text.dark.base}`]:
            {},
          //Text hover
          [`@apply hover:enabled:text-${config2.variant.outline.warning.text.light.hover} dark:hover:enabled:text-${config2.variant.outline.warning.text.dark.hover}`]:
            {},
          //Text focus
          [`@apply focus-visible:text-${config2.variant.outline.warning.text.light.active} dark:focus-visible:text-${config2.variant.outline.warning.text.dark.active}`]:
            {},
          //Text active
          [`@apply active:enabled:text-${config2.variant.outline.warning.text.light.focus} dark:active:enabled:text-${config2.variant.outline.warning.text.dark.focus}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.outline.warning.background.light.base} dark:bg-${config2.variant.outline.warning.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.outline.warning.background.light.hover} dark:hover:enabled:bg-${config2.variant.outline.warning.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.outline.warning.background.light.active} dark:active:enabled:bg-${config2.variant.outline.warning.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.outline.warning.background.light.focus} dark:focus-visible:bg-${config2.variant.outline.warning.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border-2 border-${config2.variant.outline.warning.border.light} dark:border-${config2.variant.outline.warning.border.dark}`]:
            {},
          //Shadows
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.outline.warning.shadow.size} hover:enabled:shadow-${config2.variant.outline.warning.shadow.light} dark:hover:enabled:shadow-${config2.variant.outline.warning.shadow.dark}`]:
              {},
          },
        },
        //Outline:danger
        "&.nui-button-danger": {
          //Text color
          [`@apply text-${config2.variant.outline.danger.text.light.base} dark:text-${config2.variant.outline.danger.text.dark.base}`]:
            {},
          //Text hover
          [`@apply hover:enabled:text-${config2.variant.outline.danger.text.light.hover} dark:hover:enabled:text-${config2.variant.outline.danger.text.dark.hover}`]:
            {},
          //Text focus
          [`@apply focus-visible:text-${config2.variant.outline.danger.text.light.active} dark:focus-visible:text-${config2.variant.outline.danger.text.dark.active}`]:
            {},
          //Text active
          [`@apply active:enabled:text-${config2.variant.outline.danger.text.light.focus} dark:active:enabled:text-${config2.variant.outline.danger.text.dark.focus}`]:
            {},
          //Bg main
          [`@apply bg-${config2.variant.outline.danger.background.light.base} dark:bg-${config2.variant.outline.danger.background.dark.base}`]:
            {},
          //Bg hover
          [`@apply hover:enabled:bg-${config2.variant.outline.danger.background.light.hover} dark:hover:enabled:bg-${config2.variant.outline.danger.background.dark.hover}`]:
            {},
          //Bg active
          [`@apply active:enabled:bg-${config2.variant.outline.danger.background.light.active} dark:active:enabled:bg-${config2.variant.outline.danger.background.dark.active}`]:
            {},
          //Bg focus
          [`@apply focus-visible:bg-${config2.variant.outline.danger.background.light.focus} dark:focus-visible:bg-${config2.variant.outline.danger.background.dark.focus}`]:
            {},
          //Border main
          [`@apply border-2 border-${config2.variant.outline.danger.border.light} dark:border-${config2.variant.outline.danger.border.dark}`]:
            {},
          //Shadows
          "&.nui-button-shadow-hover": {
            [`@apply hover:enabled:shadow-${config2.variant.outline.danger.shadow.size} hover:enabled:shadow-${config2.variant.outline.danger.shadow.light} dark:hover:enabled:shadow-${config2.variant.outline.danger.shadow.dark}`]:
              {},
          },
        },
      },
      // #endregion
    },
  });
}, config$K);

module.export = button;

import plugin from "tailwindcss/plugin";

const key$7 = "tabs";
const defaultConfig$7 = {
  inner: {
    font: {
      family: "sans",
    },
    margin: {
      bottom: "6",
    },
  },
  item: {
    font: {
      size: "sm",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  tabs: {
    color: {
      default: {
        font: {
          active: {
            light: "muted-800",
            dark: "muted-100",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
        border: {
          active: {
            light: "muted-500",
            dark: "muted-100",
          },
          inactive: {
            light: "transparent",
            dark: "transparent",
          },
        },
      },
      primary: {
        font: {
          active: {
            light: "muted-800",
            dark: "muted-100",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
        border: {
          active: {
            light: "primary-500",
            dark: "primary-500",
          },
          inactive: {
            light: "transparent",
            dark: "transparent",
          },
        },
      },
      light: {
        font: {
          active: {
            light: "muted-600",
            dark: "muted-100",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
        border: {
          active: {
            light: "muted-600",
            dark: "muted-100",
          },
          inactive: {
            light: "transparent",
            dark: "transparent",
          },
        },
      },
      dark: {
        font: {
          active: {
            light: "muted-900",
            dark: "muted-100",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
        border: {
          active: {
            light: "muted-900",
            dark: "muted-100",
          },
          inactive: {
            light: "transparent",
            dark: "transparent",
          },
        },
      },
      black: {
        font: {
          active: {
            light: "black",
            dark: "white",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
        border: {
          active: {
            light: "black",
            dark: "white",
          },
          inactive: {
            light: "transparent",
            dark: "transparent",
          },
        },
      },
    },
  },
  pill: {
    rounded: "rounded-xl",
    font: {
      align: "center",
      size: "xs",
    },
    color: {
      default: {
        font: {
          active: {
            light: "muted-800",
            dark: "muted-100",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
        background: {
          active: {
            light: "white",
            dark: "muted-800",
          },
        },
        border: {
          active: {
            light: "muted-200",
            dark: "muted-700",
          },
        },
        shadow: {
          active: {
            size: "lg",
            light: "muted-500/30",
            dark: "muted-800/50",
          },
        },
      },
      primary: {
        font: {
          active: {
            light: "primary-invert",
            dark: "primary-invert",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
        background: {
          active: {
            light: "primary-600",
            dark: "primary-600",
          },
        },
        shadow: {
          active: {
            size: "lg",
            light: "primary-500/50",
            dark: "primary-500/50",
          },
        },
      },
      light: {
        font: {
          active: {
            light: "muted-700",
            dark: "muted-100",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
        background: {
          active: {
            light: "muted-100",
            dark: "muted-800",
          },
        },
        shadow: {
          active: {
            size: "lg",
            light: "muted-500/30",
            dark: "muted-800/50",
          },
        },
      },
      dark: {
        font: {
          active: {
            light: "muted-100",
            dark: "muted-900",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
        background: {
          active: {
            light: "muted-900",
            dark: "muted-100",
          },
        },
        shadow: {
          active: {
            size: "lg",
            light: "muted-500/30",
            dark: "muted-800/50",
          },
        },
      },
      black: {
        font: {
          active: {
            light: "white",
            dark: "black",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
        background: {
          active: {
            light: "black",
            dark: "white",
          },
        },
        shadow: {
          active: {
            size: "lg",
            light: "muted-500/30",
            dark: "muted-800/50",
          },
        },
      },
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
};

const config$7 = {
  theme: {
    nui: {
      [key$7]: defaultConfig$7,
    },
  },
};
const tabs = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$7}`);
  addComponents({
    //Wrapper
    ".nui-tabs": {
      "@apply relative": {},
      //Tabs:inner
      ".nui-tabs-inner": {
        [`@apply font-${config2.inner.font.family} mb-${config2.inner.margin.bottom} flex`]:
          {},
      },
      //Tabs:bordered
      "&.nui-tabs-bordered": {
        //Tabs:inner
        ".nui-tabs-inner": {
          "@apply border-b border-muted-200 dark:border-muted-800": {},
        },
      },
      //Tabs:item
      ".nui-tab-item": {
        //Base
        "@apply cursor-pointer border-b-2 px-4 py-3": {},
        //Color
        [`@apply text-${config2.item.font.size}`]: {},
        //Transition
        [`@apply transition-${config2.item.transition.property} duration-${config2.item.transition.duration}`]:
          {},
        //Item:icon
        "&.nui-has-icon": {
          "@apply flex items-center gap-1": {},
        },
      },
      //Item:pill
      ".nui-pill-item": {
        [`@apply flex flex-col ${config2.pill.rounded} px-5 cursor-pointer`]:
          {},
        //font
        [`@apply text-${config2.pill.font.align}`]: {},
        //Transition
        [`@apply transition-${config2.pill.transition.property} duration-${config2.pill.transition.duration}`]:
          {},
        //Item:no-icon
        "&:not(.nui-has-icon)": {
          "@apply flex items-center gap-1 py-2": {},
        },
        //Item:icon
        "&.nui-has-icon": {
          "@apply flex items-center gap-1 py-3": {},
        },
      },
      //Color: default
      "&.nui-tabs-default": {
        //Tabs:item
        ".nui-tab-item": {
          //Item:inactive
          "&:not(.nui-active)": {
            //Base
            "@apply border-transparent": {},
            //Color
            [`@apply text-${config2.tabs.color.default.font.inactive.light} dark:text-${config2.tabs.color.default.font.inactive.dark}`]:
              {},
          },
          //Item:active
          "&.nui-active": {
            //Border
            [`@apply border-${config2.tabs.color.default.border.active.light} dark:border-${config2.tabs.color.default.border.active.dark}`]:
              {},
            //Color
            [`@apply text-${config2.tabs.color.default.font.active.light} dark:text-${config2.tabs.color.default.font.active.dark}`]:
              {},
          },
        },
        //Pills:item
        ".nui-pill-item": {
          //Item:inactive
          "&:not(.nui-active)": {
            //Color
            [`@apply text-${config2.pill.color.default.font.inactive.light} dark:text-${config2.pill.color.default.font.inactive.dark}`]:
              {},
          },
          //Item:active
          "&.nui-active": {
            //Color
            [`@apply text-${config2.pill.color.default.font.active.light} dark:text-${config2.pill.color.default.font.active.dark}`]:
              {},
            //Background
            [`@apply bg-${config2.pill.color.default.background.active.light} dark:bg-${config2.pill.color.default.background.active.dark}`]:
              {},
            //border
            [`@apply border border-${config2.pill.color.default.border.active.light} dark:border-${config2.pill.color.default.border.active.dark}`]:
              {},
            //Shadow
            [`@apply shadow-${config2.pill.color.default.shadow.active.size} shadow-${config2.pill.color.default.shadow.active.light} dark:shadow-${config2.pill.color.default.shadow.active.dark}`]:
              {},
          },
        },
      },
      //Color: primary
      "&.nui-tabs-primary": {
        //Tabs:item
        ".nui-tab-item": {
          //Item:inactive
          "&:not(.nui-active)": {
            //Base
            "@apply border-transparent": {},
            //Color
            [`@apply text-${config2.tabs.color.primary.font.inactive.light} dark:text-${config2.tabs.color.primary.font.inactive.dark}`]:
              {},
          },
          //Item:active
          "&.nui-active": {
            //Border
            [`@apply border-${config2.tabs.color.primary.border.active.light} dark:border-${config2.tabs.color.primary.border.active.dark}`]:
              {},
            //Color
            [`@apply text-${config2.tabs.color.primary.font.active.light} dark:text-${config2.tabs.color.primary.font.active.dark}`]:
              {},
          },
        },
        //Pills:item
        ".nui-pill-item": {
          //Item:inactive
          "&:not(.nui-active)": {
            //Color
            [`@apply text-${config2.pill.color.primary.font.inactive.light} dark:text-${config2.pill.color.primary.font.inactive.dark}`]:
              {},
          },
          //Item:active
          "&.nui-active": {
            //Color
            [`@apply text-${config2.pill.color.primary.font.active.light} dark:text-${config2.pill.color.primary.font.active.dark}`]:
              {},
            //Background
            [`@apply !bg-${config2.pill.color.primary.background.active.light} dark:!bg-${config2.pill.color.primary.background.active.dark}`]:
              {},
            //Shadow
            [`@apply shadow-${config2.pill.color.primary.shadow.active.size} bg-${config2.pill.color.primary.shadow.active.light} dark:bg-${config2.pill.color.primary.shadow.active.dark}`]:
              {},
          },
        },
      },
      //Color: light
      "&.nui-tabs-light": {
        //Tabs:item
        ".nui-tab-item": {
          //Item:inactive
          "&:not(.nui-active)": {
            //Base
            "@apply border-transparent": {},
            //Color
            [`@apply text-${config2.tabs.color.light.font.inactive.light} dark:text-${config2.tabs.color.light.font.inactive.dark}`]:
              {},
          },
          //Item:active
          "&.nui-active": {
            //Border
            [`@apply border-${config2.tabs.color.light.border.active.light} dark:border-${config2.tabs.color.light.border.active.dark}`]:
              {},
            //Color
            [`@apply text-${config2.tabs.color.light.font.active.light} dark:text-${config2.tabs.color.light.font.active.dark}`]:
              {},
          },
        },
        //Pills:item
        ".nui-pill-item": {
          //Item:inactive
          "&:not(.nui-active)": {
            //Color
            [`@apply text-${config2.pill.color.light.font.inactive.light} dark:text-${config2.pill.color.light.font.inactive.dark}`]:
              {},
          },
          //Item:active
          "&.nui-active": {
            //Color
            [`@apply text-${config2.pill.color.light.font.active.light} dark:text-${config2.pill.color.light.font.active.dark}`]:
              {},
            //Background
            [`@apply !bg-${config2.pill.color.light.background.active.light} dark:!bg-${config2.pill.color.light.background.active.dark}`]:
              {},
            //Shadow
            [`@apply shadow-${config2.pill.color.light.shadow.active.size} shadow-${config2.pill.color.light.shadow.active.light} dark:shadow-${config2.pill.color.light.shadow.active.dark}`]:
              {},
          },
        },
      },
      //Color: dark
      "&.nui-tabs-dark": {
        //Tabs:item
        ".nui-tab-item": {
          //Item:inactive
          "&:not(.nui-active)": {
            //Base
            "@apply border-transparent": {},
            //Color
            [`@apply text-${config2.tabs.color.dark.font.inactive.light} dark:text-${config2.tabs.color.dark.font.inactive.dark}`]:
              {},
          },
          //Item:active
          "&.nui-active": {
            //Border
            [`@apply border-${config2.tabs.color.dark.border.active.light} dark:border-${config2.tabs.color.dark.border.active.dark}`]:
              {},
            //Color
            [`@apply text-${config2.tabs.color.dark.font.active.light} dark:text-${config2.tabs.color.dark.font.active.dark}`]:
              {},
          },
        },
        //Pills:item
        ".nui-pill-item": {
          //Item:inactive
          "&:not(.nui-active)": {
            //Color
            [`@apply text-${config2.pill.color.dark.font.inactive.light} dark:text-${config2.pill.color.dark.font.inactive.dark}`]:
              {},
          },
          //Item:active
          "&.nui-active": {
            //Color
            [`@apply text-${config2.pill.color.dark.font.active.light} dark:text-${config2.pill.color.dark.font.active.dark}`]:
              {},
            //Background
            [`@apply !bg-${config2.pill.color.dark.background.active.light} dark:!bg-${config2.pill.color.dark.background.active.dark}`]:
              {},
            //Shadow
            [`@apply shadow-${config2.pill.color.dark.shadow.active.size} shadow-${config2.pill.color.dark.shadow.active.light} dark:shadow-${config2.pill.color.dark.shadow.active.dark}`]:
              {},
          },
        },
      },
      //Color: black
      "&.nui-tabs-black": {
        //Tabs:item
        ".nui-tab-item": {
          //Item:inactive
          "&:not(.nui-active)": {
            //Base
            "@apply border-transparent": {},
            //Color
            [`@apply text-${config2.tabs.color.black.font.inactive.light} dark:text-${config2.tabs.color.black.font.inactive.dark}`]:
              {},
          },
          //Item:active
          "&.nui-active": {
            //Border
            [`@apply border-${config2.tabs.color.black.border.active.light} dark:border-${config2.tabs.color.black.border.active.dark}`]:
              {},
            //Color
            [`@apply text-${config2.tabs.color.black.font.active.light} dark:text-${config2.tabs.color.black.font.active.dark}`]:
              {},
          },
        },
        //Pills:item
        ".nui-pill-item": {
          //Item:inactive
          "&:not(.nui-active)": {
            //Color
            [`@apply text-${config2.pill.color.black.font.inactive.light} dark:text-${config2.pill.color.black.font.inactive.dark}`]:
              {},
          },
          //Item:active
          "&.nui-active": {
            //Color
            [`@apply text-${config2.pill.color.black.font.active.light} dark:text-${config2.pill.color.black.font.active.dark}`]:
              {},
            //Background
            [`@apply !bg-${config2.pill.color.black.background.active.light} dark:!bg-${config2.pill.color.black.background.active.dark}`]:
              {},
            //Shadow
            [`@apply shadow-${config2.pill.color.black.shadow.active.size} shadow-${config2.pill.color.black.shadow.active.light} dark:shadow-${config2.pill.color.black.shadow.active.dark}`]:
              {},
          },
        },
      },
      //Tabs:content
      ".nui-tab-content": {
        "@apply relative block": {},
      },
      //Align:center
      "&.nui-tabs-centered": {
        ".nui-tabs-inner": {
          "@apply justify-center": {},
        },
      },
      //Align:end
      "&.nui-tabs-end": {
        ".nui-tabs-inner": {
          "@apply justify-end": {},
        },
      },
    },
  });
}, config$7);
module.export = tabs;

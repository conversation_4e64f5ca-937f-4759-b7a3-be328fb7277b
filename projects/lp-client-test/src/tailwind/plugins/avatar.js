import plugin from "tailwindcss/plugin";
const key$R = "avatar";
const defaultConfig$Q = {
  inner: {
    size: "full",
    transition: {
      property: "all",
      duration: "300",
    },
  },
  img: {
    size: "full",
    shadow: "sm",
  },
  text: {
    font: {
      family: "sans",
      weight: "medium",
    },
  },
  badge: {
    rounded: "full",
    background: {
      light: "white",
      dark: "muted-800",
    },
    img: {
      size: "full",
      rounded: "full",
    },
  },
  dot: {
    border: {
      light: "white",
      dark: "muted-800",
    },
    rounded: "full",
    color: {
      primary: "primary-500",
      info: "info-500",
      success: "success-500",
      warning: "warning-500",
      danger: "danger-500",
      yellow: "yellow-400",
      pink: "pink-500",
      indigo: "indigo-500",
    },
  },
  ring: {
    width: "2",
    offset: {
      size: "2",
      color: {
        light: "white",
        dark: "muted-800",
      },
    },
    color: {
      primary: "primary-500",
      info: "info-500",
      success: "success-500",
      warning: "warning-500",
      danger: "danger-500",
      yellow: "yellow-400",
      pink: "pink-500",
      indigo: "indigo-500",
    },
  },
  size: {
    xxs: {
      avatar: "6",
      rounded: {
        none: "rounded-none",
        sm: "rounded-sm",
        md: "rounded",
        lg: "rounded-md",
        full: "rounded-full",
      },
      dot: {
        size: "1.5",
        position: {
          rounded: {
            none: "0",
            sm: "0",
            md: "0",
            lg: "0",
            full: "0",
          },
        },
      },
      text: {
        font: {
          size: "xs",
        },
      },
      badge: {
        size: "3",
        position: "1",
      },
    },
    xs: {
      avatar: "8",
      rounded: {
        none: "rounded-none",
        sm: "rounded-sm",
        md: "rounded",
        lg: "rounded-lg",
        full: "rounded-full",
      },
      dot: {
        size: "2",
        position: {
          rounded: {
            none: "0",
            sm: "0",
            md: "0",
            lg: "0",
            full: "0",
          },
        },
      },
      text: {
        font: {
          size: "sm",
        },
      },
      badge: {
        size: "4",
        position: "1",
      },
    },
    sm: {
      avatar: "10",
      rounded: {
        none: "rounded-none",
        sm: "rounded-md",
        md: "rounded-lg",
        lg: "rounded-xl",
        full: "rounded-full",
      },
      dot: {
        size: "2",
        position: {
          rounded: {
            none: "0",
            sm: "0",
            md: "0",
            lg: "0",
            full: "0.5",
          },
        },
      },
      text: {
        font: {
          size: "sm",
        },
      },
      badge: {
        size: "5",
        position: "1",
      },
    },
    md: {
      avatar: "12",
      rounded: {
        none: "rounded-none",
        sm: "rounded-lg",
        md: "rounded-xl",
        lg: "rounded-2xl",
        full: "rounded-full",
      },
      dot: {
        size: "3",
        position: {
          rounded: {
            none: "0",
            sm: "0",
            md: "0",
            lg: "0",
            full: "0.5",
          },
        },
      },
      text: {
        font: {
          size: "sm",
        },
      },
      badge: {
        size: "5",
        position: "1",
      },
    },
    lg: {
      avatar: "16",
      rounded: {
        none: "rounded-none",
        sm: "rounded-lg",
        md: "rounded-xl",
        lg: "rounded-2xl",
        full: "rounded-full",
      },
      dot: {
        size: "3",
        position: {
          rounded: {
            none: "0",
            sm: "0",
            md: "0",
            lg: "0",
            full: "1",
          },
        },
      },
      text: {
        font: {
          size: "sm",
        },
      },
      badge: {
        size: "6",
        position: "1",
      },
    },
    xl: {
      avatar: "20",
      rounded: {
        none: "rounded-none",
        sm: "rounded-xl",
        md: "rounded-2xl",
        lg: "rounded-3xl",
        full: "rounded-full",
      },
      dot: {
        size: "4",
        position: {
          rounded: {
            none: "0",
            sm: "0",
            md: "0",
            lg: "0",
            full: "1",
          },
        },
      },
      text: {
        font: {
          size: "base",
        },
      },
      badge: {
        size: "8",
        position: "1",
      },
    },
    xxl: {
      avatar: "24",
      rounded: {
        none: "rounded-none",
        sm: "rounded-xl",
        md: "rounded-2xl",
        lg: "rounded-3xl",
        full: "rounded-full",
      },
      dot: {
        size: "4",
        position: {
          rounded: {
            none: "0",
            sm: "0",
            md: "0",
            lg: "0",
            full: "1.5",
          },
        },
      },
      text: {
        font: {
          size: "xl",
        },
      },
      badge: {
        size: "10",
        position: "1.5",
      },
    },
    xxxl: {
      avatar: "28",
      rounded: {
        none: "rounded-none",
        sm: "rounded-xl",
        md: "rounded-2xl",
        lg: "rounded-3xl",
        full: "rounded-full",
      },
      dot: {
        size: "4",
        position: {
          rounded: {
            none: "0",
            sm: "0",
            md: "0",
            lg: "0",
            full: "2",
          },
        },
      },
      text: {
        font: {
          size: "2xl",
        },
      },
      badge: {
        size: "10",
        position: "1.5",
      },
    },
    xxxxl: {
      avatar: "32",
      rounded: {
        none: "rounded-none",
        sm: "rounded-xl",
        md: "rounded-2xl",
        lg: "rounded-3xl",
        full: "rounded-full",
      },
      dot: {
        size: "4",
        position: {
          rounded: {
            none: "0",
            sm: "0",
            md: "0",
            lg: "0",
            full: "2.5",
          },
        },
      },
      text: {
        font: {
          size: "3xl",
        },
      },
      badge: {
        size: "12",
        position: "2",
      },
    },
  },
};

const config$R = {
  theme: {
    nui: {
      [key$R]: defaultConfig$Q,
    },
  },
};
const avatarPlugin = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$R}`);
  addComponents({
    ".nui-avatar": {
      //Avatar:wrapper
      "@apply relative inline-flex shrink-0 items-center justify-center outline-none":
        {},
      //Avatar:inner
      ".nui-avatar-inner": {
        //Base
        "@apply flex items-center justify-center overflow-hidden text-center":
          {},
        //Size
        [`@apply h-${config2.inner.size} w-${config2.inner.size}`]: {},
        //Transitions
        [`@apply transition-${config2.inner.transition.property} duration-${config2.inner.transition.duration}`]:
          {},
      },
      //Avatar:badge
      ".nui-avatar-badge": {
        //Base
        [`@apply absolute z-10 block overflow-hidden rounded-${config2.badge.rounded}`]:
          {},
        //Background
        [`@apply bg-${config2.badge.background.light} dark:bg-${config2.badge.background.dark}`]:
          {},
        //Badge:img
        ".nui-badge-img": {
          //Base
          "@apply relative scale-90 object-cover": {},
          //Size & Radius
          [`@apply h-${config2.badge.img.size} w-${config2.badge.img.size} rounded-${config2.badge.img.rounded}`]:
            {},
        },
      },
      //Avatar:img
      ".nui-avatar-img": {
        //Base
        "@apply object-cover": {},
        //Size & Radius
        [`@apply h-${config2.img.size} max-h-${config2.img.size} w-${config2.img.size} max-w-${config2.img.size}`]:
          {},
        //Shadow
        [`@apply shadow-${config2.img.shadow}`]: {},
      },
      //Avatar:text
      ".nui-avatar-text": {
        [`@apply font-${config2.text.font.family} font-${config2.text.font.weight} text-center uppercase`]:
          {},
      },
      //Avatar:dot
      ".nui-avatar-dot": {
        //Base
        "@apply absolute block": {},
        //Border & Radius
        [`@apply border border-${config2.dot.border.light} dark:border-${config2.dot.border.dark} rounded-${config2.dot.rounded}`]:
          {},
        //Color:primary
        "&.nui-dot-primary": {
          [`@apply bg-${config2.dot.color.primary}`]: {},
        },
        //Color:info
        "&.nui-dot-info": {
          [`@apply bg-${config2.dot.color.info}`]: {},
        },
        //Color:success
        "&.nui-dot-success": {
          [`@apply bg-${config2.dot.color.success}`]: {},
        },
        //Color:warning
        "&.nui-dot-warning": {
          [`@apply bg-${config2.dot.color.warning}`]: {},
        },
        //Color:danger
        "&.nui-dot-danger": {
          [`@apply bg-${config2.dot.color.danger}`]: {},
        },
        //Color:yellow
        "&.nui-dot-yellow": {
          [`@apply bg-${config2.dot.color.yellow}`]: {},
        },
        //Color:pink
        "&.nui-dot-pink": {
          [`@apply bg-${config2.dot.color.pink}`]: {},
        },
        //Color:indigo
        "&.nui-dot-indigo": {
          [`@apply bg-${config2.dot.color.indigo}`]: {},
        },
      },
      //Avatar:ring
      "&.nui-avatar-ring": {
        //Base (requires rounded:full)
        "&.nui-avatar-rounded-full": {
          [`@apply ring-${config2.ring.width}`]: {},
          //Offset
          [`@apply ring-offset-${config2.ring.offset.size} ring-offset-${config2.ring.offset.color.light} dark:ring-offset-${config2.ring.offset.color.dark}`]:
            {},
        },
        //Color:primary
        "&.nui-ring-primary": {
          [`@apply bg-${config2.ring.color.primary}`]: {},
        },
        //Color:info
        "&.nui-ring-info": {
          [`@apply bg-${config2.ring.color.info}`]: {},
        },
        //Color:success
        "&.nui-ring-success": {
          [`@apply bg-${config2.ring.color.success}`]: {},
        },
        //Color:warning
        "&.nui-ring-warning": {
          [`@apply bg-${config2.ring.color.warning}`]: {},
        },
        //Color:danger
        "&.nui-ring-danger": {
          [`@apply bg-${config2.ring.color.danger}`]: {},
        },
        //Color:yellow
        "&.nui-ring-yellow": {
          [`@apply bg-${config2.ring.color.yellow}`]: {},
        },
        //Color:pink
        "&.nui-ring-pink": {
          [`@apply bg-${config2.ring.color.pink}`]: {},
        },
        //Color:indigo
        "&.nui-ring-indigo": {
          [`@apply bg-${config2.ring.color.indigo}`]: {},
        },
      },
      //Size:xxs
      "&.nui-avatar-xxs": {
        [`@apply h-${config2.size.xxs.avatar} w-${config2.size.xxs.avatar}`]:
          {},
        //xxs:badge
        ".nui-avatar-badge": {
          [`@apply h-${config2.size.xxs.badge.size} w-${config2.size.xxs.badge.size} -bottom-${config2.size.xxs.badge.position} -end-${config2.size.xxs.badge.position}`]:
            {},
        },
        //xxs:dot
        ".nui-avatar-dot": {
          [`@apply h-${config2.size.xxs.dot.size} w-${config2.size.xxs.dot.size}`]:
            {},
        },
        //xxs:text
        ".nui-avatar-text": {
          [`@apply text-${config2.size.xxs.text.font.size}`]: {},
        },
        //xxs:rounded-none
        "&.nui-avatar-rounded-none": {
          [`@apply ${config2.size.xxs.rounded.none}`]: {},
          //xxs:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxs.rounded.none}`]: {},
          },
          //xxs:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxs.dot.position.rounded.none} top-${config2.size.xxs.dot.position.rounded.none}`]:
              {},
          },
        },
        //xxs:rounded-sm
        "&.nui-avatar-rounded-sm": {
          [`@apply ${config2.size.xxs.rounded.sm}`]: {},
          //xxs:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxs.rounded.sm}`]: {},
          },
          //xxs:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxs.dot.position.rounded.sm} top-${config2.size.xxs.dot.position.rounded.sm}`]:
              {},
          },
        },
        //xxs:rounded-md
        "&.nui-avatar-rounded-md": {
          [`@apply ${config2.size.xxs.rounded.md}`]: {},
          //xxs:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxs.rounded.md}`]: {},
          },
          //xxs:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxs.dot.position.rounded.md} top-${config2.size.xxs.dot.position.rounded.md}`]:
              {},
          },
        },
        //xxs:rounded-lg
        "&.nui-avatar-rounded-lg": {
          [`@apply ${config2.size.xxs.rounded.lg}`]: {},
          //xxs:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxs.rounded.lg}`]: {},
          },
          //xxs:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxs.dot.position.rounded.lg} top-${config2.size.xxs.dot.position.rounded.lg}`]:
              {},
          },
        },
        //xxs:rounded-full
        "&.nui-avatar-rounded-full": {
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxs.dot.position.rounded.full} top-${config2.size.xxs.dot.position.rounded.full}`]:
              {},
          },
        },
      },
      //Size:xs
      "&.nui-avatar-xs": {
        [`@apply h-${config2.size.xs.avatar} w-${config2.size.xs.avatar}`]: {},
        //xs:badge
        ".nui-avatar-badge": {
          [`@apply h-${config2.size.xs.badge.size} w-${config2.size.xs.badge.size} -bottom-${config2.size.xs.badge.position} -end-${config2.size.xs.badge.position}`]:
            {},
        },
        //xs:dot
        ".nui-avatar-dot": {
          [`@apply h-${config2.size.xs.dot.size} w-${config2.size.xs.dot.size}`]:
            {},
        },
        //xs:text
        ".nui-avatar-text": {
          [`@apply text-${config2.size.xs.text.font.size}`]: {},
        },
        //xs:rounded-none
        "&.nui-avatar-rounded-none": {
          [`@apply ${config2.size.xs.rounded.none}`]: {},
          //xs:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xs.rounded.none}`]: {},
          },
          //xs:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xs.dot.position.rounded.none} top-${config2.size.xs.dot.position.rounded.none}`]:
              {},
          },
        },
        //xs:rounded-sm
        "&.nui-avatar-rounded-sm": {
          [`@apply ${config2.size.xs.rounded.sm}`]: {},
          //xs:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xs.rounded.sm}`]: {},
          },
          //xs:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xs.dot.position.rounded.sm} top-${config2.size.xs.dot.position.rounded.sm}`]:
              {},
          },
        },
        //xs:rounded-md
        "&.nui-avatar-rounded-md": {
          [`@apply ${config2.size.xs.rounded.md}`]: {},
          //xs:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xs.rounded.md}`]: {},
          },
          //xs:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xs.dot.position.rounded.md} top-${config2.size.xs.dot.position.rounded.md}`]:
              {},
          },
        },
        //xs:rounded-lg
        "&.nui-avatar-rounded-lg": {
          [`@apply ${config2.size.xs.rounded.lg}`]: {},
          //xs:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xs.rounded.lg}`]: {},
          },
          //xs:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xs.dot.position.rounded.lg} top-${config2.size.xs.dot.position.rounded.lg}`]:
              {},
          },
        },
        //xs:rounded-full
        "&.nui-avatar-rounded-full": {
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xs.dot.position.rounded.full} top-${config2.size.xs.dot.position.rounded.full}`]:
              {},
          },
        },
      },
      //Size:sm
      "&.nui-avatar-sm": {
        [`@apply h-${config2.size.sm.avatar} w-${config2.size.sm.avatar}`]: {},
        //sm:badge
        ".nui-avatar-badge": {
          [`@apply h-${config2.size.sm.badge.size} w-${config2.size.sm.badge.size} -bottom-${config2.size.sm.badge.position} -end-${config2.size.sm.badge.position}`]:
            {},
        },
        //sm:dot
        ".nui-avatar-dot": {
          [`@apply h-${config2.size.sm.dot.size} w-${config2.size.sm.dot.size}`]:
            {},
        },
        //sm:text
        ".nui-avatar-text": {
          [`@apply text-${config2.size.sm.text.font.size}`]: {},
        },
        //sm:rounded-none
        "&.nui-avatar-rounded-none": {
          [`@apply ${config2.size.sm.rounded.none}`]: {},
          //sm:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.sm.rounded.none}`]: {},
          },
          //sm:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.sm.dot.position.rounded.none} top-${config2.size.sm.dot.position.rounded.none}`]:
              {},
          },
        },
        //sm:rounded-sm
        "&.nui-avatar-rounded-sm": {
          [`@apply ${config2.size.sm.rounded.sm}`]: {},
          //sm:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.sm.rounded.sm}`]: {},
          },
          //sm:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.sm.dot.position.rounded.sm} top-${config2.size.sm.dot.position.rounded.sm}`]:
              {},
          },
        },
        //sm:rounded-md
        "&.nui-avatar-rounded-md": {
          [`@apply ${config2.size.sm.rounded.md}`]: {},
          //sm:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.sm.rounded.md}`]: {},
          },
          //sm:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.sm.dot.position.rounded.md} top-${config2.size.sm.dot.position.rounded.md}`]:
              {},
          },
        },
        //sm:rounded-lg
        "&.nui-avatar-rounded-lg": {
          [`@apply ${config2.size.sm.rounded.lg}`]: {},
          //sm:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.sm.rounded.lg}`]: {},
          },
          //sm:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.sm.dot.position.rounded.lg} top-${config2.size.sm.dot.position.rounded.lg}`]:
              {},
          },
        },
        //sm:rounded-full
        "&.nui-avatar-rounded-full": {
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.sm.dot.position.rounded.full} top-${config2.size.sm.dot.position.rounded.full}`]:
              {},
          },
        },
      },
      //Size:md
      "&.nui-avatar-md": {
        [`@apply h-${config2.size.md.avatar} w-${config2.size.md.avatar}`]: {},
        //md:badge
        ".nui-avatar-badge": {
          [`@apply h-${config2.size.md.badge.size} w-${config2.size.md.badge.size} -bottom-${config2.size.md.badge.position} -end-${config2.size.md.badge.position}`]:
            {},
        },
        //md:dot
        ".nui-avatar-dot": {
          [`@apply h-${config2.size.md.dot.size} w-${config2.size.md.dot.size}`]:
            {},
        },
        //md:text
        ".nui-avatar-text": {
          [`@apply text-${config2.size.md.text.font.size}`]: {},
        },
        //md:rounded-none
        "&.nui-avatar-rounded-none": {
          [`@apply ${config2.size.md.rounded.none}`]: {},
          //md:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.md.rounded.none}`]: {},
          },
          //md:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.md.dot.position.rounded.none} top-${config2.size.md.dot.position.rounded.none}`]:
              {},
          },
        },
        //md:rounded-sm
        "&.nui-avatar-rounded-sm": {
          [`@apply ${config2.size.md.rounded.sm}`]: {},
          //md:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.md.rounded.sm}`]: {},
          },
          //md:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.md.dot.position.rounded.sm} top-${config2.size.md.dot.position.rounded.sm}`]:
              {},
          },
        },
        //md:rounded-md
        "&.nui-avatar-rounded-md": {
          [`@apply ${config2.size.md.rounded.md}`]: {},
          //md:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.md.rounded.md}`]: {},
          },
          //md:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.md.dot.position.rounded.md} top-${config2.size.md.dot.position.rounded.md}`]:
              {},
          },
        },
        //md:rounded-lg
        "&.nui-avatar-rounded-lg": {
          [`@apply ${config2.size.md.rounded.lg}`]: {},
          //md:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.md.rounded.lg}`]: {},
          },
          //md:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.md.dot.position.rounded.lg} top-${config2.size.md.dot.position.rounded.lg}`]:
              {},
          },
        },
        //md:rounded-full
        "&.nui-avatar-rounded-full": {
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.md.dot.position.rounded.full} top-${config2.size.md.dot.position.rounded.full}`]:
              {},
          },
        },
      },
      //Size:lg
      "&.nui-avatar-lg": {
        [`@apply h-${config2.size.lg.avatar} w-${config2.size.lg.avatar}`]: {},
        //lg:badge
        ".nui-avatar-badge": {
          [`@apply h-${config2.size.lg.badge.size} w-${config2.size.lg.badge.size} -bottom-${config2.size.lg.badge.position} -end-${config2.size.lg.badge.position}`]:
            {},
        },
        //lg:dot
        ".nui-avatar-dot": {
          [`@apply h-${config2.size.lg.dot.size} w-${config2.size.lg.dot.size}`]:
            {},
        },
        //lg:text
        ".nui-avatar-text": {
          [`@apply text-${config2.size.lg.text.font.size}`]: {},
        },
        //lg:rounded-none
        "&.nui-avatar-rounded-none": {
          [`@apply ${config2.size.lg.rounded.none}`]: {},
          //lg:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.lg.rounded.none}`]: {},
          },
          //lg:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.lg.dot.position.rounded.none} top-${config2.size.lg.dot.position.rounded.none}`]:
              {},
          },
        },
        //lg:rounded-sm
        "&.nui-avatar-rounded-sm": {
          [`@apply ${config2.size.lg.rounded.sm}`]: {},
          //lg:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.lg.rounded.sm}`]: {},
          },
          //lg:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.lg.dot.position.rounded.sm} top-${config2.size.lg.dot.position.rounded.sm}`]:
              {},
          },
        },
        //lg:rounded-md
        "&.nui-avatar-rounded-md": {
          [`@apply ${config2.size.lg.rounded.md}`]: {},
          //lg:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.lg.rounded.md}`]: {},
          },
          //lg:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.lg.dot.position.rounded.md} top-${config2.size.lg.dot.position.rounded.md}`]:
              {},
          },
        },
        //lg:rounded-lg
        "&.nui-avatar-rounded-lg": {
          [`@apply ${config2.size.lg.rounded.lg}`]: {},
          //lg:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.lg.rounded.lg}`]: {},
          },
          //lg:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.lg.dot.position.rounded.lg} top-${config2.size.lg.dot.position.rounded.lg}`]:
              {},
          },
        },
        //lg:rounded-full
        "&.nui-avatar-rounded-full": {
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.lg.dot.position.rounded.full} top-${config2.size.lg.dot.position.rounded.full}`]:
              {},
          },
        },
      },
      //Size:xl
      "&.nui-avatar-xl": {
        [`@apply h-${config2.size.xl.avatar} w-${config2.size.xl.avatar}`]: {},
        //xl:badge
        ".nui-avatar-badge": {
          [`@apply h-${config2.size.xl.badge.size} w-${config2.size.xl.badge.size} -bottom-${config2.size.xl.badge.position} -end-${config2.size.xl.badge.position}`]:
            {},
        },
        //xl:dot
        ".nui-avatar-dot": {
          [`@apply h-${config2.size.xl.dot.size} w-${config2.size.xl.dot.size}`]:
            {},
        },
        //xl:text
        ".nui-avatar-text": {
          [`@apply text-${config2.size.xl.text.font.size}`]: {},
        },
        //xl:rounded-none
        "&.nui-avatar-rounded-none": {
          [`@apply ${config2.size.xl.rounded.none}`]: {},
          //xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xl.rounded.none}`]: {},
          },
          //xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xl.dot.position.rounded.none} top-${config2.size.xl.dot.position.rounded.none}`]:
              {},
          },
        },
        //xl:rounded-sm
        "&.nui-avatar-rounded-sm": {
          [`@apply ${config2.size.xl.rounded.sm}`]: {},
          //xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xl.rounded.sm}`]: {},
          },
          //xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xl.dot.position.rounded.sm} top-${config2.size.xl.dot.position.rounded.sm}`]:
              {},
          },
        },
        //xl:rounded-md
        "&.nui-avatar-rounded-md": {
          [`@apply ${config2.size.xl.rounded.md}`]: {},
          //xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xl.rounded.md}`]: {},
          },
          //xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xl.dot.position.rounded.md} top-${config2.size.xl.dot.position.rounded.md}`]:
              {},
          },
        },
        //xl:rounded-lg
        "&.nui-avatar-rounded-lg": {
          [`@apply ${config2.size.xl.rounded.lg}`]: {},
          //xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xl.rounded.lg}`]: {},
          },
          //xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xl.dot.position.rounded.lg} top-${config2.size.xl.dot.position.rounded.lg}`]:
              {},
          },
        },
        //xl:rounded-full
        "&.nui-avatar-rounded-full": {
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xl.dot.position.rounded.full} top-${config2.size.xl.dot.position.rounded.full}`]:
              {},
          },
        },
      },
      //Size:2xl
      "&.nui-avatar-2xl": {
        [`@apply h-${config2.size.xxl.avatar} w-${config2.size.xxl.avatar}`]:
          {},
        //2xl:badge
        ".nui-avatar-badge": {
          [`@apply h-${config2.size.xxl.badge.size} w-${config2.size.xxl.badge.size} -bottom-${config2.size.xxl.badge.position} -end-${config2.size.xxl.badge.position}`]:
            {},
        },
        //2xl:dot
        ".nui-avatar-dot": {
          [`@apply h-${config2.size.xxl.dot.size} w-${config2.size.xxl.dot.size}`]:
            {},
        },
        //2xl:text
        ".nui-avatar-text": {
          [`@apply text-${config2.size.xxl.text.font.size}`]: {},
        },
        //2xl:rounded-none
        "&.nui-avatar-rounded-none": {
          [`@apply ${config2.size.xxl.rounded.none}`]: {},
          //2xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxl.rounded.none}`]: {},
          },
          //2xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxl.dot.position.rounded.none} top-${config2.size.xxl.dot.position.rounded.none}`]:
              {},
          },
        },
        //2xl:rounded-sm
        "&.nui-avatar-rounded-sm": {
          [`@apply ${config2.size.xxl.rounded.sm}`]: {},
          //2xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxl.rounded.sm}`]: {},
          },
          //2xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxl.dot.position.rounded.sm} top-${config2.size.xxl.dot.position.rounded.sm}`]:
              {},
          },
        },
        //2xl:rounded-md
        "&.nui-avatar-rounded-md": {
          [`@apply ${config2.size.xxl.rounded.md}`]: {},
          //2xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxl.rounded.md}`]: {},
          },
          //2xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxl.dot.position.rounded.md} top-${config2.size.xxl.dot.position.rounded.md}`]:
              {},
          },
        },
        //2xl:rounded-lg
        "&.nui-avatar-rounded-lg": {
          [`@apply ${config2.size.xxl.rounded.lg}`]: {},
          //2xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxl.rounded.lg}`]: {},
          },
          //2xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxl.dot.position.rounded.lg} top-${config2.size.xxl.dot.position.rounded.lg}`]:
              {},
          },
        },
        //2xl:rounded-full
        "&.nui-avatar-rounded-full": {
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxl.dot.position.rounded.full} top-${config2.size.xxl.dot.position.rounded.full}`]:
              {},
          },
        },
      },
      //Size:3xl
      "&.nui-avatar-3xl": {
        [`@apply h-${config2.size.xxxl.avatar} w-${config2.size.xxxl.avatar}`]:
          {},
        //3xl:badge
        ".nui-avatar-badge": {
          [`@apply h-${config2.size.xxxl.badge.size} w-${config2.size.xxxl.badge.size} -bottom-${config2.size.xxxl.badge.position} -end-${config2.size.xxxl.badge.position}`]:
            {},
        },
        //3xl:dot
        ".nui-avatar-dot": {
          [`@apply h-${config2.size.xxxl.dot.size} w-${config2.size.xxxl.dot.size}`]:
            {},
        },
        //3xl:text
        ".nui-avatar-text": {
          [`@apply text-${config2.size.xxxl.text.font.size}`]: {},
        },
        //3xl:rounded-none
        "&.nui-avatar-rounded-none": {
          [`@apply ${config2.size.xxxl.rounded.none}`]: {},
          //3xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxxl.rounded.none}`]: {},
          },
          //3xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxxl.dot.position.rounded.none} top-${config2.size.xxxl.dot.position.rounded.none}`]:
              {},
          },
        },
        //3xl:rounded-sm
        "&.nui-avatar-rounded-sm": {
          [`@apply ${config2.size.xxxl.rounded.sm}`]: {},
          //3xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxxl.rounded.sm}`]: {},
          },
          //3xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxxl.dot.position.rounded.sm} top-${config2.size.xxxl.dot.position.rounded.sm}`]:
              {},
          },
        },
        //3xl:rounded-md
        "&.nui-avatar-rounded-md": {
          [`@apply ${config2.size.xxxl.rounded.md}`]: {},
          //3xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxxl.rounded.md}`]: {},
          },
          //3xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxxl.dot.position.rounded.md} top-${config2.size.xxxl.dot.position.rounded.md}`]:
              {},
          },
        },
        //3xl:rounded-lg
        "&.nui-avatar-rounded-lg": {
          [`@apply ${config2.size.xxxl.rounded.lg}`]: {},
          //3xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxxl.rounded.lg}`]: {},
          },
          //3xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxxl.dot.position.rounded.lg} top-${config2.size.xxxl.dot.position.rounded.lg}`]:
              {},
          },
        },
        //3xl:rounded-full
        "&.nui-avatar-rounded-full": {
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxxl.dot.position.rounded.full} top-${config2.size.xxxl.dot.position.rounded.full}`]:
              {},
          },
        },
      },
      //Size:4xl
      "&.nui-avatar-4xl": {
        [`@apply h-${config2.size.xxxxl.avatar} w-${config2.size.xxxxl.avatar}`]:
          {},
        //4xl:badge
        ".nui-avatar-badge": {
          [`@apply h-${config2.size.xxxxl.badge.size} w-${config2.size.xxxxl.badge.size} -bottom-${config2.size.xxxxl.badge.position} -end-${config2.size.xxxxl.badge.position}`]:
            {},
        },
        //4xl:dot
        ".nui-avatar-dot": {
          [`@apply h-${config2.size.xxxxl.dot.size} w-${config2.size.xxxxl.dot.size}`]:
            {},
        },
        //4xl:text
        ".nui-avatar-text": {
          [`@apply text-${config2.size.xxxxl.text.font.size}`]: {},
        },
        //4xl:rounded-none
        "&.nui-avatar-rounded-none": {
          [`@apply ${config2.size.xxxxl.rounded.none}`]: {},
          //4xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxxxl.rounded.none}`]: {},
          },
          //4xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxxxl.dot.position.rounded.none} top-${config2.size.xxxxl.dot.position.rounded.none}`]:
              {},
          },
        },
        //4xl:rounded-sm
        "&.nui-avatar-rounded-sm": {
          [`@apply ${config2.size.xxxxl.rounded.sm}`]: {},
          //4xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxxxl.rounded.sm}`]: {},
          },
          //4xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxxxl.dot.position.rounded.sm} top-${config2.size.xxxxl.dot.position.rounded.sm}`]:
              {},
          },
        },
        //4xl:rounded-md
        "&.nui-avatar-rounded-md": {
          [`@apply ${config2.size.xxxxl.rounded.md}`]: {},
          //4xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxxxl.rounded.md}`]: {},
          },
          //4xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxxxl.dot.position.rounded.md} top-${config2.size.xxxxl.dot.position.rounded.md}`]:
              {},
          },
        },
        //4xl:rounded-lg
        "&.nui-avatar-rounded-lg": {
          [`@apply ${config2.size.xxxxl.rounded.lg}`]: {},
          //4xl:inner
          ".nui-avatar-inner": {
            [`@apply ${config2.size.xxxxl.rounded.lg}`]: {},
          },
          //4xl:dot
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxxxl.dot.position.rounded.lg} top-${config2.size.xxxxl.dot.position.rounded.lg}`]:
              {},
          },
        },
        //4xl:rounded-full
        "&.nui-avatar-rounded-full": {
          ".nui-avatar-dot": {
            [`@apply end-${config2.size.xxxxl.dot.position.rounded.full} top-${config2.size.xxxxl.dot.position.rounded.full}`]:
              {},
          },
        },
      },
      "&.nui-avatar-rounded-full": {
        "@apply rounded-full": {},
        ".nui-avatar-inner": {
          "@apply rounded-full": {},
        },
        ".nui-avatar-badge": {
          "@apply -bottom-0 -end-0": {},
        },
      },
      "&.nui-avatar-mask:not(.nui-avatar-rounded):not(.nui-avatar-rounded-md):not(.nui-avatar-rounded-lg):not(.nui-avatar-rounded-full)":
        {
          "@apply nui-mask": {},
        },
    },
  });
}, config$R);

module.export = avatarPlugin;

import plugin from "tailwindcss/plugin";

const key$l = "pagination";
const defaultConfig$k = {
  width: "full",
  padding: "1",
  rounded: {
    sm: "rounded-md",
    md: "rounded-lg",
    lg: "rounded-xl",
    full: "rounded-full",
  },
  list: {
    border: {
      light: "muted-200",
      dark: "muted-600",
    },
    background: {
      light: "muted-100",
      dark: "muted-700",
    },
  },
  buttons: {
    padding: "1",
    border: {
      light: "muted-200",
      dark: "muted-600",
    },
    background: {
      light: "muted-100",
      dark: "muted-700",
    },
    button: {
      background: {
        base: {
          light: "white",
          dark: "muted-800",
        },
        hover: {
          light: "muted-100",
          dark: "muted-900",
        },
      },
      font: {
        family: "sans",
        size: "sm",
        color: {
          base: {
            light: "muted-500",
            dark: "muted-400",
          },
          hover: {
            light: "muted-700",
            dark: "muted-400",
          },
        },
      },
      border: {
        light: "muted-200",
        dark: "muted-700",
      },
      icon: {
        size: "4",
      },
      transition: {
        property: "all",
        duration: "300",
      },
    },
  },
  link: {
    padding: "4",
    size: "10",
    font: {
      family: "sans",
      size: "sm",
      color: {
        active: {
          primary: {
            light: "primary-invert",
            dark: "primary-invert",
          },
          dark: {
            light: "muted-100",
            dark: "muted-900",
          },
          black: {
            light: "white",
            dark: "black",
          },
        },
        inactive: {
          base: {
            light: "muted-500",
            dark: "muted-400",
          },
          hover: {
            light: "muted-700",
            dark: "muted-400",
          },
        },
      },
    },
    border: {
      base: {
        light: "muted-200",
        dark: "muted-600",
      },
      inactive: {
        light: "muted-200",
        dark: "muted-700",
      },
      active: {
        primary: {
          light: "primary-500",
          dark: "primary-500",
        },
        dark: {
          light: "muted-900",
          dark: "muted-100",
        },
        black: {
          light: "black",
          dark: "white",
        },
      },
    },
    background: {
      base: {
        light: "muted-100",
        dark: "muted-700",
      },
      inactive: {
        base: {
          light: "white",
          dark: "muted-800",
        },
        hover: {
          light: "muted-100",
          dark: "muted-900",
        },
      },
      active: {
        primary: {
          light: "primary-500",
          dark: "primary-500",
        },
        dark: {
          light: "muted-900",
          dark: "muted-100",
        },
        black: {
          light: "black",
          dark: "white",
        },
      },
    },
    shadow: {
      active: {
        size: "sm",
        color: {
          primary: {
            light: "primary-500/50",
            dark: "primary-500/20",
          },
          dark: {
            light: "muted-300/30",
            dark: "muted-800/20",
          },
          black: {
            light: "muted-300/30",
            dark: "muted-800/20",
          },
        },
      },
    },
  },
  ellipsis: {
    size: "10",
    border: {
      light: "muted-200",
      dark: "muted-700",
    },
    font: {
      family: "sans",
      size: "sm",
      color: {
        light: "muted-500",
        dark: "muted-400",
      },
    },
    background: {
      light: "white",
      dark: "muted-800",
    },
  },
};

const config$l = {
  theme: {
    nui: {
      [key$l]: defaultConfig$k,
    },
  },
};
const pagination = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$l}`);
  addComponents({
    //Wrapper
    ".nui-pagination": {
      [`@apply inline-flex w-${config2.width} flex-col md:flex-row md:justify-between`]:
        {},
      //Pagination:list
      ".nui-pagination-list": {
        //Base
        [`@apply inline-flex flex-wrap gap-2 md:gap-1 p-${config2.padding} mb-4 md:mb-0`]:
          {},
        //Background
        [`@apply bg-${config2.list.background.light} dark:bg-${config2.list.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.list.border.light} dark:border-${config2.list.border.dark}`]:
          {},
      },
      //Pagination:buttons
      ".nui-pagination-buttons": {
        //Base
        [`@apply flex items-center justify-end gap-1 border p-${config2.buttons.padding}`]:
          {},
        //Background
        [`@apply bg-${config2.buttons.background.light} dark:bg-${config2.buttons.background.dark}`]:
          {},
        //Border
        [`@apply border-${config2.buttons.border.light} dark:border-${config2.buttons.border.dark}`]:
          {},
        //Buttons:button
        ".nui-pagination-button": {
          //Base
          "@apply nui-focus flex h-10 w-full items-center justify-center md:w-10":
            {},
          //Font
          [`@apply font-${config2.buttons.button.font.family} text-${config2.buttons.button.font.size}`]:
            {},
          //Color
          [`@apply text-${config2.buttons.button.font.color.base.light} dark:text-${config2.buttons.button.font.color.base.dark}`]:
            {},
          //Color:hover
          [`@apply hover:text-${config2.buttons.button.font.color.hover.light} dark:hover:text-${config2.buttons.button.font.color.hover.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.buttons.button.background.base.light} dark:bg-${config2.buttons.button.background.base.dark}`]:
            {},
          //Background:hover
          [`@apply hover:bg-${config2.buttons.button.background.hover.light} dark:hover:bg-${config2.buttons.button.background.hover.dark}`]:
            {},
          //Border
          [`@apply border-${config2.buttons.button.border.light} dark:border-${config2.buttons.button.border.dark}`]:
            {},
          //Transition
          [`@apply transition-${config2.buttons.button.transition.property} duration-${config2.buttons.button.transition.duration}`]:
            {},
          //Button:icon
          ".nui-pagination-button-icon": {
            [`@apply block h-${config2.buttons.button.icon.size} w-${config2.buttons.button.icon.size}`]:
              {},
          },
        },
      },
      //Pagination:link
      ".nui-pagination-link": {
        //Base
        "@apply nui-focus flex items-center justify-center mb-0 inline-flex flex-wrap gap-2 md:gap-1":
          {},
        //Size
        [`@apply h-${config2.link.size} px-${config2.link.padding}`]: {},
        //Font
        [`@apply font-${config2.link.font.family} text-${config2.link.font.size}`]:
          {},
        //Background
        [`@apply bg-${config2.link.background.base.light} dark:bg-${config2.link.background.base.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.link.border.base.light} dark:border-${config2.link.border.base.dark}`]:
          {},
        //Link:not-active
        "&:not(.nui-active)": {
          //Color:inactive:base
          [`@apply text-${config2.link.font.color.inactive.base.light} dark:text-${config2.link.font.color.inactive.base.dark}`]:
            {},
          //Color:inactive:hover
          [`@apply hover:text-${config2.link.font.color.inactive.hover.light} dark:hover:text-${config2.link.font.color.inactive.hover.dark}`]:
            {},
          //Background:inactive:base
          [`@apply bg-${config2.link.background.inactive.base.light} dark:bg-${config2.link.background.inactive.base.dark}`]:
            {},
          //Background:inactive:hover
          [`@apply hover:bg-${config2.link.background.inactive.hover.light} dark:hover:bg-${config2.link.background.inactive.hover.dark}`]:
            {},
          //Border:inactive
          [`@apply border-${config2.link.border.inactive.light} dark:border-${config2.link.border.inactive.dark}`]:
            {},
        },
      },
      "&.nui-pagination-primary": {
        //Link:primary
        ".nui-pagination-link.nui-active": {
          //Color
          [`@apply text-${config2.link.font.color.active.primary.light} dark:text-${config2.link.font.color.active.primary.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.link.background.active.primary.light} dark:bg-${config2.link.background.active.primary.dark}`]:
            {},
          //Border:active
          [`@apply border-${config2.link.border.active.primary.light} dark:border-${config2.link.border.active.primary.dark}`]:
            {},
          //Shadow:active
          [`@apply shadow-${config2.link.shadow.active.size} shadow-${config2.link.shadow.active.color.primary.light} dark:shadow-${config2.link.shadow.active.color.primary.dark}`]:
            {},
        },
      },
      "&.nui-pagination-dark": {
        //Link:dark
        ".nui-pagination-link.nui-active": {
          //Color
          [`@apply text-${config2.link.font.color.active.dark.light} dark:text-${config2.link.font.color.active.dark.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.link.background.active.dark.light} dark:bg-${config2.link.background.active.dark.dark}`]:
            {},
          //Border:active
          [`@apply border-${config2.link.border.active.dark.light} dark:border-${config2.link.border.active.dark.dark}`]:
            {},
          //Shadow:active
          [`@apply shadow-${config2.link.shadow.active.size} shadow-${config2.link.shadow.active.color.dark.light} dark:shadow-${config2.link.shadow.active.color.dark.dark}`]:
            {},
        },
      },
      "&.nui-pagination-black": {
        //Link:black
        ".nui-pagination-link.nui-active": {
          //Color
          [`@apply text-${config2.link.font.color.active.black.light} dark:text-${config2.link.font.color.active.black.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.link.background.active.black.light} dark:bg-${config2.link.background.active.black.dark}`]:
            {},
          //Border:active
          [`@apply border-${config2.link.border.active.black.light} dark:border-${config2.link.border.active.black.dark}`]:
            {},
          //Shadow:active
          [`@apply shadow-${config2.link.shadow.active.size} shadow-${config2.link.shadow.active.color.black.light} dark:shadow-${config2.link.shadow.active.color.black.dark}`]:
            {},
        },
      },
      //Pagination:ellipsis
      ".nui-pagination-ellipsis": {
        //Base
        [`@apply select-none flex h-${config2.ellipsis.size} w-${config2.ellipsis.size} items-center justify-center`]:
          {},
        //Font
        [`@apply font-${config2.ellipsis.font.family} text-${config2.ellipsis.font.size}`]:
          {},
        //Color
        [`@apply text-${config2.ellipsis.font.color.light} dark:text-${config2.ellipsis.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.ellipsis.background.light} dark:bg-${config2.ellipsis.background.dark}`]:
          {},
        //Border
        [`@apply border-${config2.ellipsis.border.light} dark:border-${config2.ellipsis.border.dark}`]:
          {},
      },
      //Rounded:sm
      "&.nui-pagination-rounded-sm .nui-pagination-list, &.nui-pagination-rounded-sm .nui-pagination-buttons, &.nui-pagination-rounded-sm .nui-pagination-buttons .nui-pagination-button,  &.nui-pagination-rounded-sm .nui-pagination-link, &.nui-pagination-rounded-sm .nui-pagination-ellipsis":
        {
          [`@apply ${config2.rounded.sm}`]: {},
        },
      //Rounded:md
      "&.nui-pagination-rounded-md .nui-pagination-list, &.nui-pagination-rounded-md .nui-pagination-buttons, &.nui-pagination-rounded-md .nui-pagination-buttons .nui-pagination-button,  &.nui-pagination-rounded-md .nui-pagination-link, &.nui-pagination-rounded-md .nui-pagination-ellipsis":
        {
          [`@apply ${config2.rounded.md}`]: {},
        },
      //Rounded:lg
      "&.nui-pagination-rounded-lg .nui-pagination-list, &.nui-pagination-rounded-lg .nui-pagination-buttons, &.nui-pagination-rounded-lg .nui-pagination-buttons .nui-pagination-button,  &.nui-pagination-rounded-lg .nui-pagination-link, &.nui-pagination-rounded-lg .nui-pagination-ellipsis":
        {
          [`@apply ${config2.rounded.lg}`]: {},
        },
      //Rounded:full
      "&.nui-pagination-rounded-full .nui-pagination-list, &.nui-pagination-rounded-full .nui-pagination-buttons, &.nui-pagination-rounded-full .nui-pagination-buttons .nui-pagination-button,  &.nui-pagination-rounded-full .nui-pagination-link, &.nui-pagination-rounded-full .nui-pagination-ellipsis":
        {
          [`@apply ${config2.rounded.full}`]: {},
        },
    },
  });
}, config$l);

module.export = pagination;

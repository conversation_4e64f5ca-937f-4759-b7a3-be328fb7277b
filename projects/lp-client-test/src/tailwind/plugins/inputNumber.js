import plugin from "tailwindcss/plugin";
const key$x = "inputNumber";
const defaultConfig$w = {
  rounded: {
    none: "rounded-none",
    sm: "rounded-md",
    md: "rounded-lg",
    lg: "rounded-xl",
    full: "rounded-full",
  },
  buttons: {
    rounded: {
      none: "none",
      sm: "md",
      md: "lg",
      lg: "xl",
      full: "full",
    },
  },
  label: {
    float: {
      height: "5",
      font: {
        family: "sans",
        color: "primary-500",
        lead: "none",
      },
      transition: {
        property: "all",
        duration: "300",
      },
    },
  },
  input: {
    width: "full",
    font: {
      family: "sans",
    },
    focus: {
      label: {
        float: {
          font: {
            color: {
              light: "primary-500",
              dark: "primary-500",
            },
          },
        },
      },
      border: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    icon: {
      color: {
        base: {
          light: "muted-400",
          dark: "muted-400",
        },
        focus: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
      transition: {
        property: "colors",
        duration: "300",
      },
    },
    action: {
      color: {
        light: "muted-400",
        dark: "muted-400",
      },
      padding: {
        sm: "8",
        md: "10",
        lg: "12",
      },
      transition: {
        property: "colors",
        duration: "300",
      },
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  error: {
    input: {
      border: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    icon: {
      color: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    font: {
      family: "sans",
      size: "[0.65rem]",
      weight: "medium",
      color: {
        light: "danger-600",
        dark: "danger-600",
      },
    },
  },
  loaded: {
    font: {
      color: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
  },
  size: {
    sm: {
      label: {
        font: {
          size: "xs",
        },
      },
      icon: {
        outer: {
          size: "8",
        },
        inner: {
          size: "4",
        },
      },
      placeload: {
        size: "8",
      },
    },
    md: {
      label: {
        font: {
          size: "[0.825rem]",
        },
      },
      icon: {
        outer: {
          size: "10",
        },
        inner: {
          size: "[1.15rem]",
        },
      },
      placeload: {
        size: "10",
      },
    },
    lg: {
      label: {
        font: {
          size: "sm",
        },
      },
      icon: {
        outer: {
          size: "12",
        },
        inner: {
          size: "5",
        },
      },
      placeload: {
        size: "12",
      },
    },
    xl: {
      label: {
        font: {
          size: "sm",
        },
      },
      icon: {
        outer: {
          size: "14",
        },
        inner: {
          size: "5",
        },
      },
      placeload: {
        size: "14",
      },
    },
  },
  color: {
    default: {
      background: {
        light: "white",
        dark: "muted-900",
      },
      border: {
        base: {
          light: "muted-300",
          dark: "muted-700",
        },
        hover: {
          light: "muted-300",
          dark: "muted-700",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-500",
      },
    },
    defaultContrast: {
      background: {
        light: "white",
        dark: "muted-950",
      },
      border: {
        base: {
          light: "muted-300",
          dark: "muted-800",
        },
        hover: {
          light: "muted-300",
          dark: "muted-800",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
    muted: {
      background: {
        light: "muted-100",
        dark: "muted-900",
      },
      border: {
        base: {
          light: "muted-100",
          dark: "muted-700",
        },
        hover: {
          light: "muted-100",
          dark: "muted-700",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-500",
      },
    },
    mutedContrast: {
      background: {
        light: "muted-100",
        dark: "muted-950",
      },
      border: {
        base: {
          light: "muted-100",
          dark: "muted-800",
        },
        hover: {
          light: "muted-100",
          dark: "muted-800",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
  },
  icon: {
    disabled: {
      input: {
        sm: {
          font: {
            size: "xs",
          },
        },
        md: {
          font: {
            size: "sm",
          },
        },
        lg: {
          font: {
            size: "sm",
          },
        },
        xl: {
          font: {
            size: "base",
          },
        },
      },
      label: {
        float: {
          sm: {
            font: {
              size: "xs",
            },
          },
          md: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          lg: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          xl: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
        },
      },
    },
    enabled: {
      input: {
        sm: {
          font: {
            size: "xs",
          },
        },
        md: {
          font: {
            size: "sm",
          },
        },
        lg: {
          font: {
            size: "sm",
          },
        },
        xl: {
          font: {
            size: "base",
          },
        },
      },
      label: {
        float: {
          sm: {
            font: {
              size: "xs",
            },
          },
          md: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          lg: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          xl: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
        },
      },
    },
  },
};

const config$x = {
  theme: {
    nui: {
      [key$x]: defaultConfig$w,
    },
  },
};
const inputNumber = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$x}`);
  addComponents({
    //Wrapper
    ".nui-input-number-wrapper": {
      "@apply relative": {},
      //Input:label
      ".nui-input-number-label, .nui-label-float": {
        "@apply nui-label": {},
      },
      //Label:float
      ".nui-label-float": {
        [`@apply h-${config2.label.float.height} absolute inline-flex items-center select-none pointer-events-none`]:
          {},
        //Font
        [`@apply font-${config2.label.float.font.family} text-${config2.label.float.font.color} leading-${config2.label.float.font.lead}`]:
          {},
        //Transition
        [`@apply transition-${config2.label.float.transition.property} duration-${config2.label.float.transition.duration}`]:
          {},
      },
      //Input:outer
      ".nui-input-number-outer": {
        "@apply nui-focus relative": {},
      },
      //Input:icon
      ".nui-input-number-icon": {
        "@apply absolute start-0 top-0 z-10 flex items-center justify-center":
          {},
        //Color
        [`@apply text-${config2.input.icon.color.base.light} dark:text-${config2.input.icon.color.base.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.input.icon.transition.property} duration-${config2.input.icon.transition.duration}`]:
          {},
      },
      //Input:buttons
      ".nui-input-number-buttons": {
        "@apply absolute top-1 end-1 flex z-10": {},
      },
      //Buttons:button
      ".nui-input-number-buttons > button": {
        "@apply outline-none flex items-center justify-center": {},
        "@apply last:border-s-0": {},
        "@apply text-muted-400 enabled:hover:text-muted-600 dark:enabled:hover:text-muted-100":
          {},
        "@apply enabled:focus:text-muted-600 dark:enabled:focus:text-muted-100":
          {},
        "@apply transition-colors duration-300": {},
        "@apply disabled:cursor-not-allowed": {},
      },
      //Button:icon
      ".nui-input-number-buttons svg": {
        "@apply h-4 w-4": {},
      },
      //Input
      ".nui-input-number": {
        //Base
        [`@apply nui-focus w-${config2.input.width} font-${config2.input.font.family} disabled:cursor-not-allowed disabled:opacity-75`]:
          {},
        //Transition
        [`@apply transition-${config2.input.transition.property} duration-${config2.input.transition.duration}`]:
          {},
        //Focus:label:float
        "&:focus-visible ~ .nui-label-float": {
          [`@apply !text-${config2.input.focus.label.float.font.color.light} dark:!text-${config2.input.focus.label.float.font.color.dark}`]:
            {},
        },
        //Focus:icon
        "&:focus-visible ~ .nui-input-number-icon": {
          [`@apply !text-${config2.input.icon.color.focus.light} dark:!text-${config2.input.icon.color.focus.dark}`]:
            {},
        },
        //Disabled:icon
        "&:disabled ~ .nui-input-number-icon": {
          "@apply cursor-not-allowed opacity-75": {},
        },
      },
      //Input:placeload
      ".nui-input-number-placeload": {
        "@apply absolute start-0 top-0 flex w-full items-center px-4": {},
        //Placeload:inner
        ".nui-placeload": {
          "@apply h-3 w-full max-w-[50%] rounded": {},
        },
      },
      //Input:action
      ".nui-input-number-action": {
        //Base
        "@apply absolute end-0 top-0 flex items-center justify-center": {},
        //Color
        [`@apply text-${config2.input.action.color.light} dark:text-${config2.input.action.color.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.input.action.transition.property} duration-${config2.input.action.transition.duration}`]:
          {},
      },
      //Rounded:sm
      "&.nui-input-number-rounded-sm": {
        ".nui-input-number, .nui-input-number-outer": {
          [`@apply ${config2.rounded.sm}`]: {},
        },
        ".nui-input-number-buttons button": {
          [`@apply first:rounded-s-${config2.buttons.rounded.sm}`]: {},
          [`@apply last:rounded-e-${config2.buttons.rounded.sm}`]: {},
        },
      },
      //Rounded:md
      "&.nui-input-number-rounded-md": {
        ".nui-input-number, .nui-input-number-outer": {
          [`@apply ${config2.rounded.md}`]: {},
        },
        ".nui-input-number-buttons button": {
          [`@apply first:rounded-s-${config2.buttons.rounded.md}`]: {},
          [`@apply last:rounded-e-${config2.buttons.rounded.md}`]: {},
        },
      },
      //Rounded:lg
      "&.nui-input-number-rounded-lg": {
        ".nui-input-number, .nui-input-number-outer": {
          [`@apply ${config2.rounded.lg}`]: {},
        },
        ".nui-input-number-buttons button": {
          [`@apply first:rounded-s-${config2.buttons.rounded.lg}`]: {},
          [`@apply last:rounded-e-${config2.buttons.rounded.lg}`]: {},
        },
      },
      //Rounded:full
      "&.nui-input-number-rounded-full": {
        ".nui-input-number, .nui-input-number-outer": {
          [`@apply ${config2.rounded.full}`]: {},
        },
        ".nui-input-number-buttons button": {
          [`@apply first:rounded-s-${config2.buttons.rounded.full}`]: {},
          [`@apply last:rounded-e-${config2.buttons.rounded.full}`]: {},
        },
      },
      //Size:sm
      "&.nui-input-number-sm": {
        //Input:label
        ".nui-input-number-label": {
          [`@apply pb-1 text-${config2.size.sm.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-1.5": {},
        },
        //Input:icon && Input:action
        ".nui-input-number-icon, .nui-input-number-action": {
          [`@apply h-${config2.size.sm.icon.outer.size} w-${config2.size.sm.icon.outer.size}`]:
            {},
          ".nui-input-number-icon-inner, .nui-input-number-action-inner": {
            [`@apply h-${config2.size.sm.icon.inner.size} w-${config2.size.sm.icon.inner.size}`]:
              {},
          },
        },
        //Input:placeload
        ".nui-input-number-placeload": {
          [`@apply h-${config2.size.sm.placeload.size}`]: {},
        },
        //Input:buttons
        ".nui-input-number-buttons button": {
          "@apply h-6 w-6": {},
        },
      },
      //Size:md
      "&.nui-input-number-md": {
        //Input:label
        ".nui-input-number-label": {
          [`@apply pb-1 text-${config2.size.md.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-2.5": {},
        },
        //Input:icon && Input:action
        ".nui-input-number-icon, .nui-input-number-action": {
          [`@apply h-${config2.size.md.icon.outer.size} w-${config2.size.md.icon.outer.size}`]:
            {},
          ".nui-input-number-icon-inner, .nui-input-number-action-inner": {
            [`@apply h-${config2.size.md.icon.inner.size} w-${config2.size.md.icon.inner.size}`]:
              {},
          },
        },
        //Input:placeload
        ".nui-input-number-placeload": {
          [`@apply h-${config2.size.md.placeload.size}`]: {},
        },
        //Input:buttons
        ".nui-input-number-buttons button": {
          "@apply  h-8 w-8": {},
        },
      },
      //Size:lg
      "&.nui-input-number-lg": {
        //Input:label
        ".nui-input-number-label": {
          [`@apply pb-1 text-${config2.size.lg.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-3.5": {},
        },
        //Input:icon && Input:action
        ".nui-input-number-icon, .nui-input-number-action": {
          [`@apply h-${config2.size.lg.icon.outer.size} w-${config2.size.lg.icon.outer.size}`]:
            {},
          ".nui-input-number-icon-inner, .nui-input-number-action-inner": {
            [`@apply h-${config2.size.lg.icon.inner.size} w-${config2.size.lg.icon.inner.size}`]:
              {},
          },
        },
        //Input:placeload
        ".nui-input-number-placeload": {
          [`@apply h-${config2.size.lg.placeload.size}`]: {},
        },
        //Input:buttons
        ".nui-input-number-buttons button": {
          "@apply h-10 w-10": {},
        },
      },
      //Size:xl
      "&.nui-input-number-xl": {
        //Input:label
        ".nui-input-number-label": {
          [`@apply pb-1 text-${config2.size.xl.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-[1.1rem]": {},
        },
        //Input:icon && Input:action
        ".nui-input-number-icon, .nui-input-number-action": {
          [`@apply h-${config2.size.xl.icon.outer.size} w-${config2.size.xl.icon.outer.size}`]:
            {},
          ".nui-input-number-icon-inner, .nui-input-number-action-inner": {
            [`@apply h-${config2.size.xl.icon.inner.size} w-${config2.size.xl.icon.inner.size}`]:
              {},
          },
        },
        //Input:placeload
        ".nui-input-number-placeload": {
          [`@apply h-${config2.size.xl.placeload.size}`]: {},
        },
        //Input:buttons
        ".nui-input-number-buttons button": {
          "@apply h-12 w-12": {},
        },
      },
      //Color:default
      "&.nui-input-number-default": {
        ".nui-input-number": {
          //Font
          [`@apply text-${config2.color.default.color.light} dark:text-${config2.color.default.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.default.placeholder.light} dark:placeholder:text-${config2.color.default.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.default.background.light} dark:bg-${config2.color.default.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.default.border.base.light} dark:border-${config2.color.default.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.default.border.hover.light} dark:hover:border-${config2.color.default.border.hover.dark}`]:
            {},
        },
        //Input:buttons
        ".nui-input-number-buttons button": {
          "@apply bg-white dark:bg-muted-800 enabled:hover:bg-muted-100 dark:enabled:hover:bg-muted-700":
            {},
          "@apply border border-muted-200 dark:border-muted-700 enabled:focus:bg-muted-100 dark:enabled:focus:bg-muted-800":
            {},
        },
      },
      //Color:defaultContrast
      "&.nui-input-number-default-contrast": {
        ".nui-input-number": {
          //Font
          [`@apply text-${config2.color.defaultContrast.color.light} dark:text-${config2.color.defaultContrast.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.defaultContrast.placeholder.light} dark:placeholder:text-${config2.color.defaultContrast.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.defaultContrast.background.light} dark:bg-${config2.color.defaultContrast.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.defaultContrast.border.base.light} dark:border-${config2.color.defaultContrast.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.defaultContrast.border.hover.light} dark:hover:border-${config2.color.defaultContrast.border.hover.dark}`]:
            {},
        },
        //Input:buttons
        ".nui-input-number-buttons button": {
          "@apply bg-white dark:bg-muted-800 enabled:hover:bg-white dark:enabled:hover:bg-muted-900":
            {},
          "@apply border border-muted-200 dark:border-muted-700 enabled:focus:bg-white dark:enabled:focus:bg-muted-900":
            {},
        },
      },
      //Color:muted
      "&.nui-input-number-muted": {
        ".nui-input-number": {
          //Font
          [`@apply text-${config2.color.muted.color.light} dark:text-${config2.color.muted.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.muted.placeholder.light} dark:placeholder:text-${config2.color.muted.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.muted.background.light} dark:bg-${config2.color.muted.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.muted.border.base.light} dark:border-${config2.color.muted.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.muted.border.hover.light} dark:hover:border-${config2.color.muted.border.hover.dark}`]:
            {},
        },
        //Input:buttons
        ".nui-input-number-buttons button": {
          "@apply bg-white dark:bg-muted-800 enabled:hover:bg-white dark:enabled:hover:bg-muted-800":
            {},
          "@apply border border-muted-200 dark:border-muted-700 enabled:focus:bg-white dark:enabled:focus:bg-muted-800":
            {},
        },
      },
      //Color:mutedContrast
      "&.nui-input-number-muted-contrast": {
        ".nui-input-number": {
          //Font
          [`@apply text-${config2.color.mutedContrast.color.light} dark:text-${config2.color.mutedContrast.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.mutedContrast.placeholder.light} dark:placeholder:text-${config2.color.mutedContrast.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.mutedContrast.background.light} dark:bg-${config2.color.mutedContrast.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.mutedContrast.border.base.light} dark:border-${config2.color.mutedContrast.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.mutedContrast.border.hover.light} dark:hover:border-${config2.color.mutedContrast.border.hover.dark}`]:
            {},
        },
      },
      //Focus:color
      "&.nui-input-number-focus": {
        ".nui-input-number": {
          //Focus
          [`@apply focus:!border-${config2.input.focus.border.color.light} dark:focus:!border-${config2.input.focus.border.color.dark}`]:
            {},
          //Force focus
          [`@apply focus:hover:!border-${config2.input.focus.border.color.light} dark:focus:hover:!border-${config2.input.focus.border.color.dark}`]:
            {},
        },
      },
      //Input:buttons
      ".nui-input-number-buttons button": {
        "@apply bg-white dark:bg-muted-800 enabled:hover:bg-white dark:enabled:hover:bg-muted-900":
          {},
        "@apply border border-muted-200 dark:border-muted-700 enabled:focus:bg-white dark:enabled:focus:bg-muted-900":
          {},
      },
      //Input:not loading
      "&:not(.nui-input-number-loading)": {
        ".nui-input-number:placeholder-shown ~ .nui-label-float": {
          [`@apply text-${config2.loaded.font.color.light} dark:text-${config2.loaded.font.color.dark}`]:
            {},
        },
      },
      //Input:loading
      "&.nui-input-number-loading": {
        ".nui-input-number": {
          "@apply !text-transparent placeholder:!text-transparent dark:placeholder:!text-transparent":
            {},
        },
        ".nui-input-number:placeholder-shown ~ .nui-label-float": {
          "@apply text-transparent": {},
        },
        ".nui-input-number-icon": {
          "@apply opacity-0": {},
        },
      },
      //Input:label:float
      "&.nui-input-number-label-float": {
        ".nui-input-number": {
          "@apply placeholder:text-transparent dark:placeholder:text-transparent":
            {},
        },
      },
      //Input:error
      "&.nui-input-number-error": {
        //Border
        ".nui-input-number": {
          [`@apply !border-${config2.error.input.border.light} dark:!border-${config2.error.input.border.dark}`]:
            {},
        },
        //Icon
        ".nui-input-number-icon": {
          [`@apply !text-${config2.error.icon.color.light} dark:!text-${config2.error.icon.color.dark}`]:
            {},
        },
      },
      //Without icon && Size:sm
      "&:not(.nui-has-icon).nui-input-number-sm": {
        ".nui-input-number": {
          [`@apply h-8 py-1 text-${config2.icon.disabled.input.sm.font.size} leading-4 ps-2 pe-[3.75rem]`]:
            {},
        },
      },
      //With icon && Size:sm
      "&.nui-has-icon.nui-input-number-sm": {
        ".nui-input-number": {
          [`@apply h-8 py-1 text-${config2.icon.enabled.input.sm.font.size} leading-4 ps-8 pe-[3.75rem]`]:
            {},
        },
      },
      //Without icon && Size:md
      "&:not(.nui-has-icon).nui-input-number-md": {
        ".nui-input-number": {
          [`@apply h-10 py-2 text-${config2.icon.disabled.input.md.font.size} leading-5 ps-3 pe-[4.75rem]`]:
            {},
        },
      },
      //With icon && Size:md
      "&.nui-has-icon.nui-input-number-md": {
        ".nui-input-number": {
          [`@apply h-10 py-2 text-${config2.icon.enabled.input.md.font.size} leading-5 ps-10 pe-[4.75rem]`]:
            {},
        },
      },
      //Without icon && Size:lg
      "&:not(.nui-has-icon).nui-input-number-lg": {
        ".nui-input-number": {
          [`@apply h-12 py-2 text-${config2.icon.disabled.input.lg.font.size} leading-5 ps-4 pe-24`]:
            {},
        },
      },
      //With icon && Size:lg
      "&.nui-has-icon.nui-input-number-lg": {
        ".nui-input-number": {
          [`@apply h-12 py-2 text-${config2.icon.enabled.input.lg.font.size} leading-5 ps-11 pe-24`]:
            {},
        },
      },
      //Without icon && Size:xl
      "&:not(.nui-has-icon).nui-input-number-xl": {
        ".nui-input-number": {
          [`@apply h-14 py-2 text-${config2.icon.disabled.input.xl.font.size} leading-5 ps-4 pe-24`]:
            {},
        },
      },
      //With icon && Size:xl
      "&.nui-has-icon.nui-input-number-xl": {
        ".nui-input-number": {
          [`@apply h-14 py-2 text-${config2.icon.enabled.input.xl.font.size} leading-5 ps-[3.25rem] pe-24`]:
            {},
        },
      },
      //With action && Size:sm
      "&.nui-has-action.nui-input-number-sm": {
        ".nui-input-number": {
          [`@apply pe-${config2.input.action.padding.sm}`]: {},
        },
      },
      //With action && Size:md
      "&.nui-has-action.nui-input-number-md": {
        ".nui-input-number": {
          [`@apply pe-${config2.input.action.padding.md}`]: {},
        },
      },
      //With action && Size:lg
      "&.nui-has-action.nui-input-number-lg": {
        ".nui-input-number": {
          [`@apply pe-${config2.input.action.padding.lg}`]: {},
        },
      },
      //Without icon && Size:sm && Label:float
      "&.nui-input-number-label-float:not(.nui-has-icon).nui-input-number-sm": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-7 text-${config2.icon.disabled.label.float.sm.font.size}`]:
            {},
        },
        ".nui-input-number:focus-visible ~ .nui-label-float": {
          "@apply !-ms-3 !-mt-7": {},
        },
        ".nui-input-number:placeholder-shown ~ .nui-label-float": {
          "@apply ms-0 mt-0": {},
        },
      },
      //With icon && Size:sm && Label:float
      "&.nui-input-number-label-float.nui-has-icon.nui-input-number-sm": {
        ".nui-label-float": {
          [`@apply start-8 -ms-8 -mt-7 text-${config2.icon.enabled.label.float.sm.font.size}`]:
            {},
        },
        ".nui-input-number:focus-visible ~ .nui-label-float": {
          "@apply !-ms-8 !-mt-7": {},
        },
        ".nui-input-number:placeholder-shown ~ .nui-label-float": {
          "@apply ms-0 mt-0": {},
        },
      },
      //Without icon && Size:md && Label:float
      "&.nui-input-number-label-float:not(.nui-has-icon).nui-input-number-md": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-8 text-${config2.icon.disabled.label.float.md.font.size.base}`]:
            {},
        },
        ".nui-input-number:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-8 !text-${config2.icon.disabled.label.float.md.font.size.focus}`]:
            {},
        },
        ".nui-input-number:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.md.font.size.unfocus}`]:
            {},
        },
      },
      //With icon && Size:md && Label:float
      "&.nui-input-number-label-float.nui-has-icon.nui-input-number-md": {
        ".nui-label-float": {
          [`@apply start-10 -ms-10 -mt-8 text-${config2.icon.enabled.label.float.md.font.size.base}`]:
            {},
        },
        ".nui-input-number:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-10 !-mt-8 !text-${config2.icon.enabled.label.float.md.font.size.focus}`]:
            {},
        },
        ".nui-input-number:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.md.font.size.unfocus}`]:
            {},
        },
      },
      //Without icon && Size:lg && Label:float
      "&.nui-input-number-label-float:not(.nui-has-icon).nui-input-number-lg": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-9 text-${config2.icon.disabled.label.float.lg.font.size.base}`]:
            {},
        },
        ".nui-input-number:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-9 !text-${config2.icon.disabled.label.float.lg.font.size.focus}`]:
            {},
        },
        ".nui-input-number:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.lg.font.size.unfocus}`]:
            {},
        },
      },
      //With icon && Size:lg && Label:float
      "&.nui-input-number-label-float.nui-has-icon.nui-input-number-lg": {
        ".nui-label-float": {
          [`@apply start-11 -ms-10 -mt-9 text-${config2.icon.enabled.label.float.lg.font.size.base}`]:
            {},
        },
        ".nui-input-number:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-10 !-mt-9 !text-${config2.icon.enabled.label.float.lg.font.size.focus}`]:
            {},
        },
        ".nui-input-number:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.lg.font.size.unfocus}`]:
            {},
        },
      },
      //Without icon && Size:xl && Label:float
      "&.nui-input-number-label-float:not(.nui-has-icon).nui-input-number-xl": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-10 text-${config2.icon.disabled.label.float.xl.font.size.base}`]:
            {},
        },
        ".nui-input-number:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-10 !text-${config2.icon.disabled.label.float.xl.font.size.focus}`]:
            {},
        },
        ".nui-input-number:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.xl.font.size.unfocus}`]:
            {},
        },
      },
      //With icon && Size:xl && Label:float
      "&.nui-input-number-label-float.nui-has-icon.nui-input-number-xl": {
        ".nui-label-float": {
          [`@apply start-[3.25rem] -ms-[3.25rem] -mt-10 text-${config2.icon.enabled.label.float.xl.font.size.base}`]:
            {},
        },
        ".nui-input-number:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-[3.25rem] !-mt-10 !text-${config2.icon.enabled.label.float.xl.font.size.focus}`]:
            {},
        },
        ".nui-input-number:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.xl.font.size.unfocus}`]:
            {},
        },
      },
    },
  });
}, config$x);

module.export = inputNumber;

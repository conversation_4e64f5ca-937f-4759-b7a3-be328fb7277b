import plugin from "tailwindcss/plugin";
const key$B = "iconBox";
const defaultConfig$A = {
  bordered: {
    border: {
      size: "2",
      color: "current",
    },
  },
  size: {
    xs: {
      size: "8",
      rounded: {
        sm: "rounded",
        md: "rounded-md",
        lg: "rounded-lg",
        full: "rounded-full",
      },
    },
    sm: {
      size: "10",
      rounded: {
        sm: "rounded",
        md: "rounded-md",
        lg: "rounded-xl",
        full: "rounded-full",
      },
    },
    md: {
      size: "12",
      rounded: {
        sm: "rounded-md",
        md: "rounded-lg",
        lg: "rounded-xl",
        full: "rounded-full",
      },
    },
    lg: {
      size: "16",
      rounded: {
        sm: "rounded-md",
        md: "rounded-lg",
        lg: "rounded-2xl",
        full: "rounded-full",
      },
    },
    xl: {
      size: "20",
      rounded: {
        sm: "rounded-lg",
        md: "rounded-xl",
        lg: "rounded-2xl",
        full: "rounded-full",
      },
    },
    xxl: {
      size: "24",
      rounded: {
        sm: "rounded-lg",
        md: "rounded-xl",
        lg: "rounded-3xl",
        full: "rounded-full",
      },
    },
  },
  color: {
    variant: {
      solid: {
        default: {
          background: {
            light: "muted-100",
            dark: "muted-700",
          },
          font: {
            color: {
              light: "muted-600",
              dark: "muted-200",
            },
          },
        },
        defaultContrast: {
          background: {
            light: "muted-100",
            dark: "muted-950",
          },
          font: {
            color: {
              light: "muted-600",
              dark: "muted-100",
            },
          },
        },
        muted: {
          background: {
            light: "muted-200",
            dark: "muted-700",
          },
          font: {
            color: {
              light: "muted-700",
              dark: "muted-200",
            },
          },
        },
        mutedContrast: {
          background: {
            light: "muted-200",
            dark: "muted-950",
          },
          font: {
            color: {
              light: "muted-700",
              dark: "muted-100",
            },
          },
        },
        light: {
          background: {
            light: "white",
            dark: "muted-700",
          },
          font: {
            color: {
              light: "muted-800",
              dark: "muted-100",
            },
          },
        },
        dark: {
          background: {
            light: "muted-900",
            dark: "muted-100",
          },
          font: {
            color: {
              light: "muted-100",
              dark: "muted-900",
            },
          },
        },
        black: {
          background: {
            light: "black",
            dark: "white",
          },
          font: {
            color: {
              light: "white",
              dark: "black",
            },
          },
        },
        primary: {
          background: {
            light: "primary-500",
            dark: "primary-500",
          },
          font: {
            color: {
              light: "primary-invert",
              dark: "primary-invert",
            },
          },
        },
        info: {
          background: {
            light: "info-500",
            dark: "info-500",
          },
          font: {
            color: {
              light: "white",
              dark: "white",
            },
          },
        },
        success: {
          background: {
            light: "success-500",
            dark: "success-500",
          },
          font: {
            color: {
              light: "white",
              dark: "white",
            },
          },
        },
        warning: {
          background: {
            light: "warning-500",
            dark: "warning-500",
          },
          font: {
            color: {
              light: "white",
              dark: "white",
            },
          },
        },
        danger: {
          background: {
            light: "danger-500",
            dark: "danger-500",
          },
          font: {
            color: {
              light: "white",
              dark: "white",
            },
          },
        },
      },
      pastel: {
        default: {
          background: {
            light: "muted-500/10",
            dark: "muted-700/10",
          },
          font: {
            color: {
              light: "muted-500",
              dark: "muted-200",
            },
          },
        },
        defaultContrast: {
          background: {
            light: "muted-500/10",
            dark: "muted-950/10",
          },
          font: {
            color: {
              light: "muted-500",
              dark: "muted-100",
            },
          },
        },
        muted: {
          background: {
            light: "muted-600/10",
            dark: "muted-700/10",
          },
          font: {
            color: {
              light: "muted-700",
              dark: "muted-200",
            },
          },
        },
        mutedContrast: {
          background: {
            light: "muted-600/10",
            dark: "muted-950/10",
          },
          font: {
            color: {
              light: "muted-700",
              dark: "muted-100",
            },
          },
        },
        light: {
          background: {
            light: "white/10",
            dark: "white/10",
          },
          font: {
            color: {
              light: "muted-800",
              dark: "muted-300",
            },
          },
        },
        dark: {
          background: {
            light: "muted-900/10",
            dark: "muted-100/10",
          },
          font: {
            color: {
              light: "muted-800",
              dark: "muted-100",
            },
          },
        },
        black: {
          background: {
            light: "black/10",
            dark: "white/10",
          },
          font: {
            color: {
              light: "black",
              dark: "white",
            },
          },
        },
        primary: {
          background: {
            light: "primary-500/10",
            dark: "primary-500/10",
          },
          font: {
            color: {
              light: "primary-500",
              dark: "primary-500",
            },
          },
        },
        info: {
          background: {
            light: "info-500/10",
            dark: "info-500/10",
          },
          font: {
            color: {
              light: "info-500",
              dark: "info-500",
            },
          },
        },
        success: {
          background: {
            light: "success-500/10",
            dark: "success-500/10",
          },
          font: {
            color: {
              light: "success-500",
              dark: "success-500",
            },
          },
        },
        warning: {
          background: {
            light: "warning-500/10",
            dark: "warning-500/10",
          },
          font: {
            color: {
              light: "warning-500",
              dark: "warning-500",
            },
          },
        },
        danger: {
          background: {
            light: "danger-500/10",
            dark: "danger-500/10",
          },
          font: {
            color: {
              light: "danger-500",
              dark: "danger-500",
            },
          },
        },
      },
      outline: {
        default: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "muted-500",
              dark: "muted-200",
            },
          },
        },
        defaultContrast: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "muted-500",
              dark: "muted-100",
            },
          },
        },
        muted: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "muted-700",
              dark: "muted-200",
            },
          },
        },
        mutedContrast: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "muted-700",
              dark: "muted-100",
            },
          },
        },
        light: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "muted-800",
              dark: "muted-400",
            },
          },
        },
        dark: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "muted-800",
              dark: "muted-100",
            },
          },
        },
        black: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "black",
              dark: "white",
            },
          },
        },
        primary: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "primary-500",
              dark: "primary-500",
            },
          },
        },
        info: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "info-500",
              dark: "info-500",
            },
          },
        },
        success: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "success-500",
              dark: "success-500",
            },
          },
        },
        warning: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "warning-500",
              dark: "warning-500",
            },
          },
        },
        danger: {
          background: {
            light: "transparent",
            dark: "transparent",
          },
          border: {
            light: "current",
            dark: "current",
          },
          font: {
            color: {
              light: "danger-500",
              dark: "danger-500",
            },
          },
        },
      },
    },
  },
};

const config$B = {
  theme: {
    nui: {
      [key$B]: defaultConfig$A,
    },
  },
};
const iconBox = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$B}`);
  addComponents({
    //Wrapper
    ".nui-icon-box": {
      "@apply relative inline-flex shrink-0 items-center justify-center": {},
      //Size:xs
      "&.nui-box-xs": {
        [`@apply h-${config2.size.xs.size} w-${config2.size.xs.size}`]: {},
        //Rounded:sm
        "&.nui-box-rounded-sm": {
          [`@apply ${config2.size.xs.rounded.sm}`]: {},
        },
        //Rounded:md
        "&.nui-box-rounded-md": {
          [`@apply ${config2.size.xs.rounded.md}`]: {},
        },
        //Rounded:lg
        "&.nui-box-rounded-lg": {
          [`@apply ${config2.size.xs.rounded.lg}`]: {},
        },
      },
      //Size:sm
      "&.nui-box-sm": {
        [`@apply h-${config2.size.sm.size} w-${config2.size.sm.size}`]: {},
        //Rounded:sm
        "&.nui-box-rounded-sm": {
          [`@apply ${config2.size.sm.rounded.sm}`]: {},
        },
        //Rounded:md
        "&.nui-box-rounded-md": {
          [`@apply ${config2.size.sm.rounded.md}`]: {},
        },
        //Rounded:lg
        "&.nui-box-rounded-lg": {
          [`@apply ${config2.size.sm.rounded.lg}`]: {},
        },
      },
      //Size:md
      "&.nui-box-md": {
        [`@apply h-${config2.size.md.size} w-${config2.size.md.size}`]: {},
        //Rounded:sm
        "&.nui-box-rounded-sm": {
          [`@apply ${config2.size.md.rounded.sm}`]: {},
        },
        //Rounded:md
        "&.nui-box-rounded-md": {
          [`@apply ${config2.size.md.rounded.md}`]: {},
        },
        //Rounded:lg
        "&.nui-box-rounded-lg": {
          [`@apply ${config2.size.md.rounded.lg}`]: {},
        },
      },
      //Size:lg
      "&.nui-box-lg": {
        [`@apply h-${config2.size.lg.size} w-${config2.size.lg.size}`]: {},
        //Rounded:sm
        "&.nui-box-rounded-sm": {
          [`@apply ${config2.size.lg.rounded.sm}`]: {},
        },
        //Rounded:md
        "&.nui-box-rounded-md": {
          [`@apply ${config2.size.lg.rounded.md}`]: {},
        },
        //Rounded:lg
        "&.nui-box-rounded-lg": {
          [`@apply ${config2.size.lg.rounded.lg}`]: {},
        },
      },
      //Size:xl
      "&.nui-box-xl": {
        [`@apply h-${config2.size.xl.size} w-${config2.size.xl.size}`]: {},
        //Rounded:sm
        "&.nui-box-rounded-sm": {
          [`@apply ${config2.size.xl.rounded.sm}`]: {},
        },
        //Rounded:md
        "&.nui-box-rounded-md": {
          [`@apply ${config2.size.xl.rounded.md}`]: {},
        },
        //Rounded:lg
        "&.nui-box-rounded-lg": {
          [`@apply ${config2.size.xl.rounded.lg}`]: {},
        },
      },
      //Size:xxl
      "&.nui-box-2xl": {
        [`@apply h-${config2.size.xxl.size} w-${config2.size.xxl.size}`]: {},
        //Rounded:sm
        "&.nui-box-rounded-sm": {
          [`@apply ${config2.size.xxl.rounded.sm}`]: {},
        },
        //Rounded:md
        "&.nui-box-rounded-md": {
          [`@apply ${config2.size.xxl.rounded.md}`]: {},
        },
        //Rounded:lg
        "&.nui-box-rounded-lg": {
          [`@apply ${config2.size.xxl.rounded.lg}`]: {},
        },
      },
      //Rounded:full
      "&.nui-box-rounded-full": {
        "@apply rounded-full": {},
      },
      //Color:default
      "&.nui-box-default": {
        //Font
        [`@apply text-${config2.color.variant.solid.default.font.color.light} dark:text-${config2.color.variant.solid.default.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.variant.solid.default.background.light} dark:bg-${config2.color.variant.solid.default.background.dark}`]:
          {},
      },
      //Variant:solid
      "&.nui-box-solid": {
        //Color:default
        "&.nui-box-default": {
          //Font
          [`@apply text-${config2.color.variant.solid.default.font.color.light} dark:text-${config2.color.variant.solid.default.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.default.background.light} dark:bg-${config2.color.variant.solid.default.background.dark}`]:
            {},
        },
        //Color:default-contrast
        "&.nui-box-default-contrast": {
          //Font
          [`@apply text-${config2.color.variant.solid.defaultContrast.font.color.light} dark:text-${config2.color.variant.solid.defaultContrast.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.defaultContrast.background.light} dark:bg-${config2.color.variant.solid.defaultContrast.background.dark}`]:
            {},
        },
        //Color:muted
        "&.nui-box-muted": {
          //Font
          [`@apply text-${config2.color.variant.solid.muted.font.color.light} dark:text-${config2.color.variant.solid.muted.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.muted.background.light} dark:bg-${config2.color.variant.solid.muted.background.dark}`]:
            {},
        },
        //Color:muted-contrast
        "&.nui-box-muted-contrast": {
          //Font
          [`@apply text-${config2.color.variant.solid.mutedContrast.font.color.light} dark:text-${config2.color.variant.solid.mutedContrast.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.mutedContrast.background.light} dark:bg-${config2.color.variant.solid.mutedContrast.background.dark}`]:
            {},
        },
        //Color:light
        "&.nui-box-light": {
          //Font
          [`@apply text-${config2.color.variant.solid.light.font.color.light} dark:text-${config2.color.variant.solid.light.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.light.background.light} dark:bg-${config2.color.variant.solid.light.background.dark}`]:
            {},
        },
        //Color:dark
        "&.nui-box-dark": {
          //Font
          [`@apply text-${config2.color.variant.solid.dark.font.color.light} dark:text-${config2.color.variant.solid.dark.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.dark.background.light} dark:bg-${config2.color.variant.solid.dark.background.dark}`]:
            {},
        },
        //Color:black
        "&.nui-box-black": {
          //Font
          [`@apply text-${config2.color.variant.solid.black.font.color.light} dark:text-${config2.color.variant.solid.black.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.black.background.light} dark:bg-${config2.color.variant.solid.black.background.dark}`]:
            {},
        },
        //Color:primary
        "&.nui-box-primary": {
          //Font
          [`@apply text-${config2.color.variant.solid.primary.font.color.light} dark:text-${config2.color.variant.solid.primary.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.primary.background.light} dark:bg-${config2.color.variant.solid.primary.background.dark}`]:
            {},
        },
        //Color:info
        "&.nui-box-info": {
          //Font
          [`@apply text-${config2.color.variant.solid.info.font.color.light} dark:text-${config2.color.variant.solid.info.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.info.background.light} dark:bg-${config2.color.variant.solid.info.background.dark}`]:
            {},
        },
        //Color:success
        "&.nui-box-success": {
          //Font
          [`@apply text-${config2.color.variant.solid.success.font.color.light} dark:text-${config2.color.variant.solid.success.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.success.background.light} dark:bg-${config2.color.variant.solid.success.background.dark}`]:
            {},
        },
        //Color:warning
        "&.nui-box-warning": {
          //Font
          [`@apply text-${config2.color.variant.solid.warning.font.color.light} dark:text-${config2.color.variant.solid.warning.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.warning.background.light} dark:bg-${config2.color.variant.solid.warning.background.dark}`]:
            {},
        },
        //Color:danger
        "&.nui-box-danger": {
          //Font
          [`@apply text-${config2.color.variant.solid.danger.font.color.light} dark:text-${config2.color.variant.solid.danger.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.solid.danger.background.light} dark:bg-${config2.color.variant.solid.danger.background.dark}`]:
            {},
        },
      },
      //Variant:pastel
      "&.nui-box-pastel": {
        //Color:default
        "&.nui-box-default": {
          //Font
          [`@apply text-${config2.color.variant.pastel.default.font.color.light} dark:text-${config2.color.variant.pastel.default.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.default.background.light} dark:bg-${config2.color.variant.pastel.default.background.dark}`]:
            {},
        },
        //Color:default-contrast
        "&.nui-box-default-contrast": {
          //Font
          [`@apply text-${config2.color.variant.pastel.defaultContrast.font.color.light} dark:text-${config2.color.variant.pastel.defaultContrast.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.defaultContrast.background.light} dark:bg-${config2.color.variant.pastel.defaultContrast.background.dark}`]:
            {},
        },
        //Color:muted
        "&.nui-box-muted": {
          //Font
          [`@apply text-${config2.color.variant.pastel.muted.font.color.light} dark:text-${config2.color.variant.pastel.muted.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.muted.background.light} dark:bg-${config2.color.variant.pastel.muted.background.dark}`]:
            {},
        },
        //Color:muted-contrast
        "&.nui-box-muted-contrast": {
          //Font
          [`@apply text-${config2.color.variant.pastel.mutedContrast.font.color.light} dark:text-${config2.color.variant.pastel.mutedContrast.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.mutedContrast.background.light} dark:bg-${config2.color.variant.pastel.mutedContrast.background.dark}`]:
            {},
        },
        //Color:light
        "&.nui-box-light": {
          //Font
          [`@apply text-${config2.color.variant.pastel.light.font.color.light} dark:text-${config2.color.variant.pastel.light.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.light.background.light} dark:bg-${config2.color.variant.pastel.light.background.dark}`]:
            {},
        },
        //Color:dark
        "&.nui-box-dark": {
          //Font
          [`@apply text-${config2.color.variant.pastel.dark.font.color.light} dark:text-${config2.color.variant.pastel.dark.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.dark.background.light} dark:bg-${config2.color.variant.pastel.dark.background.dark}`]:
            {},
        },
        //Color:black
        "&.nui-box-black": {
          //Font
          [`@apply text-${config2.color.variant.pastel.black.font.color.light} dark:text-${config2.color.variant.pastel.black.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.black.background.light} dark:bg-${config2.color.variant.pastel.black.background.dark}`]:
            {},
        },
        //Color:primary
        "&.nui-box-primary": {
          //Font
          [`@apply text-${config2.color.variant.pastel.primary.font.color.light} dark:text-${config2.color.variant.pastel.primary.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.primary.background.light} dark:bg-${config2.color.variant.pastel.primary.background.dark}`]:
            {},
        },
        //Color:info
        "&.nui-box-info": {
          //Font
          [`@apply text-${config2.color.variant.pastel.info.font.color.light} dark:text-${config2.color.variant.pastel.info.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.info.background.light} dark:bg-${config2.color.variant.pastel.info.background.dark}`]:
            {},
        },
        //Color:success
        "&.nui-box-success": {
          //Font
          [`@apply text-${config2.color.variant.pastel.success.font.color.light} dark:text-${config2.color.variant.pastel.success.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.success.background.light} dark:bg-${config2.color.variant.pastel.success.background.dark}`]:
            {},
        },
        //Color:warning
        "&.nui-box-warning": {
          //Font
          [`@apply text-${config2.color.variant.pastel.warning.font.color.light} dark:text-${config2.color.variant.pastel.warning.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.warning.background.light} dark:bg-${config2.color.variant.pastel.warning.background.dark}`]:
            {},
        },
        //Color:danger
        "&.nui-box-danger": {
          //Font
          [`@apply text-${config2.color.variant.pastel.danger.font.color.light} dark:text-${config2.color.variant.pastel.danger.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.pastel.danger.background.light} dark:bg-${config2.color.variant.pastel.danger.background.dark}`]:
            {},
        },
      },
      //Variant:outline
      "&.nui-box-outline": {
        //Color:default
        "&.nui-box-default": {
          //Font
          [`@apply text-${config2.color.variant.outline.default.font.color.light} dark:text-${config2.color.variant.outline.default.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.default.background.light}  bg-${config2.color.variant.outline.default.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.default.border.light} dark:border-${config2.color.variant.outline.default.border.dark}`]:
            {},
        },
        //Color:default-contrast
        "&.nui-box-default-contrast": {
          //Font
          [`@apply text-${config2.color.variant.outline.defaultContrast.font.color.light} dark:text-${config2.color.variant.outline.defaultContrast.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.defaultContrast.background.light}  bg-${config2.color.variant.outline.defaultContrast.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.defaultContrast.border.light} dark:border-${config2.color.variant.outline.defaultContrast.border.dark}`]:
            {},
        },
        //Color:muted
        "&.nui-box-muted": {
          //Font
          [`@apply text-${config2.color.variant.outline.muted.font.color.light} dark:text-${config2.color.variant.outline.muted.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.muted.background.light}  bg-${config2.color.variant.outline.muted.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.muted.border.light} dark:border-${config2.color.variant.outline.muted.border.dark}`]:
            {},
        },
        //Color:muted-contrast
        "&.nui-box-muted-contrast": {
          //Font
          [`@apply text-${config2.color.variant.outline.mutedContrast.font.color.light} dark:text-${config2.color.variant.outline.mutedContrast.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.mutedContrast.background.light}  bg-${config2.color.variant.outline.mutedContrast.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.mutedContrast.border.light} dark:border-${config2.color.variant.outline.mutedContrast.border.dark}`]:
            {},
        },
        //Color:light
        "&.nui-box-light": {
          //Font
          [`@apply text-${config2.color.variant.outline.light.font.color.light} dark:text-${config2.color.variant.outline.light.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.light.background.light}  bg-${config2.color.variant.outline.light.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.light.border.light} dark:border-${config2.color.variant.outline.light.border.dark}`]:
            {},
        },
        //Color:dark
        "&.nui-box-dark": {
          //Font
          [`@apply text-${config2.color.variant.outline.dark.font.color.light} dark:text-${config2.color.variant.outline.dark.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.dark.background.light}  bg-${config2.color.variant.outline.dark.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.dark.border.light} dark:border-${config2.color.variant.outline.dark.border.dark}`]:
            {},
        },
        //Color:black
        "&.nui-box-black": {
          //Font
          [`@apply text-${config2.color.variant.outline.black.font.color.light} dark:text-${config2.color.variant.outline.black.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.black.background.light}  bg-${config2.color.variant.outline.black.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.black.border.light} dark:border-${config2.color.variant.outline.black.border.dark}`]:
            {},
        },
        //Color:primary
        "&.nui-box-primary": {
          //Font
          [`@apply text-${config2.color.variant.outline.primary.font.color.light} dark:text-${config2.color.variant.outline.primary.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.primary.background.light}  bg-${config2.color.variant.outline.primary.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.primary.border.light} dark:border-${config2.color.variant.outline.primary.border.dark}`]:
            {},
        },
        //Color:info
        "&.nui-box-info": {
          //Font
          [`@apply text-${config2.color.variant.outline.info.font.color.light} dark:text-${config2.color.variant.outline.info.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.info.background.light}  bg-${config2.color.variant.outline.info.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.info.border.light} dark:border-${config2.color.variant.outline.info.border.dark}`]:
            {},
        },
        //Color:success
        "&.nui-box-success": {
          //Font
          [`@apply text-${config2.color.variant.outline.success.font.color.light} dark:text-${config2.color.variant.outline.success.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.success.background.light}  bg-${config2.color.variant.outline.success.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.success.border.light} dark:border-${config2.color.variant.outline.success.border.dark}`]:
            {},
        },
        //Color:warning
        "&.nui-box-warning": {
          //Font
          [`@apply text-${config2.color.variant.outline.warning.font.color.light} dark:text-${config2.color.variant.outline.warning.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.warning.background.light}  bg-${config2.color.variant.outline.warning.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.warning.border.light} dark:border-${config2.color.variant.outline.warning.border.dark}`]:
            {},
        },
        //Color:danger
        "&.nui-box-danger": {
          //Font
          [`@apply text-${config2.color.variant.outline.danger.font.color.light} dark:text-${config2.color.variant.outline.danger.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.variant.outline.danger.background.light}  bg-${config2.color.variant.outline.danger.background.dark}`]:
            {},
          //Border
          [`@apply border-2 border-${config2.color.variant.outline.danger.border.light} dark:border-${config2.color.variant.outline.danger.border.dark}`]:
            {},
        },
      },
      //Bordered option
      "&.nui-box-bordered": {
        [`@apply border-${config2.bordered.border.size} border-${config2.bordered.border.color}`]:
          {},
      },
      //Masks
      "&.nui-box-mask:not(.nui-box-rounded-sm):not(.nui-box-rounded-lg):not(.nui-box-rounded-full):not(.nui-box-outline):not(.nui-box-bordered)":
        {
          "@apply nui-mask": {},
        },
    },
  });
}, config$B);

module.export = iconBox;

import plugin from "tailwindcss/plugin";
const key$T = "accordion";

const defaultConfig$S = {
  wrapper: {
    width: "full",
    rounded: {
      none: "none",
      sm: "md",
      md: "lg",
      lg: "xl",
    },
    background: {
      light: "white",
      dark: "muted-800",
    },
    hover: {
      light: "muted-50/60",
      dark: "muted-800/60",
    },
    border: {
      light: "muted-300",
      dark: "muted-700",
    },
    transition: {
      property: "colors",
      duration: "300",
    },
  },
  inner: {
    border: {
      light: "muted-300",
      dark: "muted-700",
    },
  },
  header: {
    font: {
      color: {
        light: "muted-800",
        dark: "muted-100",
      },
    },
  },
  content: {
    font: {
      family: "sans",
      size: "sm",
      color: {
        light: "muted-500",
        dark: "muted-400",
      },
    },
    padding: {
      x: "4",
      y: "4",
    },
  },
  color: {
    default: {
      background: {
        base: {
          light: "white",
          dark: "muted-800",
        },
        hover: {
          light: "muted-50/60",
          dark: "muted-800/60",
        },
      },
      border: {
        light: "muted-300",
        dark: "muted-700",
      },
    },
    defaultContrast: {
      background: {
        base: {
          light: "white",
          dark: "muted-950",
        },
        hover: {
          light: "muted-50/60",
          dark: "muted-950/60",
        },
      },
      border: {
        light: "muted-300",
        dark: "muted-800",
      },
    },
    muted: {
      background: {
        base: {
          light: "muted-100",
          dark: "muted-800",
        },
        hover: {
          light: "muted-100/60",
          dark: "muted-800/60",
        },
      },
      border: {
        light: "muted-200",
        dark: "muted-700",
      },
    },
    mutedContrast: {
      background: {
        base: {
          light: "muted-100",
          dark: "muted-950",
        },
        hover: {
          light: "muted-100/60",
          dark: "muted-950/60",
        },
      },
      border: {
        light: "muted-200",
        dark: "muted-800",
      },
    },
  },
  dotColor: {
    default: {
      background: {
        light: "muted-400",
        dark: "muted-700",
      },
    },
    primary: {
      background: {
        light: "primary-500",
        dark: "primary-500",
      },
    },
    info: {
      background: {
        light: "info-500",
        dark: "info-500",
      },
    },
    success: {
      background: {
        light: "success-500",
        dark: "success-500",
      },
    },
    warning: {
      background: {
        light: "warning-500",
        dark: "warning-500",
      },
    },
    danger: {
      background: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    dark: {
      background: {
        light: "muted-900",
        dark: "muted-100",
      },
    },
    black: {
      background: {
        light: "black",
        dark: "white",
      },
    },
  },
  icon: {
    wrapper: {
      size: "8",
      rounded: "full",
      background: {
        light: "white",
        dark: "muted-700/60",
      },
      border: {
        light: "transparent",
        dark: "transparent",
      },
      transition: {
        property: "all",
        duration: "300",
      },
    },
    dot: {
      size: "3",
      rounded: "full",
      background: {
        light: "muted-200",
        dark: "muted-700",
        active: "primary-500",
      },
      transition: {
        property: "colors",
        duration: "300",
      },
    },
    chevron: {
      size: "4",
      transition: {
        property: "transform",
        duration: "300",
      },
    },
    plus: {
      size: "4",
      transition: {
        property: "transform",
        duration: "300",
      },
    },
  },
};

const config$T = {
  theme: {
    nui: {
      [key$T]: defaultConfig$S,
    },
  },
};

const plugin = require("tailwindcss/plugin");

const accordionPlugin = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$T}`) || defaultConfig; // Fallback to defaultConfig if not found

  addComponents({
    // Accordion: wrapper
    ".nui-accordion": {
      // Remove bottom borders
      "&:not(:last-child)": {
        "@apply border-b-0": {},
      },
      // Base
      [`@apply w-${config2.wrapper.width} block overflow-hidden`]: {},
      // Background
      [`@apply bg-${config2.wrapper.background.light} dark:bg-${config2.wrapper.background.dark}`]:
        {},
      // Hover
      [`@apply hover:bg-${config2.wrapper.hover.light} dark:hover:bg-${config2.wrapper.hover.dark}`]:
        {},
      // Border
      [`@apply border border-${config2.wrapper.border.light} dark:border-${config2.wrapper.border.dark}`]:
        {},
      // Transition
      [`@apply transition-${config2.wrapper.transition.property} duration-${config2.wrapper.transition.duration}`]:
        {},
      // Accordion: inner
      ".nui-accordion-detail[open]:not(:first-child)": {
        [`@apply border-t border-${config2.inner.border.light} dark:border-${config2.inner.border.dark}`]:
          {},
      },
      // Inner: dot
      ".nui-accordion-detail .nui-accordion-dot": {
        [`@apply bg-${config2.icon.dot.background.light} dark:bg-${config2.icon.dot.background.dark}`]:
          {},
      },
      // Inner: summary
      ".nui-accordion-summary": {
        "@apply cursor-pointer list-none outline-none": {},
      },
      // Inner: header
      ".nui-accordion-header": {
        "@apply flex items-center justify-between": {},
        ".nui-accordion-header-inner": {
          "@apply text-muted-800 dark:text-white": {},
        },
      },
      // Inner: dot
      ".nui-accordion-dot": {
        [`@apply ms-2 h-${config2.icon.dot.size} w-${config2.icon.dot.size} rounded-${config2.icon.dot.rounded}`]:
          {},
        // Dot transition
        [`@apply transition-${config2.icon.dot.transition.property} duration-${config2.icon.dot.transition.duration}`]:
          {},
      },
      // Icon: outer
      ".nui-icon-outer": {
        // Base
        "@apply ms-2 flex items-center justify-center": {},
        // Width & Radius
        [`@apply h-${config2.icon.wrapper.size} w-${config2.icon.wrapper.size} rounded-${config2.icon.wrapper.rounded}`]:
          {},
        // Border
        [`@apply border border-${config2.icon.wrapper.border.light} dark:border-${config2.icon.wrapper.border.dark}`]:
          {},
        // Background
        [`@apply bg-${config2.icon.wrapper.background.light} dark:bg-${config2.icon.wrapper.background.dark}`]:
          {},
        // Transition
        [`@apply transition-${config2.icon.wrapper.transition.property} duration-${config2.icon.wrapper.transition.duration}`]:
          {},
      },
      ".nui-chevron-icon": {
        // Base
        [`@apply text-muted-400 h-${config2.icon.chevron.size} w-${config2.icon.chevron.size}`]:
          {},
        // Transition
        [`@apply transition-${config2.icon.chevron.transition.property} duration-${config2.icon.chevron.transition.duration}`]:
          {},
      },
      ".nui-plus-icon": {
        // Base
        [`@apply text-muted-400 h-${config2.icon.plus.size} w-${config2.icon.plus.size}`]:
          {},
        // Transition
        [`@apply transition-${config2.icon.plus.transition.property} duration-${config2.icon.plus.transition.duration}`]:
          {},
      },
      // Accordion: content
      ".nui-accordion-content": {
        // Base
        [`@apply px-${config2.content.padding.x} pb-${config2.content.padding.y}`]:
          {},
        // Font
        [`@apply font-${config2.content.font.family} text-${config2.content.font.size} text-${config2.content.font.color.light} dark:text-${config2.content.font.color.dark}`]:
          {},
      },
      // Color: default
      "&.nui-accordion-default": {
        // Background
        [`@apply bg-${config2.color.default.background.base.light} dark:bg-${config2.color.default.background.base.dark}`]:
          {},
        // Hover
        [`@apply hover:bg-${config2.color.default.background.hover.light} dark:hover:bg-${config2.color.default.background.hover.dark}`]:
          {},
        // Border
        [`@apply border border-${config2.color.default.border.light} dark:border-${config2.color.default.border.dark}`]:
          {},
      },
      // Color: default-contrast
      "&.nui-accordion-default-contrast": {
        // Background
        [`@apply bg-${config2.color.defaultContrast.background.base.light} dark:bg-${config2.color.defaultContrast.background.base.dark}`]:
          {},
        // Hover
        [`@apply hover:bg-${config2.color.defaultContrast.background.hover.light} dark:hover:bg-${config2.color.defaultContrast.background.hover.dark}`]:
          {},
        // Border
        [`@apply border border-${config2.color.defaultContrast.border.light} dark:border-${config2.color.defaultContrast.border.dark}`]:
          {},
      },
      // Color: muted
      "&.nui-accordion-muted": {
        // Background
        [`@apply bg-${config2.color.muted.background.base.light} dark:bg-${config2.color.muted.background.base.dark}`]:
          {},
        // Hover
        [`@apply hover:bg-${config2.color.muted.background.hover.light} dark:hover:bg-${config2.color.muted.background.hover.dark}`]:
          {},
        // Border
        [`@apply border border-${config2.color.muted.border.light} dark:border-${config2.color.muted.border.dark}`]:
          {},
      },
      // Color: muted-contrast
      "&.nui-accordion-muted-contrast": {
        // Background
        [`@apply bg-${config2.color.mutedContrast.background.base.light} dark:bg-${config2.color.mutedContrast.background.base.dark}`]:
          {},
        // Hover
        [`@apply hover:bg-${config2.color.mutedContrast.background.hover.light} dark:hover:bg-${config2.color.mutedContrast.background.hover.dark}`]:
          {},
        // Border
        [`@apply border border-${config2.color.mutedContrast.border.light} dark:border-${config2.color.mutedContrast.border.dark}`]:
          {},
      },
      // Dot: default
      "&.nui-dot-default": {
        ".nui-accordion-detail[open] .nui-accordion-dot": {
          [`@apply bg-${config2.dotColor.default.background.light} dark:bg-${config2.dotColor.default.background.dark}`]:
            {},
        },
      },
      // Dot: primary
      "&.nui-dot-primary": {
        ".nui-accordion-detail[open] .nui-accordion-dot": {
          [`@apply bg-${config2.dotColor.primary.background.light} dark:bg-${config2.dotColor.primary.background.dark}`]:
            {},
        },
      },
      // Dot: info
      "&.nui-dot-info": {
        ".nui-accordion-detail[open] .nui-accordion-dot": {
          [`@apply bg-${config2.dotColor.info.background.light} dark:bg-${config2.dotColor.info.background.dark}`]:
            {},
        },
      },
      // Dot: success
      "&.nui-dot-success": {
        ".nui-accordion-detail[open] .nui-accordion-dot": {
          [`@apply bg-${config2.dotColor.success.background.light} dark:bg-${config2.dotColor.success.background.dark}`]:
            {},
        },
      },
      // Dot: warning
      "&.nui-dot-warning": {
        ".nui-accordion-detail[open] .nui-accordion-dot": {
          [`@apply bg-${config2.dotColor.warning.background.light} dark:bg-${config2.dotColor.warning.background.dark}`]:
            {},
        },
      },
      // Dot: danger
      "&.nui-dot-danger": {
        ".nui-accordion-detail[open] .nui-accordion-dot": {
          [`@apply bg-${config2.dotColor.danger.background.light} dark:bg-${config2.dotColor.danger.background.dark}`]:
            {},
        },
      },
      // Dot: dark
      "&.nui-dot-dark": {
        ".nui-accordion-detail[open] .nui-accordion-dot": {
          [`@apply bg-${config2.dotColor.dark.background.light} dark:bg-${config2.dotColor.dark.background.dark}`]:
            {},
        },
      },
      // Dot: black
      "&.nui-dot-black": {
        ".nui-accordion-detail[open] .nui-accordion-dot": {
          [`@apply bg-${config2.dotColor.black.background.light} dark:bg-${config2.dotColor.black.background.dark}`]:
            {},
        },
      },
      "&.nui-accordion-dot": {
        ".nui-accordion-header": {
          [`@apply p-${config2.content.padding.x}`]: {},
        },
      },
      "&.nui-accordion-chevron, &.nui-accordion-plus": {
        ".nui-accordion-header": {
          [`@apply px-${config2.content.padding.x} py-3`]: {},
        },
      },
      "&.nui-accordion-chevron": {
        ".nui-accordion-detail[open] .nui-icon-outer": {
          "@apply rotate-180": {},
        },
      },
      "&.nui-accordion-plus": {
        ".nui-accordion-detail[open] .nui-icon-outer": {
          "@apply rotate-45": {},
        },
      },
      // Accordion: rounded
      "&.nui-accordion-straight": {
        "&.nui-accordion:first-child": {
          [`@apply rounded-t-${config2.wrapper.rounded.none}`]: {},
        },
        "&.nui-accordion:last-child": {
          [`@apply rounded-b-${config2.wrapper.rounded.none}`]: {},
        },
      },
      "&.nui-accordion-rounded-sm": {
        "&.nui-accordion:first-child": {
          [`@apply rounded-t-${config2.wrapper.rounded.sm}`]: {},
        },
        "&.nui-accordion:last-child": {
          [`@apply rounded-b-${config2.wrapper.rounded.sm}`]: {},
        },
      },
      "&.nui-accordion-rounded-md": {
        "&.nui-accordion:first-child": {
          [`@apply rounded-t-${config2.wrapper.rounded.md}`]: {},
        },
        "&.nui-accordion:last-child": {
          [`@apply rounded-b-${config2.wrapper.rounded.md}`]: {},
        },
      },
      "&.nui-accordion-rounded-lg": {
        "&.nui-accordion:first-child": {
          [`@apply rounded-t-${config2.wrapper.rounded.lg}`]: {},
        },
        "&.nui-accordion:last-child": {
          [`@apply rounded-b-${config2.wrapper.rounded.lg}`]: {},
        },
      },
      "+ .nui-accordion": {
        [`@apply !border-t-0`]: {},
      },
    },
  });
}, config$T);

module.export = accordionPlugin;

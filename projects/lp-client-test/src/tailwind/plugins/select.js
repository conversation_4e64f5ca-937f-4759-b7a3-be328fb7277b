import plugin from "tailwindcss/plugin";

const key$d = "select";
const defaultConfig$c = {
  rounded: {
    none: "rounded-none",
    sm: "rounded-md",
    md: "rounded-lg",
    lg: "rounded-xl",
    full: "rounded-full",
  },
  label: {
    float: {
      height: "5",
      font: {
        family: "sans",
        color: "primary-500",
        lead: "none",
      },
      transition: {
        property: "all",
        duration: "300",
      },
    },
  },
  select: {
    width: "full",
    font: {
      family: "sans",
    },
    icon: {
      color: {
        base: {
          light: "muted-400",
          dark: "muted-400",
        },
        focus: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
      transition: {
        property: "colors",
        duration: "300",
      },
    },
    transition: {
      property: "all",
      duration: "300",
    },
    focus: {
      label: {
        float: {
          font: {
            color: {
              light: "primary-500",
              dark: "primary-500",
            },
          },
        },
      },
      border: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    chevron: {
      color: {
        light: "muted-400",
        dark: "muted-400",
      },
      transition: {
        property: "transform",
        duration: "300",
      },
    },
    multiple: {
      padding: "2",
      size: "32",
    },
  },
  size: {
    sm: {
      padding: "8",
      chevron: {
        outer: {
          size: "8",
        },
        inner: {
          size: "4",
        },
      },
      label: {
        font: {
          size: "xs",
        },
      },
      icon: {
        outer: {
          size: "8",
        },
        inner: {
          size: "4",
        },
      },
      placeload: {
        size: "8",
      },
    },
    md: {
      padding: "10",
      chevron: {
        outer: {
          size: "10",
        },
        inner: {
          size: "[1.15rem]",
        },
      },
      label: {
        font: {
          size: "[0.825rem]",
        },
      },
      icon: {
        outer: {
          size: "10",
        },
        inner: {
          size: "[1.15rem]",
        },
      },
      placeload: {
        size: "10",
      },
    },
    lg: {
      padding: "12",
      chevron: {
        outer: {
          size: "12",
        },
        inner: {
          size: "5",
        },
      },
      label: {
        font: {
          size: "sm",
        },
      },
      icon: {
        outer: {
          size: "12",
        },
        inner: {
          size: "5",
        },
      },
      placeload: {
        size: "12",
      },
    },
    xl: {
      padding: "14",
      chevron: {
        outer: {
          size: "14",
        },
        inner: {
          size: "5",
        },
      },
      label: {
        font: {
          size: "sm",
        },
      },
      icon: {
        outer: {
          size: "14",
        },
        inner: {
          size: "5",
        },
      },
      placeload: {
        size: "14",
      },
    },
  },
  color: {
    default: {
      background: {
        light: "white",
        dark: "muted-900",
      },
      border: {
        base: {
          light: "muted-300",
          dark: "muted-700",
        },
        hover: {
          light: "muted-300",
          dark: "muted-700",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-500",
      },
    },
    defaultContrast: {
      background: {
        light: "white",
        dark: "muted-950",
      },
      border: {
        base: {
          light: "muted-300",
          dark: "muted-800",
        },
        hover: {
          light: "muted-300",
          dark: "muted-800",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
    muted: {
      background: {
        light: "muted-100",
        dark: "muted-900",
      },
      border: {
        base: {
          light: "muted-100",
          dark: "muted-700",
        },
        hover: {
          light: "muted-100",
          dark: "muted-700",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-500",
      },
    },
    mutedContrast: {
      background: {
        light: "muted-100",
        dark: "muted-950",
      },
      border: {
        base: {
          light: "muted-100",
          dark: "muted-800",
        },
        hover: {
          light: "muted-100",
          dark: "muted-800",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
  },
  error: {
    select: {
      border: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    icon: {
      color: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    font: {
      family: "sans",
      size: "[0.65rem]",
      weight: "medium",
      color: {
        light: "danger-600",
        dark: "danger-600",
      },
    },
  },
  icon: {
    disabled: {
      select: {
        sm: {
          font: {
            size: "xs",
          },
        },
        md: {
          font: {
            size: "sm",
          },
        },
        lg: {
          font: {
            size: "sm",
          },
        },
        xl: {
          font: {
            size: "base",
          },
        },
      },
      label: {
        float: {
          sm: {
            font: {
              size: "xs",
            },
          },
          md: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          lg: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          xl: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
        },
      },
    },
    enabled: {
      select: {
        sm: {
          font: {
            size: "xs",
          },
        },
        md: {
          font: {
            size: "sm",
          },
        },
        lg: {
          font: {
            size: "sm",
          },
        },
        xl: {
          font: {
            size: "base",
          },
        },
      },
      label: {
        float: {
          sm: {
            font: {
              size: "xs",
            },
          },
          md: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          lg: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          xl: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
        },
      },
    },
  },
};

const config$d = {
  theme: {
    nui: {
      [key$d]: defaultConfig$c,
    },
  },
};
const select = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$d}`);
  addComponents({
    //Wrapper
    ".nui-select-wrapper": {
      "@apply relative": {},
      //Select:label
      ".nui-select-label, .nui-label-float": {
        "@apply nui-label": {},
      },
      //Label:float
      ".nui-label-float": {
        [`@apply h-${config2.label.float.height} absolute inline-flex items-center select-none pointer-events-none`]:
          {},
        //Font
        [`@apply font-${config2.label.float.font.family} text-${config2.label.float.font.color} leading-${config2.label.float.font.lead}`]:
          {},
        //Transition
        [`@apply transition-${config2.label.float.transition.property} duration-${config2.label.float.transition.duration}`]:
          {},
      },
      //Select:outer
      ".nui-select-outer": {
        "@apply relative": {},
      },
      //Select:icon
      ".nui-select-icon": {
        "@apply absolute start-0 top-0 z-10 flex items-center justify-center":
          {},
        //Color
        [`@apply text-${config2.select.icon.color.base.light} dark:text-${config2.select.icon.color.base.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.select.icon.transition.property} duration-${config2.select.icon.transition.duration}`]:
          {},
      },
      //Select
      ".nui-select": {
        //Base
        [`@apply nui-focus appearance-none w-${config2.select.width} font-${config2.select.font.family} disabled:cursor-not-allowed disabled:opacity-75`]:
          {},
        //Transition
        [`@apply transition-${config2.select.transition.property} duration-${config2.select.transition.duration}`]:
          {},
        //Focus:label:float
        "&:focus-visible ~ .nui-label-float": {
          [`@apply !text-${config2.select.focus.label.float.font.color.light} dark:!text-${config2.select.focus.label.float.font.color.dark}`]:
            {},
        },
        //Focus:icon
        "&:focus-visible ~ .nui-select-icon": {
          [`@apply !text-${config2.select.icon.color.focus.light} dark:!text-${config2.select.icon.color.focus.dark}`]:
            {},
        },
        //Focus:chevron
        "&:focus-visible ~ .nui-select-chevron": {
          "@apply rotate-180": {},
        },
        //Disabled:icon
        "&:disabled ~ .nui-select-icon": {
          "@apply cursor-not-allowed opacity-75": {},
        },
      },
      //Select:placeload
      ".nui-select-placeload": {
        "@apply absolute start-0 top-0 flex w-full items-center px-4": {},
        ".nui-placeload": {
          "@apply h-3 w-full max-w-[75%] rounded": {},
        },
      },
      //Select:chevron
      ".nui-select-chevron.nui-chevron": {
        "@apply pointer-events-none": {},
        "@apply absolute end-0 top-0 z-10 flex items-center justify-center": {},
        //Color
        [`@apply text-${config2.select.chevron.color.light} dark:text-${config2.select.chevron.color.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.select.chevron.transition.property} duration-${config2.select.chevron.transition.duration}`]:
          {},
      },
      //Select:multiple
      "&.nui-select-multiple": {
        ".nui-select": {
          [`@apply p-${config2.select.multiple.padding} h-${config2.select.multiple.size}`]:
            {},
        },
      },
      //Rounded:sm
      "&.nui-select-rounded-sm": {
        ".nui-select": {
          [`@apply ${config2.rounded.sm}`]: {},
        },
      },
      //Rounded:md
      "&.nui-select-rounded-md": {
        ".nui-select": {
          [`@apply ${config2.rounded.md}`]: {},
        },
      },
      //Rounded:lg
      "&.nui-select-rounded-lg": {
        ".nui-select": {
          [`@apply ${config2.rounded.lg}`]: {},
        },
      },
      //Rounded:full
      "&.nui-select-rounded-full": {
        ".nui-select": {
          [`@apply ${config2.rounded.full}`]: {},
        },
      },
      //Size:sm
      "&.nui-select-sm": {
        //Select
        ".nui-select": {
          [`@apply pe-${config2.size.sm.padding}`]: {},
        },
        //Select:label
        ".nui-select-label": {
          [`@apply pb-1 text-${config2.size.sm.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-1.5": {},
        },
        //Select:icon
        ".nui-select-icon, .nui-select-chevron": {
          [`@apply h-${config2.size.sm.icon.outer.size} w-${config2.size.sm.icon.outer.size}`]:
            {},
          ".nui-select-icon-inner, .nui-select-chevron-inner": {
            [`@apply h-${config2.size.sm.icon.inner.size} w-${config2.size.sm.icon.inner.size}`]:
              {},
          },
        },
        //Select:placeload
        ".nui-select-placeload": {
          [`@apply h-${config2.size.sm.placeload.size}`]: {},
        },
      },
      //Size:md
      "&.nui-select-md": {
        //Select
        ".nui-select": {
          [`@apply pe-${config2.size.md.padding}`]: {},
        },
        //Select:label
        ".nui-select-label": {
          [`@apply pb-1 text-${config2.size.md.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-2.5": {},
        },
        //Select:icon
        ".nui-select-icon, .nui-select-chevron": {
          [`@apply h-${config2.size.md.icon.outer.size} w-${config2.size.md.icon.outer.size}`]:
            {},
          ".nui-select-icon-inner, .nui-select-chevron-inner": {
            [`@apply h-${config2.size.md.icon.inner.size} w-${config2.size.md.icon.inner.size}`]:
              {},
          },
        },
        //Select:placeload
        ".nui-select-placeload": {
          [`@apply h-${config2.size.md.placeload.size}`]: {},
        },
      },
      //Size:lg
      "&.nui-select-lg": {
        //Select
        ".nui-select": {
          [`@apply pe-${config2.size.lg.padding}`]: {},
        },
        //Select:label
        ".nui-select-label": {
          [`@apply pb-1 text-${config2.size.lg.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-3.5": {},
        },
        //Select:icon
        ".nui-select-icon, .nui-select-chevron": {
          [`@apply h-${config2.size.lg.icon.outer.size} w-${config2.size.lg.icon.outer.size}`]:
            {},
          ".nui-select-icon-inner, .nui-select-chevron-inner": {
            [`@apply h-${config2.size.lg.icon.inner.size} w-${config2.size.lg.icon.inner.size}`]:
              {},
          },
        },
        //Select:placeload
        ".nui-select-placeload": {
          [`@apply h-${config2.size.lg.placeload.size}`]: {},
        },
      },
      //Size:xl
      "&.nui-select-xl": {
        //Select
        ".nui-select": {
          [`@apply pe-${config2.size.xl.padding}`]: {},
        },
        //Select:label
        ".nui-select-label": {
          [`@apply pb-1 text-${config2.size.xl.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-[1.1rem]": {},
        },
        //Select:icon
        ".nui-select-icon, .nui-select-chevron": {
          [`@apply h-${config2.size.xl.icon.outer.size} w-${config2.size.xl.icon.outer.size}`]:
            {},
          ".nui-select-icon-inner, .nui-select-chevron-inner": {
            [`@apply h-${config2.size.xl.icon.inner.size} w-${config2.size.xl.icon.inner.size}`]:
              {},
          },
        },
        //Select:placeload
        ".nui-select-placeload": {
          [`@apply h-${config2.size.xl.placeload.size}`]: {},
        },
      },
      //Color:default
      "&.nui-select-default": {
        ".nui-select": {
          //Font
          [`@apply text-${config2.color.default.color.light} dark:text-${config2.color.default.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.default.background.light} dark:bg-${config2.color.default.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.default.border.base.light} dark:border-${config2.color.default.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.default.border.hover.light} dark:hover:border-${config2.color.default.border.hover.dark}`]:
            {},
        },
      },
      //Color:default-contrast
      "&.nui-select-default-contrast": {
        ".nui-select": {
          //Font
          [`@apply text-${config2.color.defaultContrast.color.light} dark:text-${config2.color.defaultContrast.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.defaultContrast.background.light} dark:bg-${config2.color.defaultContrast.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.defaultContrast.border.base.light} dark:border-${config2.color.defaultContrast.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.defaultContrast.border.hover.light} dark:hover:border-${config2.color.defaultContrast.border.hover.dark}`]:
            {},
        },
      },
      //Color:muted
      "&.nui-select-muted": {
        ".nui-select": {
          //Font
          [`@apply text-${config2.color.muted.color.light} dark:text-${config2.color.muted.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.muted.background.light} dark:bg-${config2.color.muted.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.muted.border.base.light} dark:border-${config2.color.muted.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.muted.border.hover.light} dark:hover:border-${config2.color.muted.border.hover.dark}`]:
            {},
        },
      },
      //Color:muted-contrast
      "&.nui-select-muted-contrast": {
        ".nui-select": {
          //Font
          [`@apply text-${config2.color.mutedContrast.color.light} dark:text-${config2.color.mutedContrast.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.mutedContrast.background.light} dark:bg-${config2.color.mutedContrast.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.mutedContrast.border.base.light} dark:border-${config2.color.mutedContrast.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.mutedContrast.border.hover.light} dark:hover:border-${config2.color.mutedContrast.border.hover.dark}`]:
            {},
        },
      },
      //Focus:color
      "&.nui-select-focus": {
        ".nui-select": {
          //Focus
          [`@apply focus:!border-${config2.select.focus.border.color.light} dark:focus:!border-${config2.select.focus.border.color.dark}`]:
            {},
          //Force focus
          [`@apply focus:hover:!border-${config2.select.focus.border.color.light} dark:focus:hover:!border-${config2.select.focus.border.color.dark}`]:
            {},
        },
      },
      //Loaded
      "&:not(.nui-select-loading)": {
        ".nui-select ~ .nui-label-float": {
          "@apply text-transparent": {},
        },
      },
      //Loading
      "&.nui-select-loading": {
        ".nui-select": {
          "@apply !text-transparent placeholder:!text-transparent dark:placeholder:!text-transparent":
            {},
        },
        ".nui-select ~ .nui-label-float": {
          "@apply text-transparent": {},
        },
        ".nui-select-icon": {
          "@apply opacity-0": {},
        },
      },
      //Label:float
      "&.nui-select-label-float": {
        ".nui-select": {
          "@apply placeholder:text-transparent dark:placeholder:text-transparent":
            {},
        },
      },
      //Select:error
      "&.nui-select-error": {
        ".nui-select": {
          [`@apply !border-${config2.error.select.border.light} dark:!border-${config2.error.select.border.dark}`]:
            {},
        },
        //Select:icon
        ".nui-select-icon": {
          [`@apply !text-${config2.error.icon.color.light} dark:!text-${config2.error.icon.color.dark}`]:
            {},
        },
      },
      //Without:icon && Size:sm
      "&:not(.nui-has-icon).nui-select-sm": {
        ".nui-select": {
          [`@apply h-8 py-1 text-${config2.icon.disabled.select.sm.font.size} leading-4 ps-2 pe-7`]:
            {},
        },
      },
      //With:icon && Size:sm
      "&.nui-has-icon.nui-select-sm": {
        ".nui-select": {
          [`@apply h-8 py-1 text-${config2.icon.enabled.select.sm.font.size} leading-4 pe-7 ps-8`]:
            {},
        },
      },
      //Without:icon && Size:md
      "&:not(.nui-has-icon).nui-select-md": {
        ".nui-select": {
          [`@apply h-10 py-2 text-${config2.icon.disabled.select.md.font.size} leading-5 ps-3 pe-8`]:
            {},
        },
      },
      //With:icon && Size:md
      "&.nui-has-icon.nui-select-md": {
        ".nui-select": {
          [`@apply h-10 py-2 text-${config2.icon.enabled.select.md.font.size} leading-5 pe-8 ps-10`]:
            {},
        },
      },
      //Without:icon && Size:lg
      "&:not(.nui-has-icon).nui-select-lg": {
        ".nui-select": {
          [`@apply h-12 py-2 text-${config2.icon.disabled.select.lg.font.size} leading-5 ps-4 pe-9`]:
            {},
        },
      },
      //With:icon && Size:lg
      "&.nui-has-icon.nui-select-lg": {
        ".nui-select": {
          [`@apply h-12 py-2 text-${config2.icon.enabled.select.lg.font.size} leading-5 pe-9 ps-11`]:
            {},
        },
      },
      //Without:icon && Size:xl
      "&:not(.nui-has-icon).nui-select-xl": {
        ".nui-select": {
          [`@apply h-14 py-2 text-${config2.icon.disabled.select.xl.font.size} leading-5 ps-4 pe-9`]:
            {},
        },
      },
      //With:icon && Size:xl
      "&.nui-has-icon.nui-select-xl": {
        ".nui-select": {
          [`@apply h-14 py-2 text-${config2.icon.enabled.select.xl.font.size} leading-5 pe-9 ps-12`]:
            {},
        },
      },
      //Without:icon && Size:sm && Label:float
      "&.nui-select-label-float:not(.nui-has-icon).nui-select-sm": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-7 text-${config2.icon.disabled.label.float.sm.font.size}`]:
            {},
        },
        ".nui-select:focus-visible ~ .nui-label-float": {
          "@apply !-ms-3 !-mt-7": {},
        },
        ".nui-select ~ .nui-label-float": {
          "@apply ms-0 mt-0": {},
        },
      },
      //With:icon && Size:sm && Label:float
      "&.nui-select-label-float.nui-has-icon.nui-select-sm": {
        ".nui-label-float": {
          [`@apply start-8 -ms-8 -mt-7 text-${config2.icon.enabled.label.float.sm.font.size}`]:
            {},
        },
        ".nui-select:focus-visible ~ .nui-label-float": {
          "@apply !-ms-8 !-mt-7": {},
        },
        ".nui-select ~ .nui-label-float": {
          "@apply ms-0 mt-0": {},
        },
      },
      //Without:icon && Size:md && Label:float
      "&.nui-select-label-float:not(.nui-has-icon).nui-select-md": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-8 text-${config2.icon.disabled.label.float.md.font.size.base}`]:
            {},
        },
        ".nui-select:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-8 !text-${config2.icon.disabled.label.float.md.font.size.focus}`]:
            {},
        },
        ".nui-select ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.md.font.size.unfocus}`]:
            {},
        },
      },
      //With:icon && Size:md && Label:float
      "&.nui-select-label-float.nui-has-icon.nui-select-md": {
        ".nui-label-float": {
          [`@apply start-10 -ms-10 -mt-8 text-${config2.icon.enabled.label.float.md.font.size.base}`]:
            {},
        },
        ".nui-select:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-10 !-mt-8 !text-${config2.icon.enabled.label.float.md.font.size.focus}`]:
            {},
        },
        ".nui-select ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.md.font.size.unfocus}`]:
            {},
        },
      },
      //Without:icon && Size:lg && Label:float
      "&.nui-select-label-float:not(.nui-has-icon).nui-select-lg": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-9 text-${config2.icon.disabled.label.float.lg.font.size.base}`]:
            {},
        },
        ".nui-select:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-9 !text-${config2.icon.disabled.label.float.lg.font.size.focus}`]:
            {},
        },
        ".nui-select ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.lg.font.size.unfocus}`]:
            {},
        },
      },
      //With:icon && Size:lg && Label:float
      "&.nui-select-label-float.nui-has-icon.nui-select-lg": {
        ".nui-label-float": {
          [`@apply start-11 -ms-10 -mt-9 text-${config2.icon.enabled.label.float.lg.font.size.base}`]:
            {},
        },
        ".nui-select:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-10 !-mt-9 !text-${config2.icon.enabled.label.float.lg.font.size.focus}`]:
            {},
        },
        ".nui-select ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.lg.font.size.unfocus}`]:
            {},
        },
      },
      //Without:icon && Size:xl && Label:float
      "&.nui-select-label-float:not(.nui-has-icon).nui-select-xl": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-10 text-${config2.icon.disabled.label.float.xl.font.size.base}`]:
            {},
        },
        ".nui-select:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-10 !text-${config2.icon.disabled.label.float.xl.font.size.focus}`]:
            {},
        },
        ".nui-select ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.xl.font.size.unfocus}`]:
            {},
        },
      },
      //With:icon && Size:xl && Label:float
      "&.nui-select-label-float.nui-has-icon.nui-select-xl": {
        ".nui-label-float": {
          [`@apply start-[3.25rem] -ms-[3.25rem] -mt-10 text-${config2.icon.enabled.label.float.xl.font.size.base}`]:
            {},
        },
        ".nui-select:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-[3.25rem] !-mt-10 !text-${config2.icon.enabled.label.float.xl.font.size.focus}`]:
            {},
        },
        ".nui-select ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.xl.font.size.unfocus}`]:
            {},
        },
      },
    },
  });
}, config$d);

module.export = select;

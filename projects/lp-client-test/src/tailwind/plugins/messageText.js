import plugin from "tailwindcss/plugin";

const key$o = "messageText";
const defaultConfig$n = {
  padding: "5",
  rounded: {
    sm: "rounded-md",
    md: "rounded-lg",
    lg: "rounded-xl",
  },
  header: {
    gap: "2",
  },
  dot: {
    size: "2",
    rounded: "full",
  },
  close: {
    position: "2",
  },
  contrast: {
    lowContrast: {
      background: {
        light: "white",
        dark: "muted-800",
      },
      border: {
        light: "muted-300",
        dark: "muted-700",
      },
    },
    highContrast: {
      background: {
        light: "white",
        dark: "muted-950",
      },
      border: {
        light: "muted-300",
        dark: "muted-800",
      },
    },
  },
  color: {
    default: {
      dot: {
        background: {
          light: "muted-300",
          dark: "muted-700",
        },
      },
    },
    primary: {
      dot: {
        background: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    info: {
      dot: {
        background: {
          light: "info-500",
          dark: "info-500",
        },
      },
    },
    success: {
      dot: {
        background: {
          light: "success-500",
          dark: "success-500",
        },
      },
    },
    warning: {
      dot: {
        background: {
          light: "warning-500",
          dark: "warning-500",
        },
      },
    },
    danger: {
      dot: {
        background: {
          light: "danger-500",
          dark: "danger-500",
        },
      },
    },
    dark: {
      dot: {
        background: {
          light: "muted-900",
          dark: "muted-100",
        },
      },
    },
    black: {
      dot: {
        background: {
          light: "black",
          dark: "white",
        },
      },
    },
  },
};

const config$o = {
  theme: {
    nui: {
      [key$o]: defaultConfig$n,
    },
  },
};
const messageText = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$o}`);
  addComponents({
    //Wrapper
    ".nui-message-text": {
      [`@apply relative p-${config2.padding}`]: {},
      //Message:header
      ".nui-message-head": {
        [`@apply flex items-center gap-${config2.header.gap} mb-3`]: {},
      },
      //Message:dot
      ".nui-message-dot": {
        //Base
        [`@apply inline-block h-${config2.dot.size} w-${config2.dot.size} rounded-${config2.dot.rounded}`]:
          {},
      },
      //Message:close
      ".nui-message-close": {
        [`@apply absolute top-${config2.close.position} end-${config2.close.position}`]:
          {},
      },
      //Rounded:sm
      "&.nui-message-rounded-sm": {
        [`@apply ${config2.rounded.sm}`]: {},
      },
      //Rounded:md
      "&.nui-message-rounded-md": {
        [`@apply ${config2.rounded.md}`]: {},
      },
      //Rounded:lg
      "&.nui-message-rounded-lg": {
        [`@apply ${config2.rounded.lg}`]: {},
      },
      //Contrast:low
      "&.nui-message-low-contrast": {
        //Background
        [`@apply bg-${config2.contrast.lowContrast.background.light} dark:bg-${config2.contrast.lowContrast.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.contrast.lowContrast.border.light} dark:border-${config2.contrast.lowContrast.border.dark}`]:
          {},
      },
      //Contrast:high
      "&.nui-message-high-contrast": {
        //Background
        [`@apply bg-${config2.contrast.highContrast.background.light} dark:bg-${config2.contrast.highContrast.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.contrast.highContrast.border.light} dark:border-${config2.contrast.highContrast.border.dark}`]:
          {},
      },
      //Color:white
      "&.nui-message-default": {
        //Message:dot
        ".nui-message-dot": {
          [`@apply bg-${config2.color.default.dot.background.light} dark:bg-${config2.color.default.dot.background.dark}`]:
            {},
        },
      },
      //Color:primary
      "&.nui-message-primary": {
        //Message:dot
        ".nui-message-dot": {
          [`@apply bg-${config2.color.primary.dot.background.light} dark:bg-${config2.color.primary.dot.background.dark}`]:
            {},
        },
      },
      //Color:info
      "&.nui-message-info": {
        //Message:dot
        ".nui-message-dot": {
          [`@apply bg-${config2.color.info.dot.background.light} dark:bg-${config2.color.info.dot.background.dark}`]:
            {},
        },
      },
      //Color:success
      "&.nui-message-success": {
        //Message:dot
        ".nui-message-dot": {
          [`@apply bg-${config2.color.success.dot.background.light} dark:bg-${config2.color.success.dot.background.dark}`]:
            {},
        },
      },
      //Color:warning
      "&.nui-message-warning": {
        //Message:dot
        ".nui-message-dot": {
          [`@apply bg-${config2.color.warning.dot.background.light} dark:bg-${config2.color.warning.dot.background.dark}`]:
            {},
        },
      },
      //Color:danger
      "&.nui-message-danger": {
        //Message:dot
        ".nui-message-dot": {
          [`@apply bg-${config2.color.danger.dot.background.light} dark:bg-${config2.color.danger.dot.background.dark}`]:
            {},
        },
      },
      //Color:dark
      "&.nui-message-dark": {
        //Message:dot
        ".nui-message-dot": {
          [`@apply bg-${config2.color.dark.dot.background.light} dark:bg-${config2.color.dark.dot.background.dark}`]:
            {},
        },
      },
      //Color:black
      "&.nui-message-black": {
        //Message:dot
        ".nui-message-dot": {
          [`@apply bg-${config2.color.black.dot.background.light} dark:bg-${config2.color.black.dot.background.dark}`]:
            {},
        },
      },
    },
  });
}, config$o);

module.export = messageText;

import plugin from "tailwindcss/plugin";
const key$5 = "text";
const defaultConfig$5 = {
  size: {
    xs: "xs",
    sm: "sm",
    md: "base",
    lg: "lg",
    xl: "xl",
    xxl: "2xl",
    xxxl: "3xl",
    xxxxl: "4xl",
    xxxxxl: "5xl",
    xxxxxxl: "6xl",
    xxxxxxxl: "7xl",
    xxxxxxxxl: "8xl",
    xxxxxxxxxl: "9xl",
  },
  weight: {
    light: "light",
    normal: "normal",
    medium: "medium",
    semibold: "semibold",
    bold: "bold",
    extrabold: "extrabold",
  },
  lead: {
    none: "none",
    normal: "normal",
    tight: "tight",
    snug: "snug",
    loose: "loose",
  },
};

const config$5 = {
  theme: {
    nui: {
      [key$5]: defaultConfig$5,
    },
  },
};
const text = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$5}`);
  addComponents({
    ".nui-text": {
      "@apply font-sans": {},
      //Size
      "&.nui-content-xs": {
        [`@apply text-${config2.size.xs}`]: {},
      },
      "&.nui-content-sm": {
        [`@apply text-${config2.size.sm}`]: {},
      },
      "&.nui-content-md": {
        [`@apply text-${config2.size.md}`]: {},
      },
      "&.nui-content-lg": {
        [`@apply text-${config2.size.lg}`]: {},
      },
      "&.nui-content-xl": {
        [`@apply text-${config2.size.xl}`]: {},
      },
      "&.nui-content-2xl": {
        [`@apply text-${config2.size.xxl}`]: {},
      },
      "&.nui-content-3xl": {
        [`@apply text-${config2.size.xxxl}`]: {},
      },
      "&.nui-content-4xl": {
        [`@apply text-${config2.size.xxxxl}`]: {},
      },
      "&.nui-content-5xl": {
        [`@apply text-${config2.size.xxxxxl}`]: {},
      },
      "&.nui-content-6xl": {
        [`@apply text-${config2.size.xxxxxxl}`]: {},
      },
      "&.nui-content-7xl": {
        [`@apply text-${config2.size.xxxxxxxl}`]: {},
      },
      "&.nui-content-8xl": {
        [`@apply text-${config2.size.xxxxxxxxl}`]: {},
      },
      "&.nui-content-9xl": {
        [`@apply text-${config2.size.xxxxxxxxxl}`]: {},
      },
      //Weight
      "&.nui-weight-light": {
        [`@apply font-${config2.weight.light}`]: {},
      },
      "&.nui-weight-normal": {
        [`@apply font-${config2.weight.normal}`]: {},
      },
      "&.nui-weight-medium": {
        [`@apply font-${config2.weight.medium}`]: {},
      },
      "&.nui-weight-semibold": {
        [`@apply font-${config2.weight.semibold}`]: {},
      },
      "&.nui-weight-bold": {
        [`@apply font-${config2.weight.bold}`]: {},
      },
      "&.nui-weight-extrabold": {
        [`@apply font-${config2.weight.extrabold}`]: {},
      },
      //Lead
      "&.nui-lead-none": {
        [`@apply leading-${config2.lead.none}`]: {},
      },
      "&.nui-lead-normal": {
        [`@apply leading-${config2.lead.normal}`]: {},
      },
      "&.nui-lead-tight": {
        [`@apply leading-${config2.lead.tight}`]: {},
      },
      "&.nui-lead-snug": {
        [`@apply leading-${config2.lead.snug}`]: {},
      },
      "&.nui-lead-loose": {
        [`@apply leading-${config2.lead.loose}`]: {},
      },
    },
  });
}, config$5);

module.export = text;

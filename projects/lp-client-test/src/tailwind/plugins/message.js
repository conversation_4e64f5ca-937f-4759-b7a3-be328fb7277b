import plugin from "tailwindcss/plugin";
const key$n = "message";
const defaultConfig$m = {
  safeArea: {
    end: "6",
  },
  icon: {
    outer: {
      size: "10",
    },
    inner: {
      size: "5",
      color: {
        light: "white",
        dark: "white",
      },
    },
  },
  inner: {
    font: {
      family: "sans",
      size: "sm",
      color: {
        light: "muted-800",
        dark: "muted-200",
      },
    },
  },
  close: {
    color: {
      light: "muted-800",
      dark: "muted-800",
    },
    icon: {
      size: "4",
    },
    position: {
      top: "[-0.5rem]",
      end: "[-0.5rem]",
    },
    size: {
      outer: {
        width: "8",
        height: "8",
      },
      inner: {
        width: "6",
        height: "6",
      },
    },
    rounded: "rounded-full",
    transition: {
      property: "colors",
      duration: "200",
    },
  },
  rounded: {
    sm: "rounded-md",
    md: "rounded-lg",
    lg: "rounded-xl",
    full: "rounded-full",
  },
  color: {
    default: {
      background: {
        light: "white",
        dark: "muted-800",
      },
      border: {
        light: "muted-200",
        dark: "muted-700",
      },
      outer: {
        background: {
          light: "muted-600",
          dark: "muted-900",
        },
        color: {
          light: "white",
          dark: "white",
        },
      },
      inner: {
        color: {
          light: "muted-500",
          dark: "muted-400",
        },
      },
      close: {
        color: {
          light: "muted-500",
          dark: "muted-500",
        },
        background: {
          hover: {
            light: "muted-300/50",
            dark: "muted-500/30",
          },
          focus: {
            light: "muted-300/50",
            dark: "muted-500/30",
          },
          active: {
            light: "muted-300/20",
            dark: "muted-500/20",
          },
        },
      },
    },
    defaultContrast: {
      background: {
        light: "white",
        dark: "muted-950",
      },
      border: {
        light: "muted-200",
        dark: "muted-800",
      },
      outer: {
        background: {
          light: "muted-600",
          dark: "muted-900",
        },
        color: {
          light: "white",
          dark: "white",
        },
      },
      inner: {
        color: {
          light: "muted-500",
          dark: "muted-400",
        },
      },
      close: {
        color: {
          light: "muted-500",
          dark: "muted-500",
        },
        background: {
          hover: {
            light: "muted-300/50",
            dark: "muted-500/30",
          },
          focus: {
            light: "muted-300/50",
            dark: "muted-500/30",
          },
          active: {
            light: "muted-300/20",
            dark: "muted-500/20",
          },
        },
      },
    },
    muted: {
      background: {
        light: "muted-100",
        dark: "muted-500/10",
      },
      border: {
        light: "muted-200",
        dark: "muted-700",
      },
      outer: {
        background: {
          light: "muted-400",
          dark: "muted-900",
        },
        color: {
          light: "white",
          dark: "white",
        },
      },
      inner: {
        color: {
          light: "muted-500",
          dark: "muted-500",
        },
      },
      close: {
        color: {
          light: "muted-500",
          dark: "muted-500",
        },
        background: {
          hover: {
            light: "muted-300/50",
            dark: "muted-500/30",
          },
          focus: {
            light: "muted-300/50",
            dark: "muted-500/30",
          },
          active: {
            light: "muted-300/20",
            dark: "muted-500/20",
          },
        },
      },
    },
    mutedContrast: {
      background: {
        light: "muted-100",
        dark: "muted-950",
      },
      border: {
        light: "muted-200",
        dark: "muted-800",
      },
      outer: {
        background: {
          light: "muted-400",
          dark: "muted-900",
        },
        color: {
          light: "white",
          dark: "white",
        },
      },
      inner: {
        color: {
          light: "muted-500",
          dark: "muted-500",
        },
      },
      close: {
        color: {
          light: "muted-500",
          dark: "muted-500",
        },
        background: {
          hover: {
            light: "muted-300/50",
            dark: "muted-500/30",
          },
          focus: {
            light: "muted-300/50",
            dark: "muted-500/30",
          },
          active: {
            light: "muted-300/20",
            dark: "muted-500/20",
          },
        },
      },
    },
    primary: {
      background: {
        light: "primary-100",
        dark: "primary-500/10",
      },
      border: {
        light: "primary-200",
        dark: "primary-700",
      },
      outer: {
        background: {
          light: "primary-500",
          dark: "primary-500",
        },
        color: {
          light: "white",
          dark: "white",
        },
      },
      inner: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
      close: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
        background: {
          hover: {
            light: "primary-300/50",
            dark: "primary-500/30",
          },
          focus: {
            light: "primary-300/50",
            dark: "primary-500/30",
          },
          active: {
            light: "primary-300/20",
            dark: "primary-500/20",
          },
        },
      },
    },
    info: {
      background: {
        light: "info-100",
        dark: "info-500/10",
      },
      border: {
        light: "info-200",
        dark: "info-700",
      },
      outer: {
        background: {
          light: "info-500",
          dark: "info-500",
        },
        color: {
          light: "white",
          dark: "white",
        },
      },
      inner: {
        color: {
          light: "info-500",
          dark: "info-500",
        },
      },
      close: {
        color: {
          light: "info-500",
          dark: "info-500",
        },
        background: {
          hover: {
            light: "info-300/50",
            dark: "info-500/30",
          },
          focus: {
            light: "info-300/50",
            dark: "info-500/30",
          },
          active: {
            light: "info-300/20",
            dark: "info-500/20",
          },
        },
      },
    },
    success: {
      background: {
        light: "success-100",
        dark: "success-500/10",
      },
      border: {
        light: "success-200",
        dark: "success-700",
      },
      outer: {
        background: {
          light: "success-500",
          dark: "success-500",
        },
        color: {
          light: "white",
          dark: "white",
        },
      },
      inner: {
        color: {
          light: "success-500",
          dark: "success-500",
        },
      },
      close: {
        color: {
          light: "success-500",
          dark: "success-500",
        },
        background: {
          hover: {
            light: "success-300/50",
            dark: "success-500/30",
          },
          focus: {
            light: "success-300/50",
            dark: "success-500/30",
          },
          active: {
            light: "success-300/20",
            dark: "success-500/20",
          },
        },
      },
    },
    warning: {
      background: {
        light: "warning-100",
        dark: "warning-500/10",
      },
      border: {
        light: "warning-200",
        dark: "warning-700",
      },
      outer: {
        background: {
          light: "warning-500",
          dark: "warning-500",
        },
        color: {
          light: "white",
          dark: "white",
        },
      },
      inner: {
        color: {
          light: "warning-500",
          dark: "warning-500",
        },
      },
      close: {
        color: {
          light: "warning-500",
          dark: "warning-500",
        },
        background: {
          hover: {
            light: "warning-300/50",
            dark: "warning-500/30",
          },
          focus: {
            light: "warning-300/50",
            dark: "warning-500/30",
          },
          active: {
            light: "warning-300/20",
            dark: "warning-500/20",
          },
        },
      },
    },
    danger: {
      background: {
        light: "danger-100",
        dark: "danger-500/10",
      },
      border: {
        light: "danger-200",
        dark: "danger-700",
      },
      outer: {
        background: {
          light: "danger-500",
          dark: "danger-500",
        },
        color: {
          light: "white",
          dark: "white",
        },
      },
      inner: {
        color: {
          light: "danger-500",
          dark: "danger-500",
        },
      },
      close: {
        color: {
          light: "danger-500",
          dark: "danger-500",
        },
        background: {
          hover: {
            light: "danger-300/50",
            dark: "danger-500/30",
          },
          focus: {
            light: "danger-300/50",
            dark: "danger-500/30",
          },
          active: {
            light: "danger-300/20",
            dark: "danger-500/20",
          },
        },
      },
    },
    // messageMuted: {
    //   bg: 'muted-100',
    //   bgDark: 'muted-500/10',
    //   border: 'muted-200',
    //   borderDark: 'muted-700',
    //   outer: {
    //     bg: 'muted-400',
    //     text: 'white',
    //     bgDark: 'muted-900',
    //   },
    //   txtDarkInner: 'muted-500',
    //   close: {
    //     textDark: 'muted-500',
    //     bgHoverEnabled: 'muted-300/50',
    //     bgHoverEnabledDark: 'muted-500/30',
    //     bgFocusVisible: 'muted-300/50',
    //     bgFocusVisibleDark: 'muted-500/30',
    //     bgActiveEnabled: 'muted-300/20',
    //     bgActiveEnabledDark: 'muted-500/20',
    //   },
    // },
    // messagePrimary: {
    //   bg: 'primary-100',
    //   bgDark: 'primary-500/10',
    //   border: 'primary-200',
    //   borderDark: 'primary-700',
    //   outer: {
    //     bg: 'primary-500',
    //     text: 'white',
    //   },
    //   txtDarkInner: 'primary-500',
    //   close: {
    //     textDark: 'primary-500',
    //     bgHoverEnabled: 'primary-300/50',
    //     bgHoverEnabledDark: 'primary-500/30',
    //     bgFocusVisible: 'primary-300/50',
    //     bgFocusVisibleDark: 'primary-500/30',
    //     bgActiveEnabled: 'primary-300/20',
    //     bgActiveEnabledDark: 'primary-500/20',
    //   },
    // },
    // messageInfo: {
    //   bg: 'info-100',
    //   bgDark: 'info-500/10',
    //   border: 'info-200',
    //   borderDark: 'info-700',
    //   outer: {
    //     bg: 'info-500',
    //     text: 'white',
    //   },
    //   txtDarkInner: 'info-500',
    //   close: {
    //     textDark: 'info-500',
    //     bgHoverEnabled: 'info-300/50',
    //     bgHoverEnabledDark: 'info-500/30',
    //     bgFocusVisible: 'info-300/50',
    //     bgFocusVisibleDark: 'info-500/30',
    //     bgActiveEnabled: 'info-300/20',
    //     bgActiveEnabledDark: 'info-500/20',
    //   },
    // },
    // messageSuccess: {
    //   bg: 'success-100',
    //   bgDark: 'success-500/10',
    //   border: 'success-200',
    //   borderDark: 'success-700',
    //   outer: {
    //     bg: 'success-500',
    //     text: 'white',
    //   },
    //   txtDarkInner: 'success-500',
    //   close: {
    //     textDark: 'success-500',
    //     bgHoverEnabled: 'success-300/50',
    //     bgHoverEnabledDark: 'success-500/30',
    //     bgFocusVisible: 'success-300/50',
    //     bgFocusVisibleDark: 'success-500/30',
    //     bgActiveEnabled: 'success-300/20',
    //     bgActiveEnabledDark: 'success-500/20',
    //   },
    // },
    // messageWarning: {
    //   bg: 'warning-100',
    //   bgDark: 'warning-500/10',
    //   border: 'warning-200',
    //   borderDark: 'warning-700',
    //   outer: {
    //     bg: 'warning-500',
    //     text: 'white',
    //   },
    //   txtDarkInner: 'warning-500',
    //   close: {
    //     textDark: 'warning-500',
    //     bgHoverEnabled: 'warning-300/50',
    //     bgHoverEnabledDark: 'warning-500/30',
    //     bgFocusVisible: 'warning-300/50',
    //     bgFocusVisibleDark: 'warning-500/30',
    //     bgActiveEnabled: 'warning-300/20',
    //     bgActiveEnabledDark: 'warning-500/20',
    //   },
    // },
    // messageDanger: {
    //   bg: 'danger-100',
    //   bgDark: 'danger-500/10',
    //   border: 'danger-200',
    //   borderDark: 'danger-700',
    //   outer: {
    //     bg: 'danger-500',
    //     text: 'white',
    //   },
    //   txtDarkInner: 'danger-500',
    //   close: {
    //     textDark: 'danger-500',
    //     bgHoverEnabled: 'danger-300/50',
    //     bgHoverEnabledDark: 'danger-500/30',
    //     bgFocusVisible: 'danger-300/50',
    //     bgFocusVisibleDark: 'danger-500/30',
    //     bgActiveEnabled: 'danger-300/20',
    //     bgActiveEnabledDark: 'danger-500/20',
    //   },
    // },
  },
};
const config$n = {
  theme: {
    nui: {
      [key$n]: defaultConfig$m,
    },
  },
};

const message = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$n}`);

  const generateClasses = (config) => {
    return {
      // Wrapper
      ".nui-message": {
        "@apply relative flex gap-2 border": {},
        ".nui-message-icon-outer": {
          [`@apply flex h-${config.icon.outer.size} w-${config.icon.outer.size} shrink-0 items-center justify-center`]:
            {},
          ".nui-message-icon": {
            [`@apply h-${config.icon.inner.size} w-${config.icon.inner.size}`]:
              {},
            [`@apply text-${config.icon.inner.color.light} dark:text-${config.icon.inner.color.dark}`]:
              {},
          },
        },
        ".nui-message-inner-text": {
          "@apply inline-flex items-center leading-normal": {},
          [`@apply font-${config.inner.font.family} text-${config.inner.font.size}`]:
            {},
          [`@apply text-${config.inner.font.color.light} dark:text-${config.inner.font.color.dark}`]:
            {},
        },
        ".nui-message-close-wrapper": {
          [`@apply absolute top-${config.close.position.top} end-${config.close.position.end} flex items-center justify-center bg-white dark:bg-muted-950 border border-muted-200 dark:border-muted-800 ${config.close.rounded} h-${config.close.size.outer.height} w-${config.close.size.outer.width}`]:
            {},
        },
        ".nui-message-close": {
          [`@apply nui-focus flex cursor-pointer items-center justify-center shrink-0 h-${config.close.size.inner.height} w-${config.close.size.inner.width} ${config.close.rounded}`]:
            {},
          [`@apply text-${config.close.color.light} dark:text-${config.close.color.dark}`]:
            {},
          [`@apply transition-${config.close.transition.property} duration-${config.close.transition.duration}`]:
            {},
          ".nui-close-icon": {
            [`@apply h-${config.close.icon.size} w-${config.close.icon.size}`]:
              {},
          },
        },
        "&.nui-has-text": {
          [`@apply py-2 ps-2 pe-${config.safeArea.end}`]: {},
        },
        "&.nui-has-icon": {
          [`@apply py-1 ps-1 pe-${config.safeArea.end}`]: {},
        },
        "&.nui-message-rounded-sm": {
          [`@apply ${config.rounded.sm}`]: {},
          ".nui-message-icon-outer": {
            [`@apply ${config.rounded.sm}`]: {},
          },
        },
        "&.nui-message-rounded-md": {
          [`@apply ${config.rounded.md}`]: {},
          ".nui-message-icon-outer": {
            [`@apply ${config.rounded.md}`]: {},
          },
        },
        "&.nui-message-rounded-lg": {
          [`@apply ${config.rounded.lg}`]: {},
          ".nui-message-icon-outer": {
            [`@apply ${config.rounded.lg}`]: {},
          },
        },
        "&.nui-message-rounded-full": {
          [`@apply ${config.rounded.full}`]: {},
          ".nui-message-icon-outer": {
            [`@apply ${config.rounded.full}`]: {},
          },
        },
        "&.nui-message-default": {
          [`@apply border-${config.color.default.border.light} dark:border-${config.color.default.border.dark}`]:
            {},
          [`@apply bg-${config.color.default.background.light} dark:bg-${config.color.default.background.dark}`]:
            {},
          ".nui-message-icon-outer": {
            [`@apply text-${config.color.default.outer.color.light} dark:text-${config.color.default.outer.color.dark}`]:
              {},
            [`@apply bg-${config.color.default.outer.background.light} dark:bg-${config.color.default.outer.background.dark}`]:
              {},
          },
          ".nui-message-inner-text": {
            [`@apply text-${config.color.default.inner.color.light} dark:text-${config.color.default.inner.color.dark}`]:
              {},
          },
          ".nui-message-close": {
            [`@apply text-${config.color.default.close.color.light} dark:text-${config.color.default.close.color.dark}`]:
              {},
            [`@apply hover:enabled:bg-${config.color.default.close.background.hover.light} dark:hover:enabled:bg-${config.color.default.close.background.hover.dark}`]:
              {},
            [`@apply focus-visible:bg-${config.color.default.close.background.focus.light} dark:focus-visible:bg-${config.color.default.close.background.focus.dark}`]:
              {},
            [`@apply active:enabled:bg-${config.color.default.close.background.active.light} dark:active:enabled:bg-${config.color.default.close.background.active.dark}`]:
              {},
          },
        },
        "&.nui-message-default-contrast": {
          [`@apply border-${config.color.defaultContrast.border.light} dark:border-${config.color.defaultContrast.border.dark}`]:
            {},
          [`@apply bg-${config.color.defaultContrast.background.light} dark:bg-${config.color.defaultContrast.background.dark}`]:
            {},
          ".nui-message-icon-outer": {
            [`@apply text-${config.color.defaultContrast.outer.color.light} dark:text-${config.color.defaultContrast.outer.color.dark}`]:
              {},
            [`@apply bg-${config.color.defaultContrast.outer.background.light} dark:bg-${config.color.defaultContrast.outer.background.dark}`]:
              {},
          },
          ".nui-message-inner-text": {
            [`@apply text-${config.color.defaultContrast.inner.color.light} dark:text-${config.color.defaultContrast.inner.color.dark}`]:
              {},
          },
          ".nui-message-close": {
            [`@apply text-${config.color.defaultContrast.close.color.light} dark:text-${config.color.defaultContrast.close.color.dark}`]:
              {},
            [`@apply hover:enabled:bg-${config.color.defaultContrast.close.background.hover.light} dark:hover:enabled:bg-${config.color.defaultContrast.close.background.hover.dark}`]:
              {},
            [`@apply focus-visible:bg-${config.color.defaultContrast.close.background.focus.light} dark:focus-visible:bg-${config.color.defaultContrast.close.background.focus.dark}`]:
              {},
            [`@apply active:enabled:bg-${config.color.defaultContrast.close.background.active.light} dark:active:enabled:bg-${config.color.defaultContrast.close.background.active.dark}`]:
              {},
          },
        },
        "&.nui-message-muted": {
          [`@apply border-${config.color.muted.border.light} dark:border-${config.color.muted.border.dark}`]:
            {},
          [`@apply bg-${config.color.muted.background.light} dark:bg-${config.color.muted.background.dark}`]:
            {},
          ".nui-message-icon-outer": {
            [`@apply text-${config.color.muted.outer.color.light} dark:text-${config.color.muted.outer.color.dark}`]:
              {},
            [`@apply bg-${config.color.muted.outer.background.light} dark:bg-${config.color.muted.outer.background.dark}`]:
              {},
          },
          ".nui-message-inner-text": {
            [`@apply text-${config.color.muted.inner.color.light} dark:text-${config.color.muted.inner.color.dark}`]:
              {},
          },
          ".nui-message-close": {
            [`@apply text-${config.color.muted.close.color.light} dark:text-${config.color.muted.close.color.dark}`]:
              {},
            [`@apply hover:enabled:bg-${config.color.muted.close.background.hover.light} dark:hover:enabled:bg-${config.color.muted.close.background.hover.dark}`]:
              {},
            [`@apply focus-visible:bg-${config.color.muted.close.background.focus.light} dark:focus-visible:bg-${config.color.muted.close.background.focus.dark}`]:
              {},
            [`@apply active:enabled:bg-${config.color.muted.close.background.active.light} dark:active:enabled:bg-${config.color.muted.close.background.active.dark}`]:
              {},
          },
        },
        "&.nui-message-muted-contrast": {
          [`@apply border-${config.color.mutedContrast.border.light} dark:border-${config.color.mutedContrast.border.dark}`]:
            {},
          [`@apply bg-${config.color.mutedContrast.background.light} dark:bg-${config.color.mutedContrast.background.dark}`]:
            {},
          ".nui-message-icon-outer": {
            [`@apply text-${config.color.mutedContrast.outer.color.light} dark:text-${config.color.mutedContrast.outer.color.dark}`]:
              {},
            [`@apply bg-${config.color.mutedContrast.outer.background.light} dark:bg-${config.color.mutedContrast.outer.background.dark}`]:
              {},
          },
          ".nui-message-inner-text": {
            [`@apply text-${config.color.mutedContrast.inner.color.light} dark:text-${config.color.mutedContrast.inner.color.dark}`]:
              {},
          },
          ".nui-message-close": {
            [`@apply text-${config.color.mutedContrast.close.color.light} dark:text-${config.color.mutedContrast.close.color.dark}`]:
              {},
            [`@apply hover:enabled:bg-${config.color.mutedContrast.close.background.hover.light} dark:hover:enabled:bg-${config.color.mutedContrast.close.background.hover.dark}`]:
              {},
            [`@apply focus-visible:bg-${config.color.mutedContrast.close.background.focus.light} dark:focus-visible:bg-${config.color.mutedContrast.close.background.focus.dark}`]:
              {},
            [`@apply active:enabled:bg-${config.color.mutedContrast.close.background.active.light} dark:active:enabled:bg-${config.color.mutedContrast.close.background.active.dark}`]:
              {},
          },
        },
      },
    };
  };

  addComponents(generateClasses(config2));
});

module.exports = message;

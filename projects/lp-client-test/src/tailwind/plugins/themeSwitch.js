import plugin from "tailwindcss/plugin";

const key$3 = "themeSwitch";
const defaultConfig$3 = {
  outer: {
    rounded: "full",
    backgound: {
      light: "muted-200",
      dark: "muted-700",
    },
  },
  inner: {
    size: "10",
    rounded: "full",
    background: {
      light: "white",
      dark: "muted-900",
    },
    border: {
      light: "muted-200",
      dark: "muted-800",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  input: {
    size: "full",
  },
  icon: {
    sun: {
      size: "6",
      color: "yellow-400",
      duration: "300",
    },
    moon: {
      size: "6",
      color: "yellow-400",
      duration: "300",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  inverted: {
    enabled: {
      ring: {
        light: "muted-500",
        dark: "muted-400",
      },
      inner: {
        background: {
          light: "primary-700",
          dark: "primary-700",
        },
      },
    },
    disabled: {
      ring: {
        light: "muted-500",
        dark: "muted-900",
      },
      inner: {
        background: {
          light: "white",
          dark: "muted-800",
        },
        border: {
          light: "muted-300",
          dark: "muted-700",
        },
      },
    },
  },
};

const config$3 = {
  theme: {
    nui: {
      [key$3]: defaultConfig$3,
    },
  },
};
const themeSwitch = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$3}`);
  addComponents({
    //Wrapper
    ".nui-theme-switch": {
      [`@apply relative block h-6 w-14 scale-[0.8] rounded-${config2.outer.rounded}`]:
        {},
      //Background
      [`@apply bg-${config2.outer.backgound.light} dark:bg-${config2.outer.backgound.dark}`]:
        {},
      //Toggle:input
      ".nui-theme-switch-input": {
        [`@apply absolute start-0 top-0 z-10 h-${config2.input.size} w-${config2.input.size} cursor-pointer opacity-0`]:
          {},
      },
      //Toggle:inner
      ".nui-theme-switch-inner": {
        //Base
        "@apply absolute -start-1 -top-2 -ms-1 flex items-center justify-center peer-checked:ms-[45%] peer-checked:rotate-[360deg]":
          {},
        //Size
        [`@apply h-${config2.inner.size} w-${config2.inner.size} rounded-${config2.inner.rounded}`]:
          {},
        //Background
        [`@apply bg-${config2.inner.background.light} dark:bg-${config2.inner.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.inner.border.light} dark:border-${config2.inner.border.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.inner.transition.property} duration-${config2.inner.transition.duration}`]:
          {},
      },
      //Icon:sun
      ".nui-sun": {
        //Base
        "@apply pointer-events-none": {},
        //Color
        [`@apply text-${config2.icon.sun.color} dark:text-${config2.icon.sun.color}`]:
          {},
        //Size
        [`@apply h-${config2.icon.sun.size} w-${config2.icon.sun.size}`]: {},
        //Transition
        [`@apply transition-${config2.icon.transition.property} duration-${config2.icon.transition.duration}`]:
          {},
      },
      //Icon:moon
      ".nui-moon": {
        //Base
        "@apply pointer-events-none": {},
        //Color
        [`@apply text-${config2.icon.moon.color} dark:text-${config2.icon.moon.color}`]:
          {},
        //Size
        [`@apply h-${config2.icon.moon.size} w-${config2.icon.moon.size}`]: {},
        //Transition
        [`@apply transition-${config2.icon.transition.property} duration-${config2.icon.transition.duration}`]:
          {},
      },
      //Toggle:checked:inner
      ".nui-theme-switch-input:checked ~ .nui-theme-switch-inner": {
        "@apply ms-[45%] rotate-[360deg]": {},
      },
      //Toggle:not-checked:sun
      ".nui-theme-switch-input:not(:checked) ~ .nui-theme-switch-inner .nui-sun":
        {
          "@apply block": {},
        },
      //Toggle:checked:sun
      ".nui-theme-switch-input:checked ~ .nui-theme-switch-inner .nui-sun": {
        "@apply hidden": {},
      },
      //Toggle:not-checked:moon
      ".nui-theme-switch-input:not(:checked) ~ .nui-theme-switch-inner .nui-moon":
        {
          "@apply hidden": {},
        },
      //Toggle:checked:moon
      ".nui-theme-switch-input:checked ~ .nui-theme-switch-inner .nui-moon": {
        "@apply block": {},
      },
      //Toogle:inverted
      "&.nui-theme-switch-inverted": {
        [`@apply ring-offset-${config2.inverted.enabled.ring.light} dark:ring-offset-${config2.inverted.enabled.ring.dark}`]:
          {},
        ".nui-theme-switch-inner": {
          [`@apply bg-${config2.inverted.enabled.inner.background.light} dark:bg-${config2.inverted.enabled.inner.background.dark}`]:
            {},
        },
      },
      //Toggle:not-inverted
      "&:not(nui-theme-switch-inverted)": {
        //Ring
        [`@apply ring-offset-${config2.inverted.disabled.ring.light} dark:ring-offset-${config2.inverted.disabled.ring.dark}`]:
          {},
        ".nui-theme-switch-inner": {
          //Border
          [`@apply border border-${config2.inverted.disabled.inner.border.light} dark:border-${config2.inverted.disabled.inner.border.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.inverted.disabled.inner.background.light} dark:bg-${config2.inverted.disabled.inner.background.dark}`]:
            {},
        },
      },
    },
  });
}, config$3);

module.export = themeSwitch;

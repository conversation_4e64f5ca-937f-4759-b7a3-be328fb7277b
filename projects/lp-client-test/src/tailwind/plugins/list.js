import plugin from "tailwindcss/plugin";

const key$s = "list";
const defaultConfig$r = {
  ul: "disc",
  ol: "decimal",
  base: {
    font: {
      family: "sans",
      color: {
        marker: {
          light: "muted-500",
          dark: "muted-400",
        },
        text: {
          light: "muted-700",
          dark: "muted-300",
        },
      },
    },
  },
  media: {
    marker: {
      color: {
        light: "muted-500",
        dark: "muted-400",
      },
    },
  },
};

const config$s = {
  theme: {
    nui: {
      [key$s]: defaultConfig$r,
    },
  },
};
const list = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$s}`);
  addComponents({
    ".nui-list": {
      "&.nui-list-ul": {
        [`@apply list-${config2.ul}`]: {},
      },
      "&.nui-list-ol": {
        [`@apply list-${config2.ol}`]: {},
      },
      "&.nui-list-base": {
        //Base
        [`@apply space-y-1 font-${config2.base.font.family}`]: {},
        //Text
        [`@apply text-${config2.base.font.color.text.light} dark:text-${config2.base.font.color.text.dark}`]:
          {},
        //Marker
        [`@apply marker:text-${config2.base.font.color.marker.light} dark:marker:text-${config2.base.font.color.marker.dark}`]:
          {},
      },
      "&.nui-list-media": {
        [`@apply space-y-4 marker:text-${config2.media.marker.color.light} dark:marker:text-${config2.media.marker.color.dark}`]:
          {},
        ".nui-list-item": {
          "@apply flex gap-2": {},
        },
      },
    },
  });
}, config$s);

module.export = list;

import plugin from "tailwindcss/plugin";

const key$g = "progress";
const defaultConfig$f = {
  width: "full",
  rounded: {
    sm: "rounded",
    md: "rounded-md",
    lg: "rounded-lg",
    full: "rounded-full",
  },
  bar: {
    color: {
      primary: "primary-500",
      info: "info-500",
      success: "success-500",
      warning: "warning-500",
      danger: "danger-500",
      light: "bg-muted-500 dark:bg-muted-400",
      dark: "bg-muted-900 dark:bg-muted-100",
      black: "bg-black dark:bg-white",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  color: {
    default: {
      background: {
        light: "muted-200",
        dark: "muted-700",
      },
    },
    defaultContrast: {
      background: {
        light: "muted-200",
        dark: "muted-900",
      },
    },
  },
  size: {
    xs: "1",
    sm: "2",
    md: "3",
    lg: "3",
    xl: "5",
  },
};

const config$g = {
  theme: {
    nui: {
      [key$g]: defaultConfig$f,
    },
    extend: {
      keyframes: {
        "nui-progress-indeterminate": {
          "0%": { "margin-left": "-100%" },
          "60%": { "margin-left": "100%" },
          "100%": { "margin-left": "-100%" },
        },
      },
      animation: {
        "nui-progress-indeterminate": `nui-progress-indeterminate 3s linear infinite forwards`,
      },
    },
  },
};
const progress = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$g}`);
  addComponents({
    ".nui-progress": {
      [`@apply relative w-${config2.width} overflow-hidden`]: {},
      //Bar
      ".nui-progress-bar": {
        [`@apply absolute start-0 top-0 h-full transition-${config2.bar.transition.property} duration-${config2.bar.transition.duration}`]:
          {},
      },
      //Color
      "&.nui-progress-default": {
        [`@apply bg-${config2.color.default.background.light} dark:bg-${config2.color.default.background.dark}`]:
          {},
      },
      "&.nui-progress-contrast": {
        [`@apply bg-${config2.color.defaultContrast.background.light} dark:bg-${config2.color.defaultContrast.background.dark}`]:
          {},
      },
      //Size
      "&.nui-progress-xs": {
        [`@apply h-${config2.size.xs}`]: {},
      },
      "&.nui-progress-sm": {
        [`@apply h-${config2.size.sm}`]: {},
      },
      "&.nui-progress-md": {
        [`@apply h-${config2.size.md}`]: {},
      },
      "&.nui-progress-lg": {
        [`@apply h-${config2.size.lg}`]: {},
      },
      "&.nui-progress-xl": {
        [`@apply h-${config2.size.xl}`]: {},
      },
      //Rounded
      "&.nui-progress-rounded-sm, &.nui-progress-rounded-sm .nui-progress-bar":
        {
          [`@apply ${config2.rounded.sm}`]: {},
        },
      "&.nui-progress-rounded-md, &.nui-progress-rounded-md .nui-progress-bar":
        {
          [`@apply ${config2.rounded.md}`]: {},
        },
      "&.nui-progress-rounded-lg, &.nui-progress-rounded-lg .nui-progress-bar":
        {
          [`@apply ${config2.rounded.lg}`]: {},
        },
      "&.nui-progress-rounded-full, &.nui-progress-rounded-full .nui-progress-bar":
        {
          [`@apply ${config2.rounded.full}`]: {},
        },
      "&.nui-progress-indeterminate .nui-progress-bar": {
        "@apply w-full": {},
      },
      //Bar color
      "&.nui-progress-primary": {
        ".nui-progress-bar": {
          [`@apply bg-${config2.bar.color.primary}`]: {},
        },
      },
      "&.nui-progress-info": {
        ".nui-progress-bar": {
          [`@apply bg-${config2.bar.color.info}`]: {},
        },
      },
      "&.nui-progress-success": {
        ".nui-progress-bar": {
          [`@apply bg-${config2.bar.color.success}`]: {},
        },
      },
      "&.nui-progress-warning": {
        ".nui-progress-bar": {
          [`@apply bg-${config2.bar.color.warning}`]: {},
        },
      },
      "&.nui-progress-danger": {
        ".nui-progress-bar": {
          [`@apply bg-${config2.bar.color.danger}`]: {},
        },
      },
      "&.nui-progress-light": {
        ".nui-progress-bar": {
          [`@apply ${config2.bar.color.light}`]: {},
        },
      },
      "&.nui-progress-dark": {
        ".nui-progress-bar": {
          [`@apply ${config2.bar.color.dark}`]: {},
        },
      },
      "&.nui-progress-black": {
        ".nui-progress-bar": {
          [`@apply ${config2.bar.color.black}`]: {},
        },
      },
    },
  });
}, config$g);

module.export = progress;

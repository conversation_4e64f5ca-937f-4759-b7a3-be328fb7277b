import plugin from "tailwindcss/plugin";

const key$S = "autocomplete";
const defaultConfig$R = {
  rounded: {
    none: "none",
    sm: "md",
    md: "lg",
    lg: "xl",
    full: "full",
  },
  label: {
    float: {
      height: "5",
      font: {
        color: "primary-500",
      },
      transition: {
        property: "all",
        duration: "300",
      },
    },
  },
  input: {
    width: "full",
    font: {
      family: "sans",
    },
    focus: {
      label: {
        float: {
          font: {
            color: {
              light: "primary-500",
              dark: "primary-500",
            },
          },
        },
      },
      icon: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
      border: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    icon: {
      color: {
        light: "muted-400",
        dark: "muted-400",
      },
      transition: {
        property: "colors",
        duration: "300",
      },
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  results: {
    font: {
      size: "base",
    },
    shadow: {
      size: "lg",
      light: "muted-300/30",
      dark: "muted-800/20",
    },
  },
  item: {
    padding: "2",
    icon: {
      color: "success-500",
      size: "4",
    },
    media: {
      size: "8",
    },
    transition: {
      property: "colors",
      duration: "300",
    },
  },
  placeholder: {
    font: {
      family: "sans",
      color: {
        light: "muted-700",
        dark: "muted-400",
      },
    },
  },
  clear: {
    inner: {
      size: "4",
    },
    font: {
      color: {
        base: {
          light: "muted-400",
          dark: "muted-400",
        },
        hover: {
          light: "muted-700",
          dark: "muted-200",
        },
      },
    },
    transition: {
      property: "colors",
      duration: "300",
    },
  },
  error: {
    input: {
      border: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    icon: {
      color: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    font: {
      family: "sans",
      size: "[0.65rem]",
      weight: "medium",
      color: {
        light: "danger-600",
        dark: "danger-600",
      },
    },
  },
  multiple: {
    item: {
      background: {
        light: "muted-200",
        dark: "muted-700",
      },
      font: {
        family: "sans",
        size: "xs",
        weight: "medium",
        color: {
          light: "muted-400",
          dark: "muted-400",
        },
      },
    },
    icon: {
      size: "3",
    },
  },
  loaded: {
    font: {
      color: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
  },
  size: {
    sm: {
      font: {
        size: "xs",
      },
      icon: {
        size: {
          inner: "4",
          outer: "8",
        },
      },
      placeload: {
        size: "8",
      },
      clear: {
        size: "8",
      },
    },
    md: {
      font: {
        size: "[0.825rem]",
      },
      icon: {
        size: {
          inner: "[1.15rem]",
          outer: "10",
        },
      },
      placeload: {
        size: "10",
      },
      clear: {
        size: "10",
      },
    },
    lg: {
      font: {
        size: "sm",
      },
      icon: {
        size: {
          inner: "5",
          outer: "12",
        },
      },
      placeload: {
        size: "12",
      },
      clear: {
        size: "12",
      },
    },
    xl: {
      font: {
        size: "sm",
      },
      icon: {
        size: {
          inner: "5",
          outer: "14",
        },
      },
      placeload: {
        size: "14",
      },
      clear: {
        size: "14",
      },
    },
  },
  color: {
    default: {
      base: {
        background: {
          light: "white",
          dark: "muted-900",
        },
        border: {
          light: "muted-300",
          dark: "muted-700",
        },
        font: {
          color: {
            light: "muted-600",
            dark: "muted-200",
          },
        },
        placeholder: {
          color: {
            light: "muted-300",
            dark: "muted-500",
          },
        },
      },
      results: {
        outer: {
          background: {
            light: "white",
            dark: "muted-800",
          },
          border: {
            light: "muted-200",
            dark: "muted-700",
          },
        },
        inner: {
          background: {
            light: "muted-100",
            dark: "muted-700",
          },
        },
      },
    },
    defaultContrast: {
      base: {
        background: {
          light: "white",
          dark: "muted-950",
        },
        border: {
          light: "muted-300",
          dark: "muted-800",
        },
        font: {
          color: {
            light: "muted-600",
            dark: "muted-200",
          },
        },
        placeholder: {
          color: {
            light: "muted-300",
            dark: "muted-600",
          },
        },
      },
      results: {
        outer: {
          background: {
            light: "white",
            dark: "muted-950",
          },
          border: {
            light: "muted-200",
            dark: "muted-800",
          },
        },
        inner: {
          background: {
            light: "muted-100",
            dark: "muted-800",
          },
        },
      },
    },
    muted: {
      base: {
        background: {
          light: "muted-100",
          dark: "muted-900",
        },
        border: {
          light: "muted-200",
          dark: "muted-700",
        },
        font: {
          color: {
            light: "muted-600",
            dark: "muted-200",
          },
        },
        placeholder: {
          color: {
            light: "muted-300",
            dark: "muted-500",
          },
        },
      },
      results: {
        outer: {
          background: {
            light: "white",
            dark: "muted-800",
          },
          border: {
            light: "muted-200",
            dark: "muted-700",
          },
        },
        inner: {
          background: {
            light: "muted-100",
            dark: "muted-700",
          },
        },
      },
    },
    mutedContrast: {
      base: {
        background: {
          light: "muted-100",
          dark: "muted-950",
        },
        border: {
          light: "muted-100",
          dark: "muted-800",
        },
        font: {
          color: {
            light: "muted-600",
            dark: "muted-200",
          },
        },
        placeholder: {
          color: {
            light: "muted-300",
            dark: "muted-600",
          },
        },
      },
      results: {
        outer: {
          background: {
            light: "white",
            dark: "muted-950",
          },
          border: {
            light: "muted-200",
            dark: "muted-800",
          },
        },
        inner: {
          background: {
            light: "muted-100",
            dark: "muted-800",
          },
        },
      },
    },
  },
  icon: {
    disabled: {
      input: {
        sm: {
          font: {
            size: "xs",
          },
        },
        md: {
          font: {
            size: "sm",
          },
        },
        lg: {
          font: {
            size: "sm",
          },
        },
        xl: {
          font: {
            size: "base",
          },
        },
      },
      label: {
        float: {
          sm: {
            font: {
              size: "xs",
            },
          },
          md: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          lg: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          xl: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
        },
      },
    },
    enabled: {
      input: {
        sm: {
          font: {
            size: "xs",
          },
        },
        md: {
          font: {
            size: "sm",
          },
        },
        lg: {
          font: {
            size: "sm",
          },
        },
        xl: {
          font: {
            size: "base",
          },
        },
      },
      label: {
        float: {
          sm: {
            font: {
              size: "xs",
            },
          },
          md: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          lg: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          xl: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
        },
      },
    },
  },
};

const config$S = {
  theme: {
    nui: {
      [key$S]: defaultConfig$R,
    },
  },
};
const autocompletePlugin = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$S}`);
  addComponents({
    ".nui-autocomplete": {
      "@apply w-full relative": {},
      //Autocomplete:label
      ".nui-autocomplete-label, .nui-label-float": {
        "@apply nui-label": {},
      },
      //Label:float
      ".nui-label-float": {
        //Base
        "@apply pointer-events-none absolute inline-flex select-none items-center leading-none":
          {},
        //Color & Height
        [`@apply text-${config2.label.float.font.color} h-${config2.label.float.height}`]:
          {},
        //Transition
        [`@apply transition-${config2.label.float.transition.property} duration-${config2.label.float.transition.duration}`]:
          {},
      },
      //Autocomplete:multiple
      ".nui-autocomplete-multiple": {
        "@apply block": {},
        //multiple:list
        ".nui-autocomplete-multiple-list": {
          "@apply my-2 flex flex-wrap gap-1": {},
        },
        //multiple:list item
        ".nui-autocomplete-multiple-list-item": {
          //Base
          "@apply flex items-center py-2 pe-2 ps-3": {},
          //Font
          [`@apply font-${config2.multiple.item.font.family} text-${config2.multiple.item.font.size} font-${config2.multiple.item.font.weight}`]:
            {},
          //Text color
          [`@apply text-${config2.multiple.item.font.color.light} dark:text-${config2.multiple.item.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.multiple.item.background.light} dark:bg-${config2.multiple.item.background.dark}`]:
            {},
        },
        //multiple:list icon
        ".nui-autocomplete-multiple-list-item-icon": {
          [`@apply ms-1 block h-${config2.multiple.icon.size} w-${config2.multiple.icon.size}`]:
            {},
        },
      },
      //Autocomplete:outer
      ".nui-autocomplete-outer": {
        "@apply relative": {},
      },
      //Autocomplete:icon
      ".nui-autocomplete-icon": {
        "@apply absolute start-0 top-0 z-10 flex items-center justify-center":
          {},
        //Font
        [`@apply text-${config2.input.icon.color.light} dark:text-${config2.input.icon.color.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.input.icon.transition.property} duration-${config2.input.icon.transition.duration}`]:
          {},
      },
      //Autocomplete:input
      ".nui-autocomplete-input": {
        [`@apply nui-focus w-${config2.input.width} font-${config2.input.font.family}`]:
          {},
        //State
        "@apply disabled:cursor-not-allowed disabled:opacity-75": {},
        //Transition
        [`@apply transition-${config2.input.transition.property} duration-${config2.input.transition.duration}`]:
          {},
        //Focus:label float
        "&:focus-visible ~ .nui-label-float": {
          [`@apply !text-${config2.input.focus.label.float.font.color.light} dark:!text-${config2.input.focus.label.float.font.color.dark}`]:
            {},
        },
        //Focus:icon
        "&:focus-visible ~ .nui-autocomplete-icon": {
          [`@apply !text-${config2.input.focus.icon.color.light} dark:!text-${config2.input.focus.icon.color.dark}`]:
            {},
        },
        //State:disabled
        "&:disabled ~ .nui-autocomplete-icon": {
          "@apply cursor-not-allowed opacity-75": {},
        },
      },
      //Autocomplete:clear
      ".nui-autocomplete-clear": {
        "@apply absolute end-0 top-0 z-auto flex items-center justify-center cursor-pointer":
          {},
        //Text color
        [`@apply text-${config2.clear.font.color.base.light} dark:text-${config2.clear.font.color.base.dark}`]:
          {},
        //Text color hover
        [`@apply hover:text-${config2.clear.font.color.hover.light} dark:hover:text-${config2.clear.font.color.hover.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.clear.transition.property} duration-${config2.clear.transition.duration}`]:
          {},
        //Clear:inner
        ".nui-autocomplete-clear-inner": {
          [`@apply w-${config2.clear.inner.size} h-${config2.clear.inner.size}`]:
            {},
        },
      },
      //Autocomplete:placeload
      ".nui-autocomplete-placeload": {
        "@apply absolute start-0 top-0 flex w-full items-center px-4": {},
        //Placeload:inner
        ".nui-placeload": {
          "@apply h-3 w-full max-w-[75%] rounded": {},
        },
      },
      //Autocomplete:results
      ".nui-autocomplete-results": {
        "@apply nui-slimscroll absolute z-20 mt-1 max-h-[265px] w-full overflow-auto py-1 outline-none sm:text-sm":
          {},
        //Font size
        [`@apply text-${config2.results.font.size}`]: {},
        //Shadow
        [`@apply shadow-${config2.results.shadow.size} shadow-${config2.results.shadow.light} dark:shadow-${config2.results.shadow.dark}`]:
          {},
        //Results:header & footer
        ".nui-autocomplete-results-header, .nui-autocomplete-results-footer": {
          "@apply relative px-2 py-1": {},
        },
      },
      //Autocomplete:results item
      ".nui-autocomplete-results-item": {
        "@apply px-2 py-1": {},
      },
      //Item:inner
      ".nui-autocomplete-results-item-inner": {
        "@apply text-muted-800 dark:text-muted-100 flex gap-2 cursor-pointer items-center":
          {},
        //Padding
        [`@apply p-${config2.item.padding}`]: {},
        //Transition
        [`@apply transition-${config2.item.transition.property} duration-${config2.item.transition.duration}`]:
          {},
        //Item:selected
        ".nui-autocomplete-results-item-selected": {
          "@apply ms-auto flex items-center justify-center": {},
        },
        //Selected:icon
        ".nui-autocomplete-results-item-selected-icon": {
          [`@apply block text-${config2.item.icon.color}`]: {},
          //Size
          [`@apply h-${config2.item.icon.size} w-${config2.item.icon.size}`]:
            {},
        },
        //Item:media
        "&.nui-has-media": {
          ".nui-autocomplete-results-item-selected": {
            [`@apply h-${config2.item.media.size} w-${config2.item.media.size}`]:
              {},
          },
        },
      },
      //Results:placeholder
      ".nui-autocomplete-results-placeholder": {
        //Base
        "@apply relative px-4 py-2 cursor-default select-none": {},
        //Font
        [`@apply font-${config2.placeholder.font.family} text-${config2.placeholder.font.color.light} dark:text-${config2.placeholder.font.color.dark}`]:
          {},
      },
      //Rounded:sm
      "&.nui-autocomplete-rounded-sm": {
        ".nui-autocomplete-input": {
          [`@apply rounded-${config2.rounded.sm}`]: {},
        },
        //Inner elements
        ".nui-autocomplete-results, .nui-autocomplete-results-item-inner, .nui-autocomplete-multiple .nui-autocomplete-multiple-list-item":
          {
            [`@apply rounded-${config2.rounded.sm}`]: {},
          },
      },
      //Rounded:md
      "&.nui-autocomplete-rounded-md": {
        ".nui-autocomplete-input": {
          [`@apply rounded-${config2.rounded.md}`]: {},
        },
        //Inner elements
        ".nui-autocomplete-results, .nui-autocomplete-results-item-inner, .nui-autocomplete-multiple .nui-autocomplete-multiple-list-item":
          {
            [`@apply rounded-${config2.rounded.md}`]: {},
          },
      },
      //Rounded:lg
      "&.nui-autocomplete-rounded-lg": {
        ".nui-autocomplete-input": {
          [`@apply rounded-${config2.rounded.lg}`]: {},
        },
        //Inner elements
        ".nui-autocomplete-results, .nui-autocomplete-results-item-inner, .nui-autocomplete-multiple .nui-autocomplete-multiple-list-item":
          {
            [`@apply rounded-${config2.rounded.lg}`]: {},
          },
      },
      //Rounded:full
      "&.nui-autocomplete-rounded-full": {
        ".nui-autocomplete-input, .nui-autocomplete-multiple .nui-autocomplete-multiple-list-item":
          {
            [`@apply rounded-${config2.rounded.full}`]: {},
          },
        ".nui-autocomplete-results, .nui-autocomplete-results-item-inner": {
          [`@apply rounded-${config2.rounded.lg}`]: {},
        },
      },
      //Size:sm
      "&.nui-autocomplete-sm": {
        ".nui-autocomplete-label": {
          [`@apply pb-1 text-${config2.size.sm.font.size}`]: {},
        },
        ".nui-label-float": {
          "@apply top-1.5": {},
        },
        ".nui-autocomplete-icon": {
          [`@apply h-${config2.size.sm.icon.size.outer} w-${config2.size.sm.icon.size.outer}`]:
            {},
          ".nui-autocomplete-icon-inner": {
            [`@apply h-${config2.size.sm.icon.size.inner} w-${config2.size.sm.icon.size.inner}`]:
              {},
          },
        },
        ".nui-autocomplete-placeload": {
          [`@apply h-${config2.size.sm.placeload.size}`]: {},
        },
        ".nui-autocomplete-clear": {
          [`@apply h-${config2.size.sm.clear.size} w-${config2.size.sm.clear.size}`]:
            {},
        },
      },
      //Size:md
      "&.nui-autocomplete-md": {
        ".nui-autocomplete-label": {
          [`@apply pb-1 text-${config2.size.md.font.size}`]: {},
        },
        ".nui-label-float": {
          "@apply top-2.5": {},
        },
        ".nui-autocomplete-icon": {
          [`@apply h-${config2.size.md.icon.size.outer} w-${config2.size.md.icon.size.outer}`]:
            {},
          ".nui-autocomplete-icon-inner": {
            [`@apply h-${config2.size.md.icon.size.inner} w-${config2.size.md.icon.size.inner}`]:
              {},
          },
        },
        ".nui-autocomplete-placeload": {
          [`@apply h-${config2.size.md.placeload.size}`]: {},
        },
        ".nui-autocomplete-clear": {
          [`@apply h-${config2.size.md.clear.size} w-${config2.size.md.clear.size}`]:
            {},
        },
      },
      //Size:lg
      "&.nui-autocomplete-lg": {
        ".nui-autocomplete-label": {
          [`@apply pb-1 text-${config2.size.lg.font.size}`]: {},
        },
        ".nui-label-float": {
          "@apply top-3.5": {},
        },
        ".nui-autocomplete-icon": {
          [`@apply h-${config2.size.lg.icon.size.outer} w-${config2.size.lg.icon.size.outer}`]:
            {},
          ".nui-autocomplete-icon-inner": {
            [`@apply h-${config2.size.lg.icon.size.inner} w-${config2.size.lg.icon.size.inner}`]:
              {},
          },
        },
        ".nui-autocomplete-placeload": {
          [`@apply h-${config2.size.lg.placeload.size}`]: {},
        },
        ".nui-autocomplete-clear": {
          [`@apply h-${config2.size.lg.clear.size} w-${config2.size.lg.clear.size}`]:
            {},
        },
      },
      //Size:xl
      "&.nui-autocomplete-xl": {
        ".nui-autocomplete-label": {
          [`@apply pb-1 text-${config2.size.xl.font.size}`]: {},
        },
        ".nui-label-float": {
          "@apply top-[1.1rem]": {},
        },
        ".nui-autocomplete-icon": {
          [`@apply h-${config2.size.xl.icon.size.outer} w-${config2.size.xl.icon.size.outer}`]:
            {},
          ".nui-autocomplete-icon-inner": {
            [`@apply h-${config2.size.xl.icon.size.inner} w-${config2.size.xl.icon.size.inner}`]:
              {},
          },
        },
        ".nui-autocomplete-placeload": {
          [`@apply h-${config2.size.xl.placeload.size}`]: {},
        },
        ".nui-autocomplete-clear": {
          [`@apply h-${config2.size.xl.clear.size} w-${config2.size.xl.clear.size}`]:
            {},
        },
      },
      //Color:default
      "&.nui-autocomplete-default": {
        ".nui-autocomplete-input": {
          //Text
          [`@apply text-${config2.color.default.base.font.color.light} dark:text-${config2.color.default.base.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.default.base.background.light} dark:bg-${config2.color.default.base.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.default.base.border.light} dark:border-${config2.color.default.base.border.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.default.base.placeholder.color.light} dark:placeholder:text-${config2.color.default.base.placeholder.color.dark}`]:
            {},
        },
        //Results
        ".nui-autocomplete-results": {
          //Border
          [`@apply border border-${config2.color.default.results.outer.border.light} dark:border-${config2.color.default.results.outer.border.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.default.results.outer.background.light} dark:bg-${config2.color.default.results.outer.background.dark}`]:
            {},
        },
        //Results:inner
        ".nui-autocomplete-results-item-inner.nui-active, .nui-autocomplete-results-item-inner:hover":
          {
            [`@apply bg-${config2.color.default.results.inner.background.light} dark:bg-${config2.color.default.results.inner.background.dark}`]:
              {},
          },
      },
      //Color:default contrast
      "&.nui-autocomplete-default-contrast": {
        ".nui-autocomplete-input": {
          //Text
          [`@apply text-${config2.color.defaultContrast.base.font.color.light} dark:text-${config2.color.defaultContrast.base.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.defaultContrast.base.background.light} dark:bg-${config2.color.defaultContrast.base.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.defaultContrast.base.border.light} dark:border-${config2.color.defaultContrast.base.border.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.defaultContrast.base.placeholder.color.light} dark:placeholder:text-${config2.color.defaultContrast.base.placeholder.color.dark}`]:
            {},
        },
        //Results
        ".nui-autocomplete-results": {
          //Border
          [`@apply border border-${config2.color.defaultContrast.results.outer.border.light} dark:border-${config2.color.defaultContrast.results.outer.border.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.defaultContrast.results.outer.background.light} dark:bg-${config2.color.defaultContrast.results.outer.background.dark}`]:
            {},
        },
        //Results:inner
        ".nui-autocomplete-results-item-inner.nui-active, .nui-autocomplete-results-item-inner:hover":
          {
            [`@apply bg-${config2.color.defaultContrast.results.inner.background.light} dark:bg-${config2.color.defaultContrast.results.inner.background.dark}`]:
              {},
          },
      },
      //Color:muted
      "&.nui-autocomplete-muted": {
        ".nui-autocomplete-input": {
          //Text
          [`@apply text-${config2.color.muted.base.font.color.light} dark:text-${config2.color.muted.base.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.muted.base.background.light} dark:bg-${config2.color.muted.base.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.muted.base.border.light} dark:border-${config2.color.muted.base.border.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.muted.base.placeholder.color.light} dark:placeholder:text-${config2.color.muted.base.placeholder.color.dark}`]:
            {},
        },
        //Results
        ".nui-autocomplete-results": {
          //Border
          [`@apply border border-${config2.color.muted.results.outer.border.light} dark:border-${config2.color.muted.results.outer.border.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.muted.results.outer.background.light} dark:bg-${config2.color.muted.results.outer.background.dark}`]:
            {},
        },
        //Results:inner
        ".nui-autocomplete-results-item-inner.nui-active, .nui-autocomplete-results-item-inner:hover":
          {
            [`@apply bg-${config2.color.muted.results.inner.background.light} dark:bg-${config2.color.muted.results.inner.background.dark}`]:
              {},
          },
      },
      //Color:muted contrast
      "&.nui-autocomplete-muted-contrast": {
        ".nui-autocomplete-input": {
          //Text
          [`@apply text-${config2.color.mutedContrast.base.font.color.light} dark:text-${config2.color.mutedContrast.base.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.mutedContrast.base.background.light} dark:bg-${config2.color.mutedContrast.base.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.mutedContrast.base.border.light} dark:border-${config2.color.mutedContrast.base.border.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.mutedContrast.base.placeholder.color.light} dark:placeholder:text-${config2.color.mutedContrast.base.placeholder.color.dark}`]:
            {},
        },
        //Results
        ".nui-autocomplete-results": {
          //Border
          [`@apply border border-${config2.color.mutedContrast.results.outer.border.light} dark:border-${config2.color.mutedContrast.results.outer.border.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.mutedContrast.results.outer.background.light} dark:bg-${config2.color.mutedContrast.results.outer.background.dark}`]:
            {},
        },
        //Results:inner
        ".nui-autocomplete-results-item-inner.nui-active, .nui-autocomplete-results-item-inner:hover":
          {
            [`@apply bg-${config2.color.mutedContrast.results.inner.background.light} dark:bg-${config2.color.mutedContrast.results.inner.background.dark}`]:
              {},
          },
      },
      //Focus:color
      "&.nui-autocomplete-focus": {
        ".nui-autocomplete-input": {
          //Transition
          [`@apply transition-colors duration-300`]: {},
          //Focus
          [`@apply focus:!border-${config2.input.focus.border.color.light} dark:focus:!border-${config2.input.focus.border.color.dark}`]:
            {},
          //Force focus
          [`@apply focus:hover:!border-${config2.input.focus.border.color.light} dark:focus:hover:!border-${config2.input.focus.border.color.dark}`]:
            {},
        },
      },
      //Autocomplete:not loading
      "&:not(.nui-autocomplete-loading)": {
        ".nui-autocomplete-input:placeholder-shown ~ .nui-label-float": {
          [`@apply text-${config2.loaded.font.color.light} dark:text-${config2.loaded.font.color.dark}`]:
            {},
        },
      },
      //Autocomplete:loading
      "&.nui-autocomplete-loading": {
        ".nui-autocomplete-input": {
          "@apply !text-transparent placeholder:!text-transparent dark:placeholder:!text-transparent":
            {},
        },
        ".nui-autocomplete-input:placeholder-shown ~ .nui-label-float": {
          "@apply text-transparent": {},
        },
        ".nui-autocomplete-icon": {
          "@apply opacity-50": {},
        },
      },
      //Autocomplete:label float
      "&.nui-autocomplete-label-float": {
        ".nui-autocomplete-input": {
          "@apply placeholder:text-transparent dark:placeholder:text-transparent":
            {},
        },
        ".nui-autocomplete-multiple .nui-autocomplete-multiple-list": {
          "@apply mb-6": {},
        },
      },
      //Autocomplte:input error
      "&.nui-autocomplete-error": {
        ".nui-autocomplete-input": {
          [`@apply !border-${config2.error.input.border.light} dark:!border-${config2.error.input.border.dark}`]:
            {},
        },
        //Input:icon
        ".nui-autocomplete-icon": {
          [`@apply !text-${config2.error.icon.color.light} dark:!text-${config2.error.icon.color.dark}`]:
            {},
        },
      },
      //Size:sm without icon
      "&:not(.nui-has-icon).nui-autocomplete-sm": {
        ".nui-autocomplete-input": {
          [`@apply h-8 py-1 text-${config2.icon.disabled.input.sm.font.size} leading-4 px-2`]:
            {},
        },
      },
      //Size:sm with icon
      "&.nui-has-icon.nui-autocomplete-sm": {
        ".nui-autocomplete-input": {
          [`@apply h-8 py-1 text-${config2.icon.enabled.input.sm.font.size} leading-4 pe-3 ps-8`]:
            {},
        },
      },
      //Size:md without icon
      "&:not(.nui-has-icon).nui-autocomplete-md": {
        ".nui-autocomplete-input": {
          [`@apply h-10 py-2 text-${config2.icon.disabled.input.md.font.size} leading-5 px-3`]:
            {},
        },
      },
      //Size:md with icon
      "&.nui-has-icon.nui-autocomplete-md": {
        ".nui-autocomplete-input": {
          [`@apply h-10 py-2 text-${config2.icon.enabled.input.md.font.size} leading-5 pe-4 ps-10`]:
            {},
        },
      },
      //Size:lg without icon
      "&:not(.nui-has-icon).nui-autocomplete-lg": {
        ".nui-autocomplete-input": {
          [`@apply h-12 py-2 text-${config2.icon.disabled.input.lg.font.size} leading-5 px-4`]:
            {},
        },
      },
      //Size:lg with icon
      "&.nui-has-icon.nui-autocomplete-lg": {
        ".nui-autocomplete-input": {
          [`@apply h-12 py-2 text-${config2.icon.enabled.input.lg.font.size} leading-5 pe-4 ps-11`]:
            {},
        },
      },
      //Size:xl without icon
      "&:not(.nui-has-icon).nui-autocomplete-xl": {
        ".nui-autocomplete-input": {
          [`@apply h-14 py-2 text-${config2.icon.disabled.input.xl.font.size} leading-5 px-4`]:
            {},
        },
      },
      //Size:xl with icon
      "&.nui-has-icon.nui-autocomplete-xl": {
        ".nui-autocomplete-input": {
          [`@apply h-14 py-2 text-${config2.icon.enabled.input.xl.font.size} leading-5 pe-4 ps-12`]:
            {},
        },
      },
      //Size:sm without icon and label float
      "&.nui-autocomplete-label-float:not(.nui-has-icon).nui-autocomplete-sm": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-7 text-${config2.icon.disabled.label.float.sm.font.size}`]:
            {},
        },
        ".nui-autocomplete-input:focus-visible ~ .nui-label-float": {
          "@apply !-ms-3 !-mt-7": {},
        },
        ".nui-autocomplete-input:placeholder-shown ~ .nui-label-float": {
          "@apply ms-0 mt-0": {},
        },
      },
      //Size:sm with icon and label float
      "&.nui-autocomplete-label-float.nui-has-icon.nui-autocomplete-sm": {
        ".nui-label-float": {
          [`@apply start-8 -ms-8 -mt-7 text-${config2.icon.enabled.label.float.sm.font.size}`]:
            {},
        },
        ".nui-autocomplete-input:focus-visible ~ .nui-label-float": {
          "@apply !-ms-8 !-mt-7": {},
        },
        ".nui-autocomplete-input:placeholder-shown ~ .nui-label-float": {
          "@apply ms-0 mt-0": {},
        },
      },
      //Size:md without icon and label float
      "&.nui-autocomplete-label-float:not(.nui-has-icon).nui-autocomplete-md": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-8 text-${config2.icon.disabled.label.float.md.font.size.base}`]:
            {},
        },
        ".nui-autocomplete-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-8 !text-${config2.icon.disabled.label.float.md.font.size.focus}`]:
            {},
        },
        ".nui-autocomplete-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.md.font.size.unfocus}`]:
            {},
        },
      },
      //Size:md with icon and label float
      "&.nui-autocomplete-label-float.nui-has-icon.nui-autocomplete-md": {
        ".nui-label-float": {
          [`@apply start-10 -ms-10 -mt-8 text-${config2.icon.enabled.label.float.md.font.size.base}`]:
            {},
        },
        ".nui-autocomplete-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-10 !-mt-8 !text-${config2.icon.enabled.label.float.md.font.size.focus}`]:
            {},
        },
        ".nui-autocomplete-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.md.font.size.unfocus}`]:
            {},
        },
      },
      //Size:lg without icon and label float
      "&.nui-autocomplete-label-float:not(.nui-has-icon).nui-autocomplete-lg": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-9 text-${config2.icon.disabled.label.float.lg.font.size.base}`]:
            {},
        },
        ".nui-autocomplete-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-9 !text-${config2.icon.disabled.label.float.lg.font.size.focus}`]:
            {},
        },
        ".nui-autocomplete-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.lg.font.size.unfocus}`]:
            {},
        },
      },
      //Size:lg with icon and label float
      "&.nui-autocomplete-label-float.nui-has-icon.nui-autocomplete-lg": {
        ".nui-label-float": {
          [`@apply start-11 -ms-10 -mt-9 text-${config2.icon.enabled.label.float.lg.font.size.base}`]:
            {},
        },
        ".nui-autocomplete-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-10 !-mt-9 !text-${config2.icon.enabled.label.float.lg.font.size.focus}`]:
            {},
        },
        ".nui-autocomplete-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.lg.font.size.unfocus}`]:
            {},
        },
      },
      //Size:xl without icon and label float
      "&.nui-autocomplete-label-float:not(.nui-has-icon).nui-autocomplete-xl": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-10 text-${config2.icon.disabled.label.float.xl.font.size.base}`]:
            {},
        },
        ".nui-autocomplete-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-10 !text-${config2.icon.disabled.label.float.xl.font.size.focus}`]:
            {},
        },
        ".nui-autocomplete-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.xl.font.size.unfocus}`]:
            {},
        },
      },
      //Size:xl with icon and label float
      "&.nui-autocomplete-label-float.nui-has-icon.nui-autocomplete-xl": {
        ".nui-label-float": {
          [`@apply start-[3.25rem] -ms-[3.25rem] -mt-10 text-${config2.icon.enabled.label.float.xl.font.size.base}`]:
            {},
        },
        ".nui-autocomplete-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-[3.25rem] !-mt-10 !text-${config2.icon.enabled.label.float.xl.font.size.focus}`]:
            {},
        },
        ".nui-autocomplete-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.xl.font.size.unfocus}`]:
            {},
        },
      },
    },
  });
}, config$S);

module.export = autocompletePlugin;

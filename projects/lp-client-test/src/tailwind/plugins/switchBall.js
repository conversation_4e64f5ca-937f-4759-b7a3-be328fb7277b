import plugin from "tailwindcss/plugin";

const key$a = "switchBall";
const defaulConfig = {
  input: {
    size: "full",
  },
  handle: {
    size: "5",
    rounded: "rounded-full",
    border: {
      light: "muted-300",
      dark: "muted-600",
    },
    background: {
      light: "white",
      dark: "muted-700",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  track: {
    rounded: "rounded-full",
    background: {
      light: "muted-300",
      dark: "muted-600",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  icon: {
    size: "2.5",
    color: {
      light: "white",
      dark: "white",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  label: {
    single: {
      font: {
        family: "sans",
        size: "sm",
        color: {
          light: "muted-400",
          dark: "muted-400",
        },
      },
    },
    dual: {
      label: {
        font: {
          family: "sans",
          weight: "medium",
          size: "sm",
          color: {
            light: "muted-800",
            dark: "white",
          },
        },
      },
      sublabel: {
        font: {
          family: "sans",
          size: "xs",
          color: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
    },
  },
  color: {
    primary: {
      light: "primary-400",
      dark: "primary-400",
    },
    info: {
      light: "info-400",
      dark: "info-400",
    },
    success: {
      light: "success-400",
      dark: "success-400",
    },
    warning: {
      light: "warning-400",
      dark: "warning-400",
    },
    danger: {
      light: "danger-400",
      dark: "danger-400",
    },
    dark: {
      light: "muted-900",
      dark: "muted-100",
    },
    black: {
      light: "black",
      dark: "white",
    },
  },
};

const config$a = {
  theme: {
    nui: {
      [key$a]: defaulConfig,
    },
  },
};
const switchBall = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$a}`);
  addComponents({
    //Wrapper
    ".nui-switch-ball": {
      "@apply flex cursor-pointer items-center": {},
      //Switch:outer
      ".nui-switch-ball-outer": {
        [`@apply nui-focus relative block ${config2.track.rounded}`]: {},
      },
      //Switch:handle
      ".nui-switch-ball-handle": {
        [`@apply absolute start-0.5 top-1/2 -translate-y-1/2 z-10 flex items-center justify-center ${config2.handle.rounded} shadow focus:w-6`]:
          {},
        //Size
        [`@apply h-${config2.handle.size} w-${config2.handle.size}`]: {},
        //Background
        [`@apply bg-${config2.handle.background.light} dark:bg-${config2.handle.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.handle.border.light} dark:border-${config2.handle.border.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.track.transition.property} duration-${config2.track.transition.duration}`]:
          {},
      },
      //Switch:track
      ".nui-switch-ball-track": {
        //Base
        [`@apply block h-6 w-11 ${config2.track.rounded}`]: {},
        //Background
        [`@apply bg-${config2.track.background.light} dark:bg-${config2.track.background.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.track.transition.property} duration-${config2.track.transition.duration}`]:
          {},
      },
      //Switch:icon
      ".nui-switch-ball-icon": {
        //Base
        "@apply pointer-events-none absolute start-2 top-1/2 z-10 translate-y-0 fill-current opacity-0":
          {},
        //Color
        [`@apply text-${config2.icon.color.light} dark:text-${config2.icon.color.dark}`]:
          {},
        //Size
        [`@apply h-${config2.icon.size} w-${config2.icon.size}`]: {},
        //Transition
        [`@apply transition-${config2.track.transition.property} duration-${config2.track.transition.duration}`]:
          {},
      },
      //Label:single
      ".nui-switch-ball-single-label": {
        //Base
        "@apply relative ms-3 cursor-pointer select-none": {},
        //Font
        [`@apply font-${config2.label.single.font.family} text-${config2.label.single.font.size}`]:
          {},
        //Color
        [`@apply text-${config2.label.single.font.color.light} dark:text-${config2.label.single.font.color.dark}`]:
          {},
      },
      //Label:dual
      ".nui-switch-ball-dual-label": {
        "@apply ms-3": {},
        ".nui-switch-ball-label": {
          //Base
          "@apply block": {},
          //Font
          [`@apply font-${config2.label.dual.label.font.family} text-${config2.label.dual.label.font.size}`]:
            {},
          //Color
          [`@apply text-${config2.label.dual.label.font.color.light} dark:text-${config2.label.dual.label.font.color.dark}`]:
            {},
        },
        //Label:sublabel
        ".nui-switch-ball-sublabel": {
          //Base
          "@apply block": {},
          //Font
          [`@apply font-${config2.label.dual.sublabel.font.family} text-${config2.label.dual.sublabel.font.size}`]:
            {},
          //Color
          [`@apply text-${config2.label.dual.sublabel.font.color.light} dark:text-${config2.label.dual.sublabel.font.color.dark}`]:
            {},
        },
      },
      //Switch:input
      ".nui-switch-ball-input": {
        [`@apply absolute z-0 h-${config2.input.size} w-${config2.input.size} cursor-pointer opacity-0`]:
          {},
        //Input:checked:handle
        "&:checked ~ .nui-switch-ball-handle": {
          "@apply -translate-y-1/2 translate-x-full rtl:-translate-x-full": {},
        },
        //Input:checked:icon
        "&:checked ~ .nui-switch-ball-icon": {
          "@apply -translate-y-1/2 opacity-100": {},
        },
      },
      //color:primary
      "&.nui-switch-ball-primary .nui-switch-ball-input:checked ~ .nui-switch-ball-track":
        {
          [`@apply bg-${config2.color.primary.light} dark:bg-${config2.color.primary.dark}`]:
            {},
        },
      //color:info
      "&.nui-switch-ball-info .nui-switch-ball-input:checked ~ .nui-switch-ball-track":
        {
          [`@apply bg-${config2.color.info.light} dark:bg-${config2.color.info.dark}`]:
            {},
        },
      //color:success
      "&.nui-switch-ball-success .nui-switch-ball-input:checked ~ .nui-switch-ball-track":
        {
          [`@apply bg-${config2.color.success.light} dark:bg-${config2.color.success.dark}`]:
            {},
        },
      //color:warning
      "&.nui-switch-ball-warning .nui-switch-ball-input:checked ~ .nui-switch-ball-track":
        {
          [`@apply bg-${config2.color.warning.light} dark:bg-${config2.color.warning.dark}`]:
            {},
        },
      //color:danger
      "&.nui-switch-ball-danger .nui-switch-ball-input:checked ~ .nui-switch-ball-track":
        {
          [`@apply bg-${config2.color.danger.light} dark:bg-${config2.color.danger.dark}`]:
            {},
        },
      //color:dark
      "&.nui-switch-ball-dark .nui-switch-ball-input:checked ~ .nui-switch-ball-track":
        {
          [`@apply bg-${config2.color.dark.light} dark:bg-${config2.color.dark.dark}`]:
            {},
        },
      "&.nui-switch-ball-dark .nui-switch-ball-icon": {
        [`@apply text-muted-100 dark:text-muted-900`]: {},
      },
      //color:black
      "&.nui-switch-ball-black .nui-switch-ball-input:checked ~ .nui-switch-ball-track":
        {
          [`@apply bg-${config2.color.black.light} dark:bg-${config2.color.black.dark}`]:
            {},
        },
      "&.nui-switch-ball-black .nui-switch-ball-icon": {
        [`@apply text-white dark:text-black`]: {},
      },
    },
  });
}, config$a);

module.export = switchBall;

import plugin from "tailwindcss/plugin";

const key$c = "slimscroll";
const defaultConfig$b = {
  width: "[6px]",
  height: "[6px]",
  background: {
    base: {
      light: "black/5",
      dark: "white/5",
    },
    hover: {
      light: "black/20",
      dark: "white/20",
    },
  },
};

const config$c = {
  theme: {
    nui: {
      [key$c]: defaultConfig$b,
    },
  },
};
const slimscroll = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$c}`);
  addComponents({
    ".nui-slimscroll::-webkit-scrollbar, .nui-slimscroll-opaque::-webkit-scrollbar":
      {
        scrollBehavior: "smooth",
        scrollbarGutter: "stable",
        [`@apply w-${config2.width} h-${config2.height}`]: {},
      },
    ".nui-slimscroll::-webkit-scrollbar-thumb": {
      [`@apply rounded-lg bg-${config2.background.base.light} dark:bg-${config2.background.base.dark} duration-300 transition-all`]:
        {},
    },
    ".nui-slimscroll-opaque::-webkit-scrollbar-thumb": {
      "@apply rounded-lg bg-transparent duration-300 transition-all": {},
    },
    ".nui-slimscroll:hover::-webkit-scrollbar-thumb, .nui-slimscroll-opaque:hover::-webkit-scrollbar-thumb":
      {
        [`@apply bg-${config2.background.hover.light} dark:bg-${config2.background.hover.dark}`]:
          {},
      },
  });
}, config$c);
module.export = slimscroll;

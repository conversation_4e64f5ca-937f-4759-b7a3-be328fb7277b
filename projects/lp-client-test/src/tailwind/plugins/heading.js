import plugin from "tailwindcss/plugin";

const key$C = "heading";
const defaultConfig$B = {
  size: {
    xs: "xs",
    sm: "sm",
    md: "base",
    lg: "lg",
    xl: "xl",
    xxl: "2xl",
    xxxl: "3xl",
    xxxxl: "4xl",
    xxxxxl: "5xl",
    xxxxxxl: "6xl",
    xxxxxxxl: "7xl",
    xxxxxxxxl: "8xl",
    xxxxxxxxxl: "9xl",
  },
  weight: {
    light: "light",
    normal: "normal",
    medium: "medium",
    semibold: "semibold",
    bold: "bold",
    extrabold: "extrabold",
  },
  lead: {
    none: "none",
    normal: "normal",
    tight: "tight",
    snug: "snug",
    loose: "loose",
  },
};

const generateSafelist = (config) => {
  const safelist = [
    "bg-secondary-500",
    "border-secondary-500",
    "shadow-secondary-500/30",
    "dark:shadow-secondary-500/20",
  ];

  // Sizes
  Object.keys(config.size).forEach((key) => {
    safelist.push(`nui-heading-${config.size[key]}`);
  });

  // Weights
  Object.keys(config.weight).forEach((key) => {
    safelist.push(`nui-weight-${config.weight[key]}`);
  });

  // Leads
  Object.keys(config.lead).forEach((key) => {
    safelist.push(`nui-lead-${config.lead[key]}`);
  });

  return safelist;
};

const safelist = generateSafelist(defaultConfig$B);

const headingPlugin = ({ addComponents, theme }) => {
  const config = theme("nui.heading");

  const components = {
    ".nui-heading": {
      "@apply font-sans": {},
    },
  };

  // Sizes
  Object.keys(config.size).forEach((key) => {
    components[`.nui-heading-${config.size[key]}`] = {
      [`@apply text-${config.size[key]}`]: {},
    };
  });

  // Weights
  Object.keys(config.weight).forEach((key) => {
    components[`.nui-weight-${config.weight[key]}`] = {
      [`@apply font-${config.weight[key]}`]: {},
    };
  });

  // Leads
  Object.keys(config.lead).forEach((key) => {
    components[`.nui-lead-${config.lead[key]}`] = {
      [`@apply leading-${config.lead[key]}`]: {},
    };
  });

  addComponents(components);
};

console.log("safe", safelist);

module.exports = { safelist, headingPlugin };

import plugin from "tailwindcss/plugin";

const key$r = "listbox";
const defaultConfig$q = {
  rounded: {
    none: "rounded-none",
    sm: "rounded-md",
    md: "rounded-lg",
    lg: "rounded-xl",
    full: "rounded-full",
  },
  label: {
    float: {
      height: "5",
      font: {
        color: "primary-500",
      },
      transition: {
        property: "all",
        duration: "300",
      },
    },
  },
  button: {
    size: "full",
    font: {
      family: "sans",
      size: "sm",
      align: "start",
    },
    focus: {
      border: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    iconBox: {
      margin: {
        x: "2",
      },
      outer: {
        size: "6",
      },
      inner: {
        size: "4",
      },
    },
    placeholder: {
      font: {
        color: {
          light: "muted-300",
          dark: "muted-500",
        },
        align: "start",
      },
    },
    icon: {
      color: {
        light: "muted-400",
        dark: "muted-400",
      },
      transition: {
        property: "colors",
        duration: "300",
      },
    },
  },
  chevron: {
    inner: {
      size: "4",
      color: {
        light: "muted-400",
        dark: "muted-400",
      },
      transition: {
        property: "transform",
        duration: "300",
      },
    },
  },
  options: {
    ring: {
      focus: {
        light: "primary-500/50",
        dark: "primary-500/50",
      },
    },
    padding: "2",
    font: {
      size: "base",
    },
    shadow: {
      size: "lg",
      light: "muted-300/30",
      dark: "muted-800/20",
    },
  },
  option: {
    transition: {
      property: "colors",
      duration: "300",
    },
    iconBox: {
      color: {
        light: "muted-200",
        dark: "muted-400",
      },
      inner: {
        size: "5",
      },
    },
    inner: {
      heading: {
        font: {
          color: {
            light: "muted-800",
            dark: "muted-100",
          },
        },
      },
      font: {
        color: {
          light: "muted-400",
          dark: "muted-400",
        },
      },
    },
    icon: {
      color: {
        light: "primary-600",
        dark: "primary-600",
      },
      inner: {
        size: "4",
      },
    },
    activeHover: {
      font: {
        color: {
          light: "primary-600",
          dark: "primary-400",
        },
      },
      background: {
        light: "primary-500/10",
        dark: "primary-500/10",
      },
    },
  },
  error: {
    button: {
      border: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    icon: {
      color: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    font: {
      family: "sans",
      size: "[0.65rem]",
      weight: "medium",
      color: {
        light: "danger-600",
        dark: "danger-600",
      },
    },
  },
  size: {
    sm: {
      font: {
        size: "xs",
      },
      icon: {
        size: {
          inner: "4",
          outer: "8",
        },
      },
      placeload: {
        size: "8",
      },
      chevron: {
        outer: {
          size: "8",
        },
        inner: {
          size: "3",
        },
      },
    },
    md: {
      font: {
        size: "[0.825rem]",
      },
      icon: {
        size: {
          inner: "[1.15rem]",
          outer: "10",
        },
      },
      placeload: {
        size: "10",
      },
      chevron: {
        outer: {
          size: "10",
        },
        inner: {
          size: "4",
        },
      },
    },
    lg: {
      font: {
        size: "sm",
      },
      icon: {
        size: {
          inner: "5",
          outer: "12",
        },
      },
      placeload: {
        size: "12",
      },
      chevron: {
        outer: {
          size: "12",
        },
        inner: {
          size: "5",
        },
      },
    },
    xl: {
      font: {
        size: "sm",
      },
      icon: {
        size: {
          inner: "5",
          outer: "14",
        },
      },
      placeload: {
        size: "14",
      },
      chevron: {
        outer: {
          size: "14",
        },
        inner: {
          size: "5",
        },
      },
    },
  },
  color: {
    default: {
      base: {
        background: {
          light: "white",
          dark: "muted-900",
        },
        border: {
          light: "muted-300",
          dark: "muted-700",
        },
        font: {
          color: {
            light: "muted-600",
            dark: "muted-200",
          },
        },
        placeholder: {
          color: {
            light: "muted-300",
            dark: "muted-500",
          },
        },
      },
      shadow: {
        focus: {
          light: "muted-300/30",
          dark: "muted-800/20",
        },
      },
      chevron: {
        color: {
          light: "muted-200",
          dark: "muted-700",
        },
      },
      option: {
        border: {
          light: "muted-200",
          dark: "muted-600",
        },
        background: {
          light: "white",
          dark: "muted-700",
        },
      },
    },
    defaultContrast: {
      base: {
        background: {
          light: "white",
          dark: "muted-950",
        },
        border: {
          light: "muted-300",
          dark: "muted-800",
        },
        font: {
          color: {
            light: "muted-600",
            dark: "muted-200",
          },
        },
        placeholder: {
          color: {
            light: "muted-300",
            dark: "muted-600",
          },
        },
      },
      shadow: {
        focus: {
          light: "muted-300/30",
          dark: "muted-800/20",
        },
      },
      chevron: {
        color: {
          light: "muted-300",
          dark: "muted-800",
        },
      },
      option: {
        border: {
          light: "muted-200",
          dark: "muted-800",
        },
        background: {
          light: "white",
          dark: "muted-950",
        },
      },
    },
    muted: {
      base: {
        background: {
          light: "muted-100",
          dark: "muted-900",
        },
        border: {
          light: "muted-200",
          dark: "muted-700",
        },
        font: {
          color: {
            light: "muted-600",
            dark: "muted-200",
          },
        },
        placeholder: {
          color: {
            light: "muted-300",
            dark: "muted-500",
          },
        },
      },
      shadow: {
        focus: {
          light: "muted-300/30",
          dark: "muted-800/20",
        },
      },
      chevron: {
        color: {
          light: "muted-200",
          dark: "muted-700",
        },
      },
      option: {
        border: {
          light: "muted-200",
          dark: "muted-600",
        },
        background: {
          light: "white",
          dark: "muted-700",
        },
      },
    },
    mutedContrast: {
      base: {
        background: {
          light: "muted-100",
          dark: "muted-950",
        },
        border: {
          light: "muted-200",
          dark: "muted-800",
        },
        font: {
          color: {
            light: "muted-600",
            dark: "muted-200",
          },
        },
        placeholder: {
          color: {
            light: "muted-300",
            dark: "muted-600",
          },
        },
      },
      shadow: {
        focus: {
          light: "muted-300/30",
          dark: "muted-800/20",
        },
      },
      chevron: {
        color: {
          light: "muted-300",
          dark: "muted-800",
        },
      },
      option: {
        border: {
          light: "muted-200",
          dark: "muted-800",
        },
        background: {
          light: "white",
          dark: "muted-950",
        },
      },
    },
  },
  loaded: {
    font: {
      color: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
  },
  icon: {
    disabled: {
      input: {
        sm: {
          font: {
            size: "xs",
          },
        },
        md: {
          font: {
            size: "sm",
          },
        },
        lg: {
          font: {
            size: "sm",
          },
        },
        xl: {
          font: {
            size: "base",
          },
        },
      },
      label: {
        float: {
          sm: {
            font: {
              size: "xs",
            },
          },
          md: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          lg: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          xl: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
        },
      },
    },
    enabled: {
      input: {
        sm: {
          font: {
            size: "xs",
          },
        },
        md: {
          font: {
            size: "sm",
          },
        },
        lg: {
          font: {
            size: "sm",
          },
        },
        xl: {
          font: {
            size: "base",
          },
        },
      },
      label: {
        float: {
          sm: {
            font: {
              size: "xs",
            },
          },
          md: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          lg: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          xl: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
        },
      },
    },
  },
};

const config$r = {
  theme: {
    nui: {
      [key$r]: defaultConfig$q,
    },
  },
};
const listBox = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$r}`);
  addComponents({
    //Wrapper
    ".nui-listbox": {
      "@apply relative w-full": {},
      //Listbox:label
      ".nui-listbox-label, .nui-label-float": {
        "@apply nui-label": {},
      },
      //Label:float
      ".nui-label-float": {
        //Base
        "@apply pointer-events-none absolute inline-flex select-none items-center leading-none":
          {},
        //Color & Height
        [`@apply text-${config2.label.float.font.color} h-${config2.label.float.height}`]:
          {},
        //Transition
        [`@apply transition-${config2.label.float.transition.property} duration-${config2.label.float.transition.duration}`]:
          {},
      },
      //Listbox:outer
      ".nui-listbox-outer": {
        "@apply relative": {},
      },
      //Listbox:button
      ".nui-listbox-button": {
        //Base
        [`@apply nui-focus relative w-${config2.button.size} flex items-center gap-2 pe-12 ps-4 border leading-5 disabled:cursor-not-allowed disabled:opacity-75`]:
          {},
        //Font
        [`@apply font-${config2.button.font.family} text-${config2.button.font.size} text-${config2.button.font.align}`]:
          {},
        //Button:inner
        ".nui-listbox-button-inner": {
          "@apply flex w-full items-center": {},
          //Inner:icon-box
          ".nui-icon-box": {
            //Outer
            [`@apply !h-${config2.button.iconBox.outer.size} !w-${config2.button.iconBox.outer.size}`]:
              {},
            //Outer:margin
            [`@apply -ms-${config2.button.iconBox.margin.x} me-${config2.button.iconBox.margin.x}`]:
              {},
            //Inner
            ".nui-icon-box-inner": {
              [`@apply h-${config2.button.iconBox.inner.size} w-${config2.button.iconBox.inner.size}`]:
                {},
            },
          },
          //Inner:placeholder
          ".nui-listbox-placeholder": {
            [`@apply text-${config2.button.placeholder.font.color.light} dark:text-${config2.button.placeholder.font.color.dark} truncate text-${config2.button.placeholder.font.align}`]:
              {},
          },
        },
        //Inner:chevron
        "&:focus-visible ~ .nui-listbox-chevron .nui-listbox-chevron-inner, &:focus ~ .nui-listbox-chevron .nui-listbox-chevron-inner":
          {
            "@apply !rotate-180": {},
          },
      },
      //Listbox:icon
      ".nui-listbox-icon.nui-icon": {
        //Color
        [`@apply text-${config2.button.icon.color.light} dark:text-${config2.button.icon.color.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.button.icon.transition.property} duration-${config2.button.icon.transition.duration}`]:
          {},
        "@apply absolute start-0 top-0 z-10 flex items-center justify-center":
          {},
      },
      //Listbox:chevron
      ".nui-listbox-chevron.nui-chevron": {
        //Base
        "@apply pointer-events-none absolute top-0 end-0 flex items-center justify-center border-s":
          {},
        //Chevron:inner
        ".nui-listbox-chevron-inner": {
          //Transition
          [`@apply transition-${config2.chevron.inner.transition.property} duration-${config2.chevron.inner.transition.duration}`]:
            {},
          //Size
          [`@apply h-${config2.chevron.inner.size} w-${config2.chevron.inner.size}`]:
            {},
          //Color
          [`@apply text-${config2.chevron.inner.color.light} dark:text-${config2.chevron.inner.color.dark}`]:
            {},
        },
      },
      //Listbox:options
      ".nui-listbox-options": {
        [`@apply nui-slimscroll absolute z-10 mt-1 max-h-60 w-full overflow-auto p-${config2.options.padding}`]:
          {},
        //Font
        [`@apply text-${config2.options.font.size} sm:text-sm`]: {},
        //Ring
        [`@apply focus:ring-${config2.options.ring.focus.light} dark:focus:ring-${config2.options.ring.focus.dark} focus:outline-none focus:ring-1`]:
          {},
        //Shadow
        [`@apply shadow-${config2.options.shadow.size} shadow-${config2.options.shadow.light} dark:shadow-${config2.options.shadow.dark}`]:
          {},
      },
      //Listbox:option
      ".nui-listbox-option": {
        "@apply relative flex gap-2 cursor-pointer select-none items-center px-3 py-2":
          {},
        //Transition
        [`@apply transition-${config2.option.transition.property} duration-${config2.option.transition.duration}`]:
          {},
        //Option:icon-box
        ".nui-icon-box": {
          //Color
          [`@apply text-${config2.option.iconBox.color.light} dark:text-${config2.option.iconBox.color.dark} -ms-2 me-1`]:
            {},
          //Icon-box:inner
          ".nui-icon-box-inner": {
            [`@apply h-${config2.option.iconBox.inner.size} w-${config2.option.iconBox.inner.size}`]:
              {},
          },
        },
        //Option:icon
        ".nui-listbox-selected-icon": {
          [`@apply text-${config2.option.icon.color.light} dark:text-${config2.option.icon.color.light} ms-auto flex items-center`]:
            {},
        },
        //Icon:inner
        ".nui-listbox-selected-icon-inner": {
          [`@apply h-${config2.option.icon.inner.size} w-${config2.option.icon.inner.size}`]:
            {},
        },
        //Option:inner
        ".nui-listbox-option-inner": {
          ".nui-listbox-heading": {
            //Base
            "@apply block truncate": {},
            //Color
            [`@apply text-${config2.option.inner.heading.font.color.light} dark:text-${config2.option.inner.heading.font.color.dark}`]:
              {},
          },
          //Option:inner:text
          ".nui-listbox-text": {
            [`@apply text-${config2.option.inner.font.color.light} dark:text-${config2.option.inner.font.color.dark}`]:
              {},
          },
        },
        //Option:not-active
        "&:not(.nui-active), &:not(:hover)": {
          "@apply text-muted-600 dark:text-muted-200": {},
        },
        //Option:active
        "&.nui-active, &:hover": {
          //Color
          [`@apply text-${config2.option.activeHover.font.color.light} dark:text-${config2.option.activeHover.font.color.light}`]:
            {},
          //Background
          [`@apply bg-${config2.option.activeHover.background.light} dark:bg-${config2.option.activeHover.background.dark}`]:
            {},
        },
      },
      //Listbox:placeload
      ".nui-listbox-placeload.nui-loading-placeload": {
        "@apply absolute start-0 top-0 flex w-full items-center px-4": {},
        ".nui-placeload": {
          "@apply h-3 w-full max-w-[75%] rounded": {},
        },
      },
      //Size:sm
      "&.nui-listbox-sm": {
        ".nui-listbox-label": {
          [`@apply pb-1 text-${config2.size.sm.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-1.5": {},
        },
        //Listbox:icon
        ".nui-listbox-icon": {
          [`@apply h-${config2.size.sm.icon.size.outer} w-${config2.size.sm.icon.size.outer}`]:
            {},
          ".nui-listbox-icon-inner": {
            [`@apply h-${config2.size.sm.icon.size.inner} w-${config2.size.sm.icon.size.inner}`]:
              {},
          },
        },
        //Listbox:placeload
        ".nui-listbox-placeload": {
          [`@apply h-${config2.size.sm.placeload.size}`]: {},
        },
        //Listbox:chevron
        ".nui-listbox-chevron": {
          [`@apply h-${config2.size.sm.chevron.outer.size} w-${config2.size.sm.chevron.outer.size}`]:
            {},
          //Chevron:inner
          ".nui-listbox-chevron-inner": {
            [`@apply h-${config2.size.sm.chevron.inner.size} w-${config2.size.sm.chevron.inner.size}`]:
              {},
          },
        },
      },
      //Size:md
      "&.nui-listbox-md": {
        //Listbox:label
        ".nui-listbox-label": {
          [`@apply pb-1 text-${config2.size.md.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-2.5": {},
        },
      },
      //Listbox:icon
      ".nui-listbox-icon": {
        [`@apply h-${config2.size.md.icon.size.outer} w-${config2.size.md.icon.size.outer}`]:
          {},
        ".nui-listbox-icon-inner": {
          [`@apply h-${config2.size.md.icon.size.inner} w-${config2.size.md.icon.size.inner}`]:
            {},
        },
      },
      //Listbox:placeload
      ".nui-listbox-placeload": {
        [`@apply h-${config2.size.md.placeload.size}`]: {},
      },
      //Listbox:chevron
      ".nui-listbox-chevron": {
        [`@apply h-${config2.size.md.chevron.outer.size} w-${config2.size.md.chevron.outer.size}`]:
          {},
        //Chevron:inner
        ".nui-listbox-chevron-inner": {
          [`@apply h-${config2.size.md.chevron.inner.size} w-${config2.size.md.chevron.inner.size}`]:
            {},
        },
      },
      //Size:lg
      "&.nui-listbox-lg": {
        ".nui-listbox-label": {
          [`@apply pb-1 text-${config2.size.lg.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-3.5": {},
        },
        //Listbox:icon
        ".nui-listbox-icon": {
          [`@apply h-${config2.size.lg.icon.size.outer} w-${config2.size.lg.icon.size.outer}`]:
            {},
          ".nui-listbox-icon-inner": {
            [`@apply h-${config2.size.lg.icon.size.inner} w-${config2.size.lg.icon.size.inner}`]:
              {},
          },
        },
        //Listbox:placeload
        ".nui-listbox-placeload": {
          [`@apply h-${config2.size.lg.placeload.size}`]: {},
        },
        //Listbox:chevron
        ".nui-listbox-chevron": {
          [`@apply h-${config2.size.lg.chevron.outer.size} w-${config2.size.lg.chevron.outer.size}`]:
            {},
          //Chevron:inner
          ".nui-listbox-chevron-inner": {
            [`@apply h-${config2.size.lg.chevron.inner.size} w-${config2.size.lg.chevron.inner.size}`]:
              {},
          },
        },
      },
      //Size:xl
      "&.nui-listbox-xl": {
        ".nui-listbox-label": {
          [`@apply pb-1 text-${config2.size.xl.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-[1.1rem]": {},
        },
        //Listbox:icon
        ".nui-listbox-icon": {
          [`@apply h-${config2.size.xl.icon.size.outer} w-${config2.size.xl.icon.size.outer}`]:
            {},
          ".nui-listbox-icon-inner": {
            [`@apply h-${config2.size.xl.icon.size.inner} w-${config2.size.xl.icon.size.inner}`]:
              {},
          },
        },
        //Listbox:placeload
        ".nui-listbox-placeload": {
          [`@apply h-${config2.size.xl.placeload.size}`]: {},
        },
        //Listbox:chevron
        ".nui-listbox-chevron": {
          [`@apply h-${config2.size.xl.chevron.outer.size} w-${config2.size.xl.chevron.outer.size}`]:
            {},
          //Chevron:inner
          ".nui-listbox-chevron-inner": {
            [`@apply h-${config2.size.xl.chevron.inner.size} w-${config2.size.xl.chevron.inner.size}`]:
              {},
          },
        },
      },
      //Rounded:sm
      "&.nui-listbox-rounded-sm": {
        ".nui-listbox-button, .nui-listbox-options, .nui-listbox-option": {
          [`@apply ${config2.rounded.sm}`]: {},
        },
      },
      //Rounded:md
      "&.nui-listbox-rounded-md": {
        ".nui-listbox-button, .nui-listbox-options, .nui-listbox-option": {
          [`@apply ${config2.rounded.md}`]: {},
        },
      },
      //Rounded:lg
      "&.nui-listbox-rounded-lg": {
        ".nui-listbox-button, .nui-listbox-options, .nui-listbox-option": {
          [`@apply ${config2.rounded.lg}`]: {},
        },
      },
      //Rounded:full
      "&.nui-listbox-rounded-full": {
        ".nui-listbox-button": {
          [`@apply ${config2.rounded.full}`]: {},
        },
        ".nui-listbox-option, .nui-listbox-options": {
          [`@apply ${config2.rounded.lg}`]: {},
        },
      },
      //Color:default
      "&.nui-listbox-default": {
        ".nui-listbox-button": {
          //Color
          [`@apply text-${config2.color.default.base.font.color.light} dark:text-${config2.color.default.base.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.default.base.background.light} dark:bg-${config2.color.default.base.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.default.base.border.light} dark:border-${config2.color.default.base.border.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder-${config2.color.default.base.placeholder.color.light} dark:placeholder-${config2.color.default.base.placeholder.color.dark}`]:
            {},
          //Focus
          [`@apply focus:shadow-lg focus:shadow-${config2.color.default.shadow.focus.light} dark:focus:shadow-${config2.color.default.shadow.focus.dark}`]:
            {},
        },
        //Listbox:chevron
        ".nui-listbox-chevron": {
          [`@apply border-${config2.color.default.chevron.color.light} dark:border-${config2.color.default.chevron.color.dark}`]:
            {},
        },
        //Listbox:options
        ".nui-listbox-options": {
          //Background
          [`@apply bg-${config2.color.default.option.background.light} dark:bg-${config2.color.default.option.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.default.option.border.light} dark:border-${config2.color.default.option.border.dark}`]:
            {},
        },
      },
      //Color:defaultContrast
      "&.nui-listbox-default-contrast": {
        ".nui-listbox-button": {
          //Color
          [`@apply text-${config2.color.defaultContrast.base.font.color.light} dark:text-${config2.color.defaultContrast.base.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.defaultContrast.base.background.light} dark:bg-${config2.color.defaultContrast.base.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.defaultContrast.base.border.light} dark:border-${config2.color.defaultContrast.base.border.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder-${config2.color.defaultContrast.base.placeholder.color.light} dark:placeholder-${config2.color.defaultContrast.base.placeholder.color.dark}`]:
            {},
          //Focus
          [`@apply focus:shadow-lg focus:shadow-${config2.color.defaultContrast.shadow.focus.light} dark:focus:shadow-${config2.color.defaultContrast.shadow.focus.dark}`]:
            {},
        },
        //Listbox:chevron
        ".nui-listbox-chevron": {
          [`@apply border-${config2.color.defaultContrast.chevron.color.light} dark:border-${config2.color.defaultContrast.chevron.color.dark}`]:
            {},
        },
        //Listbox:options
        ".nui-listbox-options": {
          //Background
          [`@apply bg-${config2.color.defaultContrast.option.background.light} dark:bg-${config2.color.defaultContrast.option.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.defaultContrast.option.border.light} dark:border-${config2.color.defaultContrast.option.border.dark}`]:
            {},
        },
      },
      //Color:muted
      "&.nui-listbox-muted": {
        ".nui-listbox-button": {
          //Color
          [`@apply text-${config2.color.muted.base.font.color.light} dark:text-${config2.color.muted.base.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.muted.base.background.light} dark:bg-${config2.color.muted.base.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.muted.base.border.light} dark:border-${config2.color.muted.base.border.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder-${config2.color.muted.base.placeholder.color.light} dark:placeholder-${config2.color.muted.base.placeholder.color.dark}`]:
            {},
          //Focus
          [`@apply focus:shadow-lg focus:shadow-${config2.color.muted.shadow.focus.light} dark:focus:shadow-${config2.color.muted.shadow.focus.dark}`]:
            {},
        },
        //Listbox:chevron
        ".nui-listbox-chevron": {
          [`@apply border-${config2.color.muted.chevron.color.light} dark:border-${config2.color.muted.chevron.color.dark}`]:
            {},
        },
        //Listbox:options
        ".nui-listbox-options": {
          //Background
          [`@apply bg-${config2.color.muted.option.background.light} dark:bg-${config2.color.muted.option.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.muted.option.border.light} dark:border-${config2.color.muted.option.border.dark}`]:
            {},
        },
      },
      //Color:mutedContrast
      "&.nui-listbox-muted-contrast": {
        ".nui-listbox-button": {
          //Color
          [`@apply text-${config2.color.mutedContrast.base.font.color.light} dark:text-${config2.color.mutedContrast.base.font.color.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.mutedContrast.base.background.light} dark:bg-${config2.color.mutedContrast.base.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.mutedContrast.base.border.light} dark:border-${config2.color.mutedContrast.base.border.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder-${config2.color.mutedContrast.base.placeholder.color.light} dark:placeholder-${config2.color.mutedContrast.base.placeholder.color.dark}`]:
            {},
          //Focus
          [`@apply focus:shadow-lg focus:shadow-${config2.color.mutedContrast.shadow.focus.light} dark:focus:shadow-${config2.color.mutedContrast.shadow.focus.dark}`]:
            {},
        },
        //Listbox:chevron
        ".nui-listbox-chevron": {
          [`@apply border-${config2.color.mutedContrast.chevron.color.light} dark:border-${config2.color.mutedContrast.chevron.color.dark}`]:
            {},
        },
        //Listbox:options
        ".nui-listbox-options": {
          //Background
          [`@apply bg-${config2.color.mutedContrast.option.background.light} dark:bg-${config2.color.mutedContrast.option.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.mutedContrast.option.border.light} dark:border-${config2.color.mutedContrast.option.border.dark}`]:
            {},
        },
      },
      //Focus:color
      "&.nui-listbox-focus": {
        ".nui-listbox-button": {
          //Transition
          [`@apply transition-colors duration-300`]: {},
          //Focus
          [`@apply focus:!border-${config2.button.focus.border.color.light} dark:focus:!border-${config2.button.focus.border.color.dark}`]:
            {},
          //Force focus
          [`@apply focus:hover:!border-${config2.button.focus.border.color.light} dark:focus:hover:!border-${config2.button.focus.border.color.dark}`]:
            {},
        },
      },
      //Listbox:loaded
      "&:not(.nui-listbox-loading)": {
        ".nui-listbox-button ~ .nui-label-float": {
          [`@apply text-${config2.loaded.font.color.light} dark:text-${config2.loaded.font.color.dark}`]:
            {},
        },
      },
      //Listbox:loading
      "&.nui-listbox-loading": {
        ".nui-listbox-button": {
          "@apply !text-transparent placeholder:!text-transparent dark:placeholder:!text-transparent pointer-events-none":
            {},
        },
        ".nui-listbox-button ~ .nui-label-float": {
          "@apply text-transparent": {},
        },
        ".nui-listbox-icon": {
          "@apply opacity-0": {},
        },
      },
      //Label:float
      "&.nui-listbox-label-float": {
        ".nui-listbox-button": {
          "@apply placeholder:text-transparent dark:placeholder:text-transparent":
            {},
        },
      },
      //Listbox:error
      "&.nui-listbox-error": {
        ".nui-listbox-button": {
          [`@apply !border-${config2.error.button.border.light} dark:!border-${config2.error.button.border.dark}`]:
            {},
        },
        //Listbox:icon
        ".nui-listbox-icon": {
          [`@apply !text-${config2.error.icon.color.light} dark:!text-${config2.error.icon.color.dark}`]:
            {},
        },
      },
      //Without:icon && Size:sm
      "&:not(.nui-has-icon).nui-listbox-sm": {
        ".nui-listbox-button": {
          [`@apply h-8 py-1 text-${config2.icon.disabled.input.sm.font.size} leading-4 px-2`]:
            {},
        },
      },
      //With:icon && Size:sm
      "&.nui-has-icon.nui-listbox-sm": {
        ".nui-listbox-button": {
          [`@apply h-8 py-1 text-${config2.icon.enabled.input.sm.font.size} leading-4 pe-3 ps-8`]:
            {},
        },
      },
      //Without:icon && Size:md
      "&:not(.nui-has-icon).nui-listbox-md": {
        ".nui-listbox-button": {
          [`@apply h-10 py-2 text-${config2.icon.disabled.input.md.font.size} leading-5 px-3`]:
            {},
        },
      },
      //With:icon && Size:md
      "&.nui-has-icon.nui-listbox-md": {
        ".nui-listbox-button": {
          [`@apply h-10 py-2 text-${config2.icon.enabled.input.md.font.size} leading-5 pe-4 ps-10`]:
            {},
        },
      },
      //Without:icon && Size:lg
      "&:not(.nui-has-icon).nui-listbox-lg": {
        ".nui-listbox-button": {
          [`@apply h-12 py-2 text-${config2.icon.disabled.input.lg.font.size} leading-5 px-4`]:
            {},
        },
      },
      //With:icon && Size:lg
      "&.nui-has-icon.nui-listbox-lg": {
        ".nui-listbox-button": {
          [`@apply h-12 py-2 text-${config2.icon.enabled.input.lg.font.size} leading-5 pe-4 ps-11`]:
            {},
        },
      },
      //Without:icon && Size:xl
      "&:not(.nui-has-icon).nui-listbox-xl": {
        ".nui-listbox-button": {
          [`@apply h-14 py-2 text-${config2.icon.disabled.input.xl.font.size} leading-5 px-4`]:
            {},
        },
      },
      //With:icon && Size:xl
      "&.nui-has-icon.nui-listbox-xl": {
        ".nui-listbox-button": {
          [`@apply h-14 py-2 text-${config2.icon.enabled.input.xl.font.size} leading-5 pe-4 ps-12`]:
            {},
        },
      },
      //Without:icon && Size:sm && Label:float
      "&.nui-listbox-label-float:not(.nui-has-icon).nui-listbox-sm": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-7 text-${config2.icon.disabled.label.float.sm.font.size} text-transparent`]:
            {},
        },
        ".nui-listbox-button:focus-visible ~ .nui-label-float, .nui-listbox-button:focus ~ .nui-label-float, .nui-label-float-active":
          {
            [`@apply !-ms-3 !-mt-7 !text-${config2.label.float.font.color}`]:
              {},
          },
        ".nui-listbox-button ~ .nui-label-float": {
          "@apply ms-0 mt-0": {},
        },
      },
      //With:icon && Size:sm && Label:float
      "&.nui-listbox-label-float.nui-has-icon.nui-listbox-sm": {
        ".nui-label-float": {
          [`@apply start-8 -ms-8 -mt-7 text-${config2.icon.disabled.label.float.sm.font.size} text-transparent`]:
            {},
        },
        ".nui-listbox-button:focus-visible ~ .nui-label-float, .nui-listbox-button:focus ~ .nui-label-float, .nui-label-float-active":
          {
            [`@apply !-ms-8 !-mt-7 !text-${config2.label.float.font.color}`]:
              {},
          },
        ".nui-listbox-button ~ .nui-label-float": {
          "@apply ms-0 mt-0": {},
        },
      },
      //Without:icon && Size:md && Label:float
      "&.nui-listbox-label-float:not(.nui-has-icon).nui-listbox-md": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-8 text-${config2.icon.disabled.label.float.md.font.size.base} text-transparent`]:
            {},
        },
        ".nui-listbox-button:focus-visible ~ .nui-label-float, .nui-listbox-button:focus ~ .nui-label-float, .nui-label-float-active":
          {
            [`@apply !-ms-3 !-mt-8 !text-${config2.icon.disabled.label.float.md.font.size.focus} !text-${config2.label.float.font.color}`]:
              {},
          },
        ".nui-listbox-button ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.md.font.size.base}`]:
            {},
        },
      },
      //With:icon && Size:md && Label:float
      "&.nui-listbox-label-float.nui-has-icon.nui-listbox-md": {
        ".nui-label-float": {
          [`@apply start-10 -ms-10 -mt-8 text-${config2.icon.enabled.label.float.md.font.size.base} text-transparent`]:
            {},
        },
        ".nui-listbox-button:focus-visible ~ .nui-label-float, .nui-listbox-button:focus ~ .nui-label-float, .nui-label-float-active":
          {
            [`@apply !-ms-10 !-mt-8 !text-${config2.icon.enabled.label.float.md.font.size.focus} !text-${config2.label.float.font.color}`]:
              {},
          },
        ".nui-listbox-button ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.md.font.size.unfocus}`]:
            {},
        },
      },
      //Without:icon && Size:lg && Label:float
      "&.nui-listbox-label-float:not(.nui-has-icon).nui-listbox-lg": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-9 text-${config2.icon.disabled.label.float.lg.font.size.base} text-transparent`]:
            {},
        },
        ".nui-listbox-button:focus-visible ~ .nui-label-float, .nui-listbox-button:focus ~ .nui-label-float, .nui-label-float-active":
          {
            [`@apply !-ms-3 !-mt-9 !text-${config2.icon.disabled.label.float.lg.font.size.focus} !text-${config2.label.float.font.color}`]:
              {},
          },
        ".nui-listbox-button ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.lg.font.size.unfocus}`]:
            {},
        },
      },
      //With:icon && Size:lg && Label:float
      "&.nui-listbox-label-float.nui-has-icon.nui-listbox-lg": {
        ".nui-label-float": {
          [`@apply start-11 -ms-10 -mt-9 text-${config2.icon.enabled.label.float.lg.font.size.base} text-transparent`]:
            {},
        },
        ".nui-listbox-button:focus-visible ~ .nui-label-float, .nui-listbox-button:focus ~ .nui-label-float, .nui-label-float-active":
          {
            [`@apply !-ms-10 !-mt-9 !text-${config2.icon.enabled.label.float.lg.font.size.focus} !text-${config2.label.float.font.color}`]:
              {},
          },
        ".nui-listbox-button ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.lg.font.size.unfocus}`]:
            {},
        },
      },
      //Without:icon && Size:xl && Label:float
      "&.nui-listbox-label-float:not(.nui-has-icon).nui-listbox-xl": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-10 text-${config2.icon.disabled.label.float.xl.font.size.base} text-transparent`]:
            {},
        },
        ".nui-listbox-button:focus-visible ~ .nui-label-float, .nui-listbox-button:focus ~ .nui-label-float, .nui-label-float-active":
          {
            [`@apply !-ms-3 !-mt-10 !text-${config2.icon.disabled.label.float.xl.font.size.focus} !text-${config2.label.float.font.color}`]:
              {},
          },
        ".nui-listbox-button ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.xl.font.size.unfocus}`]:
            {},
        },
      },
      //With:icon && Size:xl && Label:float
      "&.nui-listbox-label-float.nui-has-icon.nui-listbox-xl": {
        ".nui-label-float": {
          [`@apply start-[3.25rem] -ms-[3.25rem] -mt-10 text-${config2.icon.enabled.label.float.xl.font.size.base} text-transparent`]:
            {},
        },
        ".nui-listbox-button:focus-visible ~ .nui-label-float, .nui-listbox-button:focus ~ .nui-label-float, .nui-label-float-active":
          {
            [`@apply !-ms-[3.25rem] !-mt-10 !text-${config2.icon.enabled.label.float.xl.font.size.focus} !text-${config2.label.float.font.color}`]:
              {},
          },
        ".nui-listbox-button ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.xl.font.size.unfocus}`]:
            {},
        },
      },
    },
  });
}, config$r);

module.export = listBox;

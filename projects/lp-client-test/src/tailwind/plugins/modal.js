import plugin from "tailwindcss/plugin";
const key$m = "modal";
const defaultConfig$l = {
  backdrop: {
    background: {
      light: "muted-800/70",
      dark: "muted-900/80",
    },
  },
  inner: {
    padding: "4",
  },
  panel: {
    size: "full",
    align: "start",
  },
  size: {
    sm: "sm",
    md: "md",
    lg: "xl",
    xl: "2xl",
    xxl: "3xl",
    xxxl: "5xl",
  },
};

const config$m = {
  theme: {
    nui: {
      [key$m]: defaultConfig$l,
    },
  },
};
const modal = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$m}`);
  addComponents({
    //Wrapper
    ".nui-modal": {
      "@apply fixed inset-0 z-[9999] flex items-center justify-center": {},
      //Modal:inner
      ".nui-modal-inner": {
        "@apply relative z-[9999]": {},
      },
      //Modal:backdrop
      ".nui-modal-backdrop": {
        [`@apply bg-${config2.backdrop.background.light} dark:bg-${config2.backdrop.background.dark} fixed inset-0`]:
          {},
      },
      //Modal:content
      ".nui-modal-content": {
        "@apply fixed inset-0": {},
      },
      //Content:inner
      ".nui-modal-content-inner": {
        [`@apply flex w-full min-h-full items-center justify-center p-${config2.inner.padding} text-center`]:
          {},
      },
      //Content:panel
      ".nui-modal-content-panel": {
        [`@apply w-${config2.panel.size} text-${config2.panel.align} align-middle transition-all`]:
          {},
      },
      //Size:sm
      "&.nui-modal-sm": {
        ".nui-modal-content-panel": {
          [`@apply max-w-${config2.size.sm}`]: {},
        },
      },
      //Size:md
      "&.nui-modal-md": {
        ".nui-modal-content-panel": {
          [`@apply max-w-${config2.size.md}`]: {},
        },
      },
      //Size:lg
      "&.nui-modal-lg": {
        ".nui-modal-content-panel": {
          [`@apply max-w-${config2.size.lg}`]: {},
        },
      },
      //Size:xl
      "&.nui-modal-xl": {
        ".nui-modal-content-panel": {
          [`@apply max-w-${config2.size.xl}`]: {},
        },
      },
      //Size:2xl
      "&.nui-modal-2xl": {
        ".nui-modal-content-panel": {
          [`@apply max-w-${config2.size.xxl}`]: {},
        },
      },
      //Size:3xl
      "&.nui-modal-3xl": {
        ".nui-modal-content-panel": {
          [`@apply max-w-${config2.size.xxxl}`]: {},
        },
      },
    },
  });
}, config$m);

module.export = modal;

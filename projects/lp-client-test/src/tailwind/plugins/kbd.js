import plugin from "tailwindcss/plugin";
const key$v = "kbd";
const defaultConfig$u = {
  font: {
    family: "mono",
    color: {
      light: "muted-700",
      dark: "muted-200",
    },
  },
  rounded: {
    sm: "rounded-sm",
    md: "rounded-md",
    lg: "rounded-lg",
    full: "rounded-full",
  },
  size: {
    xs: {
      font: {
        size: "xs",
        lead: "4",
      },
      padding: {
        x: "1",
        y: "0.5",
      },
      size: "1.2em",
      icon: {
        outer: {
          size: "4",
        },
        inner: {
          size: "3.5",
        },
      },
    },
    sm: {
      font: {
        size: "sm",
        lead: "5",
      },
      padding: {
        x: "1",
        y: "0.5",
      },
      size: "1.6em",
      icon: {
        outer: {
          size: "5",
        },
        inner: {
          size: "3.5",
        },
      },
    },
    md: {
      font: {
        size: "base",
        lead: "6",
      },
      padding: {
        x: "2",
        y: "1",
      },
      size: "2.2em",
      icon: {
        outer: {
          size: "5",
        },
        inner: {
          size: "4",
        },
      },
    },
    lg: {
      font: {
        size: "lg",
        lead: "7",
      },
      padding: {
        x: "4",
        y: "1",
      },
      size: "2.5em",
      icon: {
        outer: {
          size: "6",
        },
        inner: {
          size: "5",
        },
      },
    },
  },
  color: {
    default: {
      background: {
        light: "white",
        dark: "muted-800",
      },
      border: {
        light: "muted-500/20",
        dark: "muted-300/20",
      },
    },
    defaultContrast: {
      background: {
        light: "white",
        dark: "muted-950",
      },
      border: {
        light: "muted-500/20",
        dark: "muted-400/20",
      },
    },
    muted: {
      background: {
        light: "muted-50",
        dark: "muted-800",
      },
      border: {
        light: "muted-600/20",
        dark: "muted-300/20",
      },
    },
    mutedContrast: {
      background: {
        light: "muted-50",
        dark: "muted-950",
      },
      border: {
        light: "muted-600/20",
        dark: "muted-400/20",
      },
    },
  },
};

const config$v = {
  theme: {
    nui: {
      [key$v]: defaultConfig$u,
    },
  },
};
const kbd = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$v}`);
  addComponents({
    ".nui-kbd": {
      "@apply inline-flex items-center justify-center": {},
      //Font
      [`@apply font-${config2.font.family} leading-none text-${config2.font.color.light} dark:text-${config2.font.color.dark}`]:
        {},
      //Icon:outer
      ".nui-kbd-icon-outer": {
        "@apply inline-flex items-center justify-center": {},
      },
      //Icon:inner
      ".nui-kbd-icon": {
        "@apply shrink-0": {},
      },
      //Rounded:sm
      "&.nui-kbd-rounded-sm": {
        [`@apply ${config2.rounded.sm}`]: {},
      },
      //Rounded:md
      "&.nui-kbd-rounded-md": {
        [`@apply ${config2.rounded.md}`]: {},
      },
      //Rounded:lg
      "&.nui-kbd-rounded-lg": {
        [`@apply ${config2.rounded.lg}`]: {},
      },
      //Rounded:full
      "&.nui-kbd-rounded-full": {
        [`@apply ${config2.rounded.full}`]: {},
      },
      //Size:xs
      "&.nui-kbd-xs": {
        "@apply font-medium": {},
        //Size
        [`@apply min-h-[${config2.size.xs.size}] min-w-[${config2.size.xs.size}]`]:
          {},
        //Padding
        [`@apply px-${config2.size.xs.padding.x} py-${config2.size.xs.padding.y}`]:
          {},
        //Font
        [`@apply leading-${config2.size.xs.font.lead} text-${config2.size.xs.font.size}`]:
          {},
        //Icon:outer
        ".nui-kbd-icon-outer": {
          [`@apply w-${config2.size.xs.icon.outer.size} h-${config2.size.xs.icon.outer.size}`]:
            {},
        },
        //Icon:inner
        ".nui-kbd-icon-inner": {
          [`@apply w-${config2.size.xs.icon.inner.size} h-${config2.size.xs.icon.inner.size}`]:
            {},
        },
      },
      //Size:sm
      "&.nui-kbd-sm": {
        //Size
        [`@apply min-h-[${config2.size.sm.size}] min-w-[${config2.size.sm.size}]`]:
          {},
        //Padding
        [`@apply px-${config2.size.sm.padding.x} py-${config2.size.sm.padding.y}`]:
          {},
        //Font
        [`@apply leading-${config2.size.sm.font.lead} text-${config2.size.sm.font.size}`]:
          {},
        //Icon:outer
        ".nui-kbd-icon-outer": {
          [`@apply w-${config2.size.sm.icon.outer.size} h-${config2.size.sm.icon.outer.size}`]:
            {},
        },
        //Icon:inner
        ".nui-kbd-icon-inner": {
          [`@apply w-${config2.size.sm.icon.inner.size} h-${config2.size.sm.icon.inner.size}`]:
            {},
        },
      },
      //Size:md
      "&.nui-kbd-md": {
        //Size
        [`@apply min-h-[${config2.size.md.size}] min-w-[${config2.size.md.size}]`]:
          {},
        //Padding
        [`@apply px-${config2.size.md.padding.x} py-${config2.size.md.padding.y}`]:
          {},
        //Font
        [`@apply leading-${config2.size.md.font.lead} text-${config2.size.md.font.size}`]:
          {},
        //Icon:outer
        ".nui-kbd-icon-outer": {
          [`@apply w-${config2.size.md.icon.outer.size} h-${config2.size.md.icon.outer.size}`]:
            {},
        },
        //Icon:inner
        ".nui-kbd-icon-inner": {
          [`@apply w-${config2.size.md.icon.inner.size} h-${config2.size.md.icon.inner.size}`]:
            {},
        },
      },
      //Size:lg
      "&.nui-kbd-lg": {
        //Size
        [`@apply min-h-[${config2.size.lg.size}] min-w-[${config2.size.lg.size}]`]:
          {},
        //Padding
        [`@apply px-${config2.size.lg.padding.x} py-${config2.size.lg.padding.y}`]:
          {},
        //Font
        [`@apply leading-${config2.size.lg.font.lead} text-${config2.size.lg.font.size}`]:
          {},
        //Icon:outer
        ".nui-kbd-icon-outer": {
          [`@apply w-${config2.size.lg.icon.outer.size} h-${config2.size.lg.icon.outer.size}`]:
            {},
        },
        //Icon:inner
        ".nui-kbd-icon-inner": {
          [`@apply w-${config2.size.lg.icon.inner.size} h-${config2.size.lg.icon.inner.size}`]:
            {},
        },
      },
      //Color:default
      "&.nui-kbd-default": {
        //Background
        [`@apply bg-${config2.color.default.background.light} dark:bg-${config2.color.default.background.dark} border border-b-2`]:
          {},
        //Border
        [`@apply border-${config2.color.default.border.light} dark:border-${config2.color.default.border.dark}`]:
          {},
      },
      //Color:default-contrast
      "&.nui-kbd-default-contrast": {
        //Background
        [`@apply bg-${config2.color.defaultContrast.background.light} dark:bg-${config2.color.defaultContrast.background.dark} border border-b-2`]:
          {},
        //Border
        [`@apply border-${config2.color.defaultContrast.border.light} dark:border-${config2.color.defaultContrast.border.dark}`]:
          {},
      },
      //Color:muted
      "&.nui-kbd-muted": {
        //Background
        [`@apply bg-${config2.color.muted.background.light} dark:bg-${config2.color.muted.background.dark} border border-b-2`]:
          {},
        //Border
        [`@apply border-${config2.color.muted.border.light} dark:border-${config2.color.muted.border.dark}`]:
          {},
      },
      //Color:muted-contrast
      "&.nui-kbd-muted-contrast": {
        //Background
        [`@apply bg-${config2.color.mutedContrast.background.light} dark:bg-${config2.color.mutedContrast.background.dark} border border-b-2`]:
          {},
        //Border
        [`@apply border-${config2.color.mutedContrast.border.light} dark:border-${config2.color.mutedContrast.border.dark}`]:
          {},
      },
    },
  });
}, config$v);

module.export = kbd;

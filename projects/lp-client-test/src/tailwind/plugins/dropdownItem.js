import plugin from "tailwindcss/plugin";

const key$G = "dropdownItem";
const defaultConfig$F = {
  align: "start",
  font: {
    family: "sans",
    size: "sm",
    color: {
      inactive: {
        light: "muted-500",
        dark: "muted-500",
      },
    },
  },
  rounded: {
    none: "none",
    sm: "md",
    md: "lg",
    lg: "xl",
  },
  contrast: {
    default: {
      background: {
        light: "muted-100",
        dark: "muted-700",
      },
    },
    contrast: {
      background: {
        light: "muted-100",
        dark: "muted-900",
      },
    },
  },
  color: {
    primary: {
      font: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    info: {
      font: {
        color: {
          light: "info-500",
          dark: "info-500",
        },
      },
    },
    success: {
      font: {
        color: {
          light: "success-500",
          dark: "success-500",
        },
      },
    },
    warning: {
      font: {
        color: {
          light: "info-500",
          dark: "info-500",
        },
      },
    },
    danger: {
      font: {
        color: {
          light: "info-500",
          dark: "info-500",
        },
      },
    },
    dark: {
      font: {
        color: {
          light: "muted-900",
          dark: "muted-100",
        },
      },
    },
    black: {
      font: {
        color: {
          light: "black",
          dark: "white",
        },
      },
    },
  },
  transition: {
    property: "colors",
    duration: "300",
  },
};

const config$G = {
  theme: {
    nui: {
      [key$G]: defaultConfig$F,
    },
  },
};
const dropdownItem = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$G}`);
  addComponents({
    //Wrapper
    ".nui-dropdown-item": {
      "@apply nui-focus flex w-full items-center justify-start gap-2 px-3 py-2 cursor-pointer":
        {},
      //Font
      [`@apply text-${config2.align} font-${config2.font.family} text-${config2.font.size}`]:
        {},
      //Transition
      [`@apply transition-${config2.transition.property} duration-${config2.transition.duration}`]:
        {},
      //Item:content
      ".nui-item-content": {
        "@apply grow": {},
      },
      //Item:text
      "&:not(.nui-active)": {
        [`@apply text-${config2.font.color.inactive.light} dark:text-${config2.font.color.inactive.dark}`]:
          {},
      },
      //Item:disabled
      "&.nui-item-disabled": {
        [`@apply opacity-50 pointer-events-none`]: {},
      },
      //Contrast:default
      "&.nui-item-default": {
        //Background:hover
        [`@apply hover:bg-${config2.contrast.default.background.light} dark:hover:bg-${config2.contrast.default.background.dark}`]:
          {},
        "&.nui-active": {
          //Background:hover
          [`@apply bg-${config2.contrast.default.background.light} dark:bg-${config2.contrast.default.background.dark}`]:
            {},
        },
      },
      //Contrast:contrast
      "&.nui-item-contrast": {
        //Background:hover
        [`@apply hover:bg-${config2.contrast.contrast.background.light} dark:hover:bg-${config2.contrast.contrast.background.dark}`]:
          {},
        "&.nui-active": {
          //Background:hover
          [`@apply bg-${config2.contrast.contrast.background.light} dark:bg-${config2.contrast.contrast.background.dark}`]:
            {},
        },
      },
      //Color:primary
      "&.nui-item-primary": {
        //Font:hover
        [`@apply hover:text-${config2.color.primary.font.color.light} dark:hover:text-${config2.color.primary.font.color.dark}`]:
          {},
      },
      //Color:info
      "&.nui-item-info": {
        //Font:hover
        [`@apply hover:text-${config2.color.info.font.color.light} dark:hover:text-${config2.color.info.font.color.dark}`]:
          {},
      },
      //Color:success
      "&.nui-item-success": {
        //Font:hover
        [`@apply hover:text-${config2.color.success.font.color.light} dark:hover:text-${config2.color.success.font.color.dark}`]:
          {},
      },
      //Color:warning
      "&.nui-item-warning": {
        //Font:hover
        [`@apply hover:text-${config2.color.warning.font.color.light} dark:hover:text-${config2.color.warning.font.color.dark}`]:
          {},
      },
      //Color:danger
      "&.nui-item-danger": {
        //Font:hover
        [`@apply hover:text-${config2.color.danger.font.color.light} dark:hover:text-${config2.color.danger.font.color.dark}`]:
          {},
      },
      //Color:dark
      "&.nui-item-dark": {
        //Font:hover
        [`@apply hover:text-${config2.color.dark.font.color.light} dark:hover:text-${config2.color.dark.font.color.dark}`]:
          {},
      },
      //Color:black
      "&.nui-item-black": {
        //Font:hover
        [`@apply hover:text-${config2.color.black.font.color.light} dark:hover:text-${config2.color.black.font.color.dark}`]:
          {},
      },
      //Rounded:sm
      "&.nui-item-rounded-sm": {
        [`@apply rounded-${config2.rounded.sm}`]: {},
      },
      //Rounded:md
      "&.nui-item-rounded-md": {
        [`@apply rounded-${config2.rounded.md}`]: {},
      },
      //Rounded:lg
      "&.nui-item-rounded-lg": {
        [`@apply rounded-${config2.rounded.lg}`]: {},
      },
    },
  });
}, config$G);

module.export = dropdownItem;

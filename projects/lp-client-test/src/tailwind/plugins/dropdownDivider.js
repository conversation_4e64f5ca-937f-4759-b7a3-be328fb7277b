import plugin from "tailwindcss/plugin";

const key$H = "dropdownDivider";
const defaultConfig$G = {
  margin: {
    y: "2",
  },
  border: {
    light: "muted-200",
    dark: "muted-700",
  },
};

const config$H = {
  theme: {
    nui: {
      [key$H]: defaultConfig$G,
    },
  },
};
const dropdownDivider = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$H}`);
  addComponents({
    ".nui-dropdown-divider": {
      [`@apply my-${config2.margin.y} block h-px w-full border-t border-${config2.border.light} dark:border-${config2.border.dark}`]:
        {},
    },
  });
}, config$H);

module.export = dropdownDivider;

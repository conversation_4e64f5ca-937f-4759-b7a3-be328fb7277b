import plugin from "tailwindcss/plugin";

const key$Q = "avatarGroup";
const defaultConfig$P = {
  avatar: {
    rounded: "full",
    outer: {
      background: {
        light: "white",
        dark: "muted-800",
      },
    },
    inner: {
      background: {
        light: "primary-500/20",
        dark: "primary-500/20",
      },
      font: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    transition: {
      property: "all",
      duration: "100",
    },
  },
  count: {
    outer: {
      rounded: "full",
      background: {
        light: "white",
        dark: "muted-800",
      },
    },
    inner: {
      rounded: "full",
      size: "full",
      background: {
        light: "muted-200",
        dark: "muted-700",
      },
      border: {
        light: "white",
        dark: "muted-800",
      },
    },
    text: {
      font: {
        family: "sans",
        weight: "medium",
        color: {
          light: "muted-500",
          dark: "muted-300",
        },
      },
    },
    transition: {
      property: "all",
      duration: "100",
    },
  },
  size: {
    xxs: {
      outer: {
        size: "6",
      },
      count: {
        size: "6",
        font: {
          size: "xs",
        },
      },
    },
    xs: {
      outer: {
        size: "8",
      },
      count: {
        size: "8",
        font: {
          size: "sm",
        },
      },
    },
    sm: {
      outer: {
        size: "10",
      },
      count: {
        size: "10",
        font: {
          size: "sm",
        },
      },
    },
    md: {
      outer: {
        size: "12",
      },
      count: {
        size: "12",
        font: {
          size: "lg",
        },
      },
    },
    lg: {
      outer: {
        size: "16",
      },
      count: {
        size: "16",
        font: {
          size: "xl",
        },
      },
    },
  },
};

const config$Q = {
  theme: {
    nui: {
      [key$Q]: defaultConfig$P,
    },
  },
};
const avatarGroup = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$Q}`);
  addComponents({
    ".nui-avatar-group": {
      "@apply flex": {},
      //AvatarGroup:outer
      ".nui-avatar-outer": {
        //Base
        [`@apply relative flex shrink-0 items-center justify-center rounded-${config2.avatar.rounded}`]:
          {},
        //Background
        [`@apply bg-${config2.avatar.outer.background.light} dark:bg-${config2.avatar.outer.background.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.avatar.transition.property} duration-${config2.avatar.transition.duration} ease-in`]:
          {},
        //AvatarGroup:inner
        ".nui-avatar": {
          [`@apply bg-${config2.avatar.inner.background.light} text-${config2.avatar.inner.background.dark} !scale-90`]:
            {},
        },
      },
      //AvatarGroup:count
      ".nui-avatar-count": {
        //Base
        [`@apply relative shrink-0 rounded-${config2.count.outer.rounded}`]: {},
        //Background
        [`@apply bg-${config2.count.outer.background.light} dark:bg-${config2.count.outer.background.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.count.transition.property} duration-${config2.count.transition.duration} ease-in`]:
          {},
        //Count:inner
        ".nui-avatar-count-inner": {
          [`@apply relative scale-90 inline-flex items-center justify-center rounded-${config2.count.inner.rounded}`]:
            {},
          //Size
          [`@apply h-${config2.count.inner.size} w-${config2.count.inner.size}`]:
            {},
          //Background
          [`@apply bg-${config2.count.inner.background.light} dark:bg-${config2.count.inner.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.count.inner.border.light} dark:border-${config2.count.inner.border.dark}`]:
            {},
        },
        //Count:text
        ".nui-avatar-count-text": {
          "@apply -ms-1 uppercase": {},
          //Font
          [`@apply font-${config2.count.text.font.family} font-${config2.count.text.font.weight}`]:
            {},
          //Font Color
          [`@apply text-${config2.count.text.font.color.light} dark:text-${config2.count.text.font.color.dark}`]:
            {},
        },
      },
      //Size:xxs
      "&.nui-avatar-group-xxs": {
        //outer
        ".nui-avatar-outer": {
          [`@apply h-${config2.size.xxs.outer.size} w-${config2.size.xxs.outer.size}`]:
            {},
          "&:first-child": {
            "@apply hover:-ms-2 hover:me-2 focus:-ms-2 focus:me-2": {},
          },
          "&:not(:first-child)": {
            "@apply -ms-2 hover:-ms-4 hover:me-2 focus:-ms-4 focus:me-2": {},
          },
        },
        //count
        ".nui-avatar-count": {
          [`@apply -ms-2 h-${config2.size.xxs.count.size} w-${config2.size.xxs.count.size}`]:
            {},
          ".nui-avatar-count-text": {
            [`@apply text-${config2.size.xxs.count.font.size}`]: {},
          },
        },
      },
      //Size:xs
      "&.nui-avatar-group-xs": {
        //outer
        ".nui-avatar-outer": {
          [`@apply h-${config2.size.xs.outer.size} w-${config2.size.xs.outer.size}`]:
            {},
          "&:first-child": {
            "@apply hover:-ms-2 hover:me-2 focus:-ms-2 focus:me-2": {},
          },
          "&:not(:first-child)": {
            "@apply -ms-2 hover:-ms-4 hover:me-2 focus:-ms-4 focus:me-2": {},
          },
        },
        //count
        ".nui-avatar-count": {
          [`@apply -ms-2 h-${config2.size.xs.count.size} w-${config2.size.xs.count.size}`]:
            {},
          ".nui-avatar-count-text": {
            [`@apply text-${config2.size.xs.count.font.size}`]: {},
          },
        },
      },
      //Size:sm
      "&.nui-avatar-group-sm": {
        //outer
        ".nui-avatar-outer": {
          [`@apply h-${config2.size.sm.outer.size} w-${config2.size.sm.outer.size}`]:
            {},
          "&:first-child": {
            "@apply hover:-ms-2 hover:me-2 focus:me-2": {},
          },
          "&:not(:first-child)": {
            "@apply -ms-3 hover:-ms-5 hover:me-2 focus:-ms-5 focus:me-2": {},
          },
        },
        //count
        ".nui-avatar-count": {
          [`@apply -ms-3 h-${config2.size.sm.count.size} w-${config2.size.sm.count.size}`]:
            {},
          ".nui-avatar-count-text": {
            [`@apply text-${config2.size.sm.count.font.size}`]: {},
          },
        },
      },
      //Size:md
      "&.nui-avatar-group-md": {
        //outer
        ".nui-avatar-outer": {
          [`@apply h-${config2.size.md.outer.size} w-${config2.size.md.outer.size}`]:
            {},
          "&:first-child": {
            "@apply hover:-ms-3 hover:me-3 focus:me-3": {},
          },
          "&:not(:first-child)": {
            "@apply -ms-4 hover:-ms-7 hover:me-3 focus:me-3": {},
          },
        },
        //count
        ".nui-avatar-count": {
          [`@apply -ms-4 h-${config2.size.md.count.size} w-${config2.size.md.count.size}`]:
            {},
          ".nui-avatar-count-text": {
            [`@apply text-${config2.size.md.count.font.size}`]: {},
          },
        },
      },
      //Size:lg
      "&.nui-avatar-group-lg": {
        ".nui-avatar-outer": {
          [`@apply h-${config2.size.lg.outer.size} w-${config2.size.lg.outer.size}`]:
            {},
          "&:first-child": {
            "@apply hover:-ms-4 hover:me-4 focus:me-4": {},
          },
          "&:not(:first-child)": {
            "@apply -ms-5 hover:-ms-9 hover:me-4 focus:-ms-9 focus:me-4": {},
          },
        },
        ".nui-avatar-count": {
          [`@apply -ms-5 h-${config2.size.lg.count.size} w-${config2.size.lg.count.size}`]:
            {},
          ".nui-avatar-count-text": {
            [`@apply text-${config2.size.lg.count.font.size}`]: {},
          },
        },
      },
    },
  });
}, config$Q);

module.export = avatarGroup;

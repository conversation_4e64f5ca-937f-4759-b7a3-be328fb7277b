import plugin from "tailwindcss/plugin";

const key$i = "placeload";
const defaultConfig$h = {};

const config$i = {
  theme: {
    nui: {
      [key$i]: defaultConfig$h,
    },
    extend: {
      keyframes: {
        "nui-placeload": {
          "0%": { "background-position": "-468px 0" },
          "100%": { "background-position": "468px 0" },
        },
      },
      animation: {
        "nui-placeload": `nui-placeload 1s linear infinite forwards`,
      },
    },
  },
};
const placeload = plugin(
  ({ addComponents }) =>
    addComponents({
      ".nui-placeload": {
        position: "relative",
        background:
          "linear-gradient( to right, rgb(0 0 0 / 7%) 8% ,rgb(0 0 0 / 15%) 18%, rgb(0 0 0 / 7%) 33%)",
        backgroundSize: "1200px 104px",
      },
      ".dark .nui-placeload": {
        position: "relative",
        background:
          "linear-gradient(to right, rgb(255 255 255 / 15%) 8%, rgb(255 255 255 / 24%) 18%, rgb(255 255 255 / 15%) 33%)",
        backgroundSize: "1200px 104px",
      },
    }),
  config$i
);

module.export = placeload;

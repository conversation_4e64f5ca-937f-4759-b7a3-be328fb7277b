import plugin from "tailwindcss/plugin";

const key$k = "paragraph";
const defaultConfig$j = {
  size: {
    xs: "xs",
    sm: "sm",
    md: "base",
    lg: "lg",
    xl: "xl",
    xxl: "2xl",
    xxxl: "3xl",
    xxxxl: "4xl",
    xxxxxl: "5xl",
    xxxxxxl: "6xl",
    xxxxxxxl: "7xl",
    xxxxxxxxl: "8xl",
    xxxxxxxxxl: "9xl",
  },
  weight: {
    light: "light",
    normal: "normal",
    medium: "medium",
    semibold: "semibold",
    bold: "bold",
    extrabold: "extrabold",
  },
  lead: {
    none: "none",
    normal: "normal",
    tight: "tight",
    snug: "snug",
    loose: "loose",
  },
};

const config$k = {
  theme: {
    nui: {
      [key$k]: defaultConfig$j,
    },
  },
};
const paragraph = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$k}`);
  addComponents({
    ".nui-paragraph": {
      "@apply font-sans": {},
      //Size
      "&.nui-paragraph-xs": {
        [`@apply text-${config2.size.xs}`]: {},
      },
      "&.nui-paragraph-sm": {
        [`@apply text-${config2.size.sm}`]: {},
      },
      "&.nui-paragraph-md": {
        [`@apply text-${config2.size.md}`]: {},
      },
      "&.nui-paragraph-lg": {
        [`@apply text-${config2.size.lg}`]: {},
      },
      "&.nui-paragraph-xl": {
        [`@apply text-${config2.size.xl}`]: {},
      },
      "&.nui-paragraph-2xl": {
        [`@apply text-${config2.size.xxl}`]: {},
      },
      "&.nui-paragraph-3xl": {
        [`@apply text-${config2.size.xxxl}`]: {},
      },
      "&.nui-paragraph-4xl": {
        [`@apply text-${config2.size.xxxxl}`]: {},
      },
      "&.nui-paragraph-5xl": {
        [`@apply text-${config2.size.xxxxxl}`]: {},
      },
      "&.nui-paragraph-6xl": {
        [`@apply text-${config2.size.xxxxxxl}`]: {},
      },
      "&.nui-paragraph-7xl": {
        [`@apply text-${config2.size.xxxxxxxl}`]: {},
      },
      "&.nui-paragraph-8xl": {
        [`@apply text-${config2.size.xxxxxxxxl}`]: {},
      },
      "&.nui-paragraph-9xl": {
        [`@apply text-${config2.size.xxxxxxxxxl}`]: {},
      },
      //Weight
      "&.nui-weight-light": {
        [`@apply font-${config2.weight.light}`]: {},
      },
      "&.nui-weight-normal": {
        [`@apply font-${config2.weight.normal}`]: {},
      },
      "&.nui-weight-medium": {
        [`@apply font-${config2.weight.medium}`]: {},
      },
      "&.nui-weight-semibold": {
        [`@apply font-${config2.weight.semibold}`]: {},
      },
      "&.nui-weight-bold": {
        [`@apply font-${config2.weight.bold}`]: {},
      },
      "&.nui-weight-extrabold": {
        [`@apply font-${config2.weight.extrabold}`]: {},
      },
      //Lead
      "&.nui-lead-none": {
        [`@apply leading-${config2.lead.none}`]: {},
      },
      "&.nui-lead-normal": {
        [`@apply leading-${config2.lead.normal}`]: {},
      },
      "&.nui-lead-tight": {
        [`@apply leading-${config2.lead.tight}`]: {},
      },
      "&.nui-lead-snug": {
        [`@apply leading-${config2.lead.snug}`]: {},
      },
      "&.nui-lead-loose": {
        [`@apply leading-${config2.lead.loose}`]: {},
      },
    },
  });
}, config$k);

module.export = paragraph;

import plugin from "tailwindcss/plugin";

const key$E = "focus";
const defaultConfig$D = {
  offset: "2",
  width: "2",
  style: "dashed",
  color: {
    light: "muted-300",
    dark: "muted-600",
  },
  mode: "always",
};

const config$E = {
  theme: {
    nui: {
      [key$E]: defaultConfig$D,
    },
  },
};
const focus = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$E}`);
  const mode =
    config2.mode === "focus-visible"
      ? "&:has(:focus-visible), &:focus-visible"
      : "&:focus-within";
  addComponents({
    ".nui-focus": {
      [`@apply outline-${config2.width} outline-${config2.style} outline-offset-${config2.offset}`]:
        {},
      "@apply outline-transparent": {},
      [mode]: {
        [`@apply outline-${config2.color.light} dark:outline-${config2.color.dark}`]:
          {},
        [`@apply outline-${config2.style} ring-0`]: {},
      },
    },
  });
}, config$E);

module.export = focus;

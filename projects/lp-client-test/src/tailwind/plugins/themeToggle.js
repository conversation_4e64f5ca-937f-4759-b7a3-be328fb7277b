import plugin from "tailwindcss/plugin";

const key$2 = "themeToggle";
const defaultConfig$2 = {
  outer: {
    size: "9",
    rounded: "full",
  },
  ring: {
    light: "muted-200",
    dark: "muted-900",
  },
  inner: {
    size: "9",
    rounded: "full",
    background: {
      light: "white",
      dark: "muted-800",
    },
    border: {
      light: "muted-300",
      dark: "muted-700",
    },
  },
  input: {
    size: "full",
  },
  icon: {
    sun: {
      size: "5",
      color: "yellow-400",
      duration: "300",
    },
    moon: {
      size: "5",
      color: "yellow-400",
      duration: "300",
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  inverted: {
    enabled: {
      ring: {
        light: "muted-500",
        dark: "muted-400",
      },
      inner: {
        background: {
          light: "primary-700",
          dark: "primary-700",
        },
      },
    },
    disabled: {
      ring: {
        light: "muted-500",
        dark: "muted-900",
      },
      inner: {
        background: {
          light: "white",
          dark: "muted-800",
        },
        border: {
          light: "muted-300",
          dark: "muted-700",
        },
      },
    },
  },
  transition: {
    property: "all",
    duration: "300",
  },
};

const config$2 = {
  theme: {
    nui: {
      [key$2]: defaultConfig$2,
    },
  },
};
const themeToggle = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$2}`);
  addComponents({
    //Wrapper
    ".nui-theme-toggle": {
      //Base
      [`@apply nui-focus relative block shrink-0 overflow-hidden rounded-${config2.outer.rounded}`]:
        {},
      //Size
      [`@apply h-${config2.outer.size} w-${config2.outer.size}`]: {},
      //Ring
      [`@apply focus-visible:outline-2 ring-2 ring-transparent ring-offset-${config2.ring.light} dark:ring-offset-${config2.ring.dark}`]:
        {},
      //Transition
      [`@apply transition-${config2.transition.property} duration-${config2.transition.duration}`]:
        {},
      //Toggle:inner
      ".nui-theme-toggle-inner": {
        [`@apply relative block rounded-${config2.inner.rounded}`]: {},
        //Size
        [`@apply h-${config2.inner.size} w-${config2.inner.size}`]: {},
        //Background
        [`@apply bg-${config2.inner.background.light} dark:bg-${config2.inner.background.dark}`]:
          {},
        //Border
        [`@apply border border-${config2.inner.border.light} dark:border-${config2.inner.border.dark}`]:
          {},
      },
      //Toggle:input
      ".nui-theme-toggle-input": {
        [`@apply absolute start-0 top-0 z-[2] h-${config2.input.size} w-${config2.input.size} cursor-pointer opacity-0`]:
          {},
      },
      //Icon:sun
      ".nui-sun": {
        //Base
        "@apply pointer-events-none absolute start-1/2 top-1/2 block -translate-y-1/2 translate-x-[-50%] rtl:translate-x-[50%]":
          {},
        //Size
        [`@apply h-${config2.icon.sun.size} w-${config2.icon.sun.size}`]: {},
        //Color
        [`@apply text-${config2.icon.sun.color} dark:text-${config2.icon.sun.color}`]:
          {},
        //Transition
        [`@apply transition-${config2.icon.transition.property} duration-${config2.icon.transition.duration}`]:
          {},
      },
      //Icon:moon
      ".nui-moon": {
        "@apply pointer-events-none absolute start-1/2 top-1/2 block translate-x-[-50%] rtl:translate-x-[45%]":
          {},
        //Size
        [`@apply h-${config2.icon.moon.size} w-${config2.icon.moon.size}`]: {},
        //Color
        [`@apply text-${config2.icon.moon.color} dark:text-${config2.icon.moon.color}`]:
          {},
        //Transition
        [`@apply transition-${config2.icon.transition.property} duration-${config2.icon.transition.duration}`]:
          {},
      },
      //Toggle:not-checked:sun
      ".nui-theme-toggle-input:not(:checked) ~ .nui-theme-toggle-inner .nui-sun":
        {
          "@apply -translate-y-1/2 opacity-100": {},
        },
      //Toggle:checked:sun
      ".nui-theme-toggle-input:checked ~ .nui-theme-toggle-inner .nui-sun": {
        "@apply translate-y-[-150%] opacity-0": {},
      },
      //Toggle:checked:moon
      ".nui-theme-toggle-input:not(:checked) ~ .nui-theme-toggle-inner .nui-moon":
        {
          "@apply translate-y-[-150%] opacity-0": {},
        },
      //Toggle:not-checked:moon
      ".nui-theme-toggle-input:checked ~ .nui-theme-toggle-inner .nui-moon": {
        "@apply -translate-y-1/2 opacity-100": {},
      },
      //Toggle:inverted
      "&.nui-theme-toggle-inverted": {
        [`@apply ring-offset-${config2.inverted.enabled.ring.light} dark:ring-offset-${config2.inverted.enabled.ring.dark}`]:
          {},
        ".nui-toggle-inner": {
          [`@apply !bg-${config2.inverted.enabled.inner.background.light} dark:!bg-${config2.inverted.enabled.inner.background.dark}`]:
            {},
        },
      },
      //Toggle:not-inverted
      "&:not(nui-theme-toggle-inverted)": {
        //Ring
        [`@apply ring-offset-${config2.inverted.disabled.ring.light} dark:ring-offset-${config2.inverted.disabled.ring.dark}`]:
          {},
        ".nui-toggle-inner": {
          //Border
          [`@apply border border-${config2.inverted.disabled.inner.background.light} dark:border-${config2.inverted.disabled.inner.background.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.inverted.disabled.inner.background.light} dark:bg-${config2.inverted.disabled.inner.background.dark}`]:
            {},
        },
      },
    },
  });
}, config$2);

module.export = themeToggle;

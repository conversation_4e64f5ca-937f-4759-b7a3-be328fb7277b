import plugin from "tailwindcss/plugin";

const key$P = "breadcrumb";
const defaultConfig$O = {
  item: {
    text: {
      font: {
        family: "sans",
        size: "[0.85rem]",
        color: {
          light: "muted-500",
          dark: "muted-400",
        },
      },
      padding: {
        x: "2",
      },
    },
    icon: {
      size: "4",
    },
    dot: {
      font: {
        size: "xl",
      },
    },
    link: {
      font: {
        color: {
          primary: {
            light: {
              hover: "primary-500",
              focus: "primary-500",
            },
            dark: {
              hover: "primary-500",
              focus: "primary-500",
            },
          },
          dark: {
            light: {
              hover: "muted-900",
              focus: "muted-900",
            },
            dark: {
              hover: "muted-100",
              focus: "muted-100",
            },
          },
          black: {
            light: {
              hover: "black",
              focus: "black",
            },
            dark: {
              hover: "white",
              focus: "white",
            },
          },
        },
      },
    },
    transition: {
      property: "colors",
      duration: "300",
    },
  },
};

const config$P = {
  theme: {
    nui: {
      [key$P]: defaultConfig$O,
    },
  },
};
const breadcrumb = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$P}`);
  addComponents({
    ".nui-breadcrumb": {
      ".nui-breadcrumb-list": {
        [`@apply mb-6 flex items-center font-${config2.item.text.font.family} text-${config2.item.text.font.size}`]:
          {},
        ".nui-breadcrumb-item-mobile": {
          "@apply me-3 sm:hidden": {},
        },
        ".nui-breadcrumb-item:not(:last-child)": {
          "@apply hidden sm:flex": {},
        },
        ".nui-breadcrumb-item:last-child": {
          [`@apply flex text-${config2.item.text.font.color.light} dark:text-${config2.item.text.font.color.dark}`]:
            {},
        },
        ".nui-item-inner": {
          //Base
          [`@apply text-${config2.item.text.font.size} flex items-center gap-x-1`]:
            {},
          //Color
          [`@apply text-${config2.item.text.font.color.light} dark:text-${config2.item.text.font.color.dark}`]:
            {},
          //Transition
          [`@apply transition-${config2.item.transition.property} duration-${config2.item.transition.duration}`]:
            {},
          //Item:icon
          ".nui-item-icon": {
            //icon
            [`@apply flex items-center justify-center h-${config2.item.icon.size} w-${config2.item.icon.size} shrink-0`]:
              {},
            //dot
            "&.nui-has-dot": {
              [`@apply text-${config2.item.dot.font.size}`]: {},
            },
          },
          //Item:text
          ".nui-item-text": {
            [`@apply text-${config2.item.text.font.color.light} dark:text-${config2.item.text.font.color.dark} px-${config2.item.text.padding.x}`]:
              {},
          },
        },
      },
      //Color:primary
      "&.nui-breadcrumb-primary": {
        ".nui-item-inner": {
          "&.nui-has-link": {
            [`@apply hover:text-${config2.item.link.font.color.primary.light.hover} focus:text-${config2.item.link.font.color.primary.light.focus}`]:
              {},
            [`@apply dark:hover:text-${config2.item.link.font.color.primary.dark.hover} dark:focus:text-${config2.item.link.font.color.primary.dark.focus}`]:
              {},
          },
        },
      },
      //Color:dark
      "&.nui-breadcrumb-dark": {
        ".nui-item-inner": {
          "&.nui-has-link": {
            [`@apply hover:text-${config2.item.link.font.color.dark.light.hover} focus:text-${config2.item.link.font.color.dark.light.focus}`]:
              {},
            [`@apply dark:hover:text-${config2.item.link.font.color.dark.dark.hover} dark:focus:text-${config2.item.link.font.color.dark.dark.focus}`]:
              {},
          },
        },
      },
      //Color:black
      "&.nui-breadcrumb-black": {
        ".nui-item-inner": {
          "&.nui-has-link": {
            [`@apply hover:text-${config2.item.link.font.color.black.light.hover} focus:text-${config2.item.link.font.color.black.light.focus}`]:
              {},
            [`@apply dark:hover:text-${config2.item.link.font.color.black.dark.hover} dark:focus:text-${config2.item.link.font.color.black.dark.focus}`]:
              {},
          },
        },
      },
    },
  });
}, config$P);

module.export = breadcrumb;

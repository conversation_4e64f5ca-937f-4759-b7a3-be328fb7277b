import plugin from "tailwindcss/plugin";

const key$q = "mark";
const defaultConfig$p = {
  background: {
    light: "primary-100",
    dark: "primary-800",
  },
  color: {
    light: "primary-800",
    dark: "primary-200",
  },
};

const config$q = {
  theme: {
    nui: {
      [key$q]: defaultConfig$p,
    },
  },
};
const mark = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$q}`);
  addComponents({
    ".nui-mark": {
      [`@apply bg-${config2.background.light} dark:bg-${config2.background.dark}`]:
        {},
      [`@apply text-${config2.color.light} dark:text-${config2.color.dark}`]:
        {},
    },
  });
}, config$q);

module.export = mark;

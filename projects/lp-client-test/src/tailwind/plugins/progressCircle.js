import plugin from "tailwindcss/plugin";
const key$h = "progressCircle";
const defaultConfig$g = {
  transition: {
    property: "all",
    duration: "500",
  },
  color: {
    default: {
      color: {
        light: "muted-200",
        dark: "muted-700",
        stroke: "current",
      },
    },
    contrast: {
      color: {
        light: "muted-200",
        dark: "muted-900",
        stroke: "current",
      },
    },
  },
};

const config$h = {
  theme: {
    nui: {
      [key$h]: defaultConfig$g,
    },
  },
};
const progressCircle = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$h}`);
  addComponents({
    ".nui-progress-circle": {
      "@apply relative inline-flex items-center justify-center": {},
      //Circle:progress
      "circle:nth-child(2)": {
        [`@apply stroke-current transition-${config2.transition.property} duration-${config2.transition.duration}`]:
          {},
      },
      //Color:default
      "&.nui-progress-default circle:first-child": {
        [`@apply text-${config2.color.default.color.light} dark:text-${config2.color.default.color.dark} stroke-${config2.color.default.color.stroke}`]:
          {},
      },
      //Color:contrast
      "&.nui-progress-contrast circle:first-child": {
        [`@apply text-${config2.color.contrast.color.light} dark:text-${config2.color.contrast.color.light} stroke-${config2.color.contrast.color.stroke}`]:
          {},
      },
    },
  });
}, config$h);
module.export = progressCircle;

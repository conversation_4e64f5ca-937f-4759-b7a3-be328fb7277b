import plugin from "tailwindcss/plugin";

const key$1 = "toast";
const defaultConfig$1 = {
  font: {
    family: "sans",
  },
  title: {
    font: {
      size: "sm",
      weight: "medium",
      color: {
        light: "muted-800",
        dark: "muted-100",
      },
    },
  },
  subtitle: {
    font: {
      size: "xs",
      color: {
        light: "muted-400",
        dark: "muted-500",
      },
    },
  },
  icon: {
    outer: {
      size: "16",
    },
    inner: {
      size: "6",
    },
  },
  contrast: {
    lowContrast: {
      background: {
        light: "white",
        dark: "muted-800",
      },
      border: {
        light: "muted-300",
        dark: "muted-700",
      },
    },
    highContrast: {
      background: {
        light: "white",
        dark: "muted-950",
      },
      border: {
        light: "muted-300",
        dark: "muted-800",
      },
    },
  },
  color: {
    default: {
      icon: {
        outer: {
          background: {
            light: "muted-100",
            dark: "muted-700",
          },
        },
        inner: {
          color: {
            light: "muted-500",
            dark: "muted-500",
          },
        },
      },
    },
    primary: {
      border: {
        light: "primary-500",
        dark: "primary-500",
      },
      icon: {
        outer: {
          background: {
            light: "primary-500/10",
            dark: "primary-500/10",
          },
        },
        inner: {
          color: {
            light: "primary-500",
            dark: "primary-500",
          },
        },
      },
    },
    info: {
      border: {
        light: "info-500",
        dark: "info-500",
      },
      icon: {
        outer: {
          background: {
            light: "info-500/10",
            dark: "info-500/10",
          },
        },
        inner: {
          color: {
            light: "info-500",
            dark: "info-500",
          },
        },
      },
    },
    success: {
      border: {
        light: "success-500",
        dark: "success-500",
      },
      icon: {
        outer: {
          background: {
            light: "success-500/10",
            dark: "success-500/10",
          },
        },
        inner: {
          color: {
            light: "success-500",
            dark: "success-500",
          },
        },
      },
    },
    warning: {
      border: {
        light: "warning-500",
        dark: "warning-500",
      },
      icon: {
        outer: {
          background: {
            light: "warning-500/10",
            dark: "warning-500/10",
          },
        },
        inner: {
          color: {
            light: "warning-500",
            dark: "warning-500",
          },
        },
      },
    },
    danger: {
      border: {
        light: "danger-500",
        dark: "danger-500",
      },
      icon: {
        outer: {
          background: {
            light: "danger-500/10",
            dark: "danger-500/10",
          },
        },
        inner: {
          color: {
            light: "danger-500",
            dark: "danger-500",
          },
        },
      },
    },
    dark: {
      border: {
        light: "muted-900",
        dark: "muted-100",
      },
      icon: {
        outer: {
          background: {
            light: "muted-900/10",
            dark: "muted-100/10",
          },
        },
        inner: {
          color: {
            light: "muted-900",
            dark: "muted-100",
          },
        },
      },
    },
    black: {
      border: {
        light: "black",
        dark: "white",
      },
      icon: {
        outer: {
          background: {
            light: "black/10",
            dark: "white/10",
          },
        },
        inner: {
          color: {
            light: "black",
            dark: "white",
          },
        },
      },
    },
  },
  rounded: {
    sm: "md",
    md: "lg",
    lg: "xl",
  },
  shadow: {
    light: "muted-300/30",
    dark: "muted-800/30",
    size: "xl",
  },
};

const config$1 = {
  theme: {
    nui: {
      [key$1]: defaultConfig$1,
    },
  },
};
const toast = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$1}`);
  addComponents({
    //Wrapper
    ".nui-toast": {
      [`@apply relative flex font-${config2.font.family} overflow-hidden`]: {},
      //Toast:inner
      ".nui-toast-inner": {
        "@apply flex items-center grow gap-2 px-4 py-3": {},
      },
      //Toast:title
      ".nui-toast-title": {
        //Font
        [`@apply nui-heading nui-heading-${config2.title.font.size} nui-weight-${config2.title.font.weight}`]:
          {},
        //Color
        [`@apply text-${config2.title.font.color.light} dark:text-${config2.title.font.color.dark}`]:
          {},
      },
      //Toast:subtitle
      ".nui-toast-subtitle": {
        //Font
        [`@apply nui-paragraph text-${config2.subtitle.font.size}`]: {},
        //color
        [`@apply text-${config2.subtitle.font.color.light} dark:text-${config2.subtitle.font.color.dark}`]:
          {},
      },
      //Icon:inner
      ".nui-toast-icon": {
        [`@apply w-${config2.icon.inner.size} h-${config2.icon.inner.size}`]:
          {},
      },
      //Icon:outer
      ".nui-toast-icon-block": {
        [`@apply h-${config2.icon.outer.size} w-${config2.icon.outer.size} flex items-center justify-center`]:
          {},
      },
      //Toast:close
      ".nui-button-close": {
        "@apply ms-auto": {},
      },
      //Type:low-contrast
      "&.nui-toast-low-contrast": {
        //Background
        [`@apply bg-${config2.contrast.lowContrast.background.light} dark:bg-${config2.contrast.lowContrast.background.dark}`]:
          {},
        //Border
        [`@apply border-2 border-${config2.contrast.lowContrast.border.light} dark:border-${config2.contrast.lowContrast.border.dark}`]:
          {},
      },
      //Type:high-contrast
      "&.nui-toast-high-contrast": {
        //Background
        [`@apply bg-${config2.contrast.highContrast.background.light} dark:bg-${config2.contrast.highContrast.background.dark}`]:
          {},
        //Border
        [`@apply border-2 border-${config2.contrast.highContrast.border.light} dark:border-${config2.contrast.highContrast.border.dark}`]:
          {},
      },
      //Color:default
      "&.nui-toast-default": {
        //Icon:inner
        ".nui-toast-icon": {
          [`@apply text-${config2.color.default.icon.inner.color.light} dark:text-${config2.color.default.icon.inner.color.dark}`]:
            {},
        },
        //Icon:outer
        ".nui-toast-icon-block": {
          [`@apply bg-${config2.color.default.icon.outer.background.light} dark:bg-${config2.color.default.icon.outer.background.dark}`]:
            {},
        },
      },
      //Color:primary
      "&.nui-toast-primary": {
        //Border
        [`@apply border-2 border-${config2.color.primary.border.light} dark:border-${config2.color.primary.border.light}`]:
          {},
        //Icon:inner
        ".nui-toast-icon": {
          [`@apply text-${config2.color.primary.icon.inner.color.light} dark:text-${config2.color.primary.icon.inner.color.dark}`]:
            {},
        },
        //Icon:outer
        ".nui-toast-icon-block": {
          [`@apply bg-${config2.color.primary.icon.outer.background.light} dark:bg-${config2.color.primary.icon.outer.background.dark}`]:
            {},
        },
      },
      //Color:info
      "&.nui-toast-info": {
        //Border
        [`@apply border-2 border-${config2.color.info.border.light} dark:border-${config2.color.info.border.light}`]:
          {},
        //Icon:inner
        ".nui-toast-icon": {
          [`@apply text-${config2.color.info.icon.inner.color.light} dark:text-${config2.color.info.icon.inner.color.dark}`]:
            {},
        },
        //Icon:outer
        ".nui-toast-icon-block": {
          [`@apply bg-${config2.color.info.icon.outer.background.light} dark:bg-${config2.color.info.icon.outer.background.dark}`]:
            {},
        },
      },
      //Color:success
      "&.nui-toast-success": {
        //Border
        [`@apply border-2 border-${config2.color.success.border.light} dark:border-${config2.color.success.border.light}`]:
          {},
        //Icon:inner
        ".nui-toast-icon": {
          [`@apply text-${config2.color.success.icon.inner.color.light} dark:text-${config2.color.success.icon.inner.color.dark}`]:
            {},
        },
        //Icon:outer
        ".nui-toast-icon-block": {
          [`@apply bg-${config2.color.success.icon.outer.background.light} dark:bg-${config2.color.success.icon.outer.background.dark}`]:
            {},
        },
      },
      //Color:warning
      "&.nui-toast-warning": {
        //Border
        [`@apply border-2 border-${config2.color.warning.border.light} dark:border-${config2.color.warning.border.light}`]:
          {},
        //Icon:inner
        ".nui-toast-icon": {
          [`@apply text-${config2.color.warning.icon.inner.color.light} dark:text-${config2.color.warning.icon.inner.color.dark}`]:
            {},
        },
        //Icon:outer
        ".nui-toast-icon-block": {
          [`@apply bg-${config2.color.warning.icon.outer.background.light} dark:bg-${config2.color.warning.icon.outer.background.dark}`]:
            {},
        },
      },
      //Color:danger
      "&.nui-toast-danger": {
        //Border
        [`@apply border-2 border-${config2.color.danger.border.light} dark:border-${config2.color.danger.border.light}`]:
          {},
        //Icon:inner
        ".nui-toast-icon": {
          [`@apply text-${config2.color.danger.icon.inner.color.light} dark:text-${config2.color.danger.icon.inner.color.dark}`]:
            {},
        },
        //Icon:outer
        ".nui-toast-icon-block": {
          [`@apply bg-${config2.color.danger.icon.outer.background.light} dark:bg-${config2.color.danger.icon.outer.background.dark}`]:
            {},
        },
      },
      //Color:dark
      "&.nui-toast-dark": {
        //Border
        [`@apply border-2 border-${config2.color.dark.border.light} dark:border-${config2.color.dark.border.dark}`]:
          {},
        //Icon:inner
        ".nui-toast-icon": {
          [`@apply text-${config2.color.dark.icon.inner.color.light} dark:text-${config2.color.dark.icon.inner.color.dark}`]:
            {},
        },
        //Icon:outer
        ".nui-toast-icon-block": {
          [`@apply bg-${config2.color.dark.icon.outer.background.light} dark:bg-${config2.color.dark.icon.outer.background.dark}`]:
            {},
        },
      },
      //Color:black
      "&.nui-toast-black": {
        //Border
        [`@apply border-2 border-${config2.color.black.border.light} dark:border-${config2.color.black.border.dark}`]:
          {},
        //Icon:inner
        ".nui-toast-icon": {
          [`@apply text-${config2.color.black.icon.inner.color.light} dark:text-${config2.color.black.icon.inner.color.dark}`]:
            {},
        },
        //Icon:outer
        ".nui-toast-icon-block": {
          [`@apply bg-${config2.color.black.icon.outer.background.light} dark:bg-${config2.color.black.icon.outer.background.dark}`]:
            {},
        },
      },
      //Rounded:sm
      "&.nui-toast-rounded-sm": {
        [`@apply rounded-${config2.rounded.sm}`]: {},
      },
      //Rounded:md
      "&.nui-toast-rounded-md": {
        [`@apply rounded-${config2.rounded.md}`]: {},
      },
      //Rounded:lg
      "&.nui-toast-rounded-lg": {
        [`@apply rounded-${config2.rounded.lg}`]: {},
      },
      //Shadow:borderless
      "&.nui-toast-borderless": {
        [`@apply !border-0 shadow-${config2.shadow.light} dark:shadow-${config2.shadow.dark} shadow-${config2.shadow.size}`]:
          {},
      },
    },
  });
}, config$1);
module.export = toast;

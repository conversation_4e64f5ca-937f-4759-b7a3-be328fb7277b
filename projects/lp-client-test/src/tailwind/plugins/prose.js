import plugin from "tailwindcss/plugin";

const key$f = "prose";
const defaultConfig$e = {
  color: {
    light: "muted",
    dark: "invert",
  },
  padding: "4",
  table: {
    background: {
      light: "white",
      dark: "muted-800",
    },
    border: {
      light: "muted-200",
      dark: "muted-700",
    },
  },
  rounded: {
    sm: "rounded",
    md: "rounded-md",
    lg: "rounded-lg",
  },
};

const config$f = {
  theme: {
    nui: {
      [key$f]: defaultConfig$e,
    },
  },
};
const prose = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$f}`);
  addComponents({
    ".nui-prose": {
      //Base
      [`@apply prose prose-primary prose-${config2.color.light} dark:prose-${config2.color.dark}`]:
        {},
      //Table:background
      [`@apply prose-table:bg-${config2.table.background.light} dark:prose-table:bg-${config2.table.background.dark}`]:
        {},
      //Table:border
      [`@apply prose-table:border-separate prose-table:border-spacing-0 prose-table:border prose-table:border-${config2.table.border.light} dark:prose-table:border-${config2.table.border.dark}`]:
        {},
      //Table:th
      [`@apply prose-th:p-${config2.padding} prose-td:p-${config2.padding}`]:
        {},
      //Table:td
      [`@apply prose-td:border-t prose-td:border-${config2.table.border.light} dark:prose-td:border-${config2.table.border.dark}`]:
        {},
      //Rounded:none
      "&.nui-prose-rounded-none": {
        "@apply prose-img:rounded-none prose-pre:rounded-none prose-table:rounded-none":
          {},
      },
      //Rounded:sm
      "&.nui-prose-rounded-sm": {
        [`@apply prose-img:${config2.rounded.sm} prose-pre:${config2.rounded.sm} prose-table:${config2.rounded.sm}`]:
          {},
      },
      //Rounded:md
      "&.nui-prose-rounded-md": {
        [`@apply prose-img:${config2.rounded.md} prose-pre:${config2.rounded.md} prose-table:${config2.rounded.md}`]:
          {},
      },
      //Rounded:lg
      "&.nui-prose-rounded-lg": {
        [`@apply prose-img:${config2.rounded.lg} prose-pre:${config2.rounded.lg} prose-table:${config2.rounded.lg}`]:
          {},
      },
    },
  });
}, config$f);

module.export = prose;

import plugin from "tailwindcss/plugin";

const key$u = "label";
const defaultConfig$t = {
  font: {
    family: "sans",
    color: {
      light: "muted-400",
      dark: "muted-400/80",
    },
  },
};

const config$u = {
  theme: {
    nui: {
      [key$u]: defaultConfig$t,
    },
  },
};
const label = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$u}`);
  addComponents({
    ".nui-label": {
      [`@apply inline-block font-${config2.font.family} leading-none text-${config2.font.color.light} dark:text-${config2.font.color.dark}`]:
        {},
    },
  });
}, config$u);

module.export = label;

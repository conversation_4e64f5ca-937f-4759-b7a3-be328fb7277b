import plugin from "tailwindcss/plugin";
import { defu } from "defu";

const base = plugin(({ addBase }) =>
  addBase({
    ".dark": { colorScheme: "dark" },
  })
);

function mergePlugins(plugins) {
  let config = {};
  for (const plugin2 of plugins) {
    config = defu(config, plugin2.config ?? {});
  }
  return plugin(
    (api) => plugins.map((plugin2) => plugin2.handler(api)),
    config
  );
}

const components = mergePlugins([
  accordion,
  autocomplete,
  avatarGroup,
  avatar,
  breadcrumb,
  buttonAction,
  buttonClose,
  buttonGroup,
  buttonIcon,
  button,
  card,
  checkbox,
  dropdownDivider,
  dropdownItem,
  dropdown,
  focus,
  fullscreenDropfile,
  iconBox,
  inputFileRegular,
  inputFile,
  input,
  inputNumber,
  inputHelpText,
  kbd,
  label,
  link,
  list,
  listbox,
  mark,
  mask,
  messageText,
  message,
  modal,
  pagination,
  paragraph,
  placeholderPage,
  placeload,
  progressCircle,
  progress,
  prose,
  radio,
  select,
  slimscroll,
  snack,
  switchBall,
  switchThin,
  tabSlider,
  tabs,
  tag,
  text,
  textarea,
  themeSwitch,
  themeToggle,
  toast,
  tooltip,
]);

const utilities = mergePlugins([bgShades, textShades]);

export {
  accordion,
  autocomplete,
  avatar,
  avatarGroup,
  base,
  bgShades,
  breadcrumb,
  button,
  buttonAction,
  buttonClose,
  buttonGroup,
  buttonIcon,
  card,
  checkbox,
  components,
  dropdown,
  dropdownDivider,
  dropdownItem,
  focus,
  fullscreenDropfile,
  heading,
  iconBox,
  input,
  inputFile,
  inputFileRegular,
  inputHelpText,
  inputNumber,
  kbd,
  label,
  link,
  list,
  listbox,
  mark,
  mask,
  message,
  messageText,
  modal,
  pagination,
  paragraph,
  placeholderPage,
  placeload,
  progress,
  progressCircle,
  prose,
  radio,
  select,
  slimscroll,
  snack,
  switchBall,
  switchThin,
  tabSlider,
  tabs,
  tag,
  text,
  textShades,
  textarea,
  themeSwitch,
  themeToggle,
  toast,
  tooltip,
  utilities,
};

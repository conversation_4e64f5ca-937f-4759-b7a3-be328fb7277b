import plugin from "tailwindcss/plugin";

const key$I = "checkbox";
const defaultConfig$H = {
  rounded: {
    sm: "rounded",
    md: "rounded-md",
    lg: "rounded-lg",
    full: "rounded-full",
  },
  outer: {
    size: "5",
  },
  inner: {
    size: "full",
    background: {
      light: "white",
      dark: "muted-700",
    },
    border: {
      light: "muted-400",
      dark: "muted-700",
    },
  },
  icon: {
    size: "2.5",
    indeterminate: {
      size: "2.5",
      transition: {
        property: "all",
        duration: "300",
      },
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  input: {
    size: "5",
  },
  label: {
    font: {
      family: "sans",
      size: "sm",
      color: {
        light: "muted-400",
        dark: "muted-400",
      },
    },
  },
  error: {
    font: {
      family: "sans",
      size: "xs",
      color: {
        light: "danger-600",
        dark: "danger-600",
      },
    },
  },
  color: {
    default: {
      light: "muted-600",
      dark: "muted-200",
    },
    muted: {
      light: "muted-400",
      dark: "muted-400",
    },
    light: {
      light: "white",
      dark: "white",
    },
    dark: {
      light: "muted-900",
      dark: "muted-100",
    },
    black: {
      light: "black",
      dark: "white",
    },
    primary: {
      light: "primary-500",
      dark: "primary-500",
    },
    info: {
      light: "info-500",
      dark: "info-500",
    },
    success: {
      light: "success-500",
      dark: "success-500",
    },
    warning: {
      light: "warning-500",
      dark: "warning-500",
    },
    danger: {
      light: "danger-500",
      dark: "danger-500",
    },
  },
};

const config$I = {
  theme: {
    nui: {
      [key$I]: defaultConfig$H,
    },
  },
};
const checkbox = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$I}`);
  addComponents({
    //Wrapper
    ".nui-checkbox": {
      "@apply relative inline-flex items-start gap-1": {},
      //Outer
      ".nui-checkbox-outer": {
        [`@apply nui-focus relative flex items-center justify-center h-${config2.outer.size} w-${config2.outer.size} shrink-0 cursor-pointer disabled:cursor-not-allowed overflow-hidden`]:
          {},
      },
      //Inner
      ".nui-checkbox-inner": {
        [`@apply absolute start-0 top-0 z-0 h-${config2.inner.size} w-${config2.inner.size}`]:
          {},
        //Background
        [`@apply bg-${config2.inner.background.light} dark:bg-${config2.inner.background.dark}`]:
          {},
        //Border
        [`@apply border-2 border-${config2.inner.border.light} dark:border-${config2.inner.border.dark}`]:
          {},
      },
      //Icon
      ".nui-icon-check": {
        "@apply pointer-events-none absolute z-10 fill-current translate-y-6 opacity-0":
          {},
        //Size
        [`@apply h-${config2.icon.size} w-${config2.icon.size}`]: {},
        //Transition
        [`@apply transition-${config2.icon.transition.property} duration-${config2.icon.transition.duration}`]:
          {},
      },
      ".nui-icon-indeterminate": {
        "@apply pointer-events-none absolute z-10 fill-current translate-y-6 opacity-0":
          {},
        //Size
        [`@apply h-${config2.icon.indeterminate.size} w-${config2.icon.indeterminate.size}`]:
          {},
        //Transition
        [`@apply transition-${config2.icon.indeterminate.transition.property} duration-${config2.icon.indeterminate.transition.duration}`]:
          {},
      },
      //Input
      ".nui-checkbox-input": {
        [`@apply absolute z-20 h-${config2.input.size} w-${config2.input.size} cursor-pointer disabled:cursor-not-allowed opacity-0`]:
          {},
        //Input:checked ~ inner
        "&:checked ~ .nui-checkbox-inner, &:indeterminate ~ .nui-checkbox-inner":
          {
            "@apply border-current dark:border-current": {},
          },
        //Input:checked ~ icon
        "&:checked ~ .nui-icon-check": {
          "@apply translate-y-0 opacity-100": {},
        },
        //Input:indeterminate ~ check
        "&:indeterminate ~ .nui-icon-check": {
          "@apply !translate-y-6": {},
        },
        //Input:checked ~ icon
        "&:indeterminate ~ .nui-icon-indeterminate": {
          "@apply !translate-y-0 !opacity-100": {},
        },
      },
      //Label
      ".nui-checkbox-label-wrapper": {
        "@apply inline-flex flex-col": {},
      },
      //Label:text
      ".nui-checkbox-label-text": {
        [`@apply font-${config2.label.font.family} text-${config2.label.font.size} ms-1 cursor-pointer select-none`]:
          {},
        //Color
        [`@apply text-${config2.label.font.color.light} dark:text-${config2.label.font.color.dark}`]:
          {},
      },
      //Error
      ".nui-checkbox-error": {
        [`@apply ms-1 inline-block`]: {},
      },
      //Rounded:sm
      "&.nui-checkbox-rounded-sm .nui-checkbox-outer, &.nui-checkbox-rounded-sm .nui-checkbox-inner":
        {
          [`@apply ${config2.rounded.sm}`]: {},
        },
      //Rounded:md
      "&.nui-checkbox-rounded-md .nui-checkbox-outer, &.nui-checkbox-rounded-md .nui-checkbox-inner":
        {
          [`@apply ${config2.rounded.md}`]: {},
        },
      //Rounded:lg
      "&.nui-checkbox-rounded-lg .nui-checkbox-outer, &.nui-checkbox-rounded-lg .nui-checkbox-inner":
        {
          [`@apply ${config2.rounded.lg}`]: {},
        },
      //Rounded:full
      "&.nui-checkbox-rounded-full .nui-checkbox-outer, &.nui-checkbox-rounded-full .nui-checkbox-inner":
        {
          [`@apply ${config2.rounded.full}`]: {},
        },
      //Color:default
      "&.nui-checkbox-default": {
        [`@apply text-${config2.color.default.light} dark:text-${config2.color.default.dark}`]:
          {},
      },
      //Color:muted
      "&.nui-checkbox-muted": {
        [`@apply text-${config2.color.muted.light} dark:text-${config2.color.muted.dark}`]:
          {},
      },
      //Color:light
      "&.nui-checkbox-light": {
        [`@apply text-${config2.color.light.light} dark:text-${config2.color.light.dark}`]:
          {},
      },
      //Color:dark
      "&.nui-checkbox-dark": {
        [`@apply text-${config2.color.dark.light} dark:text-${config2.color.dark.dark}`]:
          {},
      },
      //Color:black
      "&.nui-checkbox-black": {
        [`@apply text-${config2.color.black.light} dark:text-${config2.color.black.dark}`]:
          {},
      },
      //Color:primary
      "&.nui-checkbox-primary": {
        [`@apply text-${config2.color.primary.light} dark:text-${config2.color.primary.dark}`]:
          {},
      },
      //Color:info
      "&.nui-checkbox-info": {
        [`@apply text-${config2.color.info.light} dark:text-${config2.color.info.dark}`]:
          {},
      },
      //Color:success
      "&.nui-checkbox-success": {
        [`@apply text-${config2.color.success.light} dark:text-${config2.color.success.dark}`]:
          {},
      },
      //Color:warning
      "&.nui-checkbox-warning": {
        [`@apply text-${config2.color.warning.light} dark:text-${config2.color.warning.dark}`]:
          {},
      },
      //Color:danger
      "&.nui-checkbox-danger": {
        [`@apply text-${config2.color.danger.light} dark:text-${config2.color.danger.dark}`]:
          {},
      },
    },
  });
}, config$I);

module.export = checkbox;

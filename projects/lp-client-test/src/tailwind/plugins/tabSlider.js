import plugin from "tailwindcss/plugin";

const key$8 = "tabSlider";
const defaultConfig$8 = {
  rounded: {
    sm: "rounded-md",
    md: "rounded-lg",
    lg: "rounded-xl",
    full: "rounded-full",
  },
  track: {
    width: "full",
    font: {
      family: "sans",
    },
    background: {
      light: "muted-100",
      dark: "muted-700",
    },
  },
  item: {
    font: {
      family: "sans",
      size: "sm",
    },
  },
  naver: {
    transition: {
      property: "all",
      duration: "300",
    },
  },
  color: {
    primary: {
      tabs: {
        color: {
          active: {
            light: "white",
            dark: "white",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
      naver: {
        background: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    default: {
      tabs: {
        color: {
          active: {
            light: "muted-800",
            dark: "white",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
      naver: {
        background: {
          light: "white",
          dark: "muted-800",
        },
        border: {
          light: "muted-300",
          dark: "muted-600",
        },
      },
    },
    defaultContrast: {
      tabs: {
        color: {
          active: {
            light: "muted-900",
            dark: "white",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
      naver: {
        background: {
          light: "white",
          dark: "muted-950",
        },
        border: {
          light: "muted-300",
          dark: "muted-800",
        },
      },
    },
    light: {
      tabs: {
        color: {
          active: {
            light: "white",
            dark: "white",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
      naver: {
        background: {
          light: "muted-500",
          dark: "muted-400",
        },
      },
    },
    dark: {
      tabs: {
        color: {
          active: {
            light: "white",
            dark: "muted-900",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
      naver: {
        background: {
          light: "muted-900",
          dark: "muted-100",
        },
      },
    },
    black: {
      tabs: {
        color: {
          active: {
            light: "white",
            dark: "black",
          },
          inactive: {
            light: "muted-400",
            dark: "muted-400",
          },
        },
      },
      naver: {
        background: {
          light: "black",
          dark: "white",
        },
      },
    },
  },
  size: {
    sm: {
      slots: {
        two: "[140px]",
        three: "[210px]",
      },
      track: {
        size: "8",
      },
    },
    md: {
      slots: {
        two: "[250px]",
        three: "[320px]",
      },
      track: {
        size: "10",
      },
    },
  },
};

const config$8 = {
  theme: {
    nui: {
      [key$8]: defaultConfig$8,
    },
  },
};
const tabSlider = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$8}`);
  addComponents({
    //Wrapper
    ".nui-tab-slider": {
      "@apply relative": {},
      //Tabs:inner
      ".nui-tab-slider-inner": {
        "@apply mb-6 flex": {},
      },
      //Tabs:track
      ".nui-tab-slider-track": {
        //Base
        [`@apply relative relative flex w-${config2.track.width} items-center font-${config2.track.font.family}`]:
          {},
        //Background
        [`@apply bg-${config2.track.background.light} dark:bg-${config2.track.background.dark}`]:
          {},
      },
      //Tabs:item
      ".nui-tab-slider-item": {
        //Base
        "@apply relative z-20 h-full flex flex-1 items-center justify-center":
          {},
        //Font
        [`@apply text-${config2.item.font.size} font-${config2.item.font.family}`]:
          {},
      },
      //Tabs:naver
      ".nui-tab-slider-naver": {
        //Base
        "@apply absolute start-0 top-0 z-10 h-full": {},
        //Transition
        [`@apply transition-${config2.naver.transition.property} duration-${config2.naver.transition.duration}`]:
          {},
      },
      //Tabs:content
      ".nui-tab-content": {
        "@apply relative block": {},
      },
      //Align:center
      "&.nui-tabs-centered": {
        ".nui-tab-slider-inner": {
          "@apply justify-center": {},
        },
      },
      //Align:end
      "&.nui-tabs-end": {
        ".nui-tab-slider-inner": {
          "@apply justify-end": {},
        },
      },
      //Color:default
      "&.nui-tabs-default": {
        ".nui-tab-slider-item:not(.nui-active)": {
          [`@apply text-${config2.color.default.tabs.color.inactive.light} dark:text-${config2.color.default.tabs.color.inactive.dark}`]:
            {},
        },
        ".nui-tab-slider-item.nui-active": {
          [`@apply text-${config2.color.default.tabs.color.active.light} dark:text-${config2.color.default.tabs.color.active.dark}`]:
            {},
        },
        ".nui-tab-slider-naver": {
          [`@apply bg-${config2.color.default.naver.background.light} dark:bg-${config2.color.default.naver.background.dark}`]:
            {},
          [`@apply border border-${config2.color.default.naver.border.light} dark:border-${config2.color.default.naver.border.dark}`]:
            {},
        },
      },
      //Color:default-contrast
      "&.nui-tabs-default-contrast": {
        ".nui-tab-slider-item:not(.nui-active)": {
          [`@apply text-${config2.color.defaultContrast.tabs.color.inactive.light} dark:text-${config2.color.defaultContrast.tabs.color.inactive.dark}`]:
            {},
        },
        ".nui-tab-slider-item.nui-active": {
          [`@apply text-${config2.color.defaultContrast.tabs.color.active.light} dark:text-${config2.color.defaultContrast.tabs.color.active.dark}`]:
            {},
        },
        ".nui-tab-slider-naver": {
          [`@apply bg-${config2.color.defaultContrast.naver.background.light} dark:bg-${config2.color.defaultContrast.naver.background.dark}`]:
            {},
          [`@apply border border-${config2.color.defaultContrast.naver.border.light} dark:border-${config2.color.defaultContrast.naver.border.dark}`]:
            {},
        },
      },
      //Color:primary
      "&.nui-tabs-primary": {
        ".nui-tab-slider-item:not(.nui-active)": {
          [`@apply text-${config2.color.primary.tabs.color.inactive.light} dark:text-${config2.color.primary.tabs.color.inactive.dark}`]:
            {},
        },
        ".nui-tab-slider-item.nui-active": {
          [`@apply text-${config2.color.primary.tabs.color.active.light} dark:text-${config2.color.primary.tabs.color.active.dark}`]:
            {},
        },
        ".nui-tab-slider-naver": {
          [`@apply bg-${config2.color.primary.naver.background.light} dark:bg-${config2.color.primary.naver.background.dark}`]:
            {},
        },
      },
      //Color:light
      "&.nui-tabs-light": {
        ".nui-tab-slider-item:not(.nui-active)": {
          [`@apply text-${config2.color.light.tabs.color.inactive.light} dark:text-${config2.color.light.tabs.color.inactive.dark}`]:
            {},
        },
        ".nui-tab-slider-item.nui-active": {
          [`@apply text-${config2.color.light.tabs.color.active.light} dark:text-${config2.color.light.tabs.color.active.dark}`]:
            {},
        },
        ".nui-tab-slider-naver": {
          [`@apply bg-${config2.color.light.naver.background.light} dark:bg-${config2.color.light.naver.background.dark}`]:
            {},
        },
      },
      //Color:dark
      "&.nui-tabs-dark": {
        ".nui-tab-slider-item:not(.nui-active)": {
          [`@apply text-${config2.color.dark.tabs.color.inactive.light} dark:text-${config2.color.dark.tabs.color.inactive.dark}`]:
            {},
        },
        ".nui-tab-slider-item.nui-active": {
          [`@apply text-${config2.color.dark.tabs.color.active.light} dark:text-${config2.color.dark.tabs.color.active.dark}`]:
            {},
        },
        ".nui-tab-slider-naver": {
          [`@apply bg-${config2.color.dark.naver.background.light} dark:bg-${config2.color.dark.naver.background.dark}`]:
            {},
        },
      },
      //Color:black
      "&.nui-tabs-black": {
        ".nui-tab-slider-item:not(.nui-active)": {
          [`@apply text-${config2.color.black.tabs.color.inactive.light} dark:text-${config2.color.black.tabs.color.inactive.dark}`]:
            {},
        },
        ".nui-tab-slider-item.nui-active": {
          [`@apply text-${config2.color.black.tabs.color.active.light} dark:text-${config2.color.black.tabs.color.active.dark}`]:
            {},
        },
        ".nui-tab-slider-naver": {
          [`@apply bg-${config2.color.black.naver.background.light} dark:bg-${config2.color.black.naver.background.dark}`]:
            {},
        },
      },
      //Rounded:sm
      "&.nui-tabs-rounded-sm": {
        ".nui-tab-slider-track, .nui-tab-slider-naver": {
          [`@apply ${config2.rounded.sm}`]: {},
        },
      },
      //Rounded:md
      "&.nui-tabs-rounded-md": {
        ".nui-tab-slider-track, .nui-tab-slider-naver": {
          [`@apply ${config2.rounded.md}`]: {},
        },
      },
      //Rounded:lg
      "&.nui-tabs-rounded-lg": {
        ".nui-tab-slider-track, .nui-tab-slider-naver": {
          [`@apply ${config2.rounded.lg}`]: {},
        },
      },
      //Rounded:full
      "&.nui-tabs-rounded-full": {
        ".nui-tab-slider-track, .nui-tab-slider-naver": {
          [`@apply ${config2.rounded.full}`]: {},
        },
      },
      //Size:sm
      "&.nui-tabs-sm": {
        "&.nui-tabs-two-slots .nui-tab-slider-track": {
          [`@apply max-w-${config2.size.sm.slots.two}`]: {},
        },
        "&.nui-tabs-three-slots .nui-tab-slider-track": {
          [`@apply max-w-${config2.size.sm.slots.three}`]: {},
        },
        ".nui-tab-slider-track": {
          [`@apply h-${config2.size.sm.track.size}`]: {},
        },
      },
      //Size:md
      "&.nui-tabs-md": {
        "&.nui-tabs-two-slots .nui-tab-slider-track": {
          [`@apply max-w-${config2.size.md.slots.two}`]: {},
        },
        "&.nui-tabs-three-slots .nui-tab-slider-track": {
          [`@apply max-w-${config2.size.md.slots.three}`]: {},
        },
        ".nui-tab-slider-track": {
          [`@apply h-${config2.size.md.track.size}`]: {},
        },
      },
      //Slots:two
      "&.nui-tabs-two-slots": {
        ".nui-tab-slider-naver, .nui-tab-slider-item": {
          "@apply w-1/2": {},
        },
        ".nui-tab-slider-item:first-child.nui-active ~ .nui-tab-slider-naver": {
          "@apply ms-0": {},
        },
        ".nui-tab-slider-item:nth-child(2).nui-active ~ .nui-tab-slider-naver":
          {
            "@apply ms-[50%]": {},
          },
      },
      //Slots:three
      "&.nui-tabs-three-slots": {
        ".nui-tab-slider-naver, .nui-tab-slider-item": {
          "@apply w-1/3": {},
        },
        ".nui-tab-slider-item:first-child.nui-active ~ .nui-tab-slider-naver": {
          "@apply ms-0": {},
        },
        ".nui-tab-slider-item:nth-child(2).nui-active ~ .nui-tab-slider-naver":
          {
            "@apply ms-[33.3%]": {},
          },
        ".nui-tab-slider-item:nth-child(3).nui-active ~ .nui-tab-slider-naver":
          {
            "@apply ms-[66.6%]": {},
          },
      },
    },
  });
}, config$8);

module.export = tabSlider;

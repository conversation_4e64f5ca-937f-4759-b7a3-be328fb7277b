import plugin from "tailwindcss/plugin";
const key$y = "input";
const defaultConfig$x = {
  rounded: {
    none: "rounded-none",
    sm: "rounded-md",
    md: "rounded-lg",
    lg: "rounded-xl",
    full: "rounded-full",
  },
  label: {
    float: {
      height: "5",
      font: {
        family: "sans",
        color: "primary-500",
        lead: "none",
      },
      transition: {
        property: "all",
        duration: "300",
      },
    },
  },
  input: {
    width: "full",
    font: {
      family: "sans",
    },
    focus: {
      label: {
        float: {
          font: {
            color: {
              light: "primary-500",
              dark: "primary-500",
            },
          },
        },
      },
      border: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    icon: {
      color: {
        base: {
          light: "muted-400",
          dark: "muted-400",
        },
        focus: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
      transition: {
        property: "colors",
        duration: "300",
      },
    },
    action: {
      color: {
        light: "muted-400",
        dark: "muted-400",
      },
      padding: {
        sm: "8",
        md: "10",
        lg: "12",
        xl: "14",
      },
      transition: {
        property: "colors",
        duration: "300",
      },
    },
    transition: {
      property: "all",
      duration: "300",
    },
  },
  error: {
    input: {
      border: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    icon: {
      color: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
    font: {
      family: "sans",
      size: "[0.65rem]",
      weight: "medium",
      color: {
        light: "danger-600",
        dark: "danger-600",
      },
    },
  },
  loaded: {
    font: {
      color: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
  },
  size: {
    sm: {
      label: {
        font: {
          size: "xs",
        },
      },
      icon: {
        outer: {
          size: "8",
        },
        inner: {
          size: "4",
        },
      },
      placeload: {
        size: "8",
      },
    },
    md: {
      label: {
        font: {
          size: "[0.825rem]",
        },
      },
      icon: {
        outer: {
          size: "10",
        },
        inner: {
          size: "[1.15rem]",
        },
      },
      placeload: {
        size: "10",
      },
    },
    lg: {
      label: {
        font: {
          size: "sm",
        },
      },
      icon: {
        outer: {
          size: "12",
        },
        inner: {
          size: "5",
        },
      },
      placeload: {
        size: "12",
      },
    },
    xl: {
      label: {
        font: {
          size: "sm",
        },
      },
      icon: {
        outer: {
          size: "14",
        },
        inner: {
          size: "5",
        },
      },
      placeload: {
        size: "14",
      },
    },
  },
  color: {
    default: {
      background: {
        light: "white",
        dark: "muted-900",
      },
      border: {
        base: {
          light: "muted-300",
          dark: "muted-700",
        },
        hover: {
          light: "muted-300",
          dark: "muted-700",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-500",
      },
    },
    defaultContrast: {
      background: {
        light: "white",
        dark: "muted-950",
      },
      border: {
        base: {
          light: "muted-300",
          dark: "muted-800",
        },
        hover: {
          light: "muted-300",
          dark: "muted-800",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
    muted: {
      background: {
        light: "muted-100",
        dark: "muted-900",
      },
      border: {
        base: {
          light: "muted-100",
          dark: "muted-700",
        },
        hover: {
          light: "muted-100",
          dark: "muted-700",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-500",
      },
    },
    mutedContrast: {
      background: {
        light: "muted-100",
        dark: "muted-950",
      },
      border: {
        base: {
          light: "muted-100",
          dark: "muted-800",
        },
        hover: {
          light: "muted-100",
          dark: "muted-800",
        },
      },
      color: {
        light: "muted-600",
        dark: "muted-200",
      },
      placeholder: {
        light: "muted-300",
        dark: "muted-600",
      },
    },
  },
  icon: {
    disabled: {
      input: {
        sm: {
          font: {
            size: "xs",
          },
        },
        md: {
          font: {
            size: "sm",
          },
        },
        lg: {
          font: {
            size: "sm",
          },
        },
        xl: {
          font: {
            size: "base",
          },
        },
      },
      label: {
        float: {
          sm: {
            font: {
              size: "xs",
            },
          },
          md: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          lg: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          xl: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
        },
      },
    },
    enabled: {
      input: {
        sm: {
          font: {
            size: "xs",
          },
        },
        md: {
          font: {
            size: "sm",
          },
        },
        lg: {
          font: {
            size: "sm",
          },
        },
        xl: {
          font: {
            size: "base",
          },
        },
      },
      label: {
        float: {
          sm: {
            font: {
              size: "xs",
            },
          },
          md: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          lg: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
          xl: {
            font: {
              size: {
                base: "xs",
                focus: "xs",
                unfocus: "[0.825rem]",
              },
            },
          },
        },
      },
    },
  },
};

const config$y = {
  theme: {
    nui: {
      [key$y]: defaultConfig$x,
    },
  },
};
const input = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$y}`);
  addComponents({
    //Wrapper
    ".nui-input-wrapper": {
      "@apply relative": {},
      //Input:label
      ".nui-input-label, .nui-label-float": {
        "@apply nui-label": {},
      },
      //Label:float
      ".nui-label-float": {
        [`@apply h-${config2.label.float.height} absolute inline-flex items-center select-none pointer-events-none`]:
          {},
        //Font
        [`@apply font-${config2.label.float.font.family} text-${config2.label.float.font.color} leading-${config2.label.float.font.lead}`]:
          {},
        //Transition
        [`@apply transition-${config2.label.float.transition.property} duration-${config2.label.float.transition.duration}`]:
          {},
      },
      //Input:outer
      ".nui-input-outer": {
        "@apply relative": {},
      },
      //Input:icon
      ".nui-input-icon": {
        "@apply absolute start-0 top-0 z-10 flex items-center justify-center":
          {},
        //Color
        [`@apply text-${config2.input.icon.color.base.light} dark:text-${config2.input.icon.color.base.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.input.icon.transition.property} duration-${config2.input.icon.transition.duration}`]:
          {},
      },
      //Input
      ".nui-input": {
        //Base
        [`@apply nui-focus w-${config2.input.width} font-${config2.input.font.family} disabled:cursor-not-allowed disabled:opacity-75`]:
          {},
        //Transition
        [`@apply transition-${config2.input.transition.property} duration-${config2.input.transition.duration}`]:
          {},
        //Focus:label:float
        "&:focus-visible ~ .nui-label-float": {
          [`@apply !text-${config2.input.focus.label.float.font.color.light} dark:!text-${config2.input.focus.label.float.font.color.dark}`]:
            {},
        },
        //Focus:icon
        "&:focus-visible ~ .nui-input-icon": {
          [`@apply !text-${config2.input.icon.color.focus.light} dark:!text-${config2.input.icon.color.focus.dark}`]:
            {},
        },
        //Disabled:icon
        "&:disabled ~ .nui-input-icon": {
          "@apply cursor-not-allowed opacity-75": {},
        },
      },
      //Input:placeload
      ".nui-input-placeload": {
        "@apply absolute start-0 top-0 flex w-full items-center px-4": {},
        //Placeload:inner
        ".nui-placeload": {
          "@apply h-3 w-full max-w-[75%] rounded": {},
        },
      },
      //Input:action
      ".nui-input-action": {
        //Base
        "@apply absolute end-0 top-0 flex items-center justify-center": {},
        //Color
        [`@apply text-${config2.input.action.color.light} dark:text-${config2.input.action.color.dark}`]:
          {},
        //Transition
        [`@apply transition-${config2.input.action.transition.property} duration-${config2.input.action.transition.duration}`]:
          {},
      },
      //Rounded:sm
      "&.nui-input-rounded-sm": {
        ".nui-input": {
          [`@apply ${config2.rounded.sm}`]: {},
        },
      },
      //Rounded:md
      "&.nui-input-rounded-md": {
        ".nui-input": {
          [`@apply ${config2.rounded.md}`]: {},
        },
      },
      //Rounded:lg
      "&.nui-input-rounded-lg": {
        ".nui-input": {
          [`@apply ${config2.rounded.lg}`]: {},
        },
      },
      //Rounded:full
      "&.nui-input-rounded-full": {
        ".nui-input": {
          [`@apply ${config2.rounded.full}`]: {},
        },
      },
      //Size:sm
      "&.nui-input-sm": {
        //Input:label
        ".nui-input-label": {
          [`@apply pb-1 text-${config2.size.sm.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-1.5": {},
        },
        //Input:icon && Input:action
        ".nui-input-icon, .nui-input-action": {
          [`@apply h-${config2.size.sm.icon.outer.size} w-${config2.size.sm.icon.outer.size}`]:
            {},
          ".nui-input-icon-inner, .nui-input-action-inner": {
            [`@apply h-${config2.size.sm.icon.inner.size} w-${config2.size.sm.icon.inner.size}`]:
              {},
          },
        },
        //Input:placeload
        ".nui-input-placeload": {
          [`@apply h-${config2.size.sm.placeload.size}`]: {},
        },
      },
      //Size:md
      "&.nui-input-md": {
        //Input:label
        ".nui-input-label": {
          [`@apply pb-1 text-${config2.size.md.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-2.5": {},
        },
        //Input:icon && Input:action
        ".nui-input-icon, .nui-input-action": {
          [`@apply h-${config2.size.md.icon.outer.size} w-${config2.size.md.icon.outer.size}`]:
            {},
          ".nui-input-icon-inner, .nui-input-action-inner": {
            [`@apply h-${config2.size.md.icon.inner.size} w-${config2.size.md.icon.inner.size}`]:
              {},
          },
        },
        //Input:placeload
        ".nui-input-placeload": {
          [`@apply h-${config2.size.md.placeload.size}`]: {},
        },
      },
      //Size:lg
      "&.nui-input-lg": {
        //Input:label
        ".nui-input-label": {
          [`@apply pb-1 text-${config2.size.lg.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-3.5": {},
        },
        //Input:icon && Input:action
        ".nui-input-icon, .nui-input-action": {
          [`@apply h-${config2.size.lg.icon.outer.size} w-${config2.size.lg.icon.outer.size}`]:
            {},
          ".nui-input-icon-inner, .nui-input-action-inner": {
            [`@apply h-${config2.size.lg.icon.inner.size} w-${config2.size.lg.icon.inner.size}`]:
              {},
          },
        },
        //Input:placeload
        ".nui-input-placeload": {
          [`@apply h-${config2.size.lg.placeload.size}`]: {},
        },
      },
      //Size:xl
      "&.nui-input-xl": {
        //Input:label
        ".nui-input-label": {
          [`@apply pb-1 text-${config2.size.xl.label.font.size}`]: {},
        },
        //Label:float
        ".nui-label-float": {
          "@apply top-[1.1rem]": {},
        },
        //Input:icon && Input:action
        ".nui-input-icon, .nui-input-action": {
          [`@apply h-${config2.size.xl.icon.outer.size} w-${config2.size.xl.icon.outer.size}`]:
            {},
          ".nui-input-icon-inner, .nui-input-action-inner": {
            [`@apply h-${config2.size.xl.icon.inner.size} w-${config2.size.xl.icon.inner.size}`]:
              {},
          },
        },
        //Input:placeload
        ".nui-input-placeload": {
          [`@apply h-${config2.size.xl.placeload.size}`]: {},
        },
      },
      //Color:default
      "&.nui-input-default": {
        ".nui-input": {
          //Font
          [`@apply text-${config2.color.default.color.light} dark:text-${config2.color.default.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.default.placeholder.light} dark:placeholder:text-${config2.color.default.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.default.background.light} dark:bg-${config2.color.default.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.default.border.base.light} dark:border-${config2.color.default.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.default.border.hover.light} dark:hover:border-${config2.color.default.border.hover.dark}`]:
            {},
        },
      },
      //Color:default-constrast
      "&.nui-input-default-contrast": {
        ".nui-input": {
          //Font
          [`@apply text-${config2.color.defaultContrast.color.light} dark:text-${config2.color.defaultContrast.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.defaultContrast.placeholder.light} dark:placeholder:text-${config2.color.defaultContrast.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.defaultContrast.background.light} dark:bg-${config2.color.defaultContrast.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.defaultContrast.border.base.light} dark:border-${config2.color.defaultContrast.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.defaultContrast.border.hover.light} dark:hover:border-${config2.color.defaultContrast.border.hover.dark}`]:
            {},
        },
      },
      //Color:muted
      "&.nui-input-muted": {
        ".nui-input": {
          //Font
          [`@apply text-${config2.color.muted.color.light} dark:text-${config2.color.muted.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.muted.placeholder.light} dark:placeholder:text-${config2.color.muted.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.muted.background.light} dark:bg-${config2.color.muted.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.muted.border.base.light} dark:border-${config2.color.muted.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.muted.border.hover.light} dark:hover:border-${config2.color.muted.border.hover.dark}`]:
            {},
        },
      },
      //Color:muted-constrast
      "&.nui-input-muted-contrast": {
        ".nui-input": {
          //Font
          [`@apply text-${config2.color.mutedContrast.color.light} dark:text-${config2.color.mutedContrast.color.dark}`]:
            {},
          //Placeholder
          [`@apply placeholder:text-${config2.color.mutedContrast.placeholder.light} dark:placeholder:text-${config2.color.mutedContrast.placeholder.dark}`]:
            {},
          //Background
          [`@apply bg-${config2.color.mutedContrast.background.light} dark:bg-${config2.color.mutedContrast.background.dark}`]:
            {},
          //Border
          [`@apply border border-${config2.color.mutedContrast.border.base.light} dark:border-${config2.color.mutedContrast.border.base.dark}`]:
            {},
          //Border:hover
          [`@apply hover:border-${config2.color.mutedContrast.border.hover.light} dark:hover:border-${config2.color.mutedContrast.border.hover.dark}`]:
            {},
        },
      },
      //Focus:color
      "&.nui-input-focus": {
        ".nui-input": {
          //Focus
          [`@apply focus:!border-${config2.input.focus.border.color.light} dark:focus:!border-${config2.input.focus.border.color.dark}`]:
            {},
          //Force focus
          [`@apply focus:hover:!border-${config2.input.focus.border.color.light} dark:focus:hover:!border-${config2.input.focus.border.color.dark}`]:
            {},
        },
      },
      //Input:not loading
      "&:not(.nui-input-loading)": {
        ".nui-input:placeholder-shown ~ .nui-label-float": {
          [`@apply text-${config2.loaded.font.color.light} dark:text-${config2.loaded.font.color.dark}`]:
            {},
        },
      },
      //Input:loading
      "&.nui-input-loading": {
        ".nui-input": {
          "@apply !text-transparent placeholder:!text-transparent dark:placeholder:!text-transparent":
            {},
        },
        ".nui-input:placeholder-shown ~ .nui-label-float": {
          "@apply text-transparent": {},
        },
        ".nui-input-icon": {
          "@apply opacity-0": {},
        },
      },
      //Input:label:float
      "&.nui-input-label-float": {
        ".nui-input": {
          "@apply placeholder:text-transparent dark:placeholder:text-transparent":
            {},
        },
      },
      //Input:error
      "&.nui-input-error": {
        //Border
        ".nui-input": {
          [`@apply !border-${config2.error.input.border.light} dark:!border-${config2.error.input.border.dark}`]:
            {},
        },
        //Icon
        ".nui-input-icon": {
          [`@apply !text-${config2.error.icon.color.light} dark:!text-${config2.error.icon.color.dark}`]:
            {},
        },
      },
      //Without icon && Size:sm
      "&:not(.nui-has-icon).nui-input-sm": {
        ".nui-input": {
          [`@apply h-8 py-1 text-${config2.icon.disabled.input.sm.font.size} leading-4 px-2`]:
            {},
        },
      },
      //With icon && Size:sm
      "&.nui-has-icon.nui-input-sm": {
        ".nui-input": {
          [`@apply h-8 py-1 text-${config2.icon.enabled.input.sm.font.size} leading-4 pe-3 ps-8`]:
            {},
        },
      },
      //Without icon && Size:md
      "&:not(.nui-has-icon).nui-input-md": {
        ".nui-input": {
          [`@apply h-10 py-2 text-${config2.icon.disabled.input.md.font.size} leading-5 px-3`]:
            {},
        },
      },
      //With icon && Size:md
      "&.nui-has-icon.nui-input-md": {
        ".nui-input": {
          [`@apply h-10 py-2 text-${config2.icon.enabled.input.md.font.size} leading-5 pe-4 ps-10`]:
            {},
        },
      },
      //Without icon && Size:lg
      "&:not(.nui-has-icon).nui-input-lg": {
        ".nui-input": {
          [`@apply h-12 py-2 text-${config2.icon.disabled.input.lg.font.size} leading-5 px-4`]:
            {},
        },
      },
      //With icon && Size:lg
      "&.nui-has-icon.nui-input-lg": {
        ".nui-input": {
          [`@apply h-12 py-2 text-${config2.icon.enabled.input.lg.font.size} leading-5 pe-4 ps-11`]:
            {},
        },
      },
      //Without icon && Size:xl
      "&:not(.nui-has-icon).nui-input-xl": {
        ".nui-input": {
          [`@apply h-14 py-2 text-${config2.icon.disabled.input.xl.font.size} leading-5 px-4`]:
            {},
        },
      },
      //With icon && Size:xl
      "&.nui-has-icon.nui-input-xl": {
        ".nui-input": {
          [`@apply h-14 py-2 text-${config2.icon.enabled.input.xl.font.size} leading-5 pe-4 ps-[3.25rem]`]:
            {},
        },
      },
      //With action && Size:sm
      "&.nui-has-action.nui-input-sm": {
        ".nui-input": {
          [`@apply pe-${config2.input.action.padding.sm}`]: {},
        },
      },
      //With action && Size:md
      "&.nui-has-action.nui-input-md": {
        ".nui-input": {
          [`@apply pe-${config2.input.action.padding.md}`]: {},
        },
      },
      //With action && Size:lg
      "&.nui-has-action.nui-input-lg": {
        ".nui-input": {
          [`@apply pe-${config2.input.action.padding.lg}`]: {},
        },
      },
      //With action && Size:xl
      "&.nui-has-action.nui-input-xl": {
        ".nui-input": {
          [`@apply pe-${config2.input.action.padding.xl}`]: {},
        },
      },
      //Without icon && Size:sm && Label:float
      "&.nui-input-label-float:not(.nui-has-icon).nui-input-sm": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-7 text-${config2.icon.disabled.label.float.sm.font.size}`]:
            {},
        },
        ".nui-input:focus-visible ~ .nui-label-float": {
          "@apply !-ms-3 !-mt-7": {},
        },
        ".nui-input:placeholder-shown ~ .nui-label-float": {
          "@apply ms-0 mt-0": {},
        },
      },
      //With icon && Size:sm && Label:float
      "&.nui-input-label-float.nui-has-icon.nui-input-sm": {
        ".nui-label-float": {
          [`@apply start-8 -ms-8 -mt-7 text-${config2.icon.enabled.label.float.sm.font.size}`]:
            {},
        },
        ".nui-input:focus-visible ~ .nui-label-float": {
          "@apply !-ms-8 !-mt-7": {},
        },
        ".nui-input:placeholder-shown ~ .nui-label-float": {
          "@apply ms-0 mt-0": {},
        },
      },
      //Without icon && Size:md && Label:float
      "&.nui-input-label-float:not(.nui-has-icon).nui-input-md": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-8 text-${config2.icon.disabled.label.float.md.font.size.base}`]:
            {},
        },
        ".nui-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-8 !text-${config2.icon.disabled.label.float.md.font.size.focus}`]:
            {},
        },
        ".nui-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.md.font.size.unfocus}`]:
            {},
        },
      },
      //With icon && Size:md && Label:float
      "&.nui-input-label-float.nui-has-icon.nui-input-md": {
        ".nui-label-float": {
          [`@apply start-10 -ms-10 -mt-8 text-${config2.icon.enabled.label.float.md.font.size.base}`]:
            {},
        },
        ".nui-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-10 !-mt-8 !text-${config2.icon.enabled.label.float.md.font.size.focus}`]:
            {},
        },
        ".nui-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.md.font.size.unfocus}`]:
            {},
        },
      },
      //Without icon && Size:lg && Label:float
      "&.nui-input-label-float:not(.nui-has-icon).nui-input-lg": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-9 text-${config2.icon.disabled.label.float.lg.font.size.base}`]:
            {},
        },
        ".nui-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-9 !text-${config2.icon.disabled.label.float.lg.font.size.focus}`]:
            {},
        },
        ".nui-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.lg.font.size.unfocus}`]:
            {},
        },
      },
      //With icon && Size:lg && Label:float
      "&.nui-input-label-float.nui-has-icon.nui-input-lg": {
        ".nui-label-float": {
          [`@apply start-11 -ms-9 -mt-10 text-${config2.icon.enabled.label.float.lg.font.size.base}`]:
            {},
        },
        ".nui-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-10 !-mt-9 !text-${config2.icon.enabled.label.float.lg.font.size.focus}`]:
            {},
        },
        ".nui-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.lg.font.size.unfocus}`]:
            {},
        },
      },
      //Without icon && Size:xl && Label:float
      "&.nui-input-label-float:not(.nui-has-icon).nui-input-xl": {
        ".nui-label-float": {
          [`@apply start-3 -ms-3 -mt-10 text-${config2.icon.disabled.label.float.xl.font.size.base}`]:
            {},
        },
        ".nui-input:focus-visible ~ .nui-label-float": {
          [`@apply !-ms-3 !-mt-10 !text-${config2.icon.disabled.label.float.xl.font.size.focus}`]:
            {},
        },
        ".nui-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.disabled.label.float.xl.font.size.unfocus}`]:
            {},
        },
      },
      //With icon && Size:xl && Label:float
      "&.nui-input-label-float.nui-has-icon.nui-input-xl": {
        ".nui-label-float": {
          [`@apply start-[3.25rem] ms-[-3.25rem] -mt-8 text-${config2.icon.enabled.label.float.xl.font.size.base}`]:
            {},
        },
        ".nui-input:focus-visible ~ .nui-label-float": {
          [`@apply !ms-[-3.25rem] !-mt-10 !text-${config2.icon.enabled.label.float.xl.font.size.focus}`]:
            {},
        },
        ".nui-input:placeholder-shown ~ .nui-label-float": {
          [`@apply ms-0 mt-0 text-${config2.icon.enabled.label.float.xl.font.size.unfocus}`]:
            {},
        },
      },
    },
  });
}, config$y);

module.export = input;

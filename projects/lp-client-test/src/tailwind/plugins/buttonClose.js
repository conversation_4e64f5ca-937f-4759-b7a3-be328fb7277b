import plugin from "tailwindcss/plugin";

const key$N = "buttonClose";
const defaultConfig$M = {
  size: {
    xs: {
      outer: "6",
      inner: "3",
    },
    sm: {
      outer: "8",
      inner: "4",
    },
    md: {
      outer: "10",
      inner: "4",
    },
    lg: {
      outer: "12",
      inner: "5",
    },
    xl: {
      outer: "14",
      inner: "6",
    },
  },
  rounded: {
    none: "rounded-none",
    sm: "rounded",
    md: "rounded-md",
    lg: "rounded-lg",
    full: "rounded-full",
  },
  color: {
    default: {
      background: {
        base: {
          light: "transparent",
          dark: "transparent",
        },
        hover: {
          light: "muted-100",
          dark: "muted-700",
        },
        focus: {
          light: "muted-100",
          dark: "muted-700",
        },
        active: {
          light: "transparent",
          dark: "transparent",
        },
      },
      font: {
        color: {
          light: "muted-700",
          dark: "muted-50",
        },
      },
    },
    defaultContrast: {
      background: {
        base: {
          light: "transparent",
          dark: "transparent",
        },
        hover: {
          light: "muted-100",
          dark: "muted-900",
        },
        focus: {
          light: "muted-100",
          dark: "muted-900",
        },
        active: {
          light: "transparent",
          dark: "transparent",
        },
      },
      font: {
        color: {
          light: "muted-700",
          dark: "muted-50",
        },
      },
    },
    muted: {
      background: {
        base: {
          light: "muted-100",
          dark: "muted-700",
        },
        hover: {
          light: "muted-50",
          dark: "muted-600",
        },
        focus: {
          light: "muted-50",
          dark: "muted-600",
        },
        active: {
          light: "muted-100",
          dark: "muted-700",
        },
      },
      font: {
        color: {
          light: "muted-700",
          dark: "muted-50",
        },
      },
    },
    mutedContrast: {
      background: {
        base: {
          light: "muted-100",
          dark: "muted-950",
        },
        hover: {
          light: "muted-50",
          dark: "muted-900",
        },
        focus: {
          light: "muted-50",
          dark: "muted-900",
        },
        active: {
          light: "muted-100",
          dark: "muted-950",
        },
      },
      font: {
        color: {
          light: "muted-700",
          dark: "muted-50",
        },
      },
    },
    primary: {
      background: {
        base: {
          light: "primary-500/10",
          dark: "primary-500/10",
        },
        hover: {
          light: "primary-500/20",
          dark: "primary-500/20",
        },
        focus: {
          light: "primary-500/20",
          dark: "primary-500/20",
        },
        active: {
          light: "primary-500/10",
          dark: "primary-500/10",
        },
      },
      font: {
        color: {
          light: "primary-500",
          dark: "primary-500",
        },
      },
    },
    info: {
      background: {
        base: {
          light: "info-500/10",
          dark: "info-500/10",
        },
        hover: {
          light: "info-500/20",
          dark: "info-500/20",
        },
        focus: {
          light: "info-500/20",
          dark: "info-500/20",
        },
        active: {
          light: "info-500/10",
          dark: "info-500/10",
        },
      },
      font: {
        color: {
          light: "info-500",
          dark: "info-500",
        },
      },
    },
    success: {
      background: {
        base: {
          light: "success-500/10",
          dark: "success-500/10",
        },
        hover: {
          light: "success-500/20",
          dark: "success-500/20",
        },
        focus: {
          light: "success-500/20",
          dark: "success-500/20",
        },
        active: {
          light: "success-500/10",
          dark: "success-500/10",
        },
      },
      font: {
        color: {
          light: "success-500",
          dark: "success-500",
        },
      },
    },
    warning: {
      background: {
        base: {
          light: "warning-500/10",
          dark: "warning-500/10",
        },
        hover: {
          light: "warning-500/20",
          dark: "warning-500/20",
        },
        focus: {
          light: "warning-500/20",
          dark: "warning-500/20",
        },
        active: {
          light: "warning-500/10",
          dark: "warning-500/10",
        },
      },
      font: {
        color: {
          light: "warning-500",
          dark: "warning-500",
        },
      },
    },
    danger: {
      background: {
        base: {
          light: "danger-500/10",
          dark: "danger-500/10",
        },
        hover: {
          light: "danger-500/20",
          dark: "danger-500/20",
        },
        focus: {
          light: "danger-500/20",
          dark: "danger-500/20",
        },
        active: {
          light: "danger-500/10",
          dark: "danger-500/10",
        },
      },
      font: {
        color: {
          light: "danger-500",
          dark: "danger-500",
        },
      },
    },
  },
  transition: {
    property: "colors",
    duration: "300",
  },
};

const config$N = {
  theme: {
    nui: {
      [key$N]: defaultConfig$M,
    },
  },
};
const buttonClose = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$N}`);
  addComponents({
    ".nui-button-close": {
      "@apply nui-focus flex items-center justify-center disabled:opacity-30 cursor-pointer":
        {},
      //Transition
      [`@apply transition-${config2.transition.property} duration-${config2.transition.duration}`]:
        {},
      //Size:xs
      "&.nui-button-xs": {
        [`@apply h-${config2.size.xs.outer} w-${config2.size.xs.outer}`]: {},
        //Icon
        ".nui-button-icon": {
          [`@apply h-${config2.size.xs.inner} w-${config2.size.xs.inner} fill-current`]:
            {},
        },
      },
      //Size:sm
      "&.nui-button-sm": {
        [`@apply h-${config2.size.sm.outer} w-${config2.size.sm.outer}`]: {},
        //Icon
        ".nui-button-icon": {
          [`@apply h-${config2.size.sm.inner} w-${config2.size.sm.inner} fill-current`]:
            {},
        },
      },
      //Size:md
      "&.nui-button-md": {
        [`@apply h-${config2.size.md.outer} w-${config2.size.md.outer}`]: {},
        //Icon
        ".nui-button-icon": {
          [`@apply h-${config2.size.md.inner} w-${config2.size.md.inner} fill-current`]:
            {},
        },
      },
      //Size:lg
      "&.nui-button-lg": {
        [`@apply h-${config2.size.lg.outer} w-${config2.size.lg.outer}`]: {},
        //Icon
        ".nui-button-icon": {
          [`@apply h-${config2.size.lg.inner} w-${config2.size.lg.inner} fill-current`]:
            {},
        },
      },
      //Size:xl
      "&.nui-button-xl": {
        [`@apply h-${config2.size.xl.outer} w-${config2.size.xl.outer}`]: {},
        //Icon
        ".nui-button-icon": {
          [`@apply h-${config2.size.xl.inner} w-${config2.size.xl.inner} fill-current`]:
            {},
        },
      },
      //Rounded:sm
      "&.nui-button-rounded-sm": {
        [`@apply ${config2.rounded.sm}`]: {},
      },
      //Rounded:md
      "&.nui-button-rounded-md": {
        [`@apply ${config2.rounded.md}`]: {},
      },
      //Rounded:lg
      "&.nui-button-rounded-lg": {
        [`@apply ${config2.rounded.lg}`]: {},
      },
      //Rounded:full
      "&.nui-button-rounded-full": {
        [`@apply ${config2.rounded.full}`]: {},
      },
      //Color:default
      "&.nui-button-default": {
        [`@apply text-${config2.color.default.font.color.light} dark:text-${config2.color.default.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.default.background.base.light} dark:bg-${config2.color.default.background.base.dark}`]:
          {},
        //Background hover
        [`@apply hover:bg-${config2.color.default.background.hover.light} dark:hover:bg-${config2.color.default.background.hover.dark}`]:
          {},
        //Background focus
        [`@apply focus-visible:bg-${config2.color.default.background.focus.light} dark:focus-visible:bg-${config2.color.default.background.focus.dark}`]:
          {},
        //Background active
        [`@apply active:enabled:bg-${config2.color.default.background.active.light} dark:active:enabled:bg-${config2.color.default.background.active.dark}`]:
          {},
      },
      //Color:default-contrast
      "&.nui-button-default-contrast": {
        [`@apply text-${config2.color.defaultContrast.font.color.light} dark:text-${config2.color.defaultContrast.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.defaultContrast.background.base.light} dark:bg-${config2.color.defaultContrast.background.base.dark}`]:
          {},
        //Background hover
        [`@apply hover:bg-${config2.color.defaultContrast.background.hover.light} dark:hover:bg-${config2.color.defaultContrast.background.hover.dark}`]:
          {},
        //Background focus
        [`@apply focus-visible:bg-${config2.color.defaultContrast.background.focus.light} dark:focus-visible:bg-${config2.color.defaultContrast.background.focus.dark}`]:
          {},
        //Background active
        [`@apply active:enabled:bg-${config2.color.defaultContrast.background.active.light} dark:active:enabled:bg-${config2.color.defaultContrast.background.active.dark}`]:
          {},
      },
      //Color:muted
      "&.nui-button-muted": {
        [`@apply text-${config2.color.muted.font.color.light} dark:text-${config2.color.muted.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.muted.background.base.light} dark:bg-${config2.color.muted.background.base.dark}`]:
          {},
        //Background hover
        [`@apply hover:bg-${config2.color.muted.background.hover.light} dark:hover:bg-${config2.color.muted.background.hover.dark}`]:
          {},
        //Background focus
        [`@apply focus-visible:bg-${config2.color.muted.background.focus.light} dark:focus-visible:bg-${config2.color.muted.background.focus.dark}`]:
          {},
        //Background active
        [`@apply active:enabled:bg-${config2.color.muted.background.active.light} dark:active:enabled:bg-${config2.color.muted.background.active.dark}`]:
          {},
      },
      //Color:muted-contrast
      "&.nui-button-muted-contrast": {
        [`@apply text-${config2.color.mutedContrast.font.color.light} dark:text-${config2.color.mutedContrast.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.mutedContrast.background.base.light} dark:bg-${config2.color.mutedContrast.background.base.dark}`]:
          {},
        //Background hover
        [`@apply hover:bg-${config2.color.mutedContrast.background.hover.light} dark:hover:bg-${config2.color.mutedContrast.background.hover.dark}`]:
          {},
        //Background focus
        [`@apply focus-visible:bg-${config2.color.mutedContrast.background.focus.light} dark:focus-visible:bg-${config2.color.mutedContrast.background.focus.dark}`]:
          {},
        //Background active
        [`@apply active:enabled:bg-${config2.color.mutedContrast.background.active.light} dark:active:enabled:bg-${config2.color.mutedContrast.background.active.dark}`]:
          {},
      },
      //Color:primary
      "&.nui-button-primary": {
        [`@apply text-${config2.color.primary.font.color.light} dark:text-${config2.color.primary.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.primary.background.base.light} dark:bg-${config2.color.primary.background.base.dark}`]:
          {},
        //Background hover
        [`@apply hover:bg-${config2.color.primary.background.hover.light} dark:hover:bg-${config2.color.primary.background.hover.dark}`]:
          {},
        //Background focus
        [`@apply focus-visible:bg-${config2.color.primary.background.focus.light} dark:focus-visible:bg-${config2.color.primary.background.focus.dark}`]:
          {},
        //Background active
        [`@apply active:enabled:bg-${config2.color.primary.background.active.light} dark:active:enabled:bg-${config2.color.primary.background.active.dark}`]:
          {},
      },
      //Color:info
      "&.nui-button-info": {
        [`@apply text-${config2.color.info.font.color.light} dark:text-${config2.color.info.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.info.background.base.light} dark:bg-${config2.color.info.background.base.dark}`]:
          {},
        //Background hover
        [`@apply hover:bg-${config2.color.info.background.hover.light} dark:hover:bg-${config2.color.info.background.hover.dark}`]:
          {},
        //Background focus
        [`@apply focus-visible:bg-${config2.color.info.background.focus.light} dark:focus-visible:bg-${config2.color.info.background.focus.dark}`]:
          {},
        //Background active
        [`@apply active:enabled:bg-${config2.color.info.background.active.light} dark:active:enabled:bg-${config2.color.info.background.active.dark}`]:
          {},
      },
      //Color:success
      "&.nui-button-success": {
        [`@apply text-${config2.color.success.font.color.light} dark:text-${config2.color.success.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.success.background.base.light} dark:bg-${config2.color.success.background.base.dark}`]:
          {},
        //Background hover
        [`@apply hover:bg-${config2.color.success.background.hover.light} dark:hover:bg-${config2.color.success.background.hover.dark}`]:
          {},
        //Background focus
        [`@apply focus-visible:bg-${config2.color.success.background.focus.light} dark:focus-visible:bg-${config2.color.success.background.focus.dark}`]:
          {},
        //Background active
        [`@apply active:enabled:bg-${config2.color.success.background.active.light} dark:active:enabled:bg-${config2.color.success.background.active.dark}`]:
          {},
      },
      //Color:warning
      "&.nui-button-warning": {
        [`@apply text-${config2.color.warning.font.color.light} dark:text-${config2.color.warning.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.warning.background.base.light} dark:bg-${config2.color.warning.background.base.dark}`]:
          {},
        //Background hover
        [`@apply hover:bg-${config2.color.warning.background.hover.light} dark:hover:bg-${config2.color.warning.background.hover.dark}`]:
          {},
        //Background focus
        [`@apply focus-visible:bg-${config2.color.warning.background.focus.light} dark:focus-visible:bg-${config2.color.warning.background.focus.dark}`]:
          {},
        //Background active
        [`@apply active:enabled:bg-${config2.color.warning.background.active.light} dark:active:enabled:bg-${config2.color.warning.background.active.dark}`]:
          {},
      },
      //Color:danger
      "&.nui-button-danger": {
        [`@apply text-${config2.color.danger.font.color.light} dark:text-${config2.color.danger.font.color.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.danger.background.base.light} dark:bg-${config2.color.danger.background.base.dark}`]:
          {},
        //Background hover
        [`@apply hover:bg-${config2.color.danger.background.hover.light} dark:hover:bg-${config2.color.danger.background.hover.dark}`]:
          {},
        //Background focus
        [`@apply focus-visible:bg-${config2.color.danger.background.focus.light} dark:focus-visible:bg-${config2.color.danger.background.focus.dark}`]:
          {},
        //Background active
        [`@apply active:enabled:bg-${config2.color.danger.background.active.light} dark:active:enabled:bg-${config2.color.danger.background.active.dark}`]:
          {},
      },
    },
  });
}, config$N);

module.export = buttonClose;

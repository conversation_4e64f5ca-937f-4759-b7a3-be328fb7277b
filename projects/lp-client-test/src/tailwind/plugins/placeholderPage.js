import plugin from "tailwindcss/plugin";

const key$j = "placeholderPage";
const defaultConfig$i = {
  height: {
    min: "[400px]",
  },
  inner: {
    width: "full",
  },
  content: {
    width: {
      max: "sm",
    },
  },
  size: {
    image: {
      xs: "xs",
      sm: "sm",
      md: "md",
      lg: "lg",
      xl: "xl",
    },
  },
  title: {
    font: {
      color: {
        light: "muted-800",
        dark: "white",
      },
    },
  },
  subtitle: {
    font: {
      color: {
        light: "muted-400",
        dark: "muted-400",
      },
    },
  },
};

const config$j = {
  theme: {
    nui: {
      [key$j]: defaultConfig$i,
    },
  },
};
const placeholderPage = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$j}`);
  addComponents({
    ".nui-placeholder-page": {
      [`@apply flex min-h-${config2.height.min} items-center justify-center`]:
        {},
      ".nui-placeholder-page-inner": {
        [`@apply mx-auto w-${config2.inner.width} text-center`]: {},
      },
      ".nui-placeholder-page-img": {
        "@apply mx-auto block": {},
      },
      ".nui-placeholder-page-content": {
        [`@apply mx-auto max-w-${config2.content.width.max}`]: {},
      },
      ".nui-placeholder-page-actions": {
        "@apply mt-4 flex justify-center gap-2": {},
      },
      ".nui-placeholder-page-title": {
        [`@apply text-${config2.title.font.color.light} dark:text-${config2.title.font.color.dark} mb-1 mt-4`]:
          {},
      },
      ".nui-placeholder-page-subtitle": {
        [`@apply text-${config2.subtitle.font.color.light} dark:text-${config2.subtitle.font.color.dark}`]:
          {},
      },
      "&.nui-placeholder-xs .nui-placeholder-page-img": {
        [`@apply max-w-${config2.size.image.xs}`]: {},
      },
      "&.nui-placeholder-sm .nui-placeholder-page-img": {
        [`@apply max-w-${config2.size.image.sm}`]: {},
      },
      "&.nui-placeholder-md .nui-placeholder-page-img": {
        [`@apply max-w-${config2.size.image.md}`]: {},
      },
      "&.nui-placeholder-lg .nui-placeholder-page-img": {
        [`@apply max-w-${config2.size.image.lg}`]: {},
      },
      "&.nui-placeholder-xl .nui-placeholder-page-img": {
        [`@apply max-w-${config2.size.image.xl}`]: {},
      },
    },
  });
}, config$j);

module.export = placeholderPage;

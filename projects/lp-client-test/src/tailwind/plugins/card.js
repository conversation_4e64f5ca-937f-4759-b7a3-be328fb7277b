import plugin from "tailwindcss/plugin";

const key$J = "card";
const defaultConfig$I = {
  width: "full",
  rounded: {
    none: "none",
    sm: "md",
    md: "lg",
    lg: "xl",
  },
  shadow: {
    light: "muted-300/30",
    dark: "muted-800/30",
    size: "xl",
  },
  color: {
    default: {
      background: {
        light: "white",
        dark: "muted-800",
      },
      border: {
        light: "muted-200",
        dark: "muted-700",
      },
    },
    defaultContrast: {
      background: {
        light: "white",
        dark: "muted-950",
      },
      border: {
        light: "muted-300",
        dark: "muted-800",
      },
    },
    muted: {
      background: {
        light: "muted-100",
        dark: "muted-800",
      },
      border: {
        light: "muted-200",
        dark: "muted-700",
      },
    },
    mutedContrast: {
      background: {
        light: "muted-100",
        dark: "muted-950",
      },
      border: {
        light: "muted-200",
        dark: "muted-800",
      },
    },
    dark: {
      background: {
        light: "muted-900",
        dark: "muted-100",
      },
      border: {
        light: "muted-800",
        dark: "muted-50",
      },
    },
    black: {
      background: {
        light: "black",
        dark: "white",
      },
      border: {
        light: "black",
        dark: "white",
      },
    },
    primary: {
      background: {
        light: "primary-500",
        dark: "primary-500",
      },
      border: {
        light: "primary-500",
        dark: "primary-500",
      },
    },
    info: {
      background: {
        light: "info-500/10",
        dark: "info-500/10",
      },
      border: {
        light: "info-500",
        dark: "info-500",
      },
    },
    success: {
      background: {
        light: "success-500/10",
        dark: "success-500/10",
      },
      border: {
        light: "success-500",
        dark: "success-500",
      },
    },
    warning: {
      background: {
        light: "warning-500",
        dark: "warning-500",
      },
      border: {
        light: "warning-500",
        dark: "warning-500",
      },
    },
    danger: {
      background: {
        light: "danger-500/10",
        dark: "danger-500/10",
      },
      border: {
        light: "danger-500",
        dark: "danger-500",
      },
    },
  },
  transition: {
    property: "all",
    duration: "300",
  },
};

const config$J = {
  theme: {
    nui: {
      [key$J]: defaultConfig$I,
    },
  },
};
const card = plugin(({ addComponents, theme }) => {
  const config2 = theme(`nui.${key$J}`);
  addComponents({
    ".nui-card": {
      //Base
      [`@apply relative w-${config2.width} transition-${config2.transition.property} duration-${config2.transition.duration}`]:
        {},
      //Color:default
      "&.nui-card-default": {
        //Border
        [`@apply border border-${config2.color.default.border.light} dark:border-${config2.color.default.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.default.background.light} dark:bg-${config2.color.default.background.dark}`]:
          {},
      },
      //Color:defaultContrast
      "&.nui-card-default-contrast": {
        //Border
        [`@apply border border-${config2.color.defaultContrast.border.light} dark:border-${config2.color.defaultContrast.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.defaultContrast.background.light} dark:bg-${config2.color.defaultContrast.background.dark}`]:
          {},
      },
      //Color:muted
      "&.nui-card-muted": {
        //Border
        [`@apply border border-${config2.color.muted.border.light} dark:border-${config2.color.muted.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.muted.background.light} dark:bg-${config2.color.muted.background.dark}`]:
          {},
      },
      //Color:mutedContrast
      "&.nui-card-muted-contrast": {
        //Border
        [`@apply border border-${config2.color.mutedContrast.border.light} dark:border-${config2.color.mutedContrast.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.mutedContrast.background.light} dark:bg-${config2.color.mutedContrast.background.dark}`]:
          {},
      },
      //Color:dark
      "&.nui-card-dark": {
        //Border
        [`@apply border border-${config2.color.dark.border.light} dark:border-${config2.color.dark.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.dark.background.light} dark:bg-${config2.color.dark.background.dark}`]:
          {},
      },
      //Color:black
      "&.nui-card-black": {
        //Border
        [`@apply border border-${config2.color.black.border.light} dark:border-${config2.color.black.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.black.background.light} dark:bg-${config2.color.black.background.dark}`]:
          {},
      },
      //Color:primary
      "&.nui-card-primary": {
        //Border
        [`@apply border border-${config2.color.primary.border.light} dark:border-${config2.color.primary.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.primary.background.light} dark:bg-${config2.color.primary.background.dark}`]:
          {},
      },
      //Color:info
      "&.nui-card-info": {
        //Border
        [`@apply border border-${config2.color.info.border.light} dark:border-${config2.color.info.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.info.background.light} dark:bg-${config2.color.info.background.dark}`]:
          {},
      },
      //Color:success
      "&.nui-card-success": {
        //Border
        [`@apply border border-${config2.color.success.border.light} dark:border-${config2.color.success.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.success.background.light} dark:bg-${config2.color.success.background.dark}`]:
          {},
      },
      //Color:warning
      "&.nui-card-warning": {
        //Border
        [`@apply border border-${config2.color.warning.border.light} dark:border-${config2.color.warning.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.warning.background.light} dark:bg-${config2.color.warning.background.dark}`]:
          {},
      },
      //Color:danger
      "&.nui-card-danger": {
        //Border
        [`@apply border border-${config2.color.danger.border.light} dark:border-${config2.color.danger.border.dark}`]:
          {},
        //Background
        [`@apply bg-${config2.color.danger.background.light} dark:bg-${config2.color.danger.background.dark}`]:
          {},
      },
      //Rounded:sm
      "&.nui-card-rounded-sm": {
        [`@apply rounded-${config2.rounded.sm}`]: {},
      },
      //Rounded:md
      "&.nui-card-rounded-md": {
        [`@apply rounded-${config2.rounded.md}`]: {},
      },
      //Rounded:lg
      "&.nui-card-rounded-lg": {
        [`@apply rounded-${config2.rounded.lg}`]: {},
      },
      //Shadow
      "&:not(.nui-card-primary):not(.nui-card-info):not(.nui-card-success):not(.nui-card-warning):not(.nui-card-danger)":
        {
          "&.nui-card-shadow": {
            [`@apply shadow-${config2.shadow.light} dark:shadow-${config2.shadow.dark} shadow-${config2.shadow.size}`]:
              {},
          },
          "&.nui-card-shadow-hover": {
            [`@apply hover:shadow-${config2.shadow.light} dark:hover:shadow-${config2.shadow.dark} hover:shadow-${config2.shadow.size}`]:
              {},
          },
        },
    },
  });
}, config$J);

module.export = card;

ion-button {
    //height: 40px;
    //text-transform: initial;
    //font-size: 16px;
    letter-spacing: 0;
    //font-weight: 500;
    //--box-shadow: none;
    --border-radius: 6px;
}

ion-button {
    &.button-solid {
        --background: var(--ion-color-primary);
        --color: #fff;
    }

    &.button-outline {
        --border-color: var(--ion-color-primary);
        --color: var(--ion-color-primary);
        font-weight: 300;

    }
}

.landing {
    ion-button {
        &.button-solid {
            --background: #fff;
            --color: var(--ion-color-primary);
        }
    
        &.button-outline {
            --border-color: #fff;
            --color: #fff;
            font-weight: 300;
    
        }
    }
}
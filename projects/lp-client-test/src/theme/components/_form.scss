.form {
    ion-item {
        --background: #fff;
        border: 2px solid #efefef;
        border-radius: 8px;
        margin-block-end: 0.8rem;
        --padding-start: 8px;
        margin-top: -4px;

        ion-icon, fa-icon {
            font-size: 22px;
            align-self: center;
            margin-right: 14px;
        }

        .label-floating {
            margin-top: -4px;
        }
    }

    ion-item.item-has-focus {
        --highlight-background: transparent;
        border: 2px solid var(--ion-color-primary);
    }

    ion-item.item-interactive.ion-touched.ion-invalid {
        --highlight-background: transparent;
        border: 2px solid var(--ion-color-danger);
    }

    ion-checkbox {
        --size: 28px;
        --background-checked: var(--ion-color-primary);
    }
      
    ion-checkbox::part(container) {
        border-radius: 6px;
        border: 2px solid var(--ion-color-primary);
    }

    ion-item:has(.validator-error) {
        --background: none;
        --min-height: 0;
        color: var(--ion-color-danger);
        border: none;

        .item-inner {
            border: none;
        }
    }

    .form-spacer {
        margin-block-end: 2rem;
    }
}

.menu {
    & .blur {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        background-color: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(8px);
        z-index: -1;
        border-top-left-radius: 25px;
        border-bottom-left-radius: 25px;
        box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    }

    ion-item.menu-item {
        margin-left: 10px;

        .icon {
            min-width: 60px;
            height: 60px;
            line-height: 60px;
            text-align: center;
            font-size: 1.75em;
        }

        .title {
            padding: 0 10px;
            height: 60px;
            line-height: 60px;
            white-space: nowrap;
        }
    }

    ion-item.active  {
        --background: var(--ion-color-primary);
        background: var(--ion-color-primary);
        color: #fff;
        border-top-left-radius: 50px;
        border-bottom-left-radius: 50px;
        margin-left: 10px;
    }

    ion-item.active:not(:first-child)::before  {
        content: "";
        position: absolute;
        top: -20px;
        right: 0;
        width: 20px;
        height: 20px;
        background: transparent;
        border-bottom-right-radius: 20px;
        box-shadow: 7.5px 7.5px 0 7.5px #fff;
    }

    ion-item.app {
        margin-bottom: 20px;

        img {
            max-height: 100px;
            margin: 0 auto;
        }
    }

    ion-item.lang-selector {
        margin: 0 10px 20px 10px;
    }
}
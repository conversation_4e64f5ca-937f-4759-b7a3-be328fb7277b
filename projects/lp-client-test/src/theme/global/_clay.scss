/* https://codeadrian.github.io/clay.css/ */
.clay {
    --background:var(--clay-background,rgba(0,0,0,.005));
    --border-radius:var(--clay-border-radius,20px);
    --box-shadow:var(--clay-shadow-outset,8px 8px 16px 0 rgba(0,0,0,.25)),
                    inset var(--clay-shadow-inset-primary,-8px -8px 16px 0 rgba(0,0,0,.25)),
                    inset var(--clay-shadow-inset-secondary,8px 8px 16px 0 hsla(0,0%,100%,.2));

    background:var(--clay-background,rgba(0,0,0,.005));
    border-radius:var(--clay-border-radius,20px);
    box-shadow:var(--clay-shadow-outset,8px 8px 16px 0 rgba(0,0,0,.25)),
                    inset var(--clay-shadow-inset-primary,-8px -8px 16px 0 rgba(0,0,0,.25)),
                    inset var(--clay-shadow-inset-secondary,8px 8px 16px 0 hsla(0,0%,100%,.2));
}
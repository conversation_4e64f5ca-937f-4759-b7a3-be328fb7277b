import { NgModule } from '@angular/core';
import { PreloadAllModules, RouterModule, Routes } from '@angular/router';
import { AuthGuardService } from 'lp-client-api';

// import { AppTabsPageModule } from './public/app-tabs/app-tabs.module';

const routes: Routes = [
  // Commented out lazy load for missing module
  // {
  //   path: '',
  //   loadChildren: () =>
  //     import('./public/app-tabs/app-tabs.module').then(
  //       (m) => m.AppTabsPageModule
  //     ),
  // },
  { path: '', redirectTo: 'public/landing', pathMatch: 'full' },
  // Redirect legacy 'app/*' routes to 'public/app/*'
  { path: 'app/:page/:subpage', redirectTo: 'public/app/:page/:subpage' },
  { path: 'app/:page', redirectTo: 'public/app/:page' },
  {
    path: 'public',
    loadChildren: () =>
      import('./public/public.module').then((m) => m.PublicModule),
  },
  {
    path: 'secure',
    loadChildren: () =>
      import('./secure/secure.module').then((m) => m.SecureModule),
    canActivate: [AuthGuardService],
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { preloadingStrategy: PreloadAllModules })],
  exports: [RouterModule],
})
export class AppRoutingModule {}

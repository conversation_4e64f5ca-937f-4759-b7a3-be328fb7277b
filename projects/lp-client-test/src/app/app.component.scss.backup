// ===========================================
// CSS VARIABLES AND ROOT STYLES
// ===========================================

:root {
  --header-height: 64px;
  --bottom-nav-height: 80px;
  --sidebar-width: 280px;
}

@media (max-width: 768px) {
  :root {
    --bottom-nav-height: 70px;
  }
}

// ===========================================
// DESKTOP SIDEBAR STYLES
// ===========================================

.desktop-sidebar {
  position: fixed;
  left: 0;
  top: 0;
  width: var(--sidebar-width);
  height: 100vh;
  background: #ffffff; /* Clean white background */
  border-right: 1px solid #e0e0e0;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  .sidebar-header {
    background: white;
    border-bottom: 1px solid #e0e0e0;
    padding: 24px;
    
    .sidebar-logo {
      text-align: center;
      margin-bottom: 16px;
      
      img {
        max-width: 120px;
        height: auto;
      }
    }
    
    .sidebar-profile {
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
      border-radius: 8px;
      padding: 8px;
      margin: -8px;
      
      &:hover {
        background: rgba(0, 0, 0, 0.02);
        transform: translateY(-1px);
      }
      
      .profile-avatar {
        width: 60px;
        height: 60px;
        margin: 0 auto 12px;
        position: relative;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        
        &::before {
          content: '';
          position: absolute;
          inset: -3px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--ion-color-primary, #f5821f), transparent);
          opacity: 0;
          transition: opacity 0.3s ease;
        }
        
        ion-icon {
          font-size: 60px;
          color: #9e9e9e;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          z-index: 1;
        }
      }
      
      h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #212121;
        transition: all 0.3s ease;
      }
      
      p {
        margin: 0;
        font-size: 13px;
        color: #666;
        transition: all 0.3s ease;
      }
      
      &:hover {
        .profile-avatar {
          transform: scale(1.05);
          
          &::before {
            opacity: 0.1;
          }
          
          ion-icon {
            color: var(--ion-color-primary, #f5821f);
            filter: drop-shadow(0 2px 8px rgba(245, 130, 31, 0.3));
          }
        }
        
        h3 {
          color: var(--ion-color-primary, #f5821f);
        }
        
        p {
          color: #424242;
        }
      }
    }
  }
  
  .sidebar-menu {
    padding: 12px 0;
    flex: 1;
    overflow-y: auto;
    
    .menu-items {
      .sidebar-menu-item {
        display: flex;
        align-items: center;
        padding: 12px 24px;
        margin: 2px 8px;
        border-radius: 8px;
        text-decoration: none;
        color: #424242;
        font-size: 15px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        
        // Subtle shimmer effect on hover
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
          transition: left 0.5s ease;
        }
        
        ion-icon {
          font-size: 20px;
          margin-right: 16px;
          color: #666;
          min-width: 20px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateZ(0); // Hardware acceleration
        }
        
        span {
          flex: 1;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        &:hover {
          background: rgba(0, 0, 0, 0.04);
          transform: translateX(4px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          
          &::before {
            left: 100%;
          }
          
          ion-icon {
            transform: scale(1.1) translateZ(0);
            color: var(--ion-color-primary, #f5821f);
          }
          
          span {
            font-weight: 600;
          }
        }
        
        &:active {
          transform: translateX(2px) scale(0.98);
          transition: all 0.1s ease;
        }
        
        &.active {
          background: linear-gradient(135deg, rgba(245, 130, 31, 0.1), rgba(245, 130, 31, 0.05));
          color: var(--ion-color-primary, #f5821f);
          font-weight: 600;
          box-shadow: 0 2px 8px rgba(245, 130, 31, 0.2);
          
          ion-icon {
            color: var(--ion-color-primary, #f5821f);
            transform: scale(1.1) translateZ(0);
            filter: drop-shadow(0 2px 4px rgba(245, 130, 31, 0.3));
          }
          
          &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 60%;
            background: var(--ion-color-primary, #f5821f);
            border-radius: 0 2px 2px 0;
            box-shadow: 0 0 8px rgba(245, 130, 31, 0.4);
          }
        }
        
        &.logout {
          color: #F44336;
          
          ion-icon {
            color: #F44336;
          }
          
          &:hover {
            background: rgba(244, 67, 54, 0.08);
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.15);
            
            ion-icon {
              color: #F44336;
            }
          }
        }
      }
      
      .sidebar-divider {
        height: 1px;
        background: #e0e0e0;
        margin: 12px 16px;
      }
    }
  }
  
  .sidebar-footer {
    padding: 16px 24px;
    border-top: 1px solid #e0e0e0;
    background: #fafafa;
    
    .app-version {
      text-align: center;
      
      p {
        margin: 0;
        font-size: 12px;
        color: #999;
      }
    }
  }
}

// ===========================================
// MAIN LAYOUT STYLES
// ===========================================

.main-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  
  /* Use padding for reliable sidebar spacing */
  &.with-sidebar {
    padding-left: var(--sidebar-width);
  }
  
  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    
    /* Ensure ion-router-outlet is visible and properly positioned */
    ion-router-outlet {
      flex: 1;
      display: block;
      width: 100%;
      min-height: calc(100vh - var(--header-height, 64px));
      position: relative;
    }
  }
}

/* Mobile responsive behavior */
@media (max-width: 768px) {
  .main-layout {
    &.with-sidebar {
      padding-left: 0; /* Remove sidebar padding on mobile */
    }
    
    .main-content {
      min-height: calc(100vh - var(--bottom-nav-height, 80px));
      padding-bottom: var(--bottom-nav-height, 80px);
    }
  }
}

// ===========================================
// MODERN HEADER STYLES
// ===========================================

.modern-header {
  --background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  --color: #2c3e50;
  --box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1100; /* above sidebar (1000), below overlays */
  background: var(--ion-background-color, #ffffff);
  border-bottom: 1px solid #e0e0e0;
  
  .modern-toolbar {
    --background: transparent;
    --color: #2c3e50;
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 64px;
    
    .modern-title {
      --color: #2c3e50;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: -0.025em;
    }
    
    .back-button,
    .notification-button,
    .menu-toggle-button {
      --background: transparent;
      --color: #6c757d;
      --border-radius: 8px;
      --padding-start: 8px;
      --padding-end: 8px;
      margin: 0 4px;
      height: 40px;
      width: 40px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      
      &:hover {
        --background: rgba(0, 0, 0, 0.04);
        --color: var(--ion-color-primary, #f5821f);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      &:active {
        transform: translateY(0) scale(0.96);
        transition: all 0.1s ease;
      }
      
      ion-icon {
        font-size: 20px;
      }
    }
    
    .notification-button {
      position: relative;
      
      .notification-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      
      .notification-badge {
        position: absolute;
        top: -6px;
        right: -6px;
        background: #ff4757;
        color: white;
        font-size: 10px;
        font-weight: 600;
        line-height: 1;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
        animation: pulse 2s infinite;
      }
    }
  }
}

// ===========================================
// MOBILE MENU OVERLAY STYLES
// ===========================================

.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(4px);
  z-index: 2000; /* Mobile menu overlay above all navigation */
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  &.menu-open {
    opacity: 1;
    visibility: visible;
    
    .mobile-menu-panel {
      transform: translateX(0);
    }
  }
  
  .mobile-menu-panel {
    position: absolute;
    top: 0;
    right: 0;
    width: 320px;
    height: 100%;
    background: #ffffff;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.15);
    transform: translateX(100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    flex-direction: column;
    
    .menu-header {
      padding: 20px 24px;
      border-bottom: 1px solid #e9ecef;
      display: flex;
      justify-content: space-between;
      align-items: center;
      background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
      
      h2 {
        margin: 0;
        font-size: 20px;
        font-weight: 700;
        color: #2c3e50;
      }
      
      ion-button {
        --background: transparent;
        --color: #6c757d;
        --border-radius: 50%;
        height: 36px;
        width: 36px;
        margin: 0;
        
        &:hover {
          --background: rgba(0, 0, 0, 0.04);
        }
        
        ion-icon {
          font-size: 18px;
        }
      }
    }
    
    .menu-content {
      flex: 1;
      overflow-y: auto;
      
      .profile-section,
      .logo-section {
        padding: 24px;
        text-align: center;
        border-bottom: 1px solid #e9ecef;
        
        .profile-avatar {
          width: 60px;
          height: 60px;
          margin: 0 auto 12px;
          
          ion-icon {
            font-size: 60px;
            color: var(--ion-color-primary, #f5821f);
          }
        }
        
        img {
          max-width: 120px;
          height: auto;
        }
        
        h3 {
          margin: 0 0 4px 0;
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
        }
        
        p {
          margin: 0;
          font-size: 14px;
          color: #6c757d;
        }
      }
      
      .menu-list {
        padding: 12px 0;
        
        .menu-item {
          display: flex;
          align-items: center;
          padding: 16px 24px;
          color: #495057;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          border-left: 3px solid transparent;
          
          ion-icon {
            font-size: 20px;
            margin-right: 16px;
            color: #6c757d;
            min-width: 20px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          }
          
          &:hover {
            background: rgba(0, 0, 0, 0.02);
            color: var(--ion-color-primary, #f5821f);
            border-left-color: var(--ion-color-primary, #f5821f);
            
            ion-icon {
              color: var(--ion-color-primary, #f5821f);
              transform: scale(1.1);
            }
          }
          
          &:active {
            transform: scale(0.98);
            transition: all 0.1s ease;
          }
          
          &.active {
            background: rgba(245, 130, 31, 0.08);
            color: var(--ion-color-primary, #f5821f);
            font-weight: 600;
            border-left-color: var(--ion-color-primary, #f5821f);
            
            ion-icon {
              color: var(--ion-color-primary, #f5821f);
              transform: scale(1.1);
            }
          }
          
          &.logout {
            color: #dc3545;
            
            ion-icon {
              color: #dc3545;
            }
            
            &:hover {
              background: rgba(220, 53, 69, 0.08);
              color: #dc3545;
              border-left-color: #dc3545;
              
              ion-icon {
                color: #dc3545;
              }
            }
          }
        }
        
        .menu-divider {
          height: 1px;
          background: #e9ecef;
          margin: 12px 24px;
        }
      }
    }
    
    .menu-footer {
      padding: 16px 24px;
      border-top: 1px solid #e9ecef;
      background: #f8f9fa;
      
      .app-version {
        text-align: center;
        
        p {
          margin: 0;
          font-size: 12px;
          color: #999;
        }
      }
    }
  }
}

// ===========================================
// BOTTOM NAVIGATION STYLES
// ===========================================

/* Bottom navigation styling matching lp-client */
:host {
  router-outlet + .bottom-nav {
    display: block;
  }
}

/* Bottom navigation bar */
.bottom-nav {
  backdrop-filter: blur(10px);
  background: linear-gradient(to right, #0a2463, #1e88e5, #0a2463) !important;
  border-top: none;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1050; /* Bottom nav above content but below overlays */
  transition: transform 0.3s ease-in-out;
  padding: 8px 0;
  height: var(--bottom-nav-height);
  display: flex;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

/* Background glow effect */
.bottom-nav::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(
    circle at center,
    rgba(33, 150, 243, 0.3) 0%,
    rgba(33, 150, 243, 0) 70%
  );
  animation: rotate 15s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Navigation items */
.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  text-decoration: none;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  min-width: 60px;
  border: none;
  background: none;
  cursor: pointer;
}

.nav-item:hover {
  color: rgba(255, 255, 255, 0.9);
  transform: translateY(-2px);
}

.nav-item.active {
  color: #ffffff;
  transform: translateY(-4px);
}

.nav-item.active::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 4px;
  height: 4px;
  background: #ffffff;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.8);
}

.nav-item ion-icon {
  font-size: 24px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.nav-item.active ion-icon {
  transform: scale(1.1);
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
}

.nav-item span {
  font-size: 11px;
  font-weight: 500;
  text-align: center;
  line-height: 1.2;
}

/* Click animation */
.nav-item.clicked {
  transform: scale(0.95) translateY(-2px);
}

/* Floating indicator */
.nav-indicator {
  position: absolute;
  bottom: -2px;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #64b5f6, #2196f3, #1976d2);
  border-radius: 2px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 0 10px rgba(33, 150, 243, 0.6);
}

// ===========================================
// RESPONSIVE BREAKPOINTS
// ===========================================

/* Hide sidebar on mobile, show bottom nav and mobile menu */
@media (max-width: 768px) {
  .desktop-sidebar {
    display: none;
  }
  
  .main-layout {
    margin-left: 0 !important; /* Force no sidebar margin on mobile */
    
    .main-content {
      min-height: calc(100vh - var(--header-height, 64px) - var(--bottom-nav-height, 80px));
    }
  }
  
  .modern-header {
    left: 0 !important; /* Full width on mobile, override desktop sidebar positioning */
    
    &.with-sidebar {
      left: 0 !important; /* Force full width even if with-sidebar class is present */
    }
  }
  
  .mobile-only {
    display: block !important;
  }
  
  .desktop-only {
    display: none !important;
  }
  
  .bottom-nav {
    height: var(--bottom-nav-height, 70px);
    padding: 6px 0;
  }

  .nav-item {
    padding: 6px 8px;
    min-width: 50px;
  }

  .nav-item ion-icon {
    font-size: 20px;
  }

  .nav-item span {
    font-size: 10px;
  }
}

/* Show sidebar on desktop, hide bottom nav and mobile menu toggle */
@media (min-width: 769px) {
  .mobile-menu-overlay {
    display: none;
  }
  
  .mobile-only {
    display: none !important;
  }
  
  .desktop-only {
    display: block !important;
  }
}

/* Large screens adjustments */
@media (min-width: 1200px) {
  :root {
    --sidebar-width: 300px;
  }
  
  .desktop-sidebar {
    width: var(--sidebar-width);
  }
}

// ===========================================
// UTILITY CLASSES
// ===========================================

/* Hide navigation on certain pages */
.hide-nav .bottom-nav {
  transform: translateY(100%);
}

/* Pulse animation for notifications */
@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

import { Injectable } from '@angular/core';
import { 
  PageConfiguration, 
  ComponentConfiguration, 
  NavigationConfiguration,
  ValidationResult, 
  ValidationOptions,
  ShowWhenCondition 
} from '../../environments/page-configuration.types';
import { ComponentRegistryService } from './component-registry.service';

@Injectable({
  providedIn: 'root'
})
export class PageConfigurationValidationService {
  private debug = true; // Enable debug logging for development

  constructor(private componentRegistry: ComponentRegistryService) {
    console.log('[PageConfigValidation] Service initialized');
  }

  /**
   * Validate an array of page configurations
   */
  validatePages(pages: PageConfiguration[], options: ValidationOptions = {}): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    let componentCount = 0;

    if (this.debug) {
      console.log(`[PageConfigValidation] Validating ${pages.length} pages`);
    }

    // Validate each page
    pages.forEach((page, index) => {
      const pageResult = this.validatePage(page, index, options);
      errors.push(...pageResult.errors);
      warnings.push(...pageResult.warnings);
      componentCount += page.components?.length || 0;
    });

    // Check for duplicate page paths
    const paths = pages.map(p => p.path);
    const duplicatePaths = paths.filter((path, index) => paths.indexOf(path) !== index);
    if (duplicatePaths.length > 0) {
      errors.push(`Duplicate page paths found: ${duplicatePaths.join(', ')}`);
    }

    const result: ValidationResult = {
      isValid: errors.length === 0,
      errors,
      warnings,
      context: {
        pageCount: pages.length,
        componentCount,
        validatedAt: new Date()
      }
    };

    if (this.debug) {
      console.log(`[PageConfigValidation] Validation complete:`, result);
    }

    return result;
  }

  /**
   * Validate a single page configuration
   */
  validatePage(page: PageConfiguration, pageIndex: number, options: ValidationOptions = {}): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Required fields validation
    if (!page.title) {
      errors.push(`Page ${pageIndex}: Missing required 'title' field`);
    }

    if (!page.path) {
      errors.push(`Page ${pageIndex}: Missing required 'path' field`);
    }

    if (!page.components || !Array.isArray(page.components)) {
      errors.push(`Page ${pageIndex}: Missing or invalid 'components' array`);
    } else {
      // Validate each component
      page.components.forEach((component, componentIndex) => {
        const componentResult = this.validateComponent(component, pageIndex, componentIndex, options);
        errors.push(...componentResult.errors);
        warnings.push(...componentResult.warnings);
      });
    }

    // Optional field validation
    if (page.secure !== undefined && typeof page.secure !== 'boolean') {
      warnings.push(`Page ${pageIndex}: 'secure' should be a boolean value`);
    }

    if (page.class && typeof page.class !== 'string') {
      warnings.push(`Page ${pageIndex}: 'class' should be a string value`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate a component configuration
   */
  validateComponent(
    component: ComponentConfiguration, 
    pageIndex: number, 
    componentIndex: number, 
    options: ValidationOptions = {}
  ): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];
    const componentRef = `Page ${pageIndex}, Component ${componentIndex}`;

    // Required fields validation
    if (!component.type) {
      errors.push(`${componentRef}: Missing required 'type' field`);
    } else {
      // Validate component registration if requested
      if (options.validateComponents !== false) {
        if (!this.componentRegistry.isRegistered(component.type)) {
          errors.push(`${componentRef}: Component type '${component.type}' is not registered`);
        }
      }
    }

    // Validate showWhen condition
    if (component.showWhen) {
      const validConditions: ShowWhenCondition[] = ['authenticated', 'anonymous', 'always'];
      if (!validConditions.includes(component.showWhen)) {
        errors.push(`${componentRef}: Invalid 'showWhen' value '${component.showWhen}'. Must be one of: ${validConditions.join(', ')}`);
      }
    }

    // Validate inputs vs config (should use inputs consistently)
    if (component.inputs && component.config) {
      warnings.push(`${componentRef}: Both 'inputs' and 'config' are specified. Consider using 'inputs' consistently.`);
    }

    // Validate inputs/config structure
    const inputsToValidate = component.inputs || component.config;
    if (inputsToValidate && typeof inputsToValidate !== 'object') {
      errors.push(`${componentRef}: 'inputs' or 'config' must be an object`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate navigation configuration
   */
  validateNavigation(navigation: NavigationConfiguration, options: ValidationOptions = {}): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!navigation.routes || !Array.isArray(navigation.routes)) {
      errors.push('Navigation: Missing or invalid routes array');
      return { isValid: false, errors, warnings };
    }

    // Validate each route
    navigation.routes.forEach((route, index) => {
      if (!route.label) {
        errors.push(`Navigation route ${index}: Missing required 'label' field`);
      }

      if (!route.path && !route.link) {
        errors.push(`Navigation route ${index}: Must have either 'path' or 'link' field`);
      }

      if (!route.icon) {
        warnings.push(`Navigation route ${index}: Missing 'icon' field`);
      }

      // Validate boolean fields
      const booleanFields = ['active', 'sidebar', 'main', 'more', 'admin', 'exact'];
      booleanFields.forEach(field => {
        if (route[field as keyof typeof route] !== undefined && typeof route[field as keyof typeof route] !== 'boolean') {
          warnings.push(`Navigation route ${index}: '${field}' should be a boolean value`);
        }
      });
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * Validate complete environment configuration
   */
  validateEnvironmentConfig(config: any, options: ValidationOptions = {}): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check if pages configuration exists
    if (!config.lssConfig?.pages) {
      errors.push('Environment: Missing pages configuration in lssConfig');
      return { isValid: false, errors, warnings };
    }

    const pagesConfig = config.lssConfig.pages;

    // Validate pages array
    if (!Array.isArray(pagesConfig)) {
      errors.push('Environment: Pages configuration must be an array');
      return { isValid: false, errors, warnings };
    }

    // Validate pages
    const pagesResult = this.validatePages(pagesConfig, options);
    errors.push(...pagesResult.errors);
    warnings.push(...pagesResult.warnings);

    // Validate navigation if present
    if (config.lssConfig.navigation) {
      const navResult = this.validateNavigation(config.lssConfig.navigation, options);
      errors.push(...navResult.errors);
      warnings.push(...navResult.warnings);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      context: pagesResult.context
    };
  }

  /**
   * Enable or disable debug mode
   */
  setDebugMode(enabled: boolean): void {
    this.debug = enabled;
    console.log(`[PageConfigValidation] Debug mode ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Get validation statistics
   */
  getValidationStats(result: ValidationResult): string {
    const { context } = result;
    return `Validation ${result.isValid ? 'PASSED' : 'FAILED'}: ` +
           `${context?.pageCount || 0} pages, ${context?.componentCount || 0} components, ` +
           `${result.errors.length} errors, ${result.warnings.length} warnings`;
  }
}

import { Injectable, Type } from '@angular/core';
import { ComponentRegistryService } from './component-registry.service';

@Injectable({ providedIn: 'root' })
export class DynamicComponentLoaderService {
  // Map of component names to component types (legacy support)
  private componentMap: Record<string, Type<any>> = {};

  // Debug flag
  private debug = false;

  constructor(private componentRegistry: ComponentRegistryService) {
    if (this.debug) {
      console.log('[DynamicComponentLoader] Service initialized with ComponentRegistry integration');
    }
  }

  // Expose ComponentRegistry for external access
  get componentRegistryService(): ComponentRegistryService {
    return this.componentRegistry;
  }

  // Register a component with the loader (now delegates to ComponentRegistry)
  registerComponent(name: string, component: Type<any>): void {
    // Register with both legacy map and new registry for backward compatibility
    this.componentMap[name] = component;
    this.componentRegistry.registerComponent(name, component, 'dynamic');

    if (this.debug) {
      console.log(`[DynamicComponentLoader] Registered component: ${name} (legacy + registry)`);
    }
  }

  // Log all registered components for debugging
  logRegisteredComponents(): void {
    if (this.debug) {
      console.log('[DynamicComponentLoader] Legacy components:', Object.keys(this.componentMap));
      console.log('[DynamicComponentLoader] Registry components:', this.componentRegistry.getComponentNames());
    }
  }

  // Resolve a component name to a component type (now uses ComponentRegistry first)
  resolveComponent(componentName: string): Type<any> | null {
    try {
      if (this.debug) {
        console.log(`[DynamicComponentLoader] Attempting to resolve: ${componentName}`);
        this.logRegisteredComponents();
      }

      // First, try the new ComponentRegistry
      let component = this.componentRegistry.getComponent(componentName);
      if (component) {
        if (this.debug) {
          console.log(`[DynamicComponentLoader] Found component in registry: ${componentName}`);
        }
        return component;
      }

      // Try with PascalCase in registry
      const pascalName = componentName.charAt(0).toUpperCase() + componentName.slice(1);
      component = this.componentRegistry.getComponent(pascalName);
      if (component) {
        if (this.debug) {
          console.log(`[DynamicComponentLoader] Found component in registry (PascalCase): ${pascalName}`);
        }
        return component;
      }

      // Fallback to legacy component map for backward compatibility
      if (this.componentMap[componentName]) {
        if (this.debug) {
          console.log(`[DynamicComponentLoader] Found component in legacy map: ${componentName}`);
        }
        return this.componentMap[componentName];
      }

      // Try with PascalCase in legacy map
      if (this.componentMap[pascalName]) {
        if (this.debug) {
          console.log(`[DynamicComponentLoader] Found component in legacy map (PascalCase): ${pascalName}`);
        }
        return this.componentMap[pascalName];
      }

      // Not found anywhere
      console.warn(`[DynamicComponentLoader] Component not found: ${componentName}`);
      console.warn(`[DynamicComponentLoader] Available components:`, this.getAvailableComponents());
      return null;
    } catch (error) {
      console.error(`[DynamicComponentLoader] Error resolving component ${componentName}:`, error);
      return null;
    }
  }

  // Get list of all available components
  getAvailableComponents(): string[] {
    const registryComponents = this.componentRegistry.getComponentNames();
    const legacyComponents = Object.keys(this.componentMap);

    // Combine and deduplicate
    const allComponents = [...new Set([...registryComponents, ...legacyComponents])];
    return allComponents.sort();
  }

  // Check if a component is available
  isComponentAvailable(componentName: string): boolean {
    return this.componentRegistry.isRegistered(componentName) ||
           !!this.componentMap[componentName] ||
           this.componentRegistry.isRegistered(componentName.charAt(0).toUpperCase() + componentName.slice(1)) ||
           !!this.componentMap[componentName.charAt(0).toUpperCase() + componentName.slice(1)];
  }

  // Get component registration details
  getComponentInfo(componentName: string): any {
    const registration = this.componentRegistry.getRegistration(componentName);
    if (registration) {
      return {
        name: registration.name,
        source: registration.source,
        validated: registration.validated,
        registeredAt: registration.registeredAt,
        location: 'registry'
      };
    }

    if (this.componentMap[componentName]) {
      return {
        name: componentName,
        source: 'legacy',
        validated: false,
        registeredAt: null,
        location: 'legacy'
      };
    }

    return null;
  }

  // Enable/disable debug mode
  setDebugMode(enabled: boolean): void {
    this.debug = enabled;
    this.componentRegistry.setDebugMode(enabled);
    console.log(`[DynamicComponentLoader] Debug mode ${enabled ? 'enabled' : 'disabled'}`);
  }

  // Get registry statistics
  getRegistryStats(): any {
    return {
      registry: this.componentRegistry.getStats(),
      legacy: {
        total: Object.keys(this.componentMap).length,
        components: Object.keys(this.componentMap)
      }
    };
  }
}

import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-virtual-card',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="virtual-card-stub">
      <h2>Virtual Card</h2>
      <div class="card-placeholder">
        <ion-icon name="card-outline" size="large"></ion-icon>
        <p>Your virtual loyalty card will appear here</p>
        <div class="card-info" *ngIf="profile">
          <p><strong>Member:</strong> {{ profile?.givenNames }} {{ profile?.surname }}</p>
          <p><strong>Number:</strong> {{ profile?.membershipNumber || 'N/A' }}</p>
          <p><strong>Balance:</strong> {{ profile?.currentBalance || 0 }} points</p>
        </div>
      </div>
      <p><em>This is a stub component. Virtual card functionality needs to be implemented.</em></p>
    </div>
  `,
  styles: [`
    .virtual-card-stub {
      padding: 20px;
      text-align: center;
    }
    h2 {
      color: var(--ion-color-primary);
      margin-bottom: 20px;
    }
    .card-placeholder {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 30px;
      border-radius: 15px;
      margin: 20px 0;
      box-shadow: 0 8px 20px rgba(0,0,0,0.2);
    }
    .card-placeholder ion-icon {
      font-size: 4rem;
      margin-bottom: 15px;
    }
    .card-info {
      margin-top: 20px;
      text-align: left;
    }
    .card-info p {
      margin: 8px 0;
      font-size: 14px;
    }
  `]
})
export class VirtualCardComponent {
  @Input() profile: any;
  @Input() memberService: any;
}
import { Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'otp-validator',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="otp-validator-stub">
      <h2>OTP Validator</h2>
      <p>This is a stub component. OTP validation functionality needs to be implemented.</p>
      <ion-button expand="block" (click)="onSubmit()">Continue (Stub)</ion-button>
    </div>
  `,
  styles: [`
    .otp-validator-stub {
      padding: 20px;
      text-align: center;
    }
    h2 {
      color: var(--ion-color-primary);
    }
  `]
})
export class OtpValidatorComponent {
  @Input() length: number = 6;
  @Input() autoSubmit: boolean = false;
  
  @Output() otpComplete = new EventEmitter<string>();
  @Output() otpChange = new EventEmitter<string>();

  onSubmit() {
    // Emit a dummy OTP for testing
    this.otpComplete.emit('123456');
  }
}
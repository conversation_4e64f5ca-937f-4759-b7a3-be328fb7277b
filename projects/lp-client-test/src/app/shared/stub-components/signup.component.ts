import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';

@Component({
  selector: 'app-signup',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="signup-stub">
      <h2>Sign Up</h2>
      <p>This is a stub component. User signup functionality needs to be implemented.</p>
      <ion-button expand="block" (click)="onSignup()">Sign Up (Stub)</ion-button>
    </div>
  `,
  styles: [`
    .signup-stub {
      padding: 20px;
      text-align: center;
    }
    h2 {
      color: var(--ion-color-primary);
    }
  `]
})
export class SignupComponent {
  @Input() kc: any;
  @Input() memberService: any;
  @Input() router: any;

  onSignup() {
    console.log('SignupComponent stub: Sign up button clicked');
    // Redirect to login or home page for now
    if (this.router) {
      this.router.navigate(['/login']);
    }
  }
}
import { Injectable, Type } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
// Eager imports for app-local stubs (ensure registration before dynamic pages validate)
import { SignupComponent } from './stub-components/signup.component';
import { ValidateComponent } from './stub-components/validate.component';
import { OtpValidatorComponent } from './stub-components/otp-validator.component';
import { VirtualCardComponent } from './stub-components/virtual-card.component';
// import { TransactionsComponent } from '../secure/transactions/transactions.component';
// Eager import for a library component that was reported missing during validation
import { HeadingComponent } from 'mobile-components';

// Interface for component registration metadata
export interface ComponentRegistration {
  name: string;
  component: Type<any>;
  registeredAt: Date;
  source: 'static' | 'dynamic' | 'lazy';
  validated: boolean;
}

// Interface for component validation result
export interface ComponentValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

@Injectable({
  providedIn: 'root'
})
export class ComponentRegistryService {
  private components = new Map<string, ComponentRegistration>();
  private registrationSubject = new BehaviorSubject<ComponentRegistration[]>([]);
  private debug = true; // Enable debug logging for development

  constructor() {
    console.log('[ComponentRegistry] Service initialized');

    // Eagerly register app-local components to avoid race conditions
    try {
      this.registerComponent('SignupComponent', SignupComponent, 'static');
      this.registerComponent('ValidateComponent', ValidateComponent, 'static');
      this.registerComponent('OtpValidatorComponent', OtpValidatorComponent, 'static');
      this.registerComponent('VirtualCardComponent', VirtualCardComponent, 'static');
      // this.registerComponent('TransactionsComponent', TransactionsComponent, 'static');
      // Also make sure HeadingComponent is ready immediately
      this.registerComponent('HeadingComponent', HeadingComponent, 'static');
      console.log('[ComponentRegistry] Eager local/library registrations complete');
    } catch (e) {
      console.warn('[ComponentRegistry] Eager registration error (non-fatal):', e);
    }

    // Continue with auto-registration for the rest of library components
    this.autoRegisterComponents().then(() => {
      console.log('[ComponentRegistry] Total components registered:', this.components.size);
      console.log('[ComponentRegistry] Component names:', Array.from(this.components.keys()));
    });
  }

  // Observable for component registration changes
  get registrations$(): Observable<ComponentRegistration[]> {
    return this.registrationSubject.asObservable();
  }

  // Get all registered components
  get registeredComponents(): ComponentRegistration[] {
    return Array.from(this.components.values());
  }

  // Auto-register all page-section components from mobile-components library
  private async autoRegisterComponents(): Promise<void> {
    try {
      if (this.debug) console.log('[ComponentRegistry] Auto-registering components from mobile-components');

      // Import mobile-components library
      const components = await import('mobile-components');

      // Essential components needed for dynamic pages
      const componentNames = [
        // Confirmed direct exports 
        'DashboardQuickActionsComponent',
        'ContactusComponent',
        
        // Main page components from environment configurations
        'PagesLandingTheme1Component',
        'PagesLoginTheme1Component',
        
        // Base components that are likely to exist
        'ButtonComponent', 
        'InputComponent',
        'ListComponent',
        'CheckboxComponent',
        'HeadLogoComponent',
        'ParagraphComponent',
        'StoresPageComponent',
        'TransactionsPageComponent',
        'VirtualCardPageComponent',
        'StatementsPageComponent',
        'SettingsPageComponent',
        'ContactUsPageComponent',
        'ProgramSelectionPageComponent',
        'ProgramManagementPageComponent',
        'ProductsPageComponent',
        'NotificationSettingsPageComponent',
        // Note: LibHeadLogo is created as an alias, not imported directly
        
        // Page section components that are essential
        'ProfileHeaderComponent',
        'ProfileFormComponent',
        'ProfileSettingsComponent',
        'ProfileHelpComponent',
        'DashboardHeaderComponent',
        'DashboardWelcomeComponent',
        'DashboardSummaryComponent',
        
        // Note: App-local/stub components are registered separately below in registerLocalComponents()
        // Do NOT attempt to pull them from mobile-components to avoid noisy warnings.
        // Examples: TransactionsComponent, VirtualCardComponent, SignupComponent, ValidateComponent, OtpValidatorComponent
      ];

      let registeredCount = 0;
      let failedCount = 0;
      
      console.log('[ComponentRegistry] Available exports from mobile-components:', Object.keys(components));

      for (const componentName of componentNames) {
        // Skip AbstractComponent to avoid ID collisions
        if (componentName === 'AbstractComponent') {
          console.log(`[ComponentRegistry] ⚠ Skipping ${componentName} to avoid ID collision`);
          continue;
        }
        
        if ((components as any)[componentName]) {
          const registration = this.registerComponent(
            componentName,
            (components as any)[componentName],
            'static'
          );
          if (registration.validated) {
            registeredCount++;
            console.log(`[ComponentRegistry] ✓ Successfully registered: ${componentName}`);
          } else {
            failedCount++;
            console.warn(`[ComponentRegistry] ✗ Registration failed for: ${componentName}`);
          }
        } else {
          failedCount++;
          if (this.debug) console.warn(`[ComponentRegistry] ✗ Component ${componentName} not found in mobile-components`);
        }
      }

      console.log(`[ComponentRegistry] Auto-registration complete: ${registeredCount} registered, ${failedCount} failed`);
      
      // Register component aliases for backward compatibility
      this.registerComponentAliases(components);
      
      // Register local app components (skip if already registered from eager phase)
      await this.registerLocalComponents();
      
      this.notifyRegistrationChange();

    } catch (error) {
      console.error('[ComponentRegistry] Error during auto-registration:', error);
    }
  }
  
  // Register component aliases for backward compatibility
  private registerComponentAliases(components: any): void {
    try {
      console.log('[ComponentRegistry] Registering component aliases...');
      
      // Common component aliases mapping
      const aliasMap: { [alias: string]: string } = {
        'LibHeadLogo': 'HeadLogoComponent',
        'lib-head-logo': 'HeadLogoComponent',
        'LibInfoCard': 'InfoCardComponent',
        'lib-info-card': 'InfoCardComponent',
        'LibPageWrapper': 'PageWrapperComponent',
        'lib-page-wrapper': 'PageWrapperComponent',
        'LibButton': 'ButtonComponent',
        'lib-button': 'ButtonComponent',
        'LibInput': 'InputComponent',
        'lib-input': 'InputComponent',
        'LibList': 'ListComponent',
        'lib-list': 'ListComponent',
        'LibCheckbox': 'CheckboxComponent',
        'lib-checkbox': 'CheckboxComponent',
        'MessageComponent': 'ParagraphComponent', // Fallback for message display
        'LibMessage': 'ParagraphComponent',
        'lib-message': 'ParagraphComponent'
      };
      
      let aliasRegistered = 0;
      
      for (const [alias, targetComponent] of Object.entries(aliasMap)) {
        if (components[targetComponent]) {
          // Register the alias pointing to the same component
          const registration = this.registerComponent(
            alias,
            components[targetComponent],
            'static'
          );
          if (registration.validated) {
            aliasRegistered++;
            console.log(`[ComponentRegistry] ✓ Registered alias: ${alias} → ${targetComponent}`);
          }
        } else if (this.isRegistered(targetComponent)) {
          // Target component is already registered, create alias
          const targetRegistration = this.getRegistration(targetComponent);
          if (targetRegistration) {
            const registration = this.registerComponent(
              alias,
              targetRegistration.component,
              'static'
            );
            if (registration.validated) {
              aliasRegistered++;
              console.log(`[ComponentRegistry] ✓ Registered alias: ${alias} → ${targetComponent} (from existing)`);
            }
          }
        } else {
          if (this.debug) console.warn(`[ComponentRegistry] ⚠ Cannot create alias ${alias} - target ${targetComponent} not found`);
        }
      }
      
      console.log(`[ComponentRegistry] Component aliases registered: ${aliasRegistered}`);
    } catch (error) {
      console.error('[ComponentRegistry] Error registering component aliases:', error);
    }
  }

  // Register local app components that aren't available in mobile-components
  private async registerLocalComponents(): Promise<void> {
    try {
      console.log('[ComponentRegistry] Registering local app components and stubs');
      
      const localComponents = [
        {
          name: 'OtpValidatorComponent', 
          path: './stub-components/otp-validator.component',
          required: true
        },
        {
          name: 'SignupComponent',
          path: './stub-components/signup.component', 
          required: true
        },
        {
          name: 'ValidateComponent',
          path: './stub-components/validate.component',
          required: true
        },
        {
          name: 'VirtualCardComponent',
          path: './stub-components/virtual-card.component',
          required: true
        },
        {
          name: 'TransactionsComponent',
          path: '../secure/transactions/transactions.component',
          required: false // Make this optional since it might not exist yet
        }
      ];
      
      let localRegisteredCount = 0;
      let localFailedCount = 0;
      
      for (const comp of localComponents) {
        try {
          // If already registered from eager phase, skip dynamic import
          if (this.isRegistered(comp.name)) {
            console.log(`[ComponentRegistry] ↺ Skipping dynamic import for already-registered ${comp.name}`);
            continue;
          }

          const module = await import(comp.path);
          const ComponentClass = module[comp.name];
          
          if (ComponentClass) {
            const registration = this.registerComponent(comp.name, ComponentClass, 'static');
            if (registration.validated) {
              localRegisteredCount++;
              console.log(`[ComponentRegistry] ✓ Registered local component: ${comp.name}`);
            } else {
              localFailedCount++;
              console.warn(`[ComponentRegistry] ✗ Local component ${comp.name} failed validation`);
            }
          } else {
            localFailedCount++;
            console.error(`[ComponentRegistry] ✗ Component ${comp.name} not found in module:`, Object.keys(module));
          }
        } catch (error) {
          localFailedCount++;
          if (comp.required) {
            console.error(`[ComponentRegistry] ✗ REQUIRED component ${comp.name} failed to load:`, error);
          } else {
            console.warn(`[ComponentRegistry] ⚠ Optional component ${comp.name} failed to load:`, error);
          }
        }
      }
      
      console.log(`[ComponentRegistry] Local registration complete: ${localRegisteredCount} registered, ${localFailedCount} failed`);
    } catch (error) {
      console.error('[ComponentRegistry] Error in registerLocalComponents:', error);
    }
  }

  // Register a component with type-safe validation
  registerComponent(
    name: string,
    component: Type<any>,
    source: 'static' | 'dynamic' | 'lazy' = 'dynamic'
  ): ComponentRegistration {
    if (!name || !component) {
      throw new Error('[ComponentRegistry] Component name and type are required');
    }

    // Validate the component
    const validation = this.validateComponent(component);

    const registration: ComponentRegistration = {
      name,
      component,
      registeredAt: new Date(),
      source,
      validated: validation.isValid
    };

    // Log validation results
    if (!validation.isValid) {
      console.error(`[ComponentRegistry] Component ${name} failed validation:`, validation.errors);
    }
    if (validation.warnings.length > 0) {
      console.warn(`[ComponentRegistry] Component ${name} validation warnings:`, validation.warnings);
    }

    // Register the component
    this.components.set(name, registration);

    if (this.debug) {
      console.log(`[ComponentRegistry] Registered component: ${name} (${source}, valid: ${validation.isValid})`);
    }

    this.notifyRegistrationChange();
    return registration;
  }

  // Unregister a component
  unregisterComponent(name: string): boolean {
    const existed = this.components.has(name);
    this.components.delete(name);

    if (existed) {
      if (this.debug) console.log(`[ComponentRegistry] Unregistered component: ${name}`);
      this.notifyRegistrationChange();
    }

    return existed;
  }

  // Get a registered component by name
  getComponent(name: string): Type<any> | null {
    const registration = this.components.get(name);
    return registration ? registration.component : null;
  }

  // Check if a component is registered
  isRegistered(name: string): boolean {
    return this.components.has(name);
  }

  // Get component registration details
  getRegistration(name: string): ComponentRegistration | null {
    return this.components.get(name) || null;
  }

  // Validate a component type
  private validateComponent(component: Type<any>): ComponentValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if it's a valid Angular component
      if (!component) {
        errors.push('Component is null or undefined');
        return { isValid: false, errors, warnings };
      }

      // Check if it has Angular component metadata
      const metadata = (component as any).__annotations__ || (component as any).ɵcmp;
      if (!metadata) {
        warnings.push('Component may not have Angular metadata (could be a standalone component)');
      }

      // Check if component has a constructor
      if (typeof component !== 'function') {
        errors.push('Component is not a constructor function');
      }

      // Additional validation can be added here
      // For example, checking for required inputs, outputs, etc.

    } catch (error) {
      errors.push(`Validation error: ${error}`);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  // Register components from dynamic import
  async registerFromImport(importPromise: Promise<any>, source: 'dynamic' | 'lazy' = 'dynamic'): Promise<ComponentRegistration[]> {
    try {
      const components = await importPromise;
      const registrations: ComponentRegistration[] = [];

      for (const [name, component] of Object.entries(components)) {
        if (this.isComponentType(component)) {
          const registration = this.registerComponent(name, component as Type<any>, source);
          registrations.push(registration);
        }
      }

      return registrations;
    } catch (error) {
      console.error('[ComponentRegistry] Error registering from import:', error);
      return [];
    }
  }

  // Check if an object is a component type
  private isComponentType(obj: any): boolean {
    return typeof obj === 'function' &&
           (obj.prototype || obj.__annotations__ || obj.ɵcmp);
  }

  // Get registration statistics
  getStats(): { total: number; validated: number; bySource: Record<string, number> } {
    const registrations = this.registeredComponents;
    const bySource: Record<string, number> = {};

    registrations.forEach(reg => {
      bySource[reg.source] = (bySource[reg.source] || 0) + 1;
    });

    return {
      total: registrations.length,
      validated: registrations.filter(r => r.validated).length,
      bySource
    };
  }

  // Clear all registrations (useful for testing)
  clearAll(): void {
    this.components.clear();
    this.notifyRegistrationChange();
    if (this.debug) console.log('[ComponentRegistry] All components cleared');
  }

  // Get list of component names
  getComponentNames(): string[] {
    return Array.from(this.components.keys());
  }

  // Search for components by name pattern
  searchComponents(pattern: string): ComponentRegistration[] {
    const regex = new RegExp(pattern, 'i');
    return this.registeredComponents.filter(reg => regex.test(reg.name));
  }

  // Enable/disable debug logging
  setDebugMode(enabled: boolean): void {
    this.debug = enabled;
    console.log(`[ComponentRegistry] Debug mode ${enabled ? 'enabled' : 'disabled'}`);
  }

  // Notify subscribers of registration changes
  private notifyRegistrationChange(): void {
    this.registrationSubject.next(this.registeredComponents);
  }

  // Development helper: log all registered components
  logRegisteredComponents(): void {
    console.group('[ComponentRegistry] Registered Components');
    this.registeredComponents.forEach(reg => {
      console.log(`${reg.name}: ${reg.source} (${reg.validated ? 'valid' : 'invalid'}) - ${reg.registeredAt.toISOString()}`);
    });
    console.groupEnd();
  }
}

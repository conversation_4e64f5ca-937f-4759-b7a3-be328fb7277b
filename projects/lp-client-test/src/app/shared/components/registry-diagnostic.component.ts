import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IonicModule } from '@ionic/angular';
import { ComponentRegistryService, ComponentRegistration } from '../component-registry.service';

@Component({
  selector: 'app-registry-diagnostic',
  standalone: true,
  imports: [CommonModule, IonicModule],
  template: `
    <div class="registry-diagnostic">
      <h3>Component Registry Diagnostic</h3>
      
      <div class="stats">
        <p><strong>Total Components:</strong> {{ stats.total }}</p>
        <p><strong>Validated Components:</strong> {{ stats.validated }}</p>
        <p><strong>Failed Components:</strong> {{ stats.total - stats.validated }}</p>
      </div>
      
      <div class="component-lists">
        <div class="successful">
          <h4>✅ Successfully Registered Components ({{ successfulComponents.length }})</h4>
          <ul>
            <li *ngFor="let comp of successfulComponents">
              {{ comp.name }} ({{ comp.source }})
            </li>
          </ul>
        </div>
        
        <div class="failed" *ngIf="failedComponents.length > 0">
          <h4>❌ Failed Components ({{ failedComponents.length }})</h4>
          <ul>
            <li *ngFor="let comp of failedComponents">
              {{ comp.name }} - {{ comp.source }}
            </li>
          </ul>
        </div>
      </div>
      
      <div class="test-component-lookup">
        <h4>Test Component Lookup</h4>
        <div *ngFor="let testName of testComponentNames">
          <span [class]="'result ' + (isComponentRegistered(testName) ? 'found' : 'not-found')">
            {{ testName }}: {{ isComponentRegistered(testName) ? 'FOUND' : 'NOT FOUND' }}
          </span>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .registry-diagnostic {
      padding: 16px;
      font-family: monospace;
    }
    
    .stats {
      background: #f5f5f5;
      padding: 12px;
      margin-bottom: 16px;
      border-radius: 4px;
    }
    
    .successful h4 {
      color: green;
    }
    
    .failed h4 {
      color: red;
    }
    
    ul {
      list-style-type: none;
      padding: 0;
    }
    
    li {
      padding: 4px;
      margin: 2px 0;
      background: #fafafa;
      border-left: 3px solid #ddd;
      padding-left: 8px;
    }
    
    .successful li {
      border-left-color: green;
    }
    
    .failed li {
      border-left-color: red;
    }
    
    .test-component-lookup {
      margin-top: 20px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 4px;
    }
    
    .result {
      display: block;
      padding: 4px;
      margin: 2px 0;
      font-weight: bold;
    }
    
    .found {
      color: green;
    }
    
    .not-found {
      color: red;
    }
  `]
})
export class RegistryDiagnosticComponent implements OnInit {
  
  stats = { total: 0, validated: 0, bySource: {} };
  successfulComponents: ComponentRegistration[] = [];
  failedComponents: ComponentRegistration[] = [];
  
  testComponentNames = [
    'PagesLoginTheme1Component',
    'PagesLandingTheme1Component', 
    'HeadLogoComponent',
    'SignupComponent',
    'ValidateComponent',
    'OtpValidatorComponent',
    'InputComponent',
    'ButtonComponent',
    'CheckboxComponent',
    'ListComponent',
    'ProfileHeaderComponent',
    'ProfileFormComponent',
    'ProfileSettingsComponent',
    'ProfileHelpComponent',
    'DashboardHeaderComponent',
    'DashboardWelcomeComponent',
    'DashboardQuickActionsComponent',
    'DashboardSummaryComponent',
    'TransactionsComponent',
    'VirtualCardComponent'
  ];

  constructor(private componentRegistry: ComponentRegistryService) {}

  ngOnInit() {
    this.stats = this.componentRegistry.getStats();
    const allComponents = this.componentRegistry.registeredComponents;
    
    this.successfulComponents = allComponents.filter(comp => comp.validated);
    this.failedComponents = allComponents.filter(comp => !comp.validated);
    
    console.log('Registry Diagnostic - All Components:', allComponents);
    console.log('Registry Diagnostic - Stats:', this.stats);
  }
  
  isComponentRegistered(name: string): boolean {
    return this.componentRegistry.isRegistered(name);
  }
}
import { Compo<PERSON>, Injector, OnInit, OnDestroy, ViewChild, ViewContainerRef, ChangeDetectorRef, Directive, ComponentRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ComponentsModule } from 'mobile-components';
import {
  KeyCloakService,
  MemberProfile,
  MemberService,
  LssConfig,
  MemberCommObject,
  PartnerService,
  SystemService,
  ProgramService,
  OnboardingService,
  ProgramMemberService,
  FirebaseMemberService
} from 'lp-client-api';
import { ConfigService } from '../services/config.service';
import { DynamicComponentLoaderService } from './dynamic-component-loader.service';
import { PageConfigurationValidationService } from './page-configuration-validation.service';
import { BuilderBridgeReceiverService, ComponentSelection } from '../services/builder-bridge-receiver.service';
import { ComponentPropertyDiscoveryService } from './component-property-discovery.service';
import { Subscription } from 'rxjs';
import { PageConfiguration, ValidationOptions } from '../../environments/page-configuration.types';
import { PushNotificationService } from '../services/push-notification.service';

// Define interface for component configuration
export interface ComponentConfig {
  id?: string; // Unique identifier for the component instance
  type: string;
  showWhen?: 'authenticated' | 'anonymous' | 'always';
  inputs?: { [key: string]: any };
  outputs?: { [key: string]: string }; // Output event name to handler name mapping
  config?: { [key: string]: any }; // Support legacy config property
}

@Directive()
export abstract class BaseDynamicPageComponent implements OnInit, OnDestroy {
  @ViewChild('container', { read: ViewContainerRef, static: true }) container!: ViewContainerRef;
  pageClass: string = '';

  // Initialize profile with default empty values to prevent null reference errors
  profile: any = {
    givenNames: '',
    surname: '',
    email: '',
    mobileNumber: '',
    newMembershipNumber: '',
    currentBalance: 0,
    membershipStatus: '',
    avatarUrl: ''
  };

  protected isInitialLoad = true;
  protected debug = false; // Can be overridden by subclasses
  public isAuthenticated = false;
  private authSubscription: Subscription | null = null;
  private profileSubscription: Subscription | null = null;
  private propertyUpdateSubscription: Subscription | null = null;
  
  // Map to store component references by their unique IDs
  protected componentRefs = new Map<string, ComponentRef<any>>();
  protected componentCounter = 0; // For generating unique IDs if not provided

  constructor(
    protected route: ActivatedRoute,
    protected router: Router,
    public loader: DynamicComponentLoaderService,
    protected injector: Injector,
    protected configService: ConfigService,
    protected cdr: ChangeDetectorRef,
    protected memberService: MemberService,
    public kc: KeyCloakService,
    protected pageValidator: PageConfigurationValidationService,
    protected bridgeReceiver: BuilderBridgeReceiverService,
    protected propertyDiscovery: ComponentPropertyDiscoveryService
  ) {
    // Initial setting - but will be updated via subscription
    this.isAuthenticated = this.kc.authSuccess;
   // console.log(`[${this.getComponentName()}] CONSTRUCTOR - Initial isAuthenticated: ${this.isAuthenticated}`);
  }

  ngOnInit(): void {
    // Subscribe to authentication status changes
    this.authSubscription = this.kc.authStatus.subscribe(status => {
      const newAuthState = !!status.eventData;
     // console.log(`[${this.getComponentName()}] Auth status event: ${status.eventName}, value: ${newAuthState}`);
     /* console.log(`[${this.getComponentName()}] Current auth state details:`, {
        currentIsAuthenticated: this.isAuthenticated,
        newAuthState,
        kcAuthSuccess: this.kc.authSuccess,
        statusEventData: status.eventData,
        statusEventName: status.eventName,
        userProfile: this.kc.userProfile ? 'Present' : 'Missing'
      });
*/
      if (this.isAuthenticated !== newAuthState) {
        console.log(`[${this.getComponentName()}] Auth status changed from ${this.isAuthenticated} to ${newAuthState}`);
        this.isAuthenticated = newAuthState;

        // If logged in, load profile data and handle post-login navigation
        if (newAuthState) {
        //  console.log(`[${this.getComponentName()}] User authenticated, loading profile data...`);
          this.loadUnifiedProfile();
        } else {
          // Clear profile on logout
          this.profile = {
            givenNames: '',
            surname: '',
            email: '',
            mobileNumber: '',
            newMembershipNumber: '',
            currentBalance: 0,
            membershipStatus: '',
            avatarUrl: ''
          };
          console.log(`[${this.getComponentName()}] User logged out, profile cleared`);

          // Handle logout navigation - can be overridden by subclasses
          this.handleLogoutNavigation();
        }

        // Reload components when auth status changes
        console.log(`[${this.getComponentName()}] Reloading components after auth state change`);
        this.loadPageComponents();
      } else {
        console.log(`[${this.getComponentName()}] Auth status event received but no change needed (${this.isAuthenticated} -> ${newAuthState})`);
      }
    });

    // Subscribe to profile updates
    this.profileSubscription = this.memberService.profileSubject.subscribe({
      next: (profile) => {
       // console.log(`[${this.getComponentName()}] Received profile update:`, profile);
        if (profile) {
          // Merge the profile data with our pre-initialized profile to ensure all fields exist
          this.profile = { ...this.profile, ...profile };
        //  console.log(`[${this.getComponentName()}] Profile updated:`, this.profile);
          // Re-evaluate components now that the profile has changed
          this.loadPageComponents();
        }
      },
      error: (err) => console.error(`[${this.getComponentName()}] Error receiving profile update:`, err)
    });

    // Debug log initial state
    console.log(`[${this.getComponentName()}] INIT - Auth status: ${this.isAuthenticated}, kc.authSuccess: ${this.kc.authSuccess}`);

    // Subscribe to property updates from the builder bridge
    this.propertyUpdateSubscription = this.bridgeReceiver.propertyUpdate$.subscribe(update => {
      this.updateComponentProperties(update.componentId, update.properties);
    });
    
    // Subscribe to component selection from the builder
    this.bridgeReceiver.componentSelection$.subscribe(selection => {
     // console.log(`[${this.getComponentName()}] Component selection received:`, selection);
      this.handleComponentSelection(selection);
    });

    // Register all known components
    this.registerAllComponents();

    // Handle authentication check and initial load
    this.handleInitialLoad();
  }

  // Abstract method to get component name for logging
  protected abstract getComponentName(): string;

  // Abstract method to get default page when no pageId is provided
  protected abstract getDefaultPage(): string;

  // Method to handle logout navigation - can be overridden by subclasses
  protected handleLogoutNavigation(): void {
    // Default implementation - can be overridden
    console.log(`[${this.getComponentName()}] Default logout navigation`);
  }

  // Method to handle post-login navigation - can be overridden by subclasses
  protected handlePostLoginNavigation(): void {
    // Default implementation - can be overridden by subclasses
    console.log(`[${this.getComponentName()}] Default post-login navigation`);
  }

  // Method to handle initial load - can be overridden by subclasses
  protected handleInitialLoad(): void {
    // Check authentication and handle initial load
    this.isInitialLoad = false;
    if (this.kc.authSuccess) {
      if (this.debug) console.log(`[${this.getComponentName()}] User already authenticated, retrieving profile...`);
      this.loadUnifiedProfile();
    } else {
      // Load components on first load
      this.loadPageComponents();
    }
  }

  // Register all known components with the dynamic loader service
  protected registerAllComponents(): void {
    try {
      if (this.debug) console.log(`[${this.getComponentName()}] Waiting for ComponentRegistry auto-registration to complete`);

      // Check if ComponentRegistry has already auto-registered components
      this.waitForAutoRegistration().then(() => {
        if (this.debug) {
          console.log(`[${this.getComponentName()}] ComponentRegistry auto-registration complete`);
          this.loader.logRegisteredComponents();
        }

        // After auto-registration is complete, load the page components
        this.loadPageComponents();
      }).catch(error => {
        console.error(`[${this.getComponentName()}] Error waiting for auto-registration:`, error);
        // Fallback: try to load page components anyway
        this.loadPageComponents();
      });
    } catch (error) {
      console.error(`[${this.getComponentName()}] Error in registerAllComponents:`, error);
      // Fallback: try to load page components anyway
      this.loadPageComponents();
    }
  }

  // Wait for ComponentRegistry auto-registration to complete
  private async waitForAutoRegistration(): Promise<void> {
    return new Promise((resolve, reject) => {
      // Check if components are already registered
      const registeredComponents = this.loader.componentRegistryService.getComponentNames();
      if (registeredComponents.length > 0) {
        if (this.debug) console.log(`[${this.getComponentName()}] Auto-registration already complete: ${registeredComponents.length} components`);
        resolve();
        return;
      }

      // Wait for registration changes with timeout
      const timeout = setTimeout(() => {
        console.warn(`[${this.getComponentName()}] Auto-registration timeout - proceeding anyway`);
        resolve();
      }, 5000); // 5 second timeout

      // Subscribe to registration changes
      const subscription = this.loader.componentRegistryService.registrations$.subscribe(registrations => {
        if (registrations.length > 0) {
          if (this.debug) console.log(`[${this.getComponentName()}] Auto-registration detected: ${registrations.length} components`);
          clearTimeout(timeout);
          subscription.unsubscribe();
          resolve();
        }
      });
    });
  }

  protected loadPageComponents(): void {
    console.log(`[${this.getComponentName()}] loadPageComponents called. Auth Status:`, this.isAuthenticated);
    this.route.paramMap.subscribe(params => {
      let pageId = params.get('page');
      const subpage = params.get('subpage');
      const subsubpage = params.get('subsubpage');

      // Handle nested paths like 'games/home', 'app/stores', etc.
      if (pageId && subpage && subsubpage) {
        pageId = `${pageId}/${subpage}/${subsubpage}`;
        if (this.debug) console.log(`[${this.getComponentName()}] Constructed triple nested pageId: ${pageId}`);
      } else if (pageId && subpage) {
        pageId = `${pageId}/${subpage}`;
        if (this.debug) console.log(`[${this.getComponentName()}] Constructed nested pageId: ${pageId}`);
      }

      if (this.debug) console.log(`[${this.getComponentName()}] Loading components for pageId: ${pageId}`);

      // Fallback to default page if no pageId is provided in the route
      if (!pageId) {
        pageId = this.getDefaultPage();
        if (this.debug) console.log(`[${this.getComponentName()}] No pageId in route, defaulting to '${pageId}'`);
      }

      // Clear existing components and component refs
      this.container.clear();
      this.componentRefs.forEach(ref => ref.destroy());
      this.componentRefs.clear();
      this.componentCounter = 0;

      const pagesConfig = this.configService.sysConfig.pages;
      if (!Array.isArray(pagesConfig)) {
        console.error(`[${this.getComponentName()}] Pages configuration is not an array:`, pagesConfig);
        return; // Exit if pages config is not the expected array
      }

      // Validate pages configuration if in debug mode
      if (this.debug) {
        const validationOptions: ValidationOptions = {
          validateComponents: true,
          validateNavigation: false,
          showWarnings: true
        };

        const validationResult = this.pageValidator.validatePages(pagesConfig, validationOptions);

        if (!validationResult.isValid) {
          console.error(`[${this.getComponentName()}] Page configuration validation failed:`, validationResult.errors);
          validationResult.errors.forEach(error => console.error(`[${this.getComponentName()}] Validation Error: ${error}`));
        }

        if (validationResult.warnings.length > 0) {
          console.warn(`[${this.getComponentName()}] Page configuration warnings:`, validationResult.warnings);
          validationResult.warnings.forEach(warning => console.warn(`[${this.getComponentName()}] Validation Warning: ${warning}`));
        }

        console.log(`[${this.getComponentName()}] ${this.pageValidator.getValidationStats(validationResult)}`);
      }

      // Find the page configuration in the array by path
      const pageConfig = pagesConfig.find(page => page.path === pageId);

      if (pageConfig) {
        if (this.debug) console.log(`[${this.getComponentName()}] Page config found:`, pageConfig);
        this.pageClass = pageConfig.class || ''; // Set page class

        // Add debug log before the check
        if (this.debug) console.log(`[${this.getComponentName()}] Security Check: pageConfig.secure=${pageConfig.secure}, this.isAuthenticated=${this.isAuthenticated}`);

        // Check security requirements - can be overridden by subclasses
        if (!this.checkPageSecurity(pageConfig)) {
          return; // Stop loading components if security check fails
        }

        // Load components specified in the config
        pageConfig.components.forEach((componentConfig: ComponentConfig) => {
          if (this.debug) console.log(`[${this.getComponentName()}] Processing component config:`, componentConfig);

          let shouldLoad = false;
          const showWhen = componentConfig.showWhen;

          // Add log for evaluating showWhen
          console.log(`[${this.getComponentName()}] Evaluating component ${componentConfig.type}:`, {
            showWhen,
            isAuthenticated: this.isAuthenticated,
            kcAuthSuccess: this.kc.authSuccess,
            hasProfile: !!this.profile,
            profileKeys: this.profile ? Object.keys(this.profile) : [],
            componentId: componentConfig.id || 'no-id'
          });

          if (showWhen === 'authenticated' && this.isAuthenticated) {
            shouldLoad = true;
            if (this.debug) console.log(`[${this.getComponentName()}] ✅ LOADING: showWhen='authenticated', user is authenticated.`);
          } else if (showWhen === 'anonymous' && !this.isAuthenticated) {
            shouldLoad = true;
            if (this.debug) console.log(`[${this.getComponentName()}] ✅ LOADING: showWhen='anonymous', user is anonymous.`);
          } else if (!showWhen || showWhen === 'always') { // If showWhen is not defined or 'always', always load
            shouldLoad = true;
            if (this.debug) console.log(`[${this.getComponentName()}] ✅ LOADING: showWhen is '${showWhen}' or not defined.`);
          } else {
             if (this.debug) console.log(`[${this.getComponentName()}] ❌ SKIPPING: Condition NOT met for component ${componentConfig.type}: showWhen='${showWhen}', isAuthenticated=${this.isAuthenticated}.`);
          }

          if (shouldLoad) {
            if (this.debug) console.log(`[${this.getComponentName()}] Loading component: ${componentConfig.type}`);
            this.loadComponent(componentConfig, this.container);
          } else {
            if (this.debug) console.log(`[${this.getComponentName()}] Skipping component load based on showWhen condition: ${componentConfig.type}`);
          }
        });
      } else {
        console.warn(`[${this.getComponentName()}] No configuration found for pageId: ${pageId}. Available pages:`, pagesConfig.map(p => p.path));
        this.handlePageNotFound(pageId);
      }
      this.cdr.detectChanges(); // Ensure UI updates after loading components
    });
  }

  // Method to check page security - can be overridden by subclasses
  protected checkPageSecurity(pageConfig: any): boolean {
    // Default implementation - can be overridden
    return true;
  }

  // Method to handle page not found - can be overridden by subclasses
  protected handlePageNotFound(pageId: string): void {
    // Default implementation - navigate to the default page for this component type
    const defaultPage = this.getDefaultPage();
    console.warn(`[${this.getComponentName()}] Page not found: ${pageId}. Redirecting to default page: ${defaultPage}`);
    
    // Determine the route prefix based on the component type
    const routePrefix = this.getComponentName().toLowerCase().includes('public') ? '/public' : '/secure';
    this.router.navigate([`${routePrefix}/${defaultPage}`]);
  }

  // Helper method to check if a profile is complete enough for component display
  protected hasValidProfile(): boolean {
    const hasBasicData = !!this.profile && (
      !!this.profile.givenNames || 
      !!this.profile.surname ||
      !!this.profile.newMembershipNumber ||
      !!this.profile.membershipNumber
    );
    
    if (this.debug) {
      console.log(`[${this.getComponentName()}] hasValidProfile check:`, hasBasicData, 'Profile data:', {
        givenNames: this.profile?.givenNames,
        surname: this.profile?.surname,
        newMembershipNumber: this.profile?.newMembershipNumber,
        membershipNumber: this.profile?.membershipNumber
      });
    }
    
    return hasBasicData;
  }
  
  // Evaluate dynamic expressions in component inputs
  protected evaluateExpression(expression: string): any {
    try {
      // Remove $ prefix if present
      let cleanExpression = expression.startsWith('$') ? expression.substring(1) : expression;
      
      if (this.debug) {
        console.log(`[${this.getComponentName()}] Evaluating expression: '${expression}' (clean: '${cleanExpression}')`);
        console.log(`[${this.getComponentName()}] Profile data available:`, {
          givenNames: this.profile?.givenNames,
          surname: this.profile?.surname,
          hasProfile: !!this.profile
        });
      }
      
      // Create evaluation context with available data
      const context = {
        profile: this.profile,
        lssConfig: this.configService.sysConfig,
        kc: this.kc,
        memberService: this.memberService,
        router: this.router
      };
      
      // Handle simple concatenation expressions FIRST (before individual property access)
      if (cleanExpression.includes('+')) {
        const result = this.evaluateConcatenation(cleanExpression, context);
        if (this.debug) console.log(`[${this.getComponentName()}] Expression evaluation final result:`, result);
        return result;
      }
      
      // Simple expression evaluation for common patterns
      // Handle profile property access like profile.givenNames
      if (cleanExpression.startsWith('profile.')) {
        const property = cleanExpression.split('profile.')[1];
        const result = this.getNestedProperty(this.profile, property);
        if (this.debug) console.log(`[${this.getComponentName()}] Direct profile property '${property}' result:`, result);
        return result;
      }
      
      // Handle lssConfig property access
      if (cleanExpression.startsWith('lssConfig.')) {
        const property = cleanExpression.split('lssConfig.')[1];
        return this.getNestedProperty(this.configService.sysConfig, property);
      }
      
      // Handle optional chaining like profile?.givenNames
      if (cleanExpression.includes('?.')) {
        return this.evaluateOptionalChaining(cleanExpression, context);
      }
      
      // Fallback: try direct property access
      if (context.hasOwnProperty(cleanExpression)) {
        return (context as any)[cleanExpression];
      }
      
      if (this.debug) console.warn(`[${this.getComponentName()}] Could not evaluate expression: ${expression}`);
      return expression; // Return original if can't evaluate
    } catch (error) {
      console.error(`[${this.getComponentName()}] Error evaluating expression '${expression}':`, error);
      return expression; // Return original on error
    }
  }
  
  // Get nested property from object using dot notation
  private getNestedProperty(obj: any, path: string): any {
    try {
      return path.split('.').reduce((current, prop) => {
        return current && current[prop] !== undefined ? current[prop] : undefined;
      }, obj);
    } catch {
      return undefined;
    }
  }
  
  // Evaluate concatenation expressions like "profile.givenNames + ' ' + profile.surname"
  private evaluateConcatenation(expression: string, context: any): string {
    try {
      if (this.debug) console.log(`[${this.getComponentName()}] Evaluating concatenation: '${expression}' with context:`, context);
      
      // Split by + and evaluate each part
      const parts = expression.split('+').map(part => part.trim());
      const evaluatedParts = parts.map(part => {
        // Remove quotes from string literals
        if (part.startsWith('\'') && part.endsWith('\'')) {
          return part.slice(1, -1);
        }
        if (part.startsWith('"') && part.endsWith('"')) {
          return part.slice(1, -1);
        }
        
        // Handle $ prefix in individual parts
        let cleanPart = part.startsWith('$') ? part.substring(1) : part;
        
        // Evaluate property access
        if (cleanPart.includes('.')) {
          const [objectName, ...propertyPath] = cleanPart.split('.');
          if (context[objectName]) {
            const result = this.getNestedProperty(context[objectName], propertyPath.join('.'));
            if (this.debug) console.log(`[${this.getComponentName()}] Property ${cleanPart} evaluated to:`, result);
            return result || ''; // Return empty string instead of undefined
          } else {
            if (this.debug) console.warn(`[${this.getComponentName()}] Context object '${objectName}' not found in context:`, Object.keys(context));
            return ''; // Return empty string if context object not found
          }
        }
        
        // Direct context access
        if (context[cleanPart]) {
          return context[cleanPart];
        }
        
        return part; // Return as-is if can't evaluate
      });
      
      const result = evaluatedParts.filter(part => part !== undefined && part !== null).join('');
      if (this.debug) console.log(`[${this.getComponentName()}] Concatenation result: '${result}'`);
      return result || ''; // Return empty string if result is falsy
    } catch (error) {
      console.error(`[${this.getComponentName()}] Error evaluating concatenation:`, error);
      return '';
    }
  }
  
  // Evaluate optional chaining expressions like "profile?.givenNames"
  private evaluateOptionalChaining(expression: string, context: any): any {
    try {
      const parts = expression.split('?.');
      let current = context[parts[0]];
      
      for (let i = 1; i < parts.length; i++) {
        if (current === null || current === undefined) {
          return undefined;
        }
        current = current[parts[i]];
      }
      
      return current;
    } catch (error) {
      console.error(`[${this.getComponentName()}] Error evaluating optional chaining:`, error);
      return undefined;
    }
  }

  // Component loading helper method
  protected loadComponent(componentConfig: ComponentConfig, container: ViewContainerRef): void {
    if (!componentConfig || !componentConfig.type) {
      console.error(`[${this.getComponentName()}] Invalid component configuration:`, componentConfig);
      return;
    }

    // For components that need profile data, verify we have a valid profile.
    // Allow certain components (like Landing) to render with skeletons while profile loads.
    const requiresValidProfile = (
      (componentConfig.type && (componentConfig.type.includes('Dashboard') || componentConfig.type.includes('Profile')))
    ) || (componentConfig as any).waitForValidProfile === true;
    
    // Check if this component has expressions that depend on profile data
    const hasProfileExpressions = componentConfig.inputs && 
      Object.values(componentConfig.inputs).some((value: any) => 
        typeof value === 'string' && 
        (value.includes('$profile') || value.includes('profile.'))
      );

    if (
      componentConfig.showWhen === 'authenticated' &&
      (requiresValidProfile || hasProfileExpressions) &&
      !this.hasValidProfile()
    ) {
      if (this.debug) console.warn(`[${this.getComponentName()}] Skipping ${componentConfig.type} - waiting for valid profile data (requiresValidProfile: ${requiresValidProfile}, hasProfileExpressions: ${hasProfileExpressions})`);
      return;
    }

    try {
      const componentType = this.loader.resolveComponent(componentConfig.type);
      if (!componentType) {
        console.error(`[${this.getComponentName()}] Component type '${componentConfig.type}' not registered or not found.`);
        return;
      }

      // Create injector with specific providers if needed, or use default
      const componentInjector = Injector.create({
        providers: [
          { provide: ActivatedRoute, useValue: this.route },
          { provide: Router, useValue: this.router },
          { provide: ConfigService, useValue: this.configService },
          { provide: MemberService, useValue: this.memberService },
          { provide: KeyCloakService, useValue: this.kc }
        ],
        parent: this.injector
      });

      // Create the component instance with the injector
      const componentRef = container.createComponent(componentType, { injector: componentInjector });

      // Generate unique ID if not provided
      const componentId = componentConfig.id || `component-${this.componentCounter++}`;
      
      // Store the component reference
      this.componentRefs.set(componentId, componentRef);
      
      // Store the component ID and type on the instance for reference
      (componentRef.instance as any).__componentId = componentId;
      (componentRef.instance as any).__componentType = componentConfig.type;

      // Apply the inputs to the component instance (support both 'inputs' and 'config' properties)
      const inputsToProcess = componentConfig.inputs || componentConfig.config || {};
      if (inputsToProcess && typeof inputsToProcess === 'object') {
        for (const [key, value] of Object.entries(inputsToProcess)) {
          // Check for special input values that should be replaced with actual instances
          let inputValue = value;

          // Handle special string values that should be replaced with actual services/objects
          if (typeof value === 'string') {
            // Check for expression syntax (starts with $ or contains operators)
            if (value.startsWith('$') || value.includes('?.') || value.includes('+')) {
              inputValue = this.evaluateExpression(value);
              if (this.debug) console.log(`[${this.getComponentName()}] Evaluated expression '${value}':`, inputValue);
            } else {
              switch (value) {
                case 'kc':
                case 'keyCloakService':
                case 'keycloakService':
                  inputValue = this.kc;
                  if (this.debug) {
                    console.log(
                      `[${this.getComponentName()}] Replacing '${value}' with KeyCloakService instance`
                    );
                  }
                  break;
                case 'profile':
                  inputValue = this.profile;
                  if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'profile' with profile object:`, this.profile);
                  break;
                case 'router':
                  inputValue = this.router;
                  if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'router' with Router instance`);
                  break;
                case 'memberService':
                  inputValue = this.memberService;
                  if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'memberService' with MemberService instance`);
                  break;
                case 'programService':
                  try {
                    inputValue = this.injector.get(ProgramService);
                    if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'programService' with ProgramService instance`);
                  } catch (e) {
                    console.warn(`[${this.getComponentName()}] ProgramService not available in injector`);
                    inputValue = null;
                  }
                  break;
                case 'firebaseMemberService':
                  try {
                    inputValue = this.injector.get(FirebaseMemberService);
                    if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'firebaseMemberService' with FirebaseMemberService instance`);
                  } catch (e) {
                    console.warn(`[${this.getComponentName()}] FirebaseMemberService not available in injector`);
                    inputValue = null;
                  }
                  break;
                case 'onboardingService':
                  try {
                    inputValue = this.injector.get(OnboardingService);
                    if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'onboardingService' with OnboardingService instance`);
                  } catch (e) {
                    console.warn(`[${this.getComponentName()}] OnboardingService not available in injector`);
                    inputValue = null;
                  }
                  break;
                case 'programMemberService':
                  try {
                    inputValue = this.injector.get(ProgramMemberService);
                    if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'programMemberService' with ProgramMemberService instance`);
                  } catch (e) {
                    console.warn(`[${this.getComponentName()}] ProgramMemberService not available in injector`);
                    inputValue = null;
                  }
                  break;
                case 'pushNotificationService':
                  try {
                    inputValue = this.injector.get(PushNotificationService);
                    if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'pushNotificationService' with PushNotificationService instance`);
                  } catch (e) {
                    console.warn(`[${this.getComponentName()}] PushNotificationService not available in injector`);
                    inputValue = null;
                  }
                  break;
                case 'lssConfig':
                  inputValue = this.configService.sysConfig;
                  if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'lssConfig' with sysConfig object`);
                  break;
                case 'partnerService':
                  // Try to get PartnerService from the injector
                  try {
                    inputValue = this.injector.get(PartnerService);
                    if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'partnerService' with PartnerService instance`);
                  } catch (e) {
                    console.warn(`[${this.getComponentName()}] PartnerService not available in injector`);
                    inputValue = null;
                  }
                  break;
                case 'systemService':
                  // Try to get SystemService from the injector
                  try {
                    inputValue = this.injector.get(SystemService);
                    if (this.debug) console.log(`[${this.getComponentName()}] Replacing 'systemService' with SystemService instance`);
                  } catch (e) {
                    console.warn(`[${this.getComponentName()}] SystemService not available in injector`);
                    inputValue = null;
                  }
                  break;
                default:
                  // Keep the original string value
                  break;
              }
            }
          }

          // Assign the processed input value to the component
          componentRef.instance[key] = inputValue;
          if (this.debug) console.log(`[${this.getComponentName()}] Set input '${key}' on component '${componentConfig.type}'`);
        }
      }

      // Subscribe to actionEvent if it exists
      if (componentRef.instance && (componentRef.instance as any).actionEvent) {
        (componentRef.instance as any).actionEvent.subscribe((action: string) => {
          this.handleComponentAction(action);
        });
      }
      
      // Subscribe to output events if they exist
      if (componentConfig.outputs && componentRef.instance) {
        for (const [outputName, handlerName] of Object.entries(componentConfig.outputs)) {
          if (componentRef.instance[outputName]) {
            componentRef.instance[outputName].subscribe((data: any) => {
              this.handleComponentOutput(handlerName, data);
            });
            if (this.debug) console.log(`[${this.getComponentName()}] Subscribed to output '${outputName}' with handler '${handlerName}'`);
          }
        }
      }

      // Detect changes to ensure inputs are applied
      componentRef.changeDetectorRef.detectChanges();

      if (this.debug) console.log(`[${this.getComponentName()}] Successfully created and configured component: ${componentConfig.type}`);
    } catch (error) {
      console.error(`[${this.getComponentName()}] Error creating component ${componentConfig.type}:`, error);
    }
  }

  // Handle component output events
  protected handleComponentOutput(handlerName: string, data: any): void {
    if (this.debug) console.log(`[${this.getComponentName()}] Handling output: ${handlerName}`, data);
    
    switch (handlerName) {
      case 'handleStoreNavigation':
        console.log(`[${this.getComponentName()}] Store selected for navigation:`, data);
        if (data && data.partnerId) {
          // Navigate to store detail page with the selected store data
          this.router.navigate(['/public/storedetail'], {
            state: { data: data }
          });
        }
        break;
      default:
        console.warn(`[${this.getComponentName()}] Unhandled output event: ${handlerName}`);
    }
  }

  // Handle component actions
  protected handleComponentAction(action: string): void {
    if (this.debug) console.log(`[${this.getComponentName()}] Handling action: ${action}`);
    switch (action) {
      case 'logout':
        console.log(`[${this.getComponentName()}] Logout action received`);
        this.kc.keycloak?.logout();
        break;
      case 'viewTransactions':
        console.log(`[${this.getComponentName()}] Navigate to transactions`);
        this.router.navigate(['/secure/transactions']);
        break;
      case 'downloadStatement':
        console.log(`[${this.getComponentName()}] Download statement action`);
        // Implement download statement logic
        break;
      case 'getStarted':
        console.log(`[${this.getComponentName()}] Get started action`);
        this.router.navigate(['/public/register']);
        break;
      case 'openTermsLink':
        console.log(`[${this.getComponentName()}] Open terms link action`);
        // Implement terms link opening logic
        break;
      case 'resendOtp':
        console.log(`[${this.getComponentName()}] Resend OTP action`);
        // Implement OTP resend logic
        break;
      default:
        console.warn(`[${this.getComponentName()}] Unhandled action: ${action}`);
    }
  }

  /**
   * Handle component selection from builder
   */
  protected handleComponentSelection(selection: ComponentSelection): void {
    const componentRef = this.componentRefs.get(selection.componentId);
    
    if (!componentRef) {
      console.warn(`[${this.getComponentName()}] Component with ID ${selection.componentId} not found during selection.`);
      return;
    }

    // Get the stored component type
    const componentType = (componentRef.instance as any).__componentType;
    
    if (this.debug) {
      console.log(`[${this.getComponentName()}] Component selected: ${selection.componentId}, Type: ${componentType}`);
    }

    // Send the correct component type back to the builder
    this.bridgeReceiver.sendResponseToBuilder({
      type: 'component_metadata',
      data: {
        componentId: selection.componentId,
        componentType: componentType,
        properties: this.propertyDiscovery.getComponentProperties(componentType),
        timestamp: Date.now()
      }
    });
  }

  /**
   * Update component properties dynamically
   */
  protected updateComponentProperties(componentId: string, properties: { [key: string]: any }): void {
    const componentRef = this.componentRefs.get(componentId);
    
    if (!componentRef) {
      console.warn(`[${this.getComponentName()}] Component with ID ${componentId} not found.`);
      return;
    }

    if (this.debug) {
      console.log(`[${this.getComponentName()}] Updating properties for component ${componentId}:`, properties);
    }

    // Apply the new property values
    Object.entries(properties).forEach(([key, value]) => {
      if (componentRef.instance.hasOwnProperty(key) || key in componentRef.instance) {
        componentRef.instance[key] = value;
        if (this.debug) {
          console.log(`[${this.getComponentName()}] Updated property '${key}' to:`, value);
        }
      } else {
        console.warn(`[${this.getComponentName()}] Property '${key}' does not exist on component`);
      }
    });

    // Trigger change detection to update the view
    componentRef.changeDetectorRef.detectChanges();
    
    // Also trigger global change detection for any dependent components
    this.cdr.detectChanges();
  }

  /**
   * Load user profile using unified API approach (mirrors lp-client app.component.ts logic)
   * Handles both authenticated users with valid MPACC and users requiring email search
   */
  private loadUnifiedProfile(): void {
    const emailUserId = (this.kc.userProfile?.preferred_username || this.kc.userProfile?.email || null) as string | null;
    const productId = String((this.configService.sysConfig as any)?.productId ?? 'rmic');
    let mpacc = this.kc.lpUniueReference;
    const apiBaseUrl = (this.configService.sysConfig as any)?.apiBaseUrl;
    const LP_APIID = this.kc.userProfile?.LP_APIID;
    const LP_MPACC = this.kc.userProfile?.LP_MPACC;
    
    console.log(`[${this.getComponentName()}] loadUnifiedProfile called with:`, { 
      emailUserId, 
      productId, 
      mpacc, 
      apiBaseUrl,
      LP_APIID,
      LP_MPACC,
      authSuccess: this.kc.authSuccess,
      userProfile: this.kc.userProfile 
    });
    
    // Check if we have critical configuration
    if (!apiBaseUrl) {
      console.error(`[${this.getComponentName()}] CRITICAL: No apiBaseUrl configured. Cannot load profile.`);
      console.error(`[${this.getComponentName()}] Current lssConfig:`, this.configService.sysConfig);
      this.setMinimalTokenProfile(emailUserId || '<EMAIL>');
      return;
    }

    // If we have all required parameters, load profile immediately
    if (emailUserId && productId && mpacc && mpacc !== 'Not Set' && mpacc.trim() !== '') {
      console.log(`[${this.getComponentName()}] Loading profile using unified API with complete parameters`);
      this.memberService.loadProgramMemberProfile(emailUserId, productId, mpacc).subscribe({
        next: (profile) => {
          console.log(`[${this.getComponentName()}] Unified API profile loaded successfully:`, profile);
          this.profile = { ...this.profile, ...profile };
          this.memberService.profileSubject.next(profile);
          this.handlePostLoginNavigation();
          this.loadPageComponents();
        },
        error: (err) => {
          console.error(`[${this.getComponentName()}] Unified API failed:`, err);
          this.loadPageComponents();
        }
      });
      return;
    }

    // If MPACC is missing in token, try to find it by searching with email
    if (emailUserId && productId && (!mpacc || mpacc === 'Not Set' || mpacc.trim() === '')) {
      console.log(`[${this.getComponentName()}] MPACC not available ("${mpacc}"). Searching by email to resolve membership number...`);
      const searchCriteria: MemberCommObject = { emailAddress: emailUserId };
      this.memberService.search(searchCriteria).subscribe({
        next: (profiles) => {
          const found = Array.isArray(profiles) && profiles.length > 0 ? profiles[0] : null;
          const foundMpacc = found?.membershipNumber;
          if (foundMpacc && foundMpacc.trim() !== '') {
            console.log(`[${this.getComponentName()}] Found MPACC by email search:`, foundMpacc);
            this.memberService.loadProgramMemberProfile(emailUserId, productId, foundMpacc).subscribe({
              next: (profile) => {
                console.log(`[${this.getComponentName()}] Unified API profile loaded after MPACC resolution:`, profile);
                this.profile = { ...this.profile, ...profile };
                this.memberService.profileSubject.next(profile);
                this.handlePostLoginNavigation();
                this.loadPageComponents();
              },
              error: (err) => {
                console.error(`[${this.getComponentName()}] Unified API failed after resolving MPACC:`, err);
                this.loadPageComponents();
              }
            });
          } else {
            console.warn(`[${this.getComponentName()}] Could not resolve MPACC via search. Setting minimal profile from token.`);
            this.setMinimalTokenProfile(emailUserId);
          }
        },
        error: (err) => {
          console.error(`[${this.getComponentName()}] Error searching for member by email:`, err);
          this.setMinimalTokenProfile(emailUserId);
        }
      });
      return;
    }

    // Missing key fields: fallback to minimal profile if we have email
    if (emailUserId) {
      console.warn(`[${this.getComponentName()}] Missing required parameters for unified API call. Creating minimal profile.`);
      this.setMinimalTokenProfile(emailUserId);
    } else {
      console.warn(`[${this.getComponentName()}] No email found, cannot create profile. Loading components without profile.`);
      this.loadPageComponents();
    }
  }

  /**
   * Create minimal profile from token data when full profile loading fails
   */
  private setMinimalTokenProfile(emailUserId: string): void {
    const token = this.kc.userProfile;
    const productId = String((this.configService.sysConfig as any)?.productId ?? 'rmic');
    
    console.log(`[${this.getComponentName()}] Creating minimal profile from token data:`, {
      emailUserId,
      productId,
      tokenEmail: token?.email,
      tokenGivenName: (token as any)?.given_name,
      tokenFamilyName: (token as any)?.family_name,
      fullToken: token
    });
    
    const minimal: MemberProfile = {
      uniqueId: emailUserId,
      apiId: productId,
      externalId: emailUserId,
      emailAddress: token?.email || emailUserId,
      givenNames: (token as any)?.given_name || 'User',
      surname: (token as any)?.family_name || '',
      membershipNumber: undefined,
      newMembershipNumber: undefined,
      // Add additional safe defaults
      currentBalance: 0,
      membershipStatus: 'Active'
    } as MemberProfile;
    
    console.log(`[${this.getComponentName()}] Setting minimal profile from token:`, minimal);
    
    // Update both profile subject and local profile
    this.memberService.profileSubject.next(minimal);
    this.profile = { ...this.profile, ...minimal };
    
    console.log(`[${this.getComponentName()}] Profile updated, triggering post-login navigation and component loading`);
    this.handlePostLoginNavigation();
    this.loadPageComponents();
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    if (this.authSubscription) {
      this.authSubscription.unsubscribe();
    }
    if (this.profileSubscription) {
      this.profileSubscription.unsubscribe();
    }
    if (this.propertyUpdateSubscription) {
      this.propertyUpdateSubscription.unsubscribe();
    }
    
    // Clean up component references
    this.componentRefs.forEach(ref => ref.destroy());
    this.componentRefs.clear();
  }
}

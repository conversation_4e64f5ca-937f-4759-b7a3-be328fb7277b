import { Injectable } from '@angular/core';
import { ComponentRegistryService } from './component-registry.service';

export interface ComponentPropertyMetadata {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select' | 'object' | 'array';
  label?: string;
  description?: string;
  required?: boolean;
  defaultValue?: any;
  options?: string[]; // For select type
}

@Injectable({
  providedIn: 'root'
})
export class ComponentPropertyDiscoveryService {
  // Map of component types to their property metadata
  private propertyMetadataMap = new Map<string, ComponentPropertyMetadata[]>();

  constructor(private componentRegistry: ComponentRegistryService) {
    this.initializeCommonComponentProperties();
  }

  /**
   * Get property metadata for a component type
   */
  getComponentProperties(componentType: string): ComponentPropertyMetadata[] {
    console.log('[PropertyDiscovery] Getting properties for:', componentType);
    
    // Check if we have custom metadata for this component
    if (this.propertyMetadataMap.has(componentType)) {
      const properties = this.propertyMetadataMap.get(componentType)!;
      console.log('[PropertyDiscovery] Found registered properties:', properties.length);
      return properties;
    }

    // Try different naming conventions
    const variations = [
      componentType,
      componentType.charAt(0).toUpperCase() + componentType.slice(1) + 'Component',
      componentType + 'Component',
      componentType.charAt(0).toUpperCase() + componentType.slice(1)
    ];

    for (const variation of variations) {
      if (this.propertyMetadataMap.has(variation)) {
        const properties = this.propertyMetadataMap.get(variation)!;
        console.log('[PropertyDiscovery] Found properties with variation:', variation, 'count:', properties.length);
        return properties;
      }
    }

    // Return default common properties
    const commonProps = this.getCommonProperties(componentType);
    console.log('[PropertyDiscovery] Returning common properties:', commonProps.length);
    return commonProps;
  }

  /**
   * Register property metadata for a component type
   */
  registerComponentProperties(componentType: string, properties: ComponentPropertyMetadata[]): void {
    this.propertyMetadataMap.set(componentType, properties);
  }

  /**
   * Get common properties that most components have
   */
  private getCommonProperties(componentType: string): ComponentPropertyMetadata[] {
    const commonProps: ComponentPropertyMetadata[] = [
      {
        name: 'className',
        type: 'string',
        label: 'CSS Classes',
        description: 'Custom CSS classes to apply to the component'
      }
    ];

    // Add size property for components that typically have it
    if (this.componentSupportsSize(componentType)) {
      commonProps.push({
        name: 'size',
        type: 'select',
        label: 'Size',
        options: ['xs', 'sm', 'md', 'lg', 'xl'],
        defaultValue: 'md'
      });
    }

    // Add variant property for components that typically have it
    if (this.componentSupportsVariant(componentType)) {
      commonProps.push({
        name: 'variant',
        type: 'select',
        label: 'Variant',
        options: ['default', 'primary', 'secondary', 'success', 'warning', 'danger'],
        defaultValue: 'default'
      });
    }

    // Add specific properties based on component type patterns
    if (componentType.toLowerCase().includes('button')) {
      commonProps.push(
        { name: 'text', type: 'string', label: 'Button Text' },
        { name: 'disabled', type: 'boolean', label: 'Disabled', defaultValue: false },
        { name: 'loading', type: 'boolean', label: 'Loading', defaultValue: false },
        { name: 'fullWidth', type: 'boolean', label: 'Full Width', defaultValue: false }
      );
    } else if (componentType.toLowerCase().includes('input')) {
      commonProps.push(
        { name: 'placeholder', type: 'string', label: 'Placeholder' },
        { name: 'value', type: 'string', label: 'Value' },
        { name: 'disabled', type: 'boolean', label: 'Disabled', defaultValue: false },
        { name: 'required', type: 'boolean', label: 'Required', defaultValue: false }
      );
    } else if (componentType.toLowerCase().includes('card')) {
      commonProps.push(
        { name: 'title', type: 'string', label: 'Title' },
        { name: 'subtitle', type: 'string', label: 'Subtitle' },
        { name: 'padding', type: 'boolean', label: 'Padding', defaultValue: true },
        { name: 'shadow', type: 'boolean', label: 'Shadow', defaultValue: true }
      );
    } else if (componentType.toLowerCase().includes('heading')) {
      commonProps.push(
        { name: 'text', type: 'string', label: 'Text' },
        { name: 'level', type: 'select', label: 'Level', options: ['1', '2', '3', '4', '5', '6'], defaultValue: '1' },
        { name: 'align', type: 'select', label: 'Alignment', options: ['left', 'center', 'right'], defaultValue: 'left' }
      );
    } else if (componentType.toLowerCase().includes('text') || componentType.toLowerCase().includes('paragraph')) {
      commonProps.push(
        { name: 'text', type: 'string', label: 'Text Content' },
        { name: 'align', type: 'select', label: 'Alignment', options: ['left', 'center', 'right', 'justify'], defaultValue: 'left' }
      );
    }

    return commonProps;
  }

  /**
   * Initialize property metadata for common components
   */
  private initializeCommonComponentProperties(): void {
    // Button component
    const buttonProperties: ComponentPropertyMetadata[] = [
      { name: 'text', type: 'string', label: 'Button Text', required: true },
      { name: 'className', type: 'string', label: 'CSS Classes' },
      { name: 'size', type: 'select', label: 'Size', options: ['xs', 'sm', 'md', 'lg', 'xl'], defaultValue: 'md' },
      { name: 'variant', type: 'select', label: 'Variant', options: ['default', 'primary', 'secondary', 'success', 'warning', 'danger'], defaultValue: 'default' },
      { name: 'type', type: 'select', label: 'Type', options: ['button', 'submit', 'reset'], defaultValue: 'button' },
      { name: 'disabled', type: 'boolean', label: 'Disabled', defaultValue: false },
      { name: 'loading', type: 'boolean', label: 'Loading', defaultValue: false },
      { name: 'fullWidth', type: 'boolean', label: 'Full Width', defaultValue: false },
      { name: 'icon', type: 'string', label: 'Icon Name' },
      { name: 'iconPosition', type: 'select', label: 'Icon Position', options: ['start', 'end'], defaultValue: 'start' }
    ];
    
    // Register with multiple naming conventions
    this.registerComponentProperties('ButtonComponent', buttonProperties);
    this.registerComponentProperties('button', buttonProperties);
    this.registerComponentProperties('Button', buttonProperties);

    // Card component
    const cardProperties: ComponentPropertyMetadata[] = [
      { name: 'title', type: 'string', label: 'Title' },
      { name: 'subtitle', type: 'string', label: 'Subtitle' },
      { name: 'className', type: 'string', label: 'CSS Classes' },
      { name: 'padding', type: 'boolean', label: 'Padding', defaultValue: true },
      { name: 'shadow', type: 'boolean', label: 'Shadow', defaultValue: true },
      { name: 'border', type: 'boolean', label: 'Border', defaultValue: false },
      { name: 'hoverable', type: 'boolean', label: 'Hoverable', defaultValue: false },
      { name: 'clickable', type: 'boolean', label: 'Clickable', defaultValue: false }
    ];
    
    this.registerComponentProperties('CardComponent', cardProperties);

    // Heading component (register with multiple variations for compatibility)
    const headingProperties: ComponentPropertyMetadata[] = [
      { name: 'text', type: 'string', label: 'Text', required: true },
      { name: 'level', type: 'select', label: 'Level', options: ['1', '2', '3', '4', '5', '6'], defaultValue: '1' },
      { name: 'className', type: 'string', label: 'CSS Classes' },
      { name: 'size', type: 'select', label: 'Size', options: ['xs', 'sm', 'md', 'lg', 'xl', '2xl', '3xl', '4xl', '5xl'], defaultValue: 'md' },
      { name: 'weight', type: 'select', label: 'Font Weight', options: ['thin', 'light', 'normal', 'medium', 'semibold', 'bold', 'extrabold', 'black'], defaultValue: 'normal' },
      { name: 'align', type: 'select', label: 'Alignment', options: ['left', 'center', 'right'], defaultValue: 'left' },
      { name: 'transform', type: 'select', label: 'Text Transform', options: ['none', 'uppercase', 'lowercase', 'capitalize'], defaultValue: 'none' },
      { name: 'italic', type: 'boolean', label: 'Italic', defaultValue: false },
      { name: 'truncate', type: 'boolean', label: 'Truncate', defaultValue: false }
    ];
    
    // Register with multiple naming conventions for compatibility
    this.registerComponentProperties('HeadingComponent', headingProperties);
    this.registerComponentProperties('heading', headingProperties);
    this.registerComponentProperties('Heading', headingProperties);

    // Input component
    const inputProperties: ComponentPropertyMetadata[] = [
      { name: 'placeholder', type: 'string', label: 'Placeholder' },
      { name: 'value', type: 'string', label: 'Value' },
      { name: 'type', type: 'select', label: 'Type', options: ['text', 'email', 'password', 'number', 'tel', 'url'], defaultValue: 'text' },
      { name: 'className', type: 'string', label: 'CSS Classes' },
      { name: 'size', type: 'select', label: 'Size', options: ['xs', 'sm', 'md', 'lg', 'xl'], defaultValue: 'md' },
      { name: 'variant', type: 'select', label: 'Variant', options: ['default', 'primary', 'secondary', 'success', 'warning', 'danger'], defaultValue: 'default' },
      { name: 'disabled', type: 'boolean', label: 'Disabled', defaultValue: false },
      { name: 'required', type: 'boolean', label: 'Required', defaultValue: false },
      { name: 'readonly', type: 'boolean', label: 'Read Only', defaultValue: false }
    ];
    
    this.registerComponentProperties('InputComponent', inputProperties);

    // Text/Paragraph component
    const textProperties: ComponentPropertyMetadata[] = [
      { name: 'text', type: 'string', label: 'Text Content', required: true },
      { name: 'className', type: 'string', label: 'CSS Classes' },
      { name: 'size', type: 'select', label: 'Size', options: ['xs', 'sm', 'md', 'lg', 'xl'], defaultValue: 'md' },
      { name: 'align', type: 'select', label: 'Alignment', options: ['left', 'center', 'right', 'justify'], defaultValue: 'left' },
      { name: 'weight', type: 'select', label: 'Font Weight', options: ['thin', 'light', 'normal', 'medium', 'semibold', 'bold'], defaultValue: 'normal' },
      { name: 'italic', type: 'boolean', label: 'Italic', defaultValue: false },
      { name: 'truncate', type: 'boolean', label: 'Truncate', defaultValue: false }
    ];
    
    this.registerComponentProperties('TextComponent', textProperties);

    // Add more component property definitions as needed
  }

  /**
   * Check if component type typically supports size property
   */
  private componentSupportsSize(componentType: string): boolean {
    const sizeComponents = ['button', 'input', 'card', 'heading', 'text', 'icon', 'avatar', 'badge', 'tag'];
    return sizeComponents.some(type => componentType.toLowerCase().includes(type));
  }

  /**
   * Check if component type typically supports variant property
   */
  private componentSupportsVariant(componentType: string): boolean {
    const variantComponents = ['button', 'input', 'card', 'badge', 'tag', 'alert', 'message'];
    return variantComponents.some(type => componentType.toLowerCase().includes(type));
  }
}
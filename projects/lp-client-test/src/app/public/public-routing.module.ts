import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DynamicPublicPageComponent } from './dynamic-public-page.component';

const routes: Routes = [
  // All routes now use dynamic pages from environment configuration
  // Support nested paths like 'games/home', 'app/stores', etc.
  { path: ':page/:subpage/:subsubpage', component: DynamicPublicPageComponent },
  { path: ':page/:subpage', component: DynamicPublicPageComponent },
  { path: ':page', component: DynamicPublicPageComponent },
  { path: '', redirectTo: 'landing', pathMatch: 'full' },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class PublicRoutingModule {}

import { MultiTenantContextService, ProgramContext } from './multi-tenant-context.service';
import { LssConfig } from 'lp-client-api';

describe('MultiTenantContextService', () => {
  beforeEach(() => {
    localStorage.clear();
  });

  const createService = (
    runtimeConfig: Partial<LssConfig>,
    environmentConfig?: any
  ): MultiTenantContextService => {
    const lssConfig = new LssConfig();
    Object.assign(lssConfig, runtimeConfig);
    return new MultiTenantContextService(lssConfig, environmentConfig);
  };

  it('uses runtime navigation route for program selection when available', () => {
    const service = createService({
      navigation: {
        routes: [
          {
            id: 'Programs',
            link: 'secure/program-management',
          } as any,
        ],
      },
    });

    expect(service.getProgramSelectionRoute()).toBe('/secure/program-management');
  });

  it('falls back to environment navigation route when runtime route missing', () => {
    const environment = {
      lssConfig: {
        navigation: {
          routes: [
            {
              id: 'Programs',
              link: 'programs',
            },
          ],
        },
      },
    };

    const service = createService({}, environment);

    expect(service.getProgramSelectionRoute()).toBe('/secure/programs');
  });

  it('derives program selection route from page configuration when navigation missing', () => {
    const environment = {
      lssConfig: {
        pageConfigs: [
          {
            title: 'program-management',
            path: 'program-management',
            secure: true,
          },
        ],
      },
    };

    const service = createService({}, environment);

    expect(service.getProgramSelectionRoute()).toBe('/secure/program-management');
  });

  it('falls back to environment navigation when runtime config is missing', () => {
    const environment = {
      lssConfig: {
        workflow: {
          multiTenant: true,
          navigation: {
            programDependentRoutes: ['Profile', 'Card'],
            alwaysVisibleRoutes: ['Programs'],
          },
        },
      },
    };

    const service = createService(
      {
        workflow: {
          multiTenant: true,
        } as any,
      },
      environment
    );

    expect(service.isMultiTenant).toBeTrue();
    expect(service.isNavigationItemVisible('Programs')).toBeTrue();
    expect(service.isNavigationItemVisible('Profile')).toBeFalse();
    expect(service.isNavigationItemVisible('Card')).toBeFalse();
  });

  it('merges runtime and environment navigation settings', () => {
    const environment = {
      lssConfig: {
        workflow: {
          multiTenant: true,
          navigation: {
            programDependentRoutes: ['Profile', 'Card'],
            alwaysVisibleRoutes: ['Programs'],
          },
        },
      },
    };

    const service = createService(
      {
        workflow: {
          multiTenant: true,
          navigation: {
            programDependentRoutes: ['Transactions'],
          },
        } as any,
      },
      environment
    );

    expect(service.isNavigationItemVisible('Programs')).toBeTrue();
    expect(service.isNavigationItemVisible('Profile')).toBeFalse();
    expect(service.isNavigationItemVisible('Card')).toBeFalse();
    expect(service.isNavigationItemVisible('Transactions')).toBeFalse();
  });

  it('stores context using a derived storage key from environment identifiers', () => {
    const environment = {
      client: 'Beta Portal',
      lssConfig: {},
    };

    const service = createService({}, environment);
    const context: ProgramContext = {
      programId: '123',
      programName: 'Demo Program',
      mpacc: 'ABC',
      enteredAt: new Date().toISOString(),
    };

    service.setProgramContext(context);

  const stored = localStorage.getItem('beta-portal_current_program_context');
    expect(stored).withContext('expected derived storage key to be used').not.toBeNull();
    expect(JSON.parse(stored as string)).toEqual(jasmine.objectContaining(context));
  });

  it('falls back to default storage key when no identifiers are available', () => {
    const service = createService({}, undefined);
    const context: ProgramContext = {
      programId: '456',
      programName: 'Fallback Program',
      mpacc: 'XYZ',
      enteredAt: new Date().toISOString(),
    };

    service.setProgramContext(context);
    expect(localStorage.getItem('lp-client-test_current_program_context')).not.toBeNull();

    service.clearProgramContext();
    expect(localStorage.getItem('lp-client-test_current_program_context')).toBeNull();
  });
});
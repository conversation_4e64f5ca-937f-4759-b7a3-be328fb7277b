import { Inject, Injectable, Optional } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { LssConfig } from 'lp-client-api';

type NavigationRoute = {
  id?: string;
  link?: string;
  text?: string;
  [key: string]: any;
};

export interface ProgramContext {
  programId: string;
  programName: string;
  mpacc: string;
  iconUrl?: string;
  enteredAt: string;
  // Extended program branding information
  programLogo?: string;      // Main program logo URL
  programIcon?: string;      // Program icon URL
  programHorizontal?: string; // Horizontal image URL
  programVertical?: string;   // Vertical image URL
}

@Injectable({
  providedIn: 'root'
})
export class MultiTenantContextService {
  private readonly storageKey: string;
  
  // BehaviorSubject to track current program context
  private programContextSubject = new BehaviorSubject<ProgramContext | null>(null);
  
  constructor(
    private lssConfig: LssConfig,
    @Optional() @Inject('environment') private environment?: any
  ) {
    this.storageKey = this.resolveStorageKey();
    // Load stored context on service initialization
    this.loadStoredContext();
  }

  /**
   * Check if the current app is multi-tenant
   */
  get isMultiTenant(): boolean {
    const runtimeSetting = this.lssConfig.workflow?.multiTenant;
    if (runtimeSetting !== undefined) {
      return runtimeSetting === true;
    }

    const environmentSetting = this.environment?.lssConfig?.workflow?.multiTenant;
    return environmentSetting === true;
  }

  /**
   * Get observable for program context changes
   */
  get programContext$(): Observable<ProgramContext | null> {
    return this.programContextSubject.asObservable();
  }

  /**
   * Get current program context (synchronous)
   */
  get currentProgramContext(): ProgramContext | null {
    return this.programContextSubject.value;
  }

  /**
   * Check if user has selected a program (only relevant for multi-tenant)
   */
  get hasProgramSelected(): boolean {
    if (!this.isMultiTenant) {
      return true; // Single-tenant always has "program selected"
    }
    return this.currentProgramContext !== null;
  }

  /**
   * Check if a navigation item should be visible based on multi-tenant rules
   * Uses dynamic configuration from environment
   */
  isNavigationItemVisible(routeId: string): boolean {
    console.log(`🔍 Checking visibility for route: ${routeId}`);
    console.log('  - Is multi-tenant:', this.isMultiTenant);
    
    // If not multi-tenant, all items are always visible
    if (!this.isMultiTenant) {
      console.log('  ✅ Single-tenant mode: showing all items');
      return true;
    }

    // Get navigation configuration from runtime config (with environment fallback)
    const navigationConfig = this.resolveNavigationConfig();
    console.log('  - Navigation config:', navigationConfig);
    
    if (!navigationConfig) {
      // Fallback to default behavior if no configuration
      console.log('  ✅ No navigation config: showing all items');
      return true;
    }

    console.log('  - Has program selected:', this.hasProgramSelected);
    console.log('  - Current program context:', this.currentProgramContext);
    console.log('  - Always visible routes:', navigationConfig.alwaysVisibleRoutes);
    console.log('  - Program dependent routes:', navigationConfig.programDependentRoutes);

    // Check if this route is always visible
    if (navigationConfig.alwaysVisibleRoutes?.includes(routeId)) {
      console.log('  ✅ Route is always visible');
      return true;
    }

    // Check if this route requires program selection
    if (navigationConfig.programDependentRoutes?.includes(routeId)) {
      const shouldShow = this.hasProgramSelected;
      console.log(`  ${shouldShow ? '✅' : '❌'} Route is program dependent, has program: ${shouldShow}`);
      return shouldShow;
    }

    // By default, routes not specified are visible
    console.log('  ✅ Route not in any config list: showing by default');
    return true;
  }

  /**
   * Set the current program context
   */
  setProgramContext(context: ProgramContext): void {
    this.programContextSubject.next(context);
    this.storeContext(context);
    console.log('Program context updated:', context);
  }

  /**
   * Clear the current program context (e.g., on logout)
   */
  clearProgramContext(): void {
    this.programContextSubject.next(null);
    this.removeStoredContext();
    console.log('Program context cleared');
  }

  /**
   * Get display text for current program (for UI)
   */
  getCurrentProgramDisplayText(): string {
    const context = this.currentProgramContext;
    if (!context) {
      return this.isMultiTenant ? 'Select Program' : '';
    }
    return context.programName;
  }

  /**
   * Get icon for current program (for UI)
   */
  getCurrentProgramIcon(): string {
    const context = this.currentProgramContext;
    if (!context) {
      return this.isMultiTenant ? 'list-outline' : '';
    }
    return context.iconUrl || 'apps-outline';
  }

  /**
   * Load stored context from localStorage
   */
  private loadStoredContext(): void {
    try {
      const storedData = localStorage.getItem(this.storageKey);
      if (storedData) {
        const context = JSON.parse(storedData) as ProgramContext;
        // Validate the stored context has required fields
        if (context.programId && context.mpacc) {
          this.programContextSubject.next(context);
          console.log('Loaded stored program context:', context);
        } else {
          console.warn('Stored program context is invalid, removing it');
          this.removeStoredContext();
        }
      }
    } catch (error) {
      console.error('Error loading stored program context:', error);
      this.removeStoredContext();
    }
  }

  /**
   * Store context to localStorage
   */
  private storeContext(context: ProgramContext): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(context));
    } catch (error) {
      console.error('Error storing program context:', error);
    }
  }

  /**
   * Remove stored context from localStorage
   */
  private removeStoredContext(): void {
    try {
      localStorage.removeItem(this.storageKey);
    } catch (error) {
      console.error('Error removing stored program context:', error);
    }
  }

  /**
   * Resolve navigation configuration, merging runtime config with environment fallback
   */
  private resolveNavigationConfig():
    | {
        programDependentRoutes?: string[];
        alwaysVisibleRoutes?: string[];
        [key: string]: any;
      }
    | undefined {
    const runtimeNavigation = this.lssConfig.workflow?.navigation;
    const environmentNavigation = this.environment?.lssConfig?.workflow?.navigation;

    if (!runtimeNavigation && !environmentNavigation) {
      return undefined;
    }

    if (!runtimeNavigation) {
      return environmentNavigation;
    }

    if (!environmentNavigation) {
      return runtimeNavigation;
    }

    const mergedNavigation = {
      ...environmentNavigation,
      ...runtimeNavigation,
    };

    return {
      ...mergedNavigation,
      programDependentRoutes: this.resolveRouteList(
        runtimeNavigation.programDependentRoutes,
        environmentNavigation.programDependentRoutes
      ),
      alwaysVisibleRoutes: this.resolveRouteList(
        runtimeNavigation.alwaysVisibleRoutes,
        environmentNavigation.alwaysVisibleRoutes
      ),
    };
  }

  private resolveRouteList(
    primary?: unknown,
    fallback?: unknown
  ): string[] | undefined {
    const primaryList = Array.isArray(primary)
      ? primary.filter((route): route is string => typeof route === 'string' && route.trim().length > 0)
      : undefined;
    if (primaryList && primaryList.length > 0) {
      return Array.from(new Set(primaryList));
    }

    const fallbackList = Array.isArray(fallback)
      ? fallback.filter((route): route is string => typeof route === 'string' && route.trim().length > 0)
      : undefined;
    if (fallbackList && fallbackList.length > 0) {
      return Array.from(new Set(fallbackList));
    }

    return primaryList ?? fallbackList;
  }

  /**
   * Check if user should be redirected to program selection
   * (multi-tenant apps without program context)
   */
  shouldRedirectToProgramSelection(): boolean {
    return this.isMultiTenant && !this.hasProgramSelected;
  }

  /**
   * Get the route for program selection
   */
  getProgramSelectionRoute(): string {
    const navigationRoute = this.resolveProgramSelectionNavigationLink();
    if (navigationRoute) {
      return navigationRoute;
    }

    const pageRoute = this.resolveProgramSelectionPageRoute();
    if (pageRoute) {
      return pageRoute;
    }

    return '/secure/program-management';
  }

  /**
   * Force clear program context for debugging
   */
  debugClearProgramContext(): void {
    console.log('🧹 DEBUG: Force clearing program context');
    this.clearProgramContext();
  }

  private resolveProgramSelectionNavigationLink(): string | undefined {
    const runtimeRoutes = this.extractNavigationRoutes(this.lssConfig?.navigation?.routes);
    const environmentRoutes = this.extractNavigationRoutes(
      this.environment?.lssConfig?.navigation?.routes
    );

    const navigationRoute =
      this.findProgramRoute(runtimeRoutes) ?? this.findProgramRoute(environmentRoutes);

    if (!navigationRoute?.link || typeof navigationRoute.link !== 'string') {
      return undefined;
    }

    return this.normalizeSecureRoute(navigationRoute.link);
  }

  private extractNavigationRoutes(routes: NavigationRoute[] | undefined): NavigationRoute[] {
    if (!Array.isArray(routes)) {
      return [];
    }
    return routes;
  }

  private findProgramRoute(routes: NavigationRoute[]): NavigationRoute | undefined {
    const lowerProgramId = 'programs';

    return routes.find((route) => {
      const idMatch =
        typeof route?.id === 'string' && route.id.toLowerCase() === lowerProgramId;
      const link = typeof route?.link === 'string' ? route.link.toLowerCase() : '';
      const linkMatch = link.includes('program-management') || link.includes('program-selection');
      const textMatch = typeof route?.text === 'string' && route.text.toLowerCase().includes('program');

      return idMatch || linkMatch || textMatch;
    });
  }

  private resolveProgramSelectionPageRoute(): string | undefined {
    const candidates = [
      this.lssConfig?.pages,
      (this.lssConfig as any)?.pageConfigs,
      this.environment?.lssConfig?.pages,
      this.environment?.lssConfig?.pageConfigs,
    ];

    for (const candidate of candidates) {
      const path = this.findProgramPagePath(candidate);
      if (path) {
        return this.buildSecureRoute(path);
      }
    }

    return undefined;
  }

  private findProgramPagePath(pages: any): string | undefined {
    if (!pages) {
      return undefined;
    }

    if (Array.isArray(pages)) {
      for (const page of pages) {
        const path = this.extractProgramPathFromPage(page);
        if (path) {
          return path;
        }
      }
      return undefined;
    }

    if (typeof pages === 'object') {
      for (const key of Object.keys(pages)) {
        const page = pages[key];
        const path = this.extractProgramPathFromPage(page);
        if (path) {
          return path;
        }

        if (key.toLowerCase().includes('program-management')) {
          return key;
        }
      }
    }

    return undefined;
  }

  private extractProgramPathFromPage(page: any): string | undefined {
    if (!page) {
      return undefined;
    }

    const pathCandidate: unknown = page?.path ?? page?.route ?? page?.slug;
    if (typeof pathCandidate === 'string') {
      const lowerPath = pathCandidate.toLowerCase();
      if (lowerPath.includes('program-management') || lowerPath.includes('program-selection')) {
        return pathCandidate;
      }
    }

    const title: unknown = page?.title ?? page?.name;
    if (typeof title === 'string') {
      const lowerTitle = title.toLowerCase();
      if (lowerTitle.includes('program-management')) {
        const fallbackPath = typeof pathCandidate === 'string' ? pathCandidate : lowerTitle;
        return fallbackPath;
      }
    }

    return undefined;
  }

  private buildSecureRoute(segment: string): string {
    if (!segment) {
      return '/secure/program-management';
    }

    const trimmed = segment.trim();
    if (!trimmed) {
      return '/secure/program-management';
    }

    const cleaned = trimmed.replace(/^\/+/, '');

    if (cleaned.startsWith('secure/')) {
      return `/${cleaned}`;
    }

    return `/secure/${cleaned}`;
  }

  private normalizeSecureRoute(link: string): string {
    const trimmed = link.trim();

    if (!trimmed) {
      return '/secure/program-management';
    }

    if (trimmed.startsWith('http://') || trimmed.startsWith('https://')) {
      return trimmed;
    }

    const cleaned = trimmed.replace(/\s+/g, '');

    if (cleaned.startsWith('/secure/')) {
      return cleaned;
    }

    if (cleaned.startsWith('secure/')) {
      return `/${cleaned}`;
    }

    if (cleaned.startsWith('/')) {
      return cleaned.startsWith('/secure/') ? cleaned : `/secure/${cleaned.replace(/^\/+/,'')}`;
    }

    return `/secure/${cleaned}`;
  }

  private resolveStorageKey(): string {
    const explicitKey = this.environment?.multiTenantStorageKey;
    if (typeof explicitKey === 'string' && explicitKey.trim().length > 0) {
      return explicitKey.trim();
    }

    const appIdentifierCandidates: Array<unknown> = [
      this.environment?.client,
      this.environment?.lssConfig?.appCode,
      this.environment?.lssConfig?.appName,
      (this.lssConfig as any)?.appCode,
      (this.lssConfig as any)?.appName,
    ];

    for (const candidate of appIdentifierCandidates) {
      if (typeof candidate === 'string' && candidate.trim().length > 0) {
        const normalized = candidate.trim().toLowerCase().replace(/\s+/g, '-');
        return `${normalized}_current_program_context`;
      }
    }

    return 'lp-client-test_current_program_context';
  }
}
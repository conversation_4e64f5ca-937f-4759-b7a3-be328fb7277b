import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { delay } from 'rxjs/operators';

export interface BridgeApiResponse {
  success: boolean;
  message?: string;
  data?: any;
  timestamp: number;
}

@Injectable({
  providedIn: 'root'
})
export class BridgeApiService {
  private debug = true;

  constructor() {
    this.setupApiEndpoints();
  }

  /**
   * Setup simulated API endpoints
   */
  private setupApiEndpoints(): void {
    // Expose API methods on window for external access
    (window as any).lpClientTestBridgeAPI = {
      health: this.healthCheck.bind(this),
      update: this.handleUpdate.bind(this),
      status: this.getStatus.bind(this)
    };

    if (this.debug) {
      console.log('[BridgeAPI] API endpoints exposed on window.lpClientTestBridgeAPI');
    }
  }

  /**
   * Health check endpoint
   */
  healthCheck(): BridgeApiResponse {
    return {
      success: true,
      message: 'LP Client Test is running',
      data: {
        version: '1.0.0',
        status: 'healthy',
        features: ['live-updates', 'hot-reload', 'component-sync']
      },
      timestamp: Date.now()
    };
  }

  /**
   * Handle update from builder
   */
  handleUpdate(updateData: any): BridgeApiResponse {
    if (this.debug) {
      console.log('[BridgeAPI] Received update:', updateData);
    }

    try {
      // Process the update
      this.processUpdate(updateData);

      return {
        success: true,
        message: 'Update processed successfully',
        data: {
          updateType: updateData.type,
          processed: true
        },
        timestamp: Date.now()
      };
    } catch (error) {
      console.error('[BridgeAPI] Failed to process update:', error);
      
      return {
        success: false,
        message: 'Failed to process update: ' + (error as Error).message,
        timestamp: Date.now()
      };
    }
  }

  /**
   * Get current status
   */
  getStatus(): BridgeApiResponse {
    return {
      success: true,
      data: {
        connected: true,
        lastUpdate: Date.now(),
        activeConnections: 1
      },
      timestamp: Date.now()
    };
  }

  /**
   * Process incoming update
   */
  private processUpdate(updateData: any): void {
    // Dispatch custom event for the bridge receiver
    window.dispatchEvent(new CustomEvent('builder-update', {
      detail: updateData
    }));
  }

  /**
   * Simulate HTTP endpoints for fetch requests
   */
  static simulateHttpEndpoints(): void {
    // Override fetch for specific endpoints
    const originalFetch = window.fetch;
    
    window.fetch = function(input: RequestInfo | URL, init?: RequestInit): Promise<Response> {
      const url = typeof input === 'string' ? input : input.toString();
      
      // Health check endpoint
      if (url.includes('/health')) {
        const api = (window as any).lpClientTestBridgeAPI;
        const response = api ? api.health() : { success: false, message: 'API not available' };
        
        return Promise.resolve(new Response(JSON.stringify(response), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }));
      }
      
      // Update endpoint
      if (url.includes('/api/builder/update') && init?.method === 'POST') {
        const api = (window as any).lpClientTestBridgeAPI;
        
        if (api && init.body) {
          try {
            const updateData = JSON.parse(init.body as string);
            const response = api.update(updateData);
            
            return Promise.resolve(new Response(JSON.stringify(response), {
              status: 200,
              headers: { 'Content-Type': 'application/json' }
            }));
          } catch (error) {
            return Promise.resolve(new Response(JSON.stringify({
              success: false,
              message: 'Invalid JSON in request body'
            }), {
              status: 400,
              headers: { 'Content-Type': 'application/json' }
            }));
          }
        }
      }
      
      // Events endpoint (SSE simulation)
      if (url.includes('/api/builder/events')) {
        // Return a simple success response for SSE endpoint
        return Promise.resolve(new Response(JSON.stringify({
          success: true,
          message: 'SSE endpoint ready'
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        }));
      }
      
      // Fall back to original fetch for other requests
      return originalFetch.call(this, input, init);
    };
    
    console.log('[BridgeAPI] HTTP endpoint simulation enabled');
  }
}

// Initialize HTTP endpoint simulation when service is loaded
BridgeApiService.simulateHttpEndpoints();

import { Injectable, OnDestroy } from '@angular/core';
import { BehaviorSubject, Subject, fromEvent } from 'rxjs';
import { takeUntil, filter } from 'rxjs/operators';
import { ConfigService } from './config.service';
import { ComponentRegistryService } from '../shared/component-registry.service';
import { ComponentPropertyDiscoveryService } from '../shared/component-property-discovery.service';

export interface LiveUpdateMessage {
  type: 'config_update' | 'page_update' | 'component_update' | 'reload_request' | 'property_update' | 'component_selection';
  configId: string;
  pageId?: string;
  componentId?: string;
  data?: any;
  timestamp: number;
  source?: string;
}

export interface PropertyUpdate {
  componentId: string;
  properties: { [key: string]: any };
}

export interface ComponentSelection {
  componentId: string;
  componentType: string;
}

export interface BridgeReceiverStatus {
  listening: boolean;
  lastUpdate: Date | null;
  updatesReceived: number;
  builderConnected: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class BuilderBridgeReceiverService implements OnDestroy {
  private destroy$ = new Subject<void>();
  private debug = true;

  // Receiver status
  private receiverStatus = new BehaviorSubject<BridgeReceiverStatus>({
    listening: false,
    lastUpdate: null,
    updatesReceived: 0,
    builderConnected: false
  });

  // Live updates received
  private liveUpdates = new Subject<LiveUpdateMessage>();
  
  // Property updates stream
  private propertyUpdateSource = new Subject<PropertyUpdate>();
  public propertyUpdate$ = this.propertyUpdateSource.asObservable();
  
  // Component selection stream
  private componentSelectionSource = new Subject<ComponentSelection>();
  public componentSelection$ = this.componentSelectionSource.asObservable();

  constructor(
    private configService: ConfigService,
    private componentRegistry: ComponentRegistryService,
    private propertyDiscovery: ComponentPropertyDiscoveryService
  ) {
    this.initializeReceiver();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Get receiver status as observable
   */
  getReceiverStatus() {
    return this.receiverStatus.asObservable();
  }

  /**
   * Get live updates stream
   */
  getLiveUpdates() {
    return this.liveUpdates.asObservable();
  }

  /**
   * Initialize the bridge receiver
   */
  private initializeReceiver(): void {
    if (this.debug) {
      console.log('[BridgeReceiver] Initializing builder bridge receiver');
    }

    this.setupPostMessageListener();
    this.setupAPIEndpoints();
    
    this.updateStatus({ listening: true });

    // Subscribe to live updates for processing
    this.liveUpdates.pipe(
      takeUntil(this.destroy$)
    ).subscribe(update => {
      this.processLiveUpdate(update);
    });
  }

  /**
   * Setup postMessage listener for iframe communication
   */
  private setupPostMessageListener(): void {
    fromEvent<MessageEvent>(window, 'message').pipe(
      takeUntil(this.destroy$),
      filter(event => event.data && event.data.source === 'lp-app-builder')
    ).subscribe(event => {
      if (this.debug) {
        console.log('[BridgeReceiver] Received postMessage from builder:', event.data);
      }

      const message: LiveUpdateMessage = {
        type: event.data.type,
        configId: event.data.configId,
        pageId: event.data.pageId,
        componentId: event.data.componentId,
        data: event.data.data,
        timestamp: event.data.timestamp,
        source: 'postMessage'
      };

      this.liveUpdates.next(message);
      this.updateStatus({ 
        builderConnected: true,
        lastUpdate: new Date(),
        updatesReceived: this.receiverStatus.value.updatesReceived + 1
      });
    });
  }

  /**
   * Setup API endpoints for HTTP communication
   */
  private setupAPIEndpoints(): void {
    // Note: In a real implementation, these would be actual HTTP endpoints
    // For now, we'll simulate them by exposing methods on the window object
    
    (window as any).lpClientTestAPI = {
      // Health check endpoint
      health: () => {
        return { status: 'ok', timestamp: Date.now() };
      },

      // Update endpoint
      update: (message: LiveUpdateMessage) => {
        if (this.debug) {
          console.log('[BridgeReceiver] Received API update:', message);
        }

        this.liveUpdates.next({
          ...message,
          source: 'api'
        });

        this.updateStatus({ 
          builderConnected: true,
          lastUpdate: new Date(),
          updatesReceived: this.receiverStatus.value.updatesReceived + 1
        });

        return { success: true, timestamp: Date.now() };
      },

      // Events endpoint (for SSE simulation)
      events: () => {
        // This would normally be a Server-Sent Events endpoint
        // For now, we'll just return a success response
        return { success: true, message: 'SSE endpoint ready' };
      },
      
      // Get component properties endpoint
      getComponentProperties: (componentType: string) => {
        if (this.debug) {
          console.log('[BridgeReceiver] API getComponentProperties called for:', componentType);
        }
        
        const properties = this.propertyDiscovery.getComponentProperties(componentType);
        
        return {
          componentType,
          properties,
          timestamp: Date.now()
        };
      }
    };

    if (this.debug) {
      console.log('[BridgeReceiver] API endpoints exposed on window.lpClientTestAPI');
    }
  }

  /**
   * Process incoming live update
   */
  private processLiveUpdate(update: LiveUpdateMessage): void {
    if (this.debug) {
      console.log('[BridgeReceiver] Processing live update:', update);
    }

    try {
      switch (update.type) {
        case 'config_update':
          this.handleConfigUpdate(update);
          break;
        case 'page_update':
          this.handlePageUpdate(update);
          break;
        case 'component_update':
          this.handleComponentUpdate(update);
          break;
        case 'reload_request':
          this.handleReloadRequest(update);
          break;
        case 'property_update':
          this.handlePropertyUpdate(update);
          break;
        case 'component_selection':
          this.handleComponentSelection(update);
          break;
        default:
          console.warn('[BridgeReceiver] Unknown update type:', update.type);
      }
    } catch (error) {
      console.error('[BridgeReceiver] Error processing update:', error);
    }
  }

  /**
   * Handle configuration update
   */
  private handleConfigUpdate(update: LiveUpdateMessage): void {
    if (!update.data || !update.data.config) {
      console.warn('[BridgeReceiver] Config update missing data');
      return;
    }

    const config = update.data.config;
    
    if (this.debug) {
      console.log('[BridgeReceiver] Applying config update:', config);
    }

    // Update the configuration service
    try {
      // Merge the new config with existing config
      const currentConfig = this.configService.sysConfig;
      const updatedConfig = {
        ...currentConfig,
        ...config.lssConfig,
        pages: config.pages || currentConfig.pages
      };

      // Apply the updated configuration
      (this.configService as any).config = updatedConfig;

      if (this.debug) {
        console.log('[BridgeReceiver] Configuration updated successfully');
      }

      // Trigger a page reload if needed
      if (update.data.forceReload) {
        this.reloadApplication();
      }
    } catch (error) {
      console.error('[BridgeReceiver] Failed to apply config update:', error);
    }
  }

  /**
   * Handle page update
   */
  private handlePageUpdate(update: LiveUpdateMessage): void {
    if (!update.data || !update.data.page) {
      console.warn('[BridgeReceiver] Page update missing data');
      return;
    }

    const page = update.data.page;
    
    if (this.debug) {
      console.log('[BridgeReceiver] Applying page update:', page);
    }

    try {
      // Update the page in the configuration
      const currentConfig = this.configService.sysConfig;
      if (currentConfig.pages) {
        const pageIndex = currentConfig.pages.findIndex((p: any) => p.path === page.path);
        if (pageIndex >= 0) {
          currentConfig.pages[pageIndex] = page;
          
          if (this.debug) {
            console.log('[BridgeReceiver] Page updated successfully');
          }

          // Trigger component re-registration if needed
          this.reregisterPageComponents(page);
        }
      }
    } catch (error) {
      console.error('[BridgeReceiver] Failed to apply page update:', error);
    }
  }

  /**
   * Handle component update
   */
  private handleComponentUpdate(update: LiveUpdateMessage): void {
    if (!update.data || !update.data.components) {
      console.warn('[BridgeReceiver] Component update missing data');
      return;
    }

    const components = update.data.components;
    
    if (this.debug) {
      console.log('[BridgeReceiver] Applying component update:', components);
    }

    try {
      // Convert builder components to page components
      const pageComponents = this.convertBuilderComponentsToPageComponents(components);
      
      // Update the current page configuration
      const currentConfig = this.configService.sysConfig;
      if (currentConfig.pages && update.pageId) {
        const page = currentConfig.pages.find((p: any) => p.path === update.pageId);
        if (page) {
          page.components = pageComponents;
          
          if (this.debug) {
            console.log('[BridgeReceiver] Components updated successfully');
          }

          // Trigger page refresh
          this.refreshCurrentPage();
        }
      }
    } catch (error) {
      console.error('[BridgeReceiver] Failed to apply component update:', error);
    }
  }

  /**
   * Handle reload request
   */
  private handleReloadRequest(update: LiveUpdateMessage): void {
    if (this.debug) {
      console.log('[BridgeReceiver] Processing reload request');
    }

    this.reloadApplication();
  }

  /**
   * Convert builder components to page components
   */
  private convertBuilderComponentsToPageComponents(builderComponents: any): any[] {
    const pageComponents: any[] = [];

    Object.values(builderComponents).forEach((component: any) => {
      if (!component.parentId) { // Root components only
        const pageComponent = {
          type: component.type,
          inputs: {
            ...component.properties,
            className: component.properties.tailwindClasses
          },
          showWhen: 'always'
        };

        pageComponents.push(pageComponent);
      }
    });

    return pageComponents;
  }

  /**
   * Re-register page components
   */
  private reregisterPageComponents(page: any): void {
    if (!page.components) return;

    page.components.forEach((component: any) => {
      if (component.type) {
        // Re-register component if needed
        const existingComponent = this.componentRegistry.getComponent(component.type);
        if (!existingComponent) {
          console.warn(`[BridgeReceiver] Component ${component.type} not registered`);
        }
      }
    });
  }

  /**
   * Refresh current page
   */
  private refreshCurrentPage(): void {
    // Trigger Angular change detection
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('lp-page-refresh'));
    }, 100);
  }

  /**
   * Reload the entire application
   */
  private reloadApplication(): void {
    if (this.debug) {
      console.log('[BridgeReceiver] Reloading application');
    }

    // Give a small delay to show the reload message
    setTimeout(() => {
      window.location.reload();
    }, 500);
  }

  /**
   * Update receiver status
   */
  private updateStatus(updates: Partial<BridgeReceiverStatus>): void {
    const currentStatus = this.receiverStatus.value;
    this.receiverStatus.next({
      ...currentStatus,
      ...updates
    });
  }

  /**
   * Send response back to builder (for API simulation)
   */
  sendResponseToBuilder(message: any): void {
    // Send response via postMessage
    if (window.parent !== window) {
      window.parent.postMessage({
        source: 'lp-client-test',
        type: 'response',
        data: message,
        timestamp: Date.now()
      }, '*');
    }
  }

  /**
   * Handle property update for a specific component
   */
  private handlePropertyUpdate(update: LiveUpdateMessage): void {
    if (!update.componentId || !update.data?.properties) {
      console.warn('[BridgeReceiver] Property update missing componentId or properties');
      return;
    }

    if (this.debug) {
      console.log('[BridgeReceiver] Handling property update:', update);
    }

    const propertyUpdate: PropertyUpdate = {
      componentId: update.componentId,
      properties: update.data.properties
    };

    this.propertyUpdateSource.next(propertyUpdate);
  }

  /**
   * Handle component selection
   */
  private handleComponentSelection(update: LiveUpdateMessage): void {
    if (!update.componentId || !update.data?.componentType) {
      console.warn('[BridgeReceiver] Component selection missing componentId or componentType');
      return;
    }

    if (this.debug) {
      console.log('[BridgeReceiver] Handling component selection:', update);
    }

    const selection: ComponentSelection = {
      componentId: update.componentId,
      componentType: update.data.componentType
    };

    this.componentSelectionSource.next(selection);

    // Send component metadata back to builder
    this.sendComponentMetadata(selection.componentId, selection.componentType);
  }

  /**
   * Send component metadata to builder
   */
  private sendComponentMetadata(componentId: string, componentType: string): void {
    if (this.debug) {
      console.log(`[BridgeReceiver] sendComponentMetadata called with type: ${componentType}`);
    }
    
    // Try to get properties directly first (in case componentType is already the simple name)
    let properties = this.propertyDiscovery.getComponentProperties(componentType);
    
    // If no properties found and componentType looks like a full class name, try to find the component
    if (properties.length === 0 && componentType.includes('Component')) {
      const componentClass = this.componentRegistry.getComponent(componentType);
      
      if (!componentClass) {
        console.warn(`[BridgeReceiver] Component type ${componentType} not found in registry`);
      } else {
        // Use the class name for property discovery
        properties = this.getComponentProperties(componentClass);
      }
    }

    const metadata = {
      componentId,
      componentType,
      properties,
      timestamp: Date.now()
    };

    if (this.debug) {
      console.log(`[BridgeReceiver] Sending metadata with ${properties.length} properties`);
    }

    this.sendResponseToBuilder({
      type: 'component_metadata',
      data: metadata
    });
  }

  /**
   * Get component properties using the discovery service
   */
  private getComponentProperties(componentClass: any): any[] {
    // Get the component type name from the class
    const componentType = componentClass.name || '';
    
    // Use the property discovery service to get metadata
    return this.propertyDiscovery.getComponentProperties(componentType);
  }
}

import { Injectable } from '@angular/core';
import { KeyCloakService, MemberService } from 'lp-client-api';
import { ConfigService } from './services/config.service';

@Injectable({
  providedIn: 'root'
})
export class DebugAuthStateService {

  constructor(
    private kc: KeyCloakService,
    private memberService: MemberService,
    private configService: ConfigService
  ) {}

  /**
   * Log comprehensive debug information about current authentication state
   * Call this from browser console: window['debugAuthState']()
   */
  logAuthState(): void {
    console.group('🔍 DEBUG: Authentication State Analysis');
    
    // KeyCloak state
    console.group('🔐 KeyCloak Authentication');
    console.log('authSuccess:', this.kc.authSuccess);
    console.log('userProfile present:', !!this.kc.userProfile);
    console.log('userProfile:', this.kc.userProfile);
    console.log('lpUniueReference:', this.kc.lpUniueReference);
    console.log('LP_APIID from token:', this.kc.userProfile?.LP_APIID);
    console.log('LP_MPACC from token:', this.kc.userProfile?.LP_MPACC);
    console.groupEnd();
    
    // Configuration state
    console.group('⚙️ Configuration');
    console.log('lssConfig:', this.configService.sysConfig);
    console.log('apiBaseUrl:', (this.configService.sysConfig as any)?.apiBaseUrl);
    console.log('productId:', (this.configService.sysConfig as any)?.productId);
    console.log('workflowConfig:', (this.configService.sysConfig as any)?.workflow);
    console.groupEnd();
    
    // Member service state
    console.group('👤 Member Profile State');
    try {
      console.log('Current profile subject value:', this.memberService.profileSubject.value);
    } catch (e) {
      console.log('Error accessing profileSubject:', e);
    }
    console.groupEnd();
    
    // Navigation state
    console.group('🧭 Navigation State');
    console.log('Current URL:', window.location.href);
    console.log('Current pathname:', window.location.pathname);
    console.groupEnd();
    
    // API Context Resolution
    console.group('🔗 API Context Resolution');
    const emailUserId = (this.kc.userProfile?.preferred_username || this.kc.userProfile?.email || null) as string | null;
    const productId = String((this.configService.sysConfig as any)?.productId ?? 'rmic');
    const mpacc = this.kc.lpUniueReference;
    
    console.log('Resolved context for API calls:', {
      emailUserId,
      productId, 
      mpacc,
      canLoadProfile: !!(emailUserId && productId && mpacc && mpacc !== 'Not Set')
    });
    console.groupEnd();
    
    console.groupEnd();
  }
  
  /**
   * Force profile reload for debugging
   */
  forceProfileReload(): void {
    console.log('🔄 Forcing profile reload...');
    // This would need to be implemented based on the actual member service API
    const emailUserId = (this.kc.userProfile?.preferred_username || this.kc.userProfile?.email || null) as string | null;
    const productId = String((this.configService.sysConfig as any)?.productId ?? 'rmic');
    let mpacc = this.kc.lpUniueReference;
    
    if (emailUserId && productId && mpacc && mpacc !== 'Not Set' && mpacc.trim() !== '') {
      console.log('Attempting to reload profile with:', { emailUserId, productId, mpacc });
      this.memberService.loadProgramMemberProfile(emailUserId, productId, mpacc).subscribe({
        next: (profile) => {
          console.log('✅ Profile reload successful:', profile);
          this.memberService.profileSubject.next(profile);
        },
        error: (err) => {
          console.error('❌ Profile reload failed:', err);
        }
      });
    } else {
      console.warn('❌ Cannot reload profile - missing required parameters');
      console.log('Available parameters:', { emailUserId, productId, mpacc });
    }
  }
}

// Make available globally for browser console debugging
declare let window: any;
window['debugAuthState'] = () => {
  const service = (window as any).ng?.getInjector()?.get(DebugAuthStateService);
  if (service) {
    service.logAuthState();
  } else {
    console.error('Could not access DebugAuthStateService');
  }
};

window['forceProfileReload'] = () => {
  const service = (window as any).ng?.getInjector()?.get(DebugAuthStateService);
  if (service) {
    service.forceProfileReload();
  } else {
    console.error('Could not access DebugAuthStateService');
  }
};
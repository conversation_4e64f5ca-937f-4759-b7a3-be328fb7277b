import { APP_INITIALIZER, NgModule, CUSTOM_ELEMENTS_SCHEMA, inject } from '@angular/core';
import { BrowserModule, HammerModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { IonicModule, IonicRouteStrategy } from '@ionic/angular';
import {
  InterceptorService,
  KeyCloakService,
  LogPublishersService,
  LogService,
  LssConfig,
} from 'lp-client-api';
import {
  FaIconLibrary,
  FontAwesomeModule,
} from '@fortawesome/angular-fontawesome';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { environment } from '../environments/environment';
import { RouteReuseStrategy } from '@angular/router';
import { HTTP_INTERCEPTORS,  provideHttpClient, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ConfigService } from './services/config.service';
import { GoogleMapsModule } from '@angular/google-maps';


@NgModule({
  declarations: [], // AppComponent is now standalone
  imports: [
    AppComponent,
    CommonModule,
    BrowserModule,
    FontAwesomeModule,
    IonicModule.forRoot(),
    AppRoutingModule,
    HammerModule,
    GoogleMapsModule,
  ],
  providers: [
    provideHttpClient(
      withInterceptorsFromDi()
      /*withInterceptors([(req, next) => {
        console.log('Test Intercept');
        return next(req);
      }])*/
    ),
    { provide: 'environment', useValue: environment },
    {
      provide: APP_INITIALIZER,
      useFactory: (config: ConfigService) => {
        return () => config.loadConfig();
      },
      multi: true,
      deps: [ConfigService],
    },
    {
      provide: LssConfig,
      useFactory: (config: ConfigService) => {
        return config.sysConfig;
      },
      deps: [ConfigService],
    },
    { provide: RouteReuseStrategy, useClass: IonicRouteStrategy },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: InterceptorService,
      multi: true,
      deps: [KeyCloakService],
    },
    LogService,
    LogPublishersService,
    { provide: 'componentProperties', useValue: {} }
  ],
  // bootstrap: [AppComponent], // Now using bootstrapApplication in main.ts
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppModule {
  constructor(library: FaIconLibrary) {
    library.addIconPacks(fas);
  }
}

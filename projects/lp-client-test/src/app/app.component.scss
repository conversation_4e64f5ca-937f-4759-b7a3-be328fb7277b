// EXACT WORKING COPY FROM lp-client - WILL FIX THE SIDEBAR ISSUE ONCE AND FOR ALL

:root {
  --header-height: 64px;
  --bottom-nav-height: 80px;
  --sidebar-width: 280px;
}

@media (max-width: 768px) {
  :root {
    --bottom-nav-height: 70px;
  }
}

/* Desktop Sidebar (match lp-client styling) */
.desktop-sidebar {
  display: none !important;
  position: fixed;
  left: -280px;
  top: 0;
  width: 280px;
  height: 100vh;
  background: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  z-index: 10;
  overflow-y: auto;
  overflow-x: hidden;
  pointer-events: none !important;
  transition: left 0.3s ease;
  max-width: 280px;
  box-sizing: border-box;

  @media (min-width: 768px) {
    display: block !important;
    left: 0;
    pointer-events: auto !important;
  }

  .sidebar-header {
    background: white;
    border-bottom: 1px solid #e0e0e0;
    padding: 24px;

    .sidebar-logo {
      text-align: center;
      margin-bottom: 16px;

      img {
        max-width: 120px;
        height: auto;
      }
    }

    .sidebar-profile {
      text-align: center;
      transition: all 0.3s ease;
      cursor: pointer;
      border-radius: 8px;
      padding: 8px;
      margin: -8px;

      &:hover {
        background: rgba(0, 0, 0, 0.02);
        transform: translateY(-1px);
      }

      .profile-avatar {
        width: 60px;
        height: 60px;
        margin: 0 auto 12px;
        position: relative;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

        &::before {
          content: '';
          position: absolute;
          inset: -3px;
          border-radius: 50%;
          background: linear-gradient(135deg, var(--ion-color-primary, #FF6B35), transparent);
          opacity: 0;
          transition: opacity 0.3s ease;
        }

        ion-icon {
          font-size: 60px;
          color: #9e9e9e;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          z-index: 1;
        }
      }

      h3 {
        margin: 0 0 4px 0;
        font-size: 16px;
        font-weight: 600;
        color: #212121;
        transition: all 0.3s ease;
      }

      p {
        margin: 0;
        font-size: 13px;
        color: #666;
        transition: all 0.3s ease;
      }

      &:hover {
        .profile-avatar {
          transform: scale(1.05);

          &::before {
            opacity: 0.1;
          }

          ion-icon {
            color: var(--ion-color-primary, #FF6B35);
            filter: drop-shadow(0 2px 8px rgba(255, 107, 53, 0.3));
          }
        }

        h3 {
          color: var(--ion-color-primary, #FF6B35);
        }

        p {
          color: #424242;
        }
      }
    }
  }

  .sidebar-menu {
    padding: 12px 0;

    .menu-items {
      .sidebar-menu-item {
        display: flex;
        align-items: center;
        padding: 12px 24px;
        margin: 2px 8px;
        border-radius: 8px;
        text-decoration: none;
        color: #424242;
        font-size: 15px;
        font-weight: 500;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
          transition: left 0.5s ease;
        }

        ion-icon {
          font-size: 20px;
          margin-right: 16px;
          color: #666;
          min-width: 20px;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          transform: translateZ(0);
        }

        span {
          flex: 1;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        &:hover {
          background: rgba(0, 0, 0, 0.04);
          transform: translateX(4px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

          &::before {
            left: 100%;
          }

          ion-icon {
            transform: scale(1.1) translateZ(0);
            color: var(--ion-color-primary, #FF6B35);
          }

          span {
            font-weight: 600;
          }
        }

        &:active {
          transform: translateX(2px) scale(0.98);
          transition: all 0.1s ease;
        }

        &.active {
          background: linear-gradient(135deg, rgba(255, 107, 53, 0.1), rgba(255, 107, 53, 0.05));
          color: var(--ion-color-primary, #FF6B35);
          font-weight: 600;
          box-shadow: 0 2px 8px rgba(255, 107, 53, 0.2);

          ion-icon {
            color: var(--ion-color-primary, #FF6B35);
            transform: scale(1.1) translateZ(0);
            filter: drop-shadow(0 2px 4px rgba(255, 107, 53, 0.3));
          }

          &::after {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 60%;
            background: var(--ion-color-primary, #FF6B35);
            border-radius: 0 2px 2px 0;
            box-shadow: 0 0 8px rgba(255, 107, 53, 0.4);
          }
        }

        &.logout {
          color: #F44336;

          ion-icon {
            color: #F44336;
          }

          &:hover {
            background: rgba(244, 67, 54, 0.08);
            transform: translateX(4px);
            box-shadow: 0 2px 8px rgba(244, 67, 54, 0.15);

            ion-icon {
              color: #F44336;
              transform: scale(1.1) translateZ(0);
              filter: drop-shadow(0 2px 4px rgba(244, 67, 54, 0.3));
            }
          }
        }
      }

      .sidebar-divider {
        height: 1px;
        background: #e0e0e0;
        margin: 8px 24px;
      }
    }
  }

  .sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
    padding: 16px;
    text-align: center;

    .app-version {
      p {
        margin: 0;
        font-size: 12px;
        color: #999;
      }
    }
  }
}

/* MAIN LAYOUT (EXACT COPY FROM WORKING lp-client) */
.main-layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  height: 100%;
  transition: margin-left 0.3s ease;

  &.with-sidebar {
    /* Force no margin by default (mobile behavior) */
    margin-left: 0 !important;
  }

  .main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 100vh;

    ion-router-outlet {
      flex: 1;
      display: block;
      width: 100%;
      min-height: calc(100vh - var(--header-height, 64px));
      position: relative;
    }
  }
}

/* DESKTOP RESPONSIVE (EXACT COPY FROM WORKING lp-client) */
@media (min-width: 768px) {
  /* Reserve space for the fixed sidebar using padding-left (prevents overlap) */
  .main-layout.with-sidebar {
    padding-left: var(--sidebar-width) !important;
    margin-left: 0 !important;
    transition: padding-left 0.3s ease; /* Smooth transition */
    width: 100% !important;
    max-width: 100% !important;
  }

  .mobile-only {
    display: none; /* Hide hamburger menu */
  }
}

/* Large Desktop Optimizations (match lp-client) */
@media (min-width: 1200px) {
  .desktop-sidebar {
    width: 320px; /* Slightly wider on large screens */
  }

  .main-layout.with-sidebar {
    padding-left: 320px !important;
    margin-left: 0 !important;
  }
}

/* MOBILE BEHAVIOR (EXACT COPY FROM WORKING lp-client) */
@media (max-width: 767px) {
  .desktop-sidebar {
    display: none !important; /* Always hidden on mobile */
  }

  .main-layout {
    margin-left: 0 !important; /* Force no margin on mobile */
    height: 100%;

    .main-content {
      min-height: calc(100vh - var(--bottom-nav-height, 80px));
      padding-bottom: var(--bottom-nav-height, 80px);
    }
  }

  .mobile-only {
    display: block !important; /* Ensure hamburger shows */
  }
}

/* MODERN HEADER STYLES */
.modern-header {
  --background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  --color: #2c3e50;
  --box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 1001;

  .modern-toolbar {
    --background: transparent;
    --color: #2c3e50;
    --padding-start: 16px;
    --padding-end: 16px;
    --min-height: 64px;

    .modern-title {
      --color: #2c3e50;
      font-size: 20px;
      font-weight: 600;
      letter-spacing: -0.025em;
    }

    .back-button,
    .notification-button,
    .menu-toggle-button {
      --background: transparent;
      --color: #6c757d;
      --border-radius: 8px;
      --padding-start: 8px;
      --padding-end: 8px;
      margin: 0 4px;
      height: 40px;
      width: 40px;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        --background: rgba(0, 0, 0, 0.04);
        --color: var(--ion-color-primary, #f5821f);
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &:active {
        transform: translateY(0) scale(0.96);
        transition: all 0.1s ease;
      }

      ion-icon {
        font-size: 20px;
      }
    }

    .notification-button {
      position: relative;

      .notification-wrapper {
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .notification-badge {
        position: absolute;
        top: -6px;
        right: -6px;
        background: #ff4757;
        color: white;
        font-size: 10px;
        font-weight: 600;
        line-height: 1;
        padding: 2px 6px;
        border-radius: 10px;
        min-width: 16px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(255, 71, 87, 0.3);
        animation: pulse 2s infinite;
      }
    }
  }
}

/* BOTTOM NAVIGATION STYLES */
.bottom-nav {
  backdrop-filter: blur(10px);
  background: linear-gradient(to right, #0a2463, #1e88e5, #0a2463) !important;
  border-top: none;
  box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.3);
  z-index: 1000;
  transition: transform 0.3s ease-in-out;
  padding: 8px 0;
  height: var(--bottom-nav-height);
  display: flex;
  align-items: center;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;

  .nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px 8px;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
    min-width: 60px;

    ion-icon {
      font-size: 24px;
      margin-bottom: 4px;
      transition: all 0.3s ease;
    }

    span {
      font-size: 11px;
      font-weight: 500;
      text-align: center;
    }

    &.active {
      color: #ffffff;
      transform: translateY(-2px);

      ion-icon {
        transform: scale(1.1);
        color: #ffd700;
      }
    }

    &:hover {
      color: rgba(255, 255, 255, 0.9);
      transform: translateY(-1px);

      ion-icon {
        transform: scale(1.05);
      }
    }
  }
}

/* MOBILE MENU STYLES */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  &.menu-open {
    opacity: 1;
    visibility: visible;

    .mobile-menu-panel {
      transform: translateX(0);
    }
  }

  .mobile-menu-panel {
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 100vh;
    background: white;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: -4px 0 12px rgba(0, 0, 0, 0.15);
    display: flex;
    flex-direction: column;

    .menu-header {
      background: var(--ion-color-primary, #FF6B35);
      padding: 20px;
      color: white;
      display: flex;
      justify-content: space-between;
      align-items: center;

      h2 {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
      }

      ion-button {
        --background: rgba(255, 255, 255, 0.2);
        --color: white;
        --border-radius: 50%;
        width: 36px;
        height: 36px;
      }
    }

    .menu-content {
      flex: 1;
      padding: 20px;

      .profile-section {
        text-align: center;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
        margin-bottom: 20px;

        .profile-avatar {
          margin: 0 auto 12px auto;
          width: 60px;
          height: 60px;
          background: #f0f0f0;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          ion-icon {
            font-size: 30px;
            color: #999;
          }
        }

        h3 {
          margin: 0 0 4px 0;
          font-size: 16px;
          font-weight: 600;
          color: #333;
        }

        p {
          margin: 0;
          font-size: 14px;
          color: #666;
        }
      }

      .mobile-menu-item {
        display: block;
        padding: 16px 0;
        color: #333;
        text-decoration: none;
        border-bottom: 1px solid #f0f0f0;
        transition: color 0.3s ease;

        &:hover {
          color: var(--ion-color-primary, #FF6B35);
        }

        &:last-child {
          border-bottom: none;
        }
      }

      .logout-button {
        margin-top: 20px;
        width: 100%;
      }
    }
  }
}
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { IonicModule } from '@ionic/angular';
import { ComponentsModule } from 'mobile-components';
import { SecureRoutingModule } from './secure-routing.module';
// import { DynamicSecurePageComponent } from './dynamic-secure-page.component';

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    IonicModule,
    ReactiveFormsModule,
    ComponentsModule,
    SecureRoutingModule,
  ],
  schemas: [],
  providers: [],
})
export class SecureModule {}

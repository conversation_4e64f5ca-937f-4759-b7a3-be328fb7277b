import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DynamicSecurePageComponent } from './dynamic-secure-page.component';

const routes: Routes = [
  // All routes now use dynamic pages from environment configuration
  // Support nested paths like 'games/home', 'app/stores', etc.
  {
    path: ':page/:subpage/:subsubpage',
    component: DynamicSecurePageComponent,
  },
  {
    path: ':page/:subpage',
    component: DynamicSecurePageComponent,
  },
  {
    path: ':page',
    component: DynamicSecurePageComponent,
  },
  {
    path: '',
    component: DynamicSecurePageComponent,
    pathMatch: 'full',
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class SecureRoutingModule {}

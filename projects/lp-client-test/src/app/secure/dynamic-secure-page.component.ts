import { Component, Injector, ChangeDetectorRef } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { ComponentsModule } from 'mobile-components';
import { IonicModule } from '@ionic/angular';
import { KeyCloakService, MemberService } from 'lp-client-api';
import { ConfigService } from '../services/config.service';
import { DynamicComponentLoaderService } from '../shared/dynamic-component-loader.service';
import { PageConfigurationValidationService } from '../shared/page-configuration-validation.service';
import { BuilderBridgeReceiverService } from '../services/builder-bridge-receiver.service';
import { ComponentPropertyDiscoveryService } from '../shared/component-property-discovery.service';
import { AuthService } from '../shared/auth.service';
import { BaseDynamicPageComponent } from '../shared/base-dynamic-page.component';

@Component({
  selector: 'app-dynamic-secure-page',
  template: `
    <ion-content [scrollY]="true" [scrollX]="false">
      <div [ngClass]="pageClass" class="dynamic-components-container">
        <ng-template #container></ng-template>
      </div>
    </ion-content>
  `,
  styles: [`
    :host {
      display: block;
      height: 100%;
    }
    
    ion-content {
      --background: transparent;
      --color: inherit;
      height: 100%;
    }
    
    .dynamic-components-container {
      display: flex;
      flex-direction: column;
      gap: 0;
      min-height: 100%;
      padding: 0;
    }

    /* Ensure components are rendered without default margins */
    .dynamic-components-container > * {
      margin: 0;
    }
    
    /* Ensure proper scrolling behavior */
    .dynamic-components-container {
      overflow-y: visible;
      overflow-x: hidden;
    }
    
    /* Handle page classes that might set background colors */
    .dynamic-components-container.bg-base {
      background: var(--ion-color-base, #0b69b4);
    }
    
    .dynamic-components-container.h-full {
      min-height: 100%;
    }
    
    .dynamic-components-container.h-screen {
      height: 100vh;
      min-height: 100vh;
    }
  `],
  standalone: true,
  imports: [CommonModule, ComponentsModule, IonicModule]
})
export class DynamicSecurePageComponent extends BaseDynamicPageComponent {
  protected override debug = true; // Enable debug for secure pages

  constructor(
    route: ActivatedRoute,
    router: Router,
    loader: DynamicComponentLoaderService,
    injector: Injector,
    configService: ConfigService,
    cdr: ChangeDetectorRef,
    memberService: MemberService,
    kc: KeyCloakService,
    pageValidator: PageConfigurationValidationService,
    bridgeReceiver: BuilderBridgeReceiverService,
    propertyDiscovery: ComponentPropertyDiscoveryService,
    private authService: AuthService
  ) {
    super(route, router, loader, injector, configService, cdr, memberService, kc, pageValidator, bridgeReceiver, propertyDiscovery);
  }

  // Implement abstract methods from base class
  protected getComponentName(): string {
    return 'DynamicSecurePage';
  }

  protected getDefaultPage(): string {
    return 'dashboard';
  }

  // Override logout navigation for secure pages
  protected override handleLogoutNavigation(): void {
    console.log(`[${this.getComponentName()}] User logged out, redirecting to login`);
    this.router.navigate(['/public/login']);
  }

  // Override initial load handling for secure pages
  protected override handleInitialLoad(): void {
    // Check authentication and redirect if not authenticated
    if (!this.isAuthenticated) {
      console.log(`[${this.getComponentName()}] User not authenticated, redirecting to login`);
      this.router.navigate(['/public/login']);
      return;
    }

    // Call parent implementation for authenticated users
    super.handleInitialLoad();
  }

  // Override page security check for secure pages
  protected override checkPageSecurity(pageConfig: any): boolean {
    // Secure pages should only be accessible when authenticated
    if (pageConfig.secure && !this.isAuthenticated) {
      if (this.debug) console.log(`[${this.getComponentName()}] Condition MET: Secure page requested, but user not authenticated. Redirecting to login.`);
      this.router.navigate(['/public/login']);
      return false; // Stop loading components
    }
    return true;
  }

  // Override page not found handling for secure pages
  protected override handlePageNotFound(pageId: string): void {
    console.warn(`[${this.getComponentName()}] No configuration found for pageId: ${pageId}. Redirecting to dashboard.`);
    this.router.navigate(['/secure/dashboard']);
  }
}

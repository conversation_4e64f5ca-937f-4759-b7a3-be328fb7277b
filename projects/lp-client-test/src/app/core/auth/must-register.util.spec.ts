import { KeyCloakService } from 'lp-client-api';
import { mustRegister, hasValidAuthState } from './must-register.util';

describe('mustRegister utility', () => {
  let mockKeyCloakService: Partial<KeyCloakService>;

  beforeEach(() => {
    mockKeyCloakService = {
      authSuccess: true,
      lpUniueReference: 'TEST123',
      userProfile: {
        mustRegister: false
      }
    } as Partial<KeyCloakService>;
  });

  describe('mustRegister', () => {
    it('should return true when userProfile.mustRegister is true', () => {
      mockKeyCloakService.userProfile!.mustRegister = true;
      
      const result = mustRegister(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(true);
    });

    it('should return true when lpUniueReference is missing', () => {
      mockKeyCloakService.lpUniueReference = undefined;
      
      const result = mustRegister(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(true);
    });

    it('should return true when lpUniueReference is "Not Set"', () => {
      mockKeyCloakService.lpUniueReference = 'Not Set';
      
      const result = mustRegister(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(true);
    });

    it('should return true when lpUniueReference is empty string', () => {
      mockKeyCloakService.lpUniueReference = '';
      
      const result = mustRegister(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(true);
    });

    it('should return true when lpUniueReference is only whitespace', () => {
      mockKeyCloakService.lpUniueReference = '   ';
      
      const result = mustRegister(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(true);
    });

    it('should return false when user has valid membership and mustRegister is false', () => {
      mockKeyCloakService.userProfile!.mustRegister = false;
      mockKeyCloakService.lpUniueReference = 'VALID_MEMBERSHIP_123';
      
      const result = mustRegister(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(false);
    });

    it('should prioritize userProfile.mustRegister over membership reference', () => {
      mockKeyCloakService.userProfile!.mustRegister = true;
      mockKeyCloakService.lpUniueReference = 'VALID_MEMBERSHIP_123';
      
      const result = mustRegister(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(true);
    });
  });

  describe('hasValidAuthState', () => {
    it('should return true when authSuccess is true and userProfile exists', () => {
      mockKeyCloakService.authSuccess = true;
      mockKeyCloakService.userProfile = { email: '<EMAIL>' };
      
      const result = hasValidAuthState(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(true);
    });

    it('should return false when authSuccess is false', () => {
      mockKeyCloakService.authSuccess = false;
      mockKeyCloakService.userProfile = { email: '<EMAIL>' };
      
      const result = hasValidAuthState(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(false);
    });

    it('should return false when userProfile is missing', () => {
      mockKeyCloakService.authSuccess = true;
      mockKeyCloakService.userProfile = undefined;
      
      const result = hasValidAuthState(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(false);
    });

    it('should return false when userProfile is null', () => {
      mockKeyCloakService.authSuccess = true;
      mockKeyCloakService.userProfile = null;
      
      const result = hasValidAuthState(mockKeyCloakService as KeyCloakService);
      
      expect(result).toBe(false);
    });
  });
});
import { KeyCloakService } from 'lp-client-api';

/**
 * Determines if a user must register based on authentication state
 * Mirrors the logic used in lp-client's handleAuthLogin() method
 */
export function mustRegister(kc: KeyCloakService): boolean {
  // Primary check: userProfile mustRegister flag
  if (kc.userProfile?.mustRegister) {
    return true;
  }

  // Secondary check: missing or invalid membership reference
  const membershipRef = kc.lpUniueReference;
  if (!membershipRef || membershipRef === 'Not Set' || membershipRef.trim() === '') {
    return true;
  }

  return false;
}

/**
 * Type guard to check if we have the minimum required authentication state
 */
export function hasValidAuthState(kc: KeyCloakService): boolean {
  return kc.authSuccess && !!kc.userProfile;
}
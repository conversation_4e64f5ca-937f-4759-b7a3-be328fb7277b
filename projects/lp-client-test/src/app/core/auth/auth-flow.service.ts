import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, from, of, EMPTY } from 'rxjs';
import { switchMap, tap, catchError, take } from 'rxjs/operators';
import { Preferences } from '@capacitor/preferences';
import { 
  KeyCloakService, 
  MemberService, 
  MemberProfile,
  LssConfig 
} from 'lp-client-api';
import { mustRegister, hasValidAuthState } from './must-register.util';
import { MultiTenantContextService } from '../../services/multi-tenant-context.service';

export interface AuthStatus {
  eventName: string;
  [key: string]: any;
}

@Injectable({
  providedIn: 'root'
})
export class AuthFlowService {
  private readonly STORAGE_KEYS = {
    LOGIN: 'login',
    MEMBERSHIP_NUMBER: 'membershipNumber',
    RETURN_URL: 'returnUrl'
  };

  constructor(
    private readonly kc: KeyCloakService,
    private readonly memberService: MemberService,
    private readonly router: Router,
    private readonly lssConfig: LssConfig,
    private readonly multiTenantContext: MultiTenantContextService
  ) {}

  /**
   * Handle authentication login event - mirrors lp-client's handleAuthLogin()
   * This is the main entry point after successful login
   */
  handleAuthLogin(): Observable<void> {
    console.log('[AuthFlowService] Handling auth login');
    
    if (!hasValidAuthState(this.kc)) {
      console.error('[AuthFlowService] Invalid auth state');
      return EMPTY;
    }

    return from(this.saveTokens()).pipe(
      switchMap(() => this.loadProfileAndNotifications()),
      tap(() => console.log('[AuthFlowService] Profile and notifications loaded')),
      catchError(error => {
        console.error('[AuthFlowService] Error during login flow:', error);
        // Don't fail the entire flow - proceed with navigation
        return of(undefined);
      }),
      switchMap(() => this.navigateAfterLogin()),
      tap(() => console.log('[AuthFlowService] Navigation completed'))
    );
  }

  /**
   * Load profile and notifications - replicates lp-client's loadProfileAndNotifications
   */
  private loadProfileAndNotifications(): Observable<MemberProfile | undefined> {
    const emailUserId = this.kc.userProfile?.preferred_username || 
                       this.kc.userProfile?.email || 
                       null;
    const productId = String((this.lssConfig as any).productId ?? 'rmic');
    let mpacc = this.kc.lpUniueReference;

    console.log('[AuthFlowService] Loading profile with:', { 
      emailUserId, 
      productId, 
      mpacc,
      lssConfigKeys: Object.keys(this.lssConfig),
      apiBaseUrl: (this.lssConfig as any).apiBaseUrl,
      hasApiBaseUrl: !!(this.lssConfig as any).apiBaseUrl
    });

    // If we have all required parameters, load profile immediately
    if (emailUserId && productId && mpacc && mpacc !== 'Not Set' && mpacc.trim() !== '') {
      return this.memberService.loadProgramMemberProfile(emailUserId, productId, mpacc).pipe(
        tap(profile => this.handleProfileSuccess(profile)),
        catchError(error => {
          console.error('[AuthFlowService] Unified API FAILED:', error);
          return of(undefined);
        })
      );
    }

    // If MPACC is missing, try to find it by searching with email
    if (emailUserId && productId && (!mpacc || mpacc === 'Not Set' || mpacc.trim() === '')) {
      console.log('[AuthFlowService] MPACC not available. Searching by email...');
      
      return this.memberService.search({ emailAddress: emailUserId }).pipe(
        switchMap(profiles => {
          const found = Array.isArray(profiles) && profiles.length > 0 ? profiles[0] : null;
          const foundMpacc = found?.membershipNumber;
          
          if (foundMpacc && foundMpacc.trim() !== '') {
            mpacc = foundMpacc;
            console.log('[AuthFlowService] Found MPACC by email search:', mpacc);
            
            return this.memberService.loadProgramMemberProfile(emailUserId, productId, mpacc).pipe(
              tap(profile => this.handleProfileSuccess(profile))
            );
          } else {
            console.warn('[AuthFlowService] Could not resolve MPACC via search');
            return this.createMinimalProfile(emailUserId, productId);
          }
        }),
        catchError(error => {
          console.error('[AuthFlowService] Error searching for member:', error);
          return this.createMinimalProfile(emailUserId, productId);
        })
      );
    }

    // Fallback: create minimal profile if we can't load full profile
    if (emailUserId) {
      return this.createMinimalProfile(emailUserId, productId);
    }

    console.warn('[AuthFlowService] No email found, cannot create profile');
    return of(undefined);
  }

  /**
   * Handle successful profile loading
   */
  private handleProfileSuccess(profile: MemberProfile): void {
    // Update the profile subject to notify all subscribers
    this.memberService.profileSubject?.next(profile);

    const mpaccPersist = (
      profile.newMembershipNumber ||
      profile.membershipNumber ||
      profile.uniqueId ||
      ''
    ).toString();

    if (mpaccPersist) {
      try {
        Preferences.set({
          key: this.STORAGE_KEYS.MEMBERSHIP_NUMBER,
          value: mpaccPersist,
        });
      } catch (error) {
        console.error('[AuthFlowService] Error saving membership number:', error);
      }
      (this.lssConfig as any).memberCard = mpaccPersist;
    }

    // Signal completion to other services that might be listening
    this.memberService.profileLoadComplete?.next(true);
  }

  /**
   * Create minimal profile from token data
   */
  private createMinimalProfile(emailUserId: string, productId: string): Observable<MemberProfile> {
    const token = this.kc.userProfile;
    const minimalProfile: MemberProfile = {
      uniqueId: emailUserId,
      apiId: productId,
      externalId: emailUserId,
      emailAddress: token?.email || emailUserId,
      givenNames: (token as any)?.given_name || '',
      surname: (token as any)?.family_name || '',
      membershipNumber: undefined,
      newMembershipNumber: undefined
    } as MemberProfile;

    this.memberService.profileSubject?.next(minimalProfile);
    this.memberService.profileLoadComplete?.next(true);
    
    return of(minimalProfile);
  }

  /**
   * Save authentication tokens to preferences
   */
  private async saveTokens(): Promise<void> {
    if (this.kc.keycloak) {
      try {
        await Preferences.set({
          key: this.STORAGE_KEYS.LOGIN,
          value: JSON.stringify({
            refreshToken: this.kc.keycloak.refreshToken,
            idToken: this.kc.keycloak.idToken,
            token: this.kc.keycloak.token,
          }),
        });
        console.log('[AuthFlowService] Tokens saved');
      } catch (error) {
        console.error('[AuthFlowService] Error saving tokens:', error);
      }
    }
  }

  /**
   * Determine where to navigate after successful login
   */
  private navigateAfterLogin(): Observable<void> {
    // Check if we need to redirect from specific routes
    const currentUrl = this.router.url;
    console.log('[AuthFlowService] Current URL for navigation check:', currentUrl);

    // Routes that should redirect to secure dashboard after login
    const redirectRoutes = [
      '/public/signup', 
      '/public/login', 
      '/public/home',
      '/',
      '/public/landing'
    ];

    // Check for preserved return URL
    return from(this.getReturnUrl()).pipe(
      switchMap(returnUrl => {
        // Check if auto-redirect is enabled based on config
        const autoRedirect = this.lssConfig?.pages?.landing?.autoRedirectOnLogin !== false;
        let targetUrl = autoRedirect ? '/secure/dashboard' : '/public/landing';

        if (returnUrl && this.isValidReturnUrl(returnUrl)) {
          targetUrl = returnUrl;
          console.log('[AuthFlowService] Using preserved return URL:', targetUrl);
        } else if (redirectRoutes.includes(currentUrl) || currentUrl.startsWith('/public/login')) {
          // Use workflow-based navigation logic similar to lp-client PostLoginService
          const workflowConfig = this.lssConfig?.workflow;
          const isMultiTenant = workflowConfig?.multiTenant || false;
          const workflowType = workflowConfig?.type;
          
          if (autoRedirect) {
            if (isMultiTenant && workflowType === 'program-selection') {
              if (this.multiTenantContext.shouldRedirectToProgramSelection()) {
                targetUrl = this.multiTenantContext.getProgramSelectionRoute();
                console.log('[AuthFlowService] Multi-tenant mode - no program selected, redirecting to program selection');
              } else {
                targetUrl = '/secure/dashboard';
                console.log('[AuthFlowService] Multi-tenant mode - program selected, redirecting to dashboard');
              }
            } else {
              // Single-tenant: go directly to secure dashboard
              targetUrl = '/secure/dashboard';
              console.log('[AuthFlowService] Single-tenant mode - redirecting to dashboard');
            }
          } else {
            targetUrl = '/public/landing';
            console.log('[AuthFlowService] Auto-redirect disabled - staying on landing');
          }
        } else {
          // Stay on current route if it's a valid app route
          console.log('[AuthFlowService] Staying on current route:', currentUrl);
          return of(undefined);
        }

        // Clear return URL after use
        this.clearReturnUrl();
        
        // Navigate to target URL
        console.log('[AuthFlowService] Navigating to:', targetUrl);
        this.router.navigateByUrl(targetUrl, { replaceUrl: true });
        
        return of(undefined);
      })
    );
  }

  /**
   * Save return URL before authentication
   */
  async saveReturnUrl(url: string): Promise<void> {
    if (this.isValidReturnUrl(url)) {
      try {
        await Preferences.set({
          key: this.STORAGE_KEYS.RETURN_URL,
          value: url
        });
      } catch (error) {
        console.error('[AuthFlowService] Error saving return URL:', error);
      }
    }
  }

  /**
   * Get preserved return URL
   */
  private async getReturnUrl(): Promise<string | null> {
    try {
      const result = await Preferences.get({ key: this.STORAGE_KEYS.RETURN_URL });
      return result.value;
    } catch (error) {
      console.error('[AuthFlowService] Error getting return URL:', error);
      return null;
    }
  }

  /**
   * Clear return URL from storage
   */
  private async clearReturnUrl(): Promise<void> {
    try {
      await Preferences.remove({ key: this.STORAGE_KEYS.RETURN_URL });
    } catch (error) {
      console.error('[AuthFlowService] Error clearing return URL:', error);
    }
  }

  /**
   * Validate return URL to prevent malicious redirects
   */
  private isValidReturnUrl(url: string): boolean {
    if (!url || url.trim() === '') return false;
    
    // Must be internal route (starts with /)
    if (!url.startsWith('/')) return false;
    
    // Avoid redirect loops to auth pages
    const authRoutes = ['/public/login', '/public/signup', '/public/validate'];
    if (authRoutes.some(route => url.startsWith(route))) return false;
    
    return true;
  }

  /**
   * Get notification count - called after successful login
   */
  getNotificationCount(): void {
    this.kc.authed().then(isAuthed => {
      if (isAuthed) {
        const membershipId = this.kc.lpUniueReference;
        if (membershipId && membershipId !== 'Not Set') {
          this.memberService.getNotificationsCount(membershipId).subscribe({
            next: (body: any) => {
              console.log('[AuthFlowService] Notification count:', body.count);
            },
            error: (error) => {
              console.error('[AuthFlowService] Error getting notification count:', error);
            }
          });
        }
      }
    });
  }
}
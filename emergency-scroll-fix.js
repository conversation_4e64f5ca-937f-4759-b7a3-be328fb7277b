// EMERGENCY SCROLLING FIX
// Paste this into the browser console on the onboarding flow page

console.log('🚨 EMERGENCY SCROLLING FIX - Starting...');

function emergencyScrollFix() {
  // 1. Remove all height constraints from critical elements
  const criticalSelectors = [
    'html', 'body', 'ion-app', 'ion-content', 'ion-router-outlet',
    'lib-page-wrapper', 'app-onboarding-flow', '.onboarding-flow-content',
    '.main-layout', '.page-wrapper', '.flex-1', '.h-full', '.min-h-full'
  ];
  
  criticalSelectors.forEach(selector => {
    const elements = document.querySelectorAll(selector);
    elements.forEach(el => {
      el.style.height = 'auto';
      el.style.minHeight = 'auto';
      el.style.maxHeight = 'none';
      el.style.overflow = 'visible';
      el.style.flex = 'none';
    });
  });
  
  // 2. Force ion-content to be scrollable
  const ionContent = document.querySelector('ion-content');
  if (ionContent) {
    ionContent.style.overflowY = 'auto';
    ionContent.style.overflowX = 'hidden';
    ionContent.style.height = '100vh';
    ionContent.style.maxHeight = '100vh';
    
    // Fix scroll content inside ion-content
    const scrollContent = ionContent.querySelector('.scroll-content');
    if (scrollContent) {
      scrollContent.style.height = 'auto';
      scrollContent.style.minHeight = 'auto';
      scrollContent.style.overflow = 'visible';
    }
  }
  
  // 3. Add test content to make page definitely scrollable
  const testContent = document.createElement('div');
  testContent.id = 'emergency-scroll-test';
  testContent.style.cssText = `
    height: 200vh;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 40px;
    margin: 20px;
    border-radius: 10px;
    font-family: Arial, sans-serif;
    position: relative;
    z-index: 9999;
  `;
  
  testContent.innerHTML = `
    <div style="text-align: center;">
      <h2>🚨 EMERGENCY SCROLL TEST</h2>
      <p>This content is 200% of viewport height</p>
      <p>Current viewport height: ${window.innerHeight}px</p>
      <p>This div height: ${window.innerHeight * 2}px</p>
    </div>
    <div style="text-align: center;">
      <h2>🎯 SCROLL SUCCESS!</h2>
      <p>If you can see this by scrolling, the fix worked!</p>
      <button onclick="document.getElementById('emergency-scroll-test').remove()" 
              style="padding: 10px 20px; background: white; color: #333; border: none; border-radius: 5px; cursor: pointer;">
        Remove Test Content
      </button>
    </div>
  `;
  
  // Find the best place to insert test content
  const targets = [
    document.querySelector('.onboarding-flow-content'),
    document.querySelector('lib-page-wrapper'),
    document.querySelector('ion-content'),
    document.body
  ];
  
  const target = targets.find(t => t) || document.body;
  target.appendChild(testContent);
  
  console.log('✅ Emergency scroll fix applied!');
  console.log('📍 Test content added to:', target.tagName);
  
  // 4. Test scrolling
  setTimeout(() => {
    const scrollTest = () => {
      const scrollableElements = [
        document.documentElement,
        document.body,
        document.querySelector('ion-content'),
        document.querySelector('.scroll-content')
      ].filter(Boolean);
      
      console.log('🧪 Testing scroll on elements:', scrollableElements.length);
      
      scrollableElements.forEach((el, index) => {
        const initialScroll = el.scrollTop;
        el.scrollTop = 100;
        const newScroll = el.scrollTop;
        
        console.log(`Element ${index + 1} (${el.tagName}):`, 
                   `${initialScroll} → ${newScroll}`, 
                   newScroll > initialScroll ? '✅ SCROLLS' : '❌ NO SCROLL');
        
        // Reset
        el.scrollTop = initialScroll;
      });
    };
    
    scrollTest();
  }, 1000);
  
  return {
    testContentAdded: true,
    targetElement: target.tagName,
    viewportHeight: window.innerHeight,
    testContentHeight: window.innerHeight * 2
  };
}

// Auto-run the fix
const result = emergencyScrollFix();
console.log('🎯 Emergency fix result:', result);

// Make function available globally
window.emergencyScrollFix = emergencyScrollFix;

// Add a visual indicator
const indicator = document.createElement('div');
indicator.style.cssText = `
  position: fixed;
  top: 10px;
  right: 10px;
  background: #ff6b6b;
  color: white;
  padding: 10px;
  border-radius: 5px;
  font-family: Arial, sans-serif;
  font-size: 12px;
  z-index: 99999;
  box-shadow: 0 2px 10px rgba(0,0,0,0.3);
`;
indicator.innerHTML = '🚨 Emergency Scroll Fix Active';
document.body.appendChild(indicator);

// Remove indicator after 5 seconds
setTimeout(() => {
  indicator.remove();
}, 5000);

console.log('🎯 Instructions:');
console.log('1. Navigate to /secure/onboarding-flow');
console.log('2. You should see colorful test content');
console.log('3. Try scrolling to see if you can reach the bottom');
console.log('4. Run emergencyScrollFix() again if needed');

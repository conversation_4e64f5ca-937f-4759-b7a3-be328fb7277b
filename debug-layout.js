// Debug script to run in browser console
console.log("=== Layout Debug ===");

// Check if elements exist
const sidebar = document.querySelector('.desktop-sidebar');
const mainLayout = document.querySelector('.main-layout');
const mainContent = document.querySelector('.main-content');
const routerOutlet = document.querySelector('ion-router-outlet');
const header = document.querySelector('.modern-header');

console.log("Elements found:");
console.log("- Sidebar:", !!sidebar, sidebar ? "visible" : "hidden");
console.log("- Main Layout:", !!mainLayout);
console.log("- Main Content:", !!mainContent);
console.log("- Router Outlet:", !!routerOutlet);
console.log("- Header:", !!header);

// Check classes and styles
if (mainLayout) {
  console.log("Main Layout classes:", mainLayout.className);
  console.log("Main Layout computed margin-left:", getComputedStyle(mainLayout).marginLeft);
}

if (mainContent) {
  console.log("Main Content classes:", mainContent.className);
  console.log("Main Content computed styles:", {
    position: getComputedStyle(mainContent).position,
    width: getComputedStyle(mainContent).width,
    height: getComputedStyle(mainContent).height
  });
}

if (routerOutlet) {
  console.log("Router Outlet children:", routerOutlet.children.length);
  console.log("Router Outlet innerHTML length:", routerOutlet.innerHTML.length);
  console.log("Router Outlet computed styles:", {
    display: getComputedStyle(routerOutlet).display,
    position: getComputedStyle(routerOutlet).position,
    width: getComputedStyle(routerOutlet).width,
    height: getComputedStyle(routerOutlet).height
  });
}

// Check for ion-page content
const ionPages = document.querySelectorAll('.ion-page');
console.log("Ion Pages found:", ionPages.length);

ionPages.forEach((page, index) => {
  console.log(`Ion Page ${index}:`, {
    classes: page.className,
    display: getComputedStyle(page).display,
    position: getComputedStyle(page).position,
    top: getComputedStyle(page).top,
    left: getComputedStyle(page).left,
    width: getComputedStyle(page).width,
    height: getComputedStyle(page).height,
    visibility: getComputedStyle(page).visibility,
    opacity: getComputedStyle(page).opacity,
    zIndex: getComputedStyle(page).zIndex
  });
});

// Check viewport
console.log("Viewport:", {
  innerWidth: window.innerWidth,
  innerHeight: window.innerHeight
});